<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roomchik - Товары для дома и интерьера | Сайт в разработке</title>
    <meta name="description" content="Уникальный интернет-магазин товаров для дома и интерьера. Товары уже доступны для заказа! Создаем уютный дом вместе.">
    <meta name="keywords" content="товары для дома, интерьер, мебель, декор, уют, дизайн">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Roomchik - Товары для дома и интерьера">
    <meta property="og:description" content="Уникальный интернет-магазин товаров для дома и интерьера. Товары уже доступны для заказа!">
    <meta property="og:type" content="website">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏠</text></svg>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: #667eea;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Hero Section */
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 1rem 2.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: none;
            cursor: pointer;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        /* Features Section */
        .features {
            padding: 80px 0;
            background: white;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: #f8f9fa;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Products Section */
        .products {
            padding: 80px 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .products h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .products-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 3rem;
            font-size: 1.1rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f0f0f0;
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
            font-size: 1.1rem;
        }

        .product-price {
            color: #667eea;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .product-vendor {
            color: #999;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .badges {
            margin-bottom: 1rem;
        }

        .badge {
            display: inline-block;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            color: white;
            margin-right: 0.5rem;
        }

        .badge-new { background: #27ae60; }
        .badge-sale { background: #e74c3c; }
        .badge-bestseller { background: #f39c12; }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #666;
            font-size: 1.1rem;
        }

        .view-all-products {
            text-align: center;
            margin-top: 2rem;
        }

        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0 2rem;
            text-align: center;
        }

        .footer h3 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .footer p {
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .contact-info {
            margin: 2rem 0;
        }

        .contact-info a {
            color: #667eea;
            text-decoration: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 480px) {
            .hero h1 {
                font-size: 2rem;
            }

            .cta-button {
                padding: 0.8rem 2rem;
                font-size: 1rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    🏠 Roomchik
                </a>
                <div class="status-badge">
                    В разработке
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Создаем уютный дом вместе</h1>
                <p>Мы создаем уникальный интернет-магазин товаров для дома и интерьера. Товары уже доступны для заказа!</p>
                <button class="cta-button" onclick="scrollToProducts()">
                    Посмотреть товары 🛍️
                </button>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2>Почему выбирают нас</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🏡</div>
                    <h3>Товары для дома</h3>
                    <p>Широкий ассортимент качественных товаров для создания уютного и стильного дома</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>Стильный интерьер</h3>
                    <p>Помогаем создать интерьер мечты с помощью тщательно отобранных предметов декора</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🚚</div>
                    <h3>Быстрая доставка</h3>
                    <p>Оперативная доставка по всей стране с возможностью отслеживания заказа</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products" id="products">
        <div class="container">
            <h2>Наши товары</h2>
            <p class="products-subtitle">Уже сейчас доступны для заказа</p>
            
            <div id="products-container">
                <div class="loading">
                    Загружаем товары... 🔄
                </div>
            </div>
            
            <div class="view-all-products">
                <button class="cta-button" onclick="loadMoreProducts()">
                    Загрузить еще товары
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>🏠 Roomchik</h3>
            <p>Скоро откроется полноценный интернет-магазин товаров для дома и интерьера</p>
            <div class="contact-info">
                <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>📱 Телефон: <a href="tel:+380123456789">+38 (012) 345-67-89</a></p>
            </div>
            <p>&copy; 2024 Roomchik. Все права защищены.</p>
        </div>
    </footer>

    <script>
        // Конфигурация API
        const API_CONFIG = {
            // Замените на ваш реальный домен API
            baseUrl: 'https://api.roomchik.com/api',
            // Альтернативные варианты:
            // baseUrl: 'https://your-domain.com:3001/api',
            // baseUrl: 'http://localhost:3001/api' // для локального тестирования
        };

        let currentPage = 1;
        let isLoading = false;

        // Функция для плавной прокрутки к товарам
        function scrollToProducts() {
            document.getElementById('products').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Загрузка товаров
        async function loadProducts(page = 1, append = false) {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('products-container');
            
            if (!append) {
                container.innerHTML = '<div class="loading">Загружаем товары... 🔄</div>';
            }

            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/products?limit=12&page=${page}`);
                const result = await response.json();

                if (result.success && result.data.length > 0) {
                    const productsHtml = result.data.map(product => `
                        <div class="product-card">
                            <img src="${product.image || 'https://via.placeholder.com/280x200?text=Товар'}" 
                                 alt="${product.name}" 
                                 class="product-image"
                                 onerror="this.src='https://via.placeholder.com/280x200?text=Товар'">
                            <div class="product-info">
                                <div class="badges">
                                    ${product.is_new ? '<span class="badge badge-new">Новинка</span>' : ''}
                                    ${product.is_on_sale ? '<span class="badge badge-sale">Скидка</span>' : ''}
                                    ${product.is_bestseller ? '<span class="badge badge-bestseller">Хит</span>' : ''}
                                </div>
                                <div class="product-name">${product.name}</div>
                                <div class="product-vendor">${product.vendor || product.brand || ''}</div>
                                <div class="product-price">
                                    ${product.price} ₴
                                    ${product.original_price && product.original_price > product.price ? 
                                      `<span style="text-decoration: line-through; color: #999; margin-left: 10px; font-size: 0.9rem;">${product.original_price} ₴</span>` : ''}
                                </div>
                            </div>
                        </div>
                    `).join('');

                    if (append) {
                        const existingGrid = container.querySelector('.products-grid');
                        if (existingGrid) {
                            existingGrid.innerHTML += productsHtml;
                        }
                    } else {
                        container.innerHTML = `
                            <div class="products-grid">
                                ${productsHtml}
                            </div>
                        `;
                    }
                } else if (!append) {
                    container.innerHTML = `
                        <div class="loading">
                            <p>Товары скоро появятся! 🛍️</p>
                            <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.7;">
                                Мы работаем над наполнением каталога
                            </p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Ошибка загрузки товаров:', error);
                container.innerHTML = `
                    <div class="loading">
                        <p>Временные технические работы 🔧</p>
                        <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.7;">
                            Попробуйте обновить страницу через несколько минут
                        </p>
                    </div>
                `;
            } finally {
                isLoading = false;
            }
        }

        // Загрузить больше товаров
        function loadMoreProducts() {
            currentPage++;
            loadProducts(currentPage, true);
        }

        // Загрузка товаров при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
        });

        // Плавная анимация при скролле
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
    </script>
</body>
</html>
