<!DOCTYPE html>
<html>
<head>
    <title>Supabase Test</title>
</head>
<body>
    <h1>Testing Supabase Client</h1>
    <div id="results"></div>
    
    <script>
        // Test if we can access the app's console logs
        console.log('Test page loaded');
        
        // Try to access the app's window object
        setTimeout(() => {
            const results = document.getElementById('results');
            try {
                // Try to get Supabase from the main window
                if (window.parent && window.parent !== window) {
                    const parentConsole = window.parent.console;
                    if (parentConsole) {
                        results.innerHTML += '<p>✅ Can access parent console</p>';
                    }
                }
                
                results.innerHTML += '<p>Console logs should appear in browser DevTools</p>';
                results.innerHTML += '<p>Press F12 to open DevTools and check Console tab</p>';
                
            } catch (error) {
                results.innerHTML += '<p>❌ Error: ' + error.message + '</p>';
            }
        }, 1000);
    </script>
</body>
</html>
