<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email & Service Worker Test Suite</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-good { color: #10b981; font-weight: 600; }
        .status-warning { color: #f59e0b; font-weight: 600; }
        .status-error { color: #ef4444; font-weight: 600; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px 4px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }
        button:hover { background: #2563eb; }
        .danger { background: #ef4444; }
        .danger:hover { background: #dc2626; }
        .success { background: #10b981; }
        .success:hover { background: #059669; }
        .log {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 16px;
            margin: 16px 0;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .test-result {
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-success {
            background: #ecfdf5;
            border-color: #10b981;
            color: #047857;
        }
        .test-error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #dc2626;
        }
        .test-warning {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #d97706;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Email & Service Worker Test Suite</h1>
        <p>This tool helps you verify that your email system and service worker are functioning correctly after the recent fixes.</p>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🔧 Service Worker Tests</h2>
            <div id="sw-status">Checking...</div>
            <button onclick="testServiceWorker()">Run SW Tests</button>
            <button onclick="clearServiceWorker()" class="danger">Clear SW Cache</button>
            <div id="sw-results"></div>
        </div>

        <div class="container">
            <h2>📧 Email System Tests</h2>
            <div id="email-status">Ready to test</div>
            <button onclick="testEmailFunction()">Test Email Function</button>
            <button onclick="testEmailLogs()">Check Email Logs</button>
            <div id="email-results"></div>
        </div>
    </div>

    <div class="container">
        <h2>🌐 Network & API Tests</h2>
        <button onclick="testSupabaseConnection()">Test Supabase</button>
        <button onclick="testStaticAssets()">Test Static Assets</button>
        <button onclick="testApiEndpoints()">Test API Endpoints</button>
        <div id="network-results"></div>
    </div>

    <div class="container">
        <h2>📊 Test Results</h2>
        <div id="logs" class="log">Test results will appear here...\n</div>
        <button onclick="clearLogs()">Clear Logs</button>
        <button onclick="exportResults()" class="success">Export Results</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logs.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(`${prefix} ${message}`);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = 'Logs cleared...\n';
        }

        function addTestResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        async function testServiceWorker() {
            log('🔧 Starting Service Worker tests...');
            const resultsContainer = document.getElementById('sw-results');
            resultsContainer.innerHTML = '';

            try {
                // Test 1: Service Worker Support
                if ('serviceWorker' in navigator) {
                    addTestResult('sw-results', '✅ Service Worker supported', 'success');
                    log('Service Worker is supported');
                } else {
                    addTestResult('sw-results', '❌ Service Worker not supported', 'error');
                    log('Service Worker not supported', 'error');
                    return;
                }

                // Test 2: Registration Status
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    addTestResult('sw-results', `✅ Service Worker registered (${registration.scope})`, 'success');
                    log(`Service Worker registered at: ${registration.scope}`);
                    
                    // Test 3: Active Status
                    if (registration.active) {
                        addTestResult('sw-results', '✅ Service Worker is active', 'success');
                        log('Service Worker is active');
                    } else {
                        addTestResult('sw-results', '⚠️ Service Worker not active', 'warning');
                        log('Service Worker not active', 'warning');
                    }
                } else {
                    addTestResult('sw-results', '⚠️ Service Worker not registered', 'warning');
                    log('Service Worker not registered', 'warning');
                }

                // Test 4: Cache Status
                const cacheNames = await caches.keys();
                if (cacheNames.length > 0) {
                    addTestResult('sw-results', `✅ Found ${cacheNames.length} cache(s): ${cacheNames.join(', ')}`, 'success');
                    log(`Active caches: ${cacheNames.join(', ')}`);
                } else {
                    addTestResult('sw-results', '⚠️ No caches found', 'warning');
                    log('No caches found', 'warning');
                }

                // Test 5: Fetch Test
                const testResponse = await fetch('/manifest.json');
                if (testResponse.ok) {
                    addTestResult('sw-results', '✅ Fetch requests working normally', 'success');
                    log('Fetch test successful');
                } else {
                    addTestResult('sw-results', '❌ Fetch test failed', 'error');
                    log('Fetch test failed', 'error');
                }

            } catch (error) {
                addTestResult('sw-results', `❌ Service Worker test error: ${error.message}`, 'error');
                log(`Service Worker test error: ${error.message}`, 'error');
            }
        }

        async function clearServiceWorker() {
            log('🧹 Clearing Service Worker and caches...');
            try {
                // Unregister service worker
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    await registration.unregister();
                    log('Service Worker unregistered', 'success');
                }

                // Clear all caches
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                log(`Cleared ${cacheNames.length} cache(s)`, 'success');

                // Reload page
                setTimeout(() => {
                    log('Reloading page in 2 seconds...');
                    window.location.reload();
                }, 2000);

            } catch (error) {
                log(`Error clearing Service Worker: ${error.message}`, 'error');
            }
        }

        async function testEmailFunction() {
            log('📧 Testing email function...');
            const resultsContainer = document.getElementById('email-results');
            resultsContainer.innerHTML = '';

            try {
                const response = await fetch('/api/send-test-email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        to: '<EMAIL>',
                        type: 'test'
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    addTestResult('email-results', '✅ Email function test successful', 'success');
                    log(`Email test successful: ${JSON.stringify(result)}`);
                } else {
                    addTestResult('email-results', `❌ Email function test failed: ${response.status}`, 'error');
                    log(`Email test failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addTestResult('email-results', `❌ Email test error: ${error.message}`, 'error');
                log(`Email test error: ${error.message}`, 'error');
            }
        }

        async function testEmailLogs() {
            log('📋 Checking email logs...');
            try {
                // This would need to be connected to your actual Supabase client
                log('Email logs check - would need Supabase client integration', 'warning');
                addTestResult('email-results', '⚠️ Email logs check requires Supabase integration', 'warning');
            } catch (error) {
                log(`Email logs error: ${error.message}`, 'error');
            }
        }

        async function testSupabaseConnection() {
            log('🗄️ Testing Supabase connection...');
            const resultsContainer = document.getElementById('network-results');
            
            try {
                // Test basic connectivity to Supabase
                const response = await fetch('https://dmdijuuwnbwngerkbfak.supabase.co/rest/v1/', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE5Nzg3ODIsImV4cCI6MjA0NzU1NDc4Mn0.6nM6DX8gYXxdSF4EGNHwFSUoYO7PtGuNg1vXC9_cT0g'
                    }
                });

                if (response.ok) {
                    addTestResult('network-results', '✅ Supabase connection successful', 'success');
                    log('Supabase connection test passed');
                } else {
                    addTestResult('network-results', `❌ Supabase connection failed: ${response.status}`, 'error');
                    log(`Supabase connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                addTestResult('network-results', `❌ Supabase test error: ${error.message}`, 'error');
                log(`Supabase test error: ${error.message}`, 'error');
            }
        }

        async function testStaticAssets() {
            log('🖼️ Testing static assets...');
            const assets = ['/favicon.ico', '/manifest.json', '/logo192.png'];
            
            for (const asset of assets) {
                try {
                    const response = await fetch(asset);
                    if (response.ok) {
                        addTestResult('network-results', `✅ Static asset loaded: ${asset}`, 'success');
                        log(`Static asset OK: ${asset}`);
                    } else {
                        addTestResult('network-results', `❌ Static asset failed: ${asset}`, 'error');
                        log(`Static asset failed: ${asset}`, 'error');
                    }
                } catch (error) {
                    addTestResult('network-results', `❌ Static asset error: ${asset}`, 'error');
                    log(`Static asset error ${asset}: ${error.message}`, 'error');
                }
            }
        }

        async function testApiEndpoints() {
            log('🔗 Testing API endpoints...');
            // Add tests for your specific API endpoints here
            addTestResult('network-results', '⚠️ API endpoint tests - configure for your specific endpoints', 'warning');
            log('API endpoint tests need configuration for your specific endpoints', 'warning');
        }

        function exportResults() {
            const logs = document.getElementById('logs').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-results-${new Date().toISOString().slice(0,19)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            log('Test results exported', 'success');
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            log('🚀 Test suite loaded - ready to run tests');
            testServiceWorker();
        });

        // Update status indicators
        function updateStatus() {
            // Service Worker status
            navigator.serviceWorker.getRegistration().then(reg => {
                const swStatus = document.getElementById('sw-status');
                if (reg && reg.active) {
                    swStatus.innerHTML = '<span class="status-good">✅ Service Worker Active</span>';
                } else if (reg) {
                    swStatus.innerHTML = '<span class="status-warning">⚠️ Service Worker Registered but not Active</span>';
                } else {
                    swStatus.innerHTML = '<span class="status-error">❌ Service Worker Not Registered</span>';
                }
            });
        }

        updateStatus();
        setInterval(updateStatus, 5000); // Update every 5 seconds
    </script>
</body>
</html>
