const CACHE_NAME = 'online-store-v2';
const STATIC_ASSETS = [
  '/favicon.ico',
  '/logo192.png',
  '/logo512.png',
  '/manifest.json'
];

// Установка сервис-воркера и предварительное кеширование статических ресурсов
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME).then(cache => {
      console.log('Предварительное кеширование статических ресурсов');
      return cache.addAll(STATIC_ASSETS).catch(error => {
        console.error('Error caching static assets:', error);
        // Continue even if some assets fail to cache
        return Promise.resolve();
      });
    })
  );
  // Take control immediately
  self.skipWaiting();
});

// Активация сервис-воркера и очистка старых кешей
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then(keyList => {
      return Promise.all(
        keyList.map(key => {
          if (key !== CACHE_NAME) {
            console.log('Удаление старого кеша:', key);
            return caches.delete(key);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker activated');
      return self.clients.claim();
    })
  );
});

// Очень консервативная стратегия кеширования - только для статических ресурсов
self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  
  // Полностью игнорируем все API запросы и динамический контент
  if (
    // API запросы
    event.request.url.includes('/api/') ||
    event.request.url.includes('supabase') ||
    event.request.url.includes('amazonaws.com') ||
    event.request.url.includes('resend.com') ||
    // Динамические методы
    event.request.method !== 'GET' ||
    // Динамические страницы React Router
    url.pathname.includes('/admin') ||
    url.pathname.includes('/profile') ||
    url.pathname.includes('/orders') ||
    url.pathname.includes('/product') ||
    url.pathname.includes('/category') ||
    // Исключаем HTML страницы полностью
    event.request.headers.get('accept')?.includes('text/html')
  ) {
    // Просто пропускаем - никакого кеширования
    return;
  }
  
  // Кешируем только явно статические ресурсы
  if (
    url.pathname.endsWith('.png') ||
    url.pathname.endsWith('.jpg') ||
    url.pathname.endsWith('.jpeg') ||
    url.pathname.endsWith('.gif') ||
    url.pathname.endsWith('.svg') ||
    url.pathname.endsWith('.ico') ||
    url.pathname.endsWith('.webp') ||
    url.pathname.endsWith('.css') ||
    url.pathname.endsWith('.js') ||
    url.pathname.endsWith('.woff') ||
    url.pathname.endsWith('.woff2') ||
    url.pathname.endsWith('.ttf') ||
    url.pathname === '/manifest.json'
  ) {
    event.respondWith(
      caches.match(event.request).then(cachedResponse => {
        if (cachedResponse) {
          return cachedResponse;
        }
        
        return fetch(event.request).then(response => {
          if (response && response.status === 200) {
            const responseToCache = response.clone();
            caches.open(CACHE_NAME).then(cache => {
              cache.put(event.request, responseToCache);
            });
          }
          return response;
        }).catch(error => {
          console.error('Fetch error:', error);
          throw error;
        });
      })
    );
  }
});

// Обработка сообщений от клиентского кода
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});