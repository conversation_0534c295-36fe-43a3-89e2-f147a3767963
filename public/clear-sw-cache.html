<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a8a;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #a71e2a;
        }
        .success {
            background: #28a745;
        }
        .success:hover {
            background: #1e7e34;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Service Worker Debug Tool</h1>
        
        <div>
            <h3>Service Worker Status</h3>
            <p id="sw-status">Проверяем...</p>
            <button onclick="checkServiceWorkerStatus()">Обновить статус</button>
        </div>

        <div>
            <h3>Actions</h3>
            <button onclick="unregisterServiceWorker()" class="danger">Удалить Service Worker</button>
            <button onclick="clearAllCaches()" class="danger">Очистить все кеши</button>
            <button onclick="forceUpdate()" class="success">Принудительное обновление</button>
            <button onclick="testFetch()">Тест fetch запроса</button>
        </div>

        <div>
            <h3>Cache Information</h3>
            <button onclick="listCaches()">Показать кеши</button>
            <div id="cache-info"></div>
        </div>

        <div>
            <h3>Logs</h3>
            <div id="logs" class="log"></div>
            <button onclick="clearLogs()">Очистить логи</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function checkServiceWorkerStatus() {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        const status = `
                            Зарегистрирован: Да<br>
                            Scope: ${registration.scope}<br>
                            Active: ${registration.active ? 'Да' : 'Нет'}<br>
                            Installing: ${registration.installing ? 'Да' : 'Нет'}<br>
                            Waiting: ${registration.waiting ? 'Да' : 'Нет'}
                        `;
                        document.getElementById('sw-status').innerHTML = status;
                        log('Service Worker статус получен');
                    } else {
                        document.getElementById('sw-status').textContent = 'Service Worker не зарегистрирован';
                        log('Service Worker не найден');
                    }
                } catch (error) {
                    log(`Ошибка проверки Service Worker: ${error.message}`);
                    document.getElementById('sw-status').textContent = `Ошибка: ${error.message}`;
                }
            } else {
                document.getElementById('sw-status').textContent = 'Service Worker не поддерживается';
                log('Service Worker не поддерживается браузером');
            }
        }

        async function unregisterServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        await registration.unregister();
                        log('Service Worker успешно удален');
                        checkServiceWorkerStatus();
                    } else {
                        log('Service Worker не зарегистрирован');
                    }
                } catch (error) {
                    log(`Ошибка удаления Service Worker: ${error.message}`);
                }
            }
        }

        async function clearAllCaches() {
            try {
                const cacheNames = await caches.keys();
                const deletePromises = cacheNames.map(cacheName => caches.delete(cacheName));
                await Promise.all(deletePromises);
                log(`Удалено кешей: ${cacheNames.length}`);
                listCaches();
            } catch (error) {
                log(`Ошибка очистки кешей: ${error.message}`);
            }
        }

        async function listCaches() {
            try {
                const cacheNames = await caches.keys();
                let cacheInfo = `<p>Найдено кешей: ${cacheNames.length}</p>`;
                
                for (const cacheName of cacheNames) {
                    const cache = await caches.open(cacheName);
                    const keys = await cache.keys();
                    cacheInfo += `<p><strong>${cacheName}</strong>: ${keys.length} записей</p>`;
                }
                
                document.getElementById('cache-info').innerHTML = cacheInfo;
                log('Информация о кешах обновлена');
            } catch (error) {
                log(`Ошибка получения информации о кешах: ${error.message}`);
            }
        }

        function forceUpdate() {
            if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
                navigator.serviceWorker.controller.postMessage({type: 'SKIP_WAITING'});
                log('Отправлен сигнал принудительного обновления');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                log('Нет активного Service Worker для обновления');
            }
        }

        async function testFetch() {
            try {
                log('Тестируем fetch запрос...');
                const response = await fetch('/manifest.json');
                if (response.ok) {
                    log(`Fetch успешен: ${response.status} ${response.statusText}`);
                } else {
                    log(`Fetch ошибка: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                log(`Fetch исключение: ${error.message}`);
            }
        }

        // Автоматически проверяем статус при загрузке
        window.addEventListener('load', () => {
            checkServiceWorkerStatus();
            listCaches();
            log('Debug tool загружен');
        });

        // Слушаем ошибки
        window.addEventListener('error', (event) => {
            log(`Глобальная ошибка: ${event.error?.message || event.message}`);
        });
    </script>
</body>
</html>
