<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>404 | Unsplash Source</title>
  <style>
    html,
    body {
      height: 100%;
    }

    body {
      height: 100%;
      width: 100%;
      background-color: #111111;
      font-family: -apple-system, BlinkMacSystemFont, "San Francisco", "Helvetica Neue", Helvetica, Ubuntu, Roboto, Noto, "Segoe UI", Arial, sans-serif;
      color: #ffffff;
      text-align: center;
      margin: 0;
      background-size: cover;
      background-position: center center;
    }

    .background-overlay {
      width: 100%;
      height: 100%;

      position: fixed;
      z-index: 8;
      background: transparent;
      background: -webkit-linear-gradient(top, rgba(0,0,0,0.80) 0%, rgba(0,0,0,0.50) 40%, rgba(0,0,0,0.50) 70%, rgba(0,0,0,0.80) 100%);
      background: linear-gradient(to bottom, rgba(0,0,0,0.80) 0%, rgba(0,0,0,0.50) 40%, rgba(0,0,0,0.50) 70%, rgba(0,0,0,0.80) 100%);
    }

    .content {
      position: relative;
      z-index: 10;
      height: 100%;
      width: 100%;
    }

    .header {
      position: absolute;
      top: 30px;
      z-index: 12;
      width: 100%;
    }

    .header svg {
      margin-top: -13px;
      width: 32px;
    }

    h1 {
      font-size: 80px;
      margin: 0;
      font-weight: 800;
      line-height: 1;
      letter-spacing: 0.04em;
      text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    a,
    a:hover,
    a:visited {
      color: #ffffff;
    }

    a {
      opacity: 0.6;
      -webkit-transition: all 0.3s ease-in-out;
      transition: all 0.3s ease-in-out;
    }
    a:hover,
    a:focus,
    a:active {
      opacity: 1;
    }

    .error-message {
      margin-top: 12px;
      margin-bottom: 36px;
    }

    .error-message__main {
      font-size: 18px;
      margin: 12px auto;
      line-height: 1.5em;
      text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .error-message__sub {
      font-size: 15px;
      margin: 12px auto;
      line-height: 1.5em;
      text-shadow: 0 1px 2px rgba(0,0,0,0.1);
      opacity: 0.8;
    }

    .table-wrapper {
      display: table;
      width: 100%;
      height: 100%;
    }

    .container {
      margin: auto;
      max-width: 520px;
    }

    .cell-wrapper {
      display: table-cell;
      vertical-align: middle;
    }

    .btn {
      font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
      display: inline-block;
      color: #ffffff;
      padding: 0 10px;
      line-height: 32px;
      font-size: 11px;
      font-weight: 600;
      border-radius: 6px;
      border: 1px solid #ffffff;
      text-transform: uppercase;
      text-decoration: none;
      letter-spacing: 0.06em;
      background-color: transparent;
    }

    .btn:hover {
      text-decoration: none;
      color: #ffffff;
      border-color: #ffffff;
    }
  </style>
</head>
<body>
  <div class="background-overlay"></div>
  <div class="header">
    <div class="container">
      <a href="/" title="Back to Unsplash">
        <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
          <path d="M10 9V0h12v9H10zm12 5h10v18H0V14h10v9h12v-9z" fill="#ffffff" fill-rule="nonzero"/>
        </svg>
      </a>
    </div> <!-- close .container -->
  </div> <!-- close .header -->

  <div class="content">
    <div class="table-wrapper">
      <div class="cell-wrapper">

        <div class="container">

          <h1>404</h1>

          <div class="error-message">
            <p class="error-message__main">The photo you were looking for doesn't match Unsplash Source's URL structure.</p>
          </div> <!-- close .error-message -->
        </div> <!-- close .container -->

        <a href="/" class="btn">
          Back to Source
        </a>
      </div> <!-- close .cell-wrapper -->
    </div> <!-- close .table-wrapper -->
  </div> <!-- close .content -->

  <script>
    (function (window, undefined) {

      var c,
        cache,
        state,
        view,
        dispatch,
        defaults;

      c = cache = {};
      state = {};
      view = {};
      dispatch = {};
      defaults = {
        gifs: {
          fail: [
            'https://images.unsplash.com/gifs/fail/fail-1.gif',
            'https://images.unsplash.com/gifs/fail/fail-2.gif',
            'https://images.unsplash.com/gifs/fail/fail-3.gif',
            'https://images.unsplash.com/gifs/fail/fail-5.gif',
            'https://images.unsplash.com/gifs/fail/fail-6.gif',
            'https://images.unsplash.com/gifs/fail/fail-7.gif',
            'https://images.unsplash.com/gifs/fail/fail-8.gif',
            'https://images.unsplash.com/gifs/fail/fail-9.gif',
            'https://images.unsplash.com/gifs/fail/fail-11.gif',
            'https://images.unsplash.com/gifs/fail/fail-12.gif',
            'https://images.unsplash.com/gifs/fail/fail-13.gif',
            'https://images.unsplash.com/gifs/fail/fail-14.gif',
            'https://images.unsplash.com/gifs/fail/fail-15.gif',
            'https://images.unsplash.com/gifs/fail/fail-16.gif',
            'https://images.unsplash.com/gifs/fail/fail-17.gif',
            'https://images.unsplash.com/gifs/fail/fail-18.gif',
            'https://images.unsplash.com/gifs/fail/fail-20.gif',
            'https://images.unsplash.com/gifs/fail/fail-21.gif',
            'https://images.unsplash.com/gifs/fail/fail-22.gif',
          ],
          weird: [
            'https://images.unsplash.com/gifs/weird/weird-1.gif',
            'https://images.unsplash.com/gifs/weird/weird-3.gif',
            'https://images.unsplash.com/gifs/weird/weird-4.gif',
            'https://images.unsplash.com/gifs/weird/weird-6.gif',
            'https://images.unsplash.com/gifs/weird/weird-8.gif',
            'https://images.unsplash.com/gifs/weird/weird-9.gif',
            'https://images.unsplash.com/gifs/weird/weird-10.gif',
            'https://images.unsplash.com/gifs/weird/weird-11.gif',
            'https://images.unsplash.com/gifs/weird/weird-12.gif',
            'https://images.unsplash.com/gifs/weird/weird-13.gif',
            'https://images.unsplash.com/gifs/weird/weird-14.gif',
            'https://images.unsplash.com/gifs/weird/weird-15.gif',
            'https://images.unsplash.com/gifs/weird/weird-16.gif',
          ]
        }
      };

      cache.init = function () {
        this.body = document.body;
      };

      state.init = function () {
        this.gifs = defaults.gifs.fail;
      };

      state.getRandomGif = function () {
        return this.gifs[Math.floor(Math.random() * this.gifs.length)];
      };

      view.init = function () {
        c.body.style.backgroundImage = 'url(' + state.getRandomGif() + ')';
      };

      cache.init();
      state.init();
      view.init();

    })(window);
  </script>

  <script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

    ga('create', 'UA-36049670-4', 'auto');
    ga('send', 'pageview');
    ga('send', 'event', 'action', '404');
  </script>

</body>
</html>
