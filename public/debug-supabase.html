<!DOCTYPE html>
<html>
<head>
    <title>Supabase Debug</title>
</head>
<body>
    <h1>Supabase Debug Test</h1>
    <div id="output"></div>
    
    <script>
        // Test if we can access the main React app's console logs
        console.log('Debug page loaded');
        
        // Check if we can see the console logs from the main app
        window.addEventListener('message', (event) => {
            console.log('Message received:', event.data);
        });
        
        // Simple test to see console output
        setInterval(() => {
            console.log('Debug interval check:', new Date().toISOString());
        }, 5000);
    </script>
</body>
</html>
