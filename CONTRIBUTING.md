# CONTRIBUTING.md

Спасибо за интерес к участию в разработке проекта «Online Store»! Ниже описаны основные правила и рекомендации.

## 1. Процесс внесения изменений

1. Форкните репозиторий и клонируйте вашу копию:
   ```bash
   git clone https://github.com/your-org/online-store.git
   cd online-store
   ```
2. Создайте новую ветку от `main`:
   ```bash
   git checkout main
   git pull origin main
   git checkout -b feat/описание-вашей-фичи
   ```
3. Вносите изменения в отдельной ветке. Для мелких правок можно использовать `fix/...`, для задач — `feat/...`, для исправлений багов — `bugfix/...`.
4. Перед коммитом убедитесь, что все тесты проходят и код форматирован:
   ```bash
   npm run lint:fix
   npm run format:all
   npm test -- --watchAll=false
   ```
5. Закоммитьте изменения с ясным сообщением:
   - Тема: коротко опишите суть (например: `feat: добавить пагинацию на странице продуктов`)
   - Тело (необязательно): детализация, мотивация, описание подхода.
6. Отправьте ветку в ваш форк и откройте Pull Request в `main`:
   ```bash
   git push origin feat/описание-ваши-фичи
   ```

## 2. Code Style и форматирование

- Проект использует **ESLint** и **Prettier**.
- Настроены pre-commit хуки (Husky + lint-staged) для автоматической проверки.
- Пожалуйста, запускайте перед коммитом:
  ```bash
  npm run lint:fix
  npm run format:all
  ```

## 3. Тестирование

- Пишите unit-тесты с **Jest + React Testing Library** для новых компонентов и функций.
- Для end-to-end тестов используйте **Cypress** (при наличии) — примеры можно найти в `cypress/`.

## 4. Релиз-план

1. Обновите версию в `package.json` согласно семантическому версионированию (SemVer):
   - `PATCH` (исправления багов)
   - `MINOR` (новый функционал без ломки API)
   - `MAJOR` (изменения, ломающие обратную совместимость)
2. Добавьте запись в `CHANGELOG.md` (формат `Keep a Changelog`):
   ```markdown
   ## [1.2.0] - 2025-07-03
   ### Added
   - Описание новой фичи
   ### Fixed
   - Описание исправления
   ```
3. После слияния PR создаётся тег и релиз в GitHub:
   ```bash
   git tag v1.2.0
   git push origin v1.2.0
   ```

---

Спасибо за ваш вклад! Если есть вопросы, создавайте Issue или пишите в чат команды. 