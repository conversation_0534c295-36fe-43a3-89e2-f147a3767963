# 🏠 Roomchik Landing Page - Руководство по настройке

## 📁 Файлы для размещения

1. **`landing-page.html`** - Основная страница-заглушка
2. **Этот файл с инструкциями** (не нужен на сервере)

## 🚀 Быстрая настройка

### 1. Настройка API URL

В файле `landing-page.html` найдите секцию:

```javascript
// Конфигурация API
const API_CONFIG = {
    baseUrl: 'https://api.roomchik.com/api',
};
```

**Замените URL на ваш реальный домен API:**

```javascript
const API_CONFIG = {
    baseUrl: 'https://your-domain.com/api',
    // или если API на другом порту:
    // baseUrl: 'https://your-domain.com:3001/api',
};
```

### 2. Настройка контактной информации

Найдите секцию footer и обновите контакты:

```html
<div class="contact-info">
    <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
    <p>📱 Телефон: <a href="tel:+380123456789">+38 (012) 345-67-89</a></p>
</div>
```

### 3. Настройка мета-тегов

Обновите мета-теги в `<head>`:

```html
<title>Ваш магазин - Товары для дома и интерьера | Сайт в разработке</title>
<meta name="description" content="Ваше описание магазина">
<meta property="og:title" content="Ваш магазин - Товары для дома и интерьера">
```

## 🌐 Размещение на сервере

### Вариант 1: Простое размещение
1. Загрузите `landing-page.html` в корневую папку сайта
2. Переименуйте в `index.html`
3. Готово! Страница доступна по адресу вашего домена

### Вариант 2: В подпапке
1. Создайте папку `landing` на сервере
2. Загрузите `landing-page.html` в эту папку
3. Переименуйте в `index.html`
4. Страница будет доступна по адресу `yourdomain.com/landing/`

## 🔧 Настройка API сервера

### На том же сервере
Если API будет на том же сервере, что и landing page:

```javascript
const API_CONFIG = {
    baseUrl: '/api', // относительный путь
};
```

### На отдельном сервере/поддомене
```javascript
const API_CONFIG = {
    baseUrl: 'https://api.yourdomain.com/api',
};
```

### С портом
```javascript
const API_CONFIG = {
    baseUrl: 'https://yourdomain.com:3001/api',
};
```

## 🎨 Кастомизация дизайна

### Изменение цветовой схемы
Найдите CSS переменные в стилях:

```css
/* Основные цвета */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* Фон героя */
background: linear-gradient(45deg, #ff6b6b, #ee5a24); /* Кнопки */
color: #667eea; /* Логотип и акценты */
```

### Изменение текстов
Все тексты находятся в HTML и легко редактируются:

- Заголовок: `<h1>Создаем уютный дом вместе</h1>`
- Описание: `<p>Мы создаем уникальный интернет-магазин...</p>`
- Кнопка: `Посмотреть товары 🛍️`

### Добавление логотипа
Замените эмодзи в логотипе на изображение:

```html
<a href="#" class="logo">
    <img src="logo.png" alt="Logo" style="height: 40px;">
    Roomchik
</a>
```

## 📱 Мобильная адаптация

Страница полностью адаптивна и будет корректно отображаться на:
- 📱 Мобильных устройствах
- 📱 Планшетах  
- 💻 Десктопах
- 🖥️ Больших экранах

## 🔍 SEO оптимизация

Страница уже включает:
- ✅ Мета-теги для поисковиков
- ✅ Open Graph для соцсетей
- ✅ Семантическую разметку
- ✅ Быструю загрузку
- ✅ Мобильную адаптацию

## 🚨 Устранение неполадок

### API не загружается
1. Проверьте URL в `API_CONFIG.baseUrl`
2. Убедитесь, что API сервер запущен
3. Проверьте CORS настройки на API сервере
4. Откройте консоль браузера (F12) для просмотра ошибок

### Товары не отображаются
1. Проверьте, что в базе данных есть товары
2. Убедитесь, что товары имеют `is_active: true`
3. Проверьте формат ответа API

### Проблемы с изображениями
Страница использует placeholder изображения, если основные не загружаются.

## 📞 Поддержка

При возникновении проблем:
1. Проверьте консоль браузера (F12 → Console)
2. Убедитесь в правильности настройки API URL
3. Проверьте доступность API сервера

## 🎯 Готовые варианты URL для API

```javascript
// Локальное тестирование
baseUrl: 'http://localhost:3001/api'

// Поддомен
baseUrl: 'https://api.yourdomain.com/api'

// Основной домен с портом
baseUrl: 'https://yourdomain.com:3001/api'

// Основной домен с путем
baseUrl: 'https://yourdomain.com/api'

// Относительный путь (если на том же сервере)
baseUrl: '/api'
```

Выберите подходящий вариант и замените в коде!
