name: Automatic Feed Processing

on:
  schedule:
    # Запускается каждые 6 часов: в 00:00, 06:00, 12:00, 18:00 UTC
    - cron: '0 */6 * * *'
  workflow_dispatch: # Позволяет запускать вручную

jobs:
  process-feeds:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Process all active feeds
      env:
        REACT_APP_SUPABASE_URL: ${{ secrets.REACT_APP_SUPABASE_URL }}
        REACT_APP_SUPABASE_ANON_KEY: ${{ secrets.REACT_APP_SUPABASE_ANON_KEY }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🚀 Starting automatic feed processing..."
        node scripts/process-feeds.js
        echo "✅ Feed processing completed"

    - name: Send notification on failure
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          const title = '❌ Feed Processing Failed'
          const body = `
          Automatic feed processing failed at ${new Date().toISOString()}
          
          **Workflow:** ${{ github.workflow }}
          **Run ID:** ${{ github.run_id }}
          **Repository:** ${{ github.repository }}
          
          Please check the logs for details.
          `
          
          // Можно добавить отправку в Slack, Discord, Telegram или email
          console.log(title)
          console.log(body)
# Force workflow update
