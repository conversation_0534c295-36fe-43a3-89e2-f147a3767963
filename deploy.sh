#!/bin/bash

# Quick Deploy Script for roomchic.shop
# This script helps you deploy your React build to your existing server

set -e  # Exit on any error

echo "🚀 Room Chic Shop - Deployment Script"
echo "=====================================

# Configuration
BUILD_DIR="build"
DOMAIN="roomchic.shop"
BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if build directory exists
check_build() {
    print_step "Checking build directory..."
    if [ ! -d "$BUILD_DIR" ]; then
        print_error "Build directory not found. Running npm run build..."
        npm run build
        if [ $? -eq 0 ]; then
            print_success "Build completed successfully"
        else
            print_error "Build failed. Please fix errors and try again."
            exit 1
        fi
    else
        print_success "Build directory found"
    fi
}

# Test local build
test_local() {
    print_step "Testing local build..."
    if command -v serve >/dev/null 2>&1; then
        print_success "serve is available"
        echo "  You can test locally with: npx serve -s build -p 3000"
    else
        print_warning "serve not found. Install with: npm install -g serve"
    fi
}

# Generate deployment package
create_package() {
    print_step "Creating deployment package..."
    
    PACKAGE_NAME="roomchic-shop-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    cd "$BUILD_DIR"
    tar -czf "../$PACKAGE_NAME" .
    cd ..
    
    print_success "Package created: $PACKAGE_NAME"
    echo "  Package size: $(du -h "$PACKAGE_NAME" | cut -f1)"
}

# Show deployment options
show_deployment_options() {
    print_step "Deployment Options"
    echo ""
    echo "Since your domain $DOMAIN is already configured with nginx,"
    echo "you have several options:"
    echo ""
    echo "🔥 Option 1: Manual Upload (Recommended)"
    echo "   1. Download the package: $PACKAGE_NAME"
    echo "   2. Upload to your server via FTP/SFTP/Panel"
    echo "   3. Extract in web root directory"
    echo "   4. Update nginx configuration if needed"
    echo ""
    echo "🔧 Option 2: Direct Server Access (if you have SSH)"
    echo "   1. scp $PACKAGE_NAME user@$DOMAIN:/var/www/"
    echo "   2. ssh user@$DOMAIN"
    echo "   3. cd /var/www && tar -xzf $PACKAGE_NAME"
    echo "   4. sudo systemctl reload nginx"
    echo ""
    echo "🌐 Option 3: Alternative Hosting"
    echo "   - Deploy to Vercel: npx vercel --prod"
    echo "   - Deploy to Netlify: npx netlify deploy --prod --dir=build"
    echo "   - Use subdomain: staging.roomchic.shop"
    echo ""
}

# Check domain status
check_domain() {
    print_step "Checking domain status..."
    
    echo "🔍 DNS Check:"
    if nslookup "$DOMAIN" >/dev/null 2>&1; then
        IP=$(nslookup "$DOMAIN" | grep "Address:" | tail -1 | cut -d' ' -f2)
        print_success "Domain resolves to: $IP"
    else
        print_error "Domain resolution failed"
        return 1
    fi
    
    echo ""
    echo "🌐 HTTP Check:"
    if curl -s -I "http://$DOMAIN" >/dev/null 2>&1; then
        STATUS=$(curl -s -I "http://$DOMAIN" | head -1)
        print_success "HTTP response: $STATUS"
    else
        print_warning "HTTP check failed"
    fi
    
    echo ""
    echo "🔒 HTTPS Check:"
    if curl -s -I "https://$DOMAIN" >/dev/null 2>&1; then
        STATUS=$(curl -s -I "https://$DOMAIN" | head -1)
        print_success "HTTPS response: $STATUS"
    else
        print_warning "HTTPS check failed"
    fi
}

# Generate nginx configuration
generate_nginx_config() {
    print_step "Generating nginx configuration..."
    
    cat > nginx-roomchic.conf << 'EOF'
server {
    listen 80;
    listen [::]:80;
    server_name roomchic.shop www.roomchic.shop;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name roomchic.shop www.roomchic.shop;

    # SSL configuration (adjust paths as needed)
    ssl_certificate /etc/ssl/certs/roomchic.shop.crt;
    ssl_certificate_key /etc/ssl/private/roomchic.shop.key;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Document root
    root /var/www/roomchic.shop;
    index index.html index.htm;

    # Handle React Router
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Service worker - no cache
    location /serviceWorker.js {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # API proxy (if needed for Supabase)
    location /api/ {
        proxy_pass https://dmdijuuwnbwngerkbfak.supabase.co/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Security
    location ~ /\. {
        deny all;
    }
}
EOF

    print_success "Nginx configuration saved to: nginx-roomchic.conf"
    echo "  📝 Copy this to: /etc/nginx/sites-available/roomchic.shop"
    echo "  🔗 Enable with: sudo ln -s /etc/nginx/sites-available/roomchic.shop /etc/nginx/sites-enabled/"
    echo "  ♻️  Reload with: sudo systemctl reload nginx"
}

# Main execution
main() {
    echo "Starting deployment process for $DOMAIN..."
    echo ""
    
    check_build
    test_local
    create_package
    check_domain
    generate_nginx_config
    show_deployment_options
    
    echo ""
    print_success "Deployment preparation complete!"
    echo ""
    echo "📦 Files ready:"
    echo "  - Build package: $PACKAGE_NAME"
    echo "  - Nginx config: nginx-roomchic.conf"
    echo ""
    echo "🎯 Next steps:"
    echo "  1. Upload $PACKAGE_NAME to your server"
    echo "  2. Extract in web root: tar -xzf $PACKAGE_NAME"
    echo "  3. Update nginx configuration"
    echo "  4. Test at https://roomchic.shop"
    echo ""
    echo "🔍 For troubleshooting, test locally first:"
    echo "  npx serve -s build -p 3000"
    echo "  Then visit: http://localhost:3000"
}

# Run main function
main
