-- Миграция для добавления новых полей к таблице banners для поддержки слайдера

-- Добавляем новые колонки
ALTER TABLE banners 
ADD COLUMN IF NOT EXISTS subtitle TEXT,
ADD COLUMN IF NOT EXISTS button_text VARCHAR(100),
ADD COLUMN IF NOT EXISTS button_link VARCHAR(500),
ADD COLUMN IF NOT EXISTS slider_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS auto_play BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS auto_play_interval INTEGER DEFAULT 5000;

-- Обновляем существующие записи, устанавливая значения по умолчанию
UPDATE banners 
SET 
  subtitle = COALESCE(subtitle, ''),
  button_text = COALESCE(button_text, 'Подробнее'),
  button_link = COALESCE(button_link, link_url, '/products'),
  slider_enabled = COALESCE(slider_enabled, false),
  auto_play = COALESCE(auto_play, true),
  auto_play_interval = COALESCE(auto_play_interval, 5000)
WHERE 
  subtitle IS NULL 
  OR button_text IS NULL 
  OR button_link IS NULL 
  OR slider_enabled IS NULL 
  OR auto_play IS NULL 
  OR auto_play_interval IS NULL;

-- Создаем индекс для быстрого поиска активных баннеров слайдера
CREATE INDEX IF NOT EXISTS idx_banners_slider_active 
ON banners (slider_enabled, is_active, position) 
WHERE slider_enabled = true AND is_active = true;

-- Добавляем комментарии к новым полям
COMMENT ON COLUMN banners.subtitle IS 'Подзаголовок баннера для слайдера';
COMMENT ON COLUMN banners.button_text IS 'Текст кнопки в баннере';
COMMENT ON COLUMN banners.button_link IS 'Ссылка кнопки в баннере';
COMMENT ON COLUMN banners.slider_enabled IS 'Включен ли баннер в слайдер';
COMMENT ON COLUMN banners.auto_play IS 'Автопроигрывание слайдера';
COMMENT ON COLUMN banners.auto_play_interval IS 'Интервал автопроигрывания в миллисекундах';
