-- Fix product_params RLS policies for admin operations
-- This will allow authenticated users to manage product parameters

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;

-- Create new policies that allow authenticated users (especially admins) to manage product_params
CREATE POLICY "product_params_select_policy" ON product_params
  FOR SELECT USING (true); -- Allow everyone to read product parameters

CREATE POLICY "product_params_insert_policy" ON product_params
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL -- Require authentication
  );

CREATE POLICY "product_params_update_policy" ON product_params
  FOR UPDATE USING (
    auth.uid() IS NOT NULL -- Require authentication
  );

CREATE POLICY "product_params_delete_policy" ON product_params
  FOR DELETE USING (
    auth.uid() IS NOT NULL -- Require authentication
  );

-- Grant necessary permissions
GRANT ALL ON product_params TO authenticated;
GRANT SELECT ON product_params TO anon;
