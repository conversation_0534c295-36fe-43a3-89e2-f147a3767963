#!/usr/bin/env node

// Check Supabase Edge Function deployment and setup
console.log('🔧 Starting Edge Function diagnostic...');

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('📦 Dependencies loaded');

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

console.log('🔑 Environment check:');
console.log('   URL present:', !!supabaseUrl);
console.log('   Key present:', !!supabaseKey);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkEdgeFunction() {
  console.log('📧 Testing send-email Edge Function...\n');
  
  const testPayload = {
    type: 'order_confirmation',
    orderId: 'test-12345',
    orderData: {
      id: 'test-12345',
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+1234567890',
      total_amount: 100,
      created_at: new Date().toISOString(),
      shipping_address: {
        city: 'Test City',
        nova_poshta_office: '1'
      },
      order_items: [
        {
          id: 1,
          product_name: 'Test Product',
          quantity: 1,
          price: 100
        }
      ],
      payment_method: 'cash_on_delivery',
      payment_status: 'pending'
    }
  };

  try {
    console.log('🧪 Invoking send-email function with test data...');
    
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: testPayload
    });

    if (error) {
      console.log('❌ Function invocation failed:');
      console.log('   Error:', error.message);
      
      if (error.message.includes('not found')) {
        console.log('\n💡 Possible solutions:');
        console.log('   1. Deploy the function: npx supabase functions deploy send-email');
        console.log('   2. Check function name in Supabase Dashboard');
      } else if (error.message.includes('401') || error.message.includes('403')) {
        console.log('\n💡 Possible solutions:');
        console.log('   1. Check API key permissions');
        console.log('   2. Verify function is public or authenticated properly');
      } else if (error.message.includes('400')) {
        console.log('\n💡 This might be due to missing RESEND_API_KEY');
        console.log('   Configure environment variables in Supabase Dashboard');
      }
      
      return false;
    }

    console.log('✅ Function responded successfully!');
    console.log('   Response:', data);
    
    if (data?.success) {
      console.log('🎉 Email function is working correctly!');
      return true;
    } else {
      console.log('⚠️ Function responded but with error:', data?.error);
      return false;
    }

  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
    return false;
  }
}

async function checkEnvironmentSetup() {
  console.log('\n🔍 Environment Setup Checklist:\n');
  
  console.log('1. Supabase Project Configuration:');
  console.log(`   ✅ URL: ${supabaseUrl}`);
  console.log(`   ✅ Anon Key: ${supabaseKey ? 'Configured' : 'Missing'}`);
  
  console.log('\n2. Required Edge Function Environment Variables:');
  console.log('   🔑 RESEND_API_KEY: Required for sending emails');
  console.log('   📧 FROM_EMAIL: Optional (defaults to resend.dev)');
  console.log('   🔗 SUPABASE_URL: Required for database access');
  console.log('   🔐 SUPABASE_SERVICE_ROLE_KEY: Required for database access');
  
  console.log('\n3. Setup Instructions:');
  console.log('   📋 Go to: https://supabase.com/dashboard');
  console.log('   🎯 Navigate to: Settings → Edge Functions');
  console.log('   ➕ Add secrets:');
  console.log('      RESEND_API_KEY=your_resend_key_here');
  console.log('      FROM_EMAIL=<EMAIL>');
}

async function provideNextSteps(functionWorks) {
  console.log('\n' + '='.repeat(60));
  console.log('📋 NEXT STEPS:');
  console.log('='.repeat(60));
  
  if (functionWorks) {
    console.log('🎉 GREAT! Your email system is fully operational!');
    console.log('\n✅ What works:');
    console.log('   • Supabase Edge Function deployed');
    console.log('   • Email service responding');
    console.log('   • Integration with frontend');
    console.log('\n🧪 Test in your app:');
    console.log('   • Create a test order at: http://localhost:3001');
    console.log('   • Check admin panel at: http://localhost:3001/admin');
  } else {
    console.log('🔧 Email system needs configuration');
    console.log('\n❌ Current issues:');
    console.log('   • Edge Function not working properly');
    console.log('   • Likely missing RESEND_API_KEY');
    console.log('\n🛠️ To fix:');
    console.log('   1. Get Resend API key from https://resend.com');
    console.log('   2. Add it to Supabase Edge Functions settings');
    console.log('   3. Redeploy function: npx supabase functions deploy send-email');
    console.log('\n⏰ Current workaround:');
    console.log('   • MockEmailService is working as fallback');
    console.log('   • Orders can be created without real emails');
  }
}

async function main() {
  console.log('='.repeat(60));
  console.log('🧪 SUPABASE EDGE FUNCTION DIAGNOSTIC');
  console.log('='.repeat(60));
  
  const functionWorks = await checkEdgeFunction();
  await checkEnvironmentSetup();
  await provideNextSteps(functionWorks);
  
  console.log('\n📚 Documentation:');
  console.log('   • Full setup: EMAIL_SETUP_INSTRUCTIONS.md');
  console.log('   • Status report: EMAIL_SYSTEM_FINAL_STATUS.md');
}

main().catch(console.error);
