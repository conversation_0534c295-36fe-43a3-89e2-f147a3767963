-- Remove problematic email triggers that cause "schema 'net' does not exist" error
-- These triggers use net.http_post() which requires PostgreSQL net extension

-- Remove triggers
DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders;
DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders;

-- <PERSON><PERSON><PERSON> functions
DROP FUNCTION IF EXISTS send_order_confirmation_email();
DROP FUNCTION IF EXISTS send_status_update_email();

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';
