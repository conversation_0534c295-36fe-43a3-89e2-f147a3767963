# Manual Email Notifications Deployment Guide

Since Docker is not available, here's how to deploy the email notification system manually using the Supabase Dashboard.

## Step 1: Deploy the Edge Function

1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/dmdijuuwnbwngerkbfak
2. Navigate to **Edge Functions** in the left sidebar
3. Click **Create a new function**
4. Name it: `send-email`
5. Copy the entire content from `supabase/functions/send-email/index.ts` and paste it into the editor
6. Click **Deploy function**

## Step 2: Set Environment Variables

1. In the Supabase Dashboard, go to **Settings** > **Edge Functions**
2. Click **Environment Variables**
3. Add these variables:
   - `RESEND_API_KEY`: Your Resend API key (get from https://resend.com)
   - `FROM_EMAIL`: Your sender email (optional, <NAME_EMAIL>)
   - `SUPABASE_URL`: https://dmdijuuwnbwngerkbfak.supabase.co
   - `SUPABASE_SERVICE_ROLE_KEY`: Your service role key from .env.local

## Step 3: Create Database Tables and Triggers

1. Go to **SQL Editor** in your Supabase Dashboard
2. Copy and run the following SQL commands one by one:

### Create Email Logs Table:
```sql
-- Create email logs table for tracking sent emails
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  recipient TEXT NOT NULL,
  email_type TEXT NOT NULL, -- 'order_confirmation', 'status_update'
  subject TEXT NOT NULL,
  status TEXT DEFAULT 'pending', -- 'pending', 'sent', 'failed'
  sent_at TIMESTAMPTZ,
  error_message TEXT,
  external_id TEXT, -- ID from email service provider (e.g., Resend)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_email_logs_order_id ON email_logs(order_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);

-- Enable RLS (Row Level Security)
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for email_logs table
CREATE POLICY email_logs_insert_policy ON email_logs
    FOR INSERT
    TO authenticated, anon, service_role
    WITH CHECK (true);

CREATE POLICY email_logs_select_policy ON email_logs
    FOR SELECT
    TO authenticated, anon, service_role
    USING (true);

CREATE POLICY email_logs_update_policy ON email_logs
    FOR UPDATE
    TO authenticated, anon, service_role
    USING (true);

-- Grant permissions
GRANT ALL ON email_logs TO authenticated;
GRANT ALL ON email_logs TO anon;
GRANT ALL ON email_logs TO service_role;
```

### Create Email Functions and Triggers:

**ВАЖНО:** Сначала включите HTTP расширение в SQL Editor:
```sql
-- Enable HTTP extension for making HTTP requests from database
CREATE EXTENSION IF NOT EXISTS http;
```

Затем создайте функции:
```sql
-- Create function to send order confirmation email
CREATE OR REPLACE FUNCTION send_order_confirmation_email()
RETURNS TRIGGER AS $$
DECLARE
  request_id int;
BEGIN
  -- Only send confirmation email for new orders
  IF TG_OP = 'INSERT' THEN
    -- Call the Edge Function to send email using http extension
    SELECT http_post(
      'https://dmdijuuwnbwngerkbfak.supabase.co/functions/v1/send-email',
      jsonb_build_object(
        'type', 'order_confirmation',
        'orderId', NEW.id::text
      )::text,
      'application/json',
      ARRAY[
        http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU'),
        http_header('Content-Type', 'application/json')
      ]
    ) INTO request_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to send status update email
CREATE OR REPLACE FUNCTION send_status_update_email()
RETURNS TRIGGER AS $$
DECLARE
  request_id int;
BEGIN
  -- Only send status update email when status actually changes
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    -- Call the Edge Function to send email using http extension
    SELECT http_post(
      'https://dmdijuuwnbwngerkbfak.supabase.co/functions/v1/send-email',
      jsonb_build_object(
        'type', 'status_update',
        'orderId', NEW.id::text,
        'oldStatus', OLD.status
      )::text,
      'application/json',
      ARRAY[
        http_header('Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU'),
        http_header('Content-Type', 'application/json')
      ]
    ) INTO request_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders;
CREATE TRIGGER trigger_send_order_confirmation_email
  AFTER INSERT ON orders
  FOR EACH ROW
  EXECUTE FUNCTION send_order_confirmation_email();

DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders;  
CREATE TRIGGER trigger_send_status_update_email
  AFTER UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION send_status_update_email();
```

## Step 4: Get Resend API Key

1. Go to https://resend.com
2. Sign up for an account (free tier includes 3,000 emails/month)
3. Create an API key
4. Copy the API key and add it to the Edge Functions environment variables

## Step 5: Test the System

1. Start your React application: `npm start`
2. Go to the Admin Panel > Settings > Email Notifications
3. Use the **Test Email** feature to verify everything works
4. Create a test order to check automatic email sending

## Step 6: Verify Deployment

After deployment, verify that:

1. ✅ Edge Function `send-email` is deployed and shows as active
2. ✅ Environment variables are set (RESEND_API_KEY, FROM_EMAIL)
3. ✅ `email_logs` table is created
4. ✅ Database triggers are created and active
5. ✅ Email Service in frontend can connect to the system

## Troubleshooting

### If emails are not sending:
1. Check Edge Functions logs in Supabase Dashboard
2. Verify RESEND_API_KEY is correct
3. Check email_logs table for error messages
4. Ensure triggers are created properly

### If database operations fail:
1. Check that orders table exists
2. Verify RLS policies allow operations
3. Check that net.http_post extension is available

### If Edge Function fails:
1. Check function logs in Dashboard
2. Verify environment variables are set
3. Test function manually using the Dashboard

## Next Steps

Once deployed:
1. Monitor email logs in the admin panel
2. Customize email templates as needed
3. Set up your own domain in Resend for better deliverability
4. Monitor usage and upgrade plans if needed

---

**Estimated Setup Time: 15-20 minutes**

The system will automatically send:
- Order confirmation emails when new orders are created
- Status update emails when order status changes
- All emails are logged for monitoring and debugging
