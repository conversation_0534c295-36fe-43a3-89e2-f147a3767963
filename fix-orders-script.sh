#!/bin/bash

# Fix orders database issues script
# This script will apply the SQL fixes to resolve order creation problems

echo "🔧 Starting order database fixes..."

# Check if Supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "fix-orders-rls.sql" ]; then
    echo "❌ fix-orders-rls.sql not found. Please run this script from the project root."
    exit 1
fi

echo "📋 Applying SQL fixes to database..."

# Apply the SQL fixes
if supabase db reset --local 2>/dev/null; then
    echo "✅ Local database reset successful"
    echo "📤 Applying fixes to remote database..."
    
    # Apply to remote database
    supabase db push
    
    if [ $? -eq 0 ]; then
        echo "✅ Database fixes applied successfully!"
    else
        echo "⚠️  Remote push failed, trying direct SQL execution..."
        # Fallback: execute SQL directly
        cat fix-orders-rls.sql | supabase db sql
    fi
else
    echo "ℹ️  Local reset not available, applying SQL directly..."
    # Execute SQL directly
    cat fix-orders-rls.sql | supabase db sql
fi

echo ""
echo "🧪 Testing order creation..."

# Test order creation with Node.js
cat > test-order-creation.js << 'EOF'
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('❌ REACT_APP_SUPABASE_ANON_KEY not found in environment');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testOrderCreation() {
  console.log('🧪 Testing order creation...');
  
  try {
    // Test 1: Basic table access
    console.log('1️⃣ Testing table access...');
    const { data: testAccess, error: accessError } = await supabase
      .from('orders')
      .select('id')
      .limit(1);
    
    if (accessError) {
      console.error('❌ Table access failed:', accessError.message);
      return false;
    }
    console.log('✅ Table access successful');
    
    // Test 2: Order insertion
    console.log('2️⃣ Testing order insertion...');
    const testOrder = {
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+1234567890',
      shipping_address: { city: 'Test City' },
      total_amount: 99.99,
      status: 'test',
      payment_method: 'test',
      payment_status: 'test',
      notes: 'Test order'
    };
    
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert([testOrder])
      .select()
      .single();
    
    if (orderError) {
      console.error('❌ Order insertion failed:', orderError.message);
      return false;
    }
    console.log('✅ Order insertion successful, ID:', order.id);
    
    // Test 3: Order items insertion
    console.log('3️⃣ Testing order items insertion...');
    const testItem = {
      order_id: order.id,
      product_id: '550e8400-e29b-41d4-a716-************', // dummy UUID
      product_name: 'Test Product',
      quantity: 1,
      price: 99.99
    };
    
    const { data: item, error: itemError } = await supabase
      .from('order_items')
      .insert([testItem])
      .select()
      .single();
    
    if (itemError) {
      console.error('❌ Order items insertion failed:', itemError.message);
      // Clean up order
      await supabase.from('orders').delete().eq('id', order.id);
      return false;
    }
    console.log('✅ Order items insertion successful');
    
    // Test 4: Cleanup
    console.log('4️⃣ Cleaning up test data...');
    await supabase.from('order_items').delete().eq('order_id', order.id);
    await supabase.from('orders').delete().eq('id', order.id);
    console.log('✅ Cleanup successful');
    
    return true;
  } catch (error) {
    console.error('❌ Test failed with exception:', error.message);
    return false;
  }
}

testOrderCreation().then(success => {
  if (success) {
    console.log('\n🎉 All tests passed! Order creation should now work properly.');
  } else {
    console.log('\n❌ Tests failed. Please check the database configuration.');
  }
  process.exit(success ? 0 : 1);
});
EOF

# Run the test
if command -v node &> /dev/null; then
    echo "🧪 Running order creation test..."
    node test-order-creation.js
    
    # Clean up test file
    rm -f test-order-creation.js
else
    echo "ℹ️  Node.js not found, skipping automatic test"
    echo "   Please test order creation manually in your application"
fi

echo ""
echo "🎯 Order database fixes completed!"
echo ""
echo "📝 Next steps:"
echo "   1. Test order creation in your application"
echo "   2. Check browser console for any remaining errors"
echo "   3. Verify email notifications are working"
echo ""
echo "🔍 If issues persist, check:"
echo "   - Supabase project settings"
echo "   - RLS policies in the Supabase dashboard"
echo "   - Network connectivity"
