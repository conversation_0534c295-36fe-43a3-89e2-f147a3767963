const { createClient } = require('@supabase/supabase-js');
const { executeSqlBatch } = require('../src/utils/database');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Setup Supabase client
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to split SQL content into statements while preserving PL/pgSQL blocks
function splitSqlStatements(sqlContent) {
  const statements = [];
  let currentStatement = '';
  let inDollarQuote = false;
  let dollarTag = '';
  
  // Split the content into lines to process
  const lines = sqlContent.split('\n');
  
  for (const line of lines) {
    // Skip empty lines and comments
    if (line.trim() === '' || line.trim().startsWith('--')) {
      currentStatement += line + '\n';
      continue;
    }
    
    // Check for dollar quoted blocks (like $$ or $BODY$)
    if (!inDollarQuote) {
      // Look for beginning of dollar quote
      const dollarQuoteMatch = line.match(/\$([a-zA-Z0-9_]*)\$/);
      if (dollarQuoteMatch) {
        inDollarQuote = true;
        dollarTag = dollarQuoteMatch[0];
      }
    } else {
      // Look for matching end of dollar quote
      if (line.includes(dollarTag)) {
        inDollarQuote = false;
        dollarTag = '';
      }
    }
    
    currentStatement += line + '\n';
    
    // If we're not in a dollar quote block and the line contains a semicolon
    if (!inDollarQuote && line.includes(';')) {
      statements.push(currentStatement.trim());
      currentStatement = '';
    }
  }
  
  // Add the last statement if there's anything left
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }
  
  return statements;
}

async function runMigrations() {
  try {
    console.log('Running SQL migrations...');
    const sqlPath = path.join(__dirname, 'migrations', 'update_structure.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Split SQL into individual statements with proper handling of PL/pgSQL blocks
    const statements = splitSqlStatements(sql);
    console.log(`Found ${statements.length} SQL statements to execute`);

    // Check if exec_sql function exists
    try {
      await supabase.rpc('exec_sql', { query: 'SELECT 1' });
      
      // If we reach here, exec_sql exists, so use batch execution
      console.log('Using batch execution for improved performance');
      const result = await executeSqlBatch(statements);
      
      if (!result) {
        console.warn('Warning: Some migrations may have failed');
      }
      
    } catch (error) {
      // exec_sql function doesn't exist, so create it first
      console.log('Creating exec_sql function first...');
      // Create the SQL execution function
      const createFuncSql = `
        CREATE OR REPLACE FUNCTION exec_sql(query text)
        RETURNS void AS $$
        BEGIN
          EXECUTE query;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
      `;

      await supabase.from('_temp_migrations').insert([{ query: createFuncSql }]);

      // Execute statements one by one after creating the function
      for (const statement of statements) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        const { error: retryError } = await supabase.rpc('exec_sql', { query: statement });
        if (retryError) {
          console.warn(`Warning executing statement: ${retryError.message}`);
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error running migrations:', error);
    return false;
  }
}

// Function to create tables directly using Supabase
async function createTables() {
  try {
    console.log('Creating required tables...');

    // Combine all table creation and alteration statements into one batch
    const statements = [
      `
      CREATE TABLE IF NOT EXISTS banners (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        title TEXT NOT NULL,
        subtitle TEXT,
        image_url TEXT NOT NULL,
        link TEXT,
        display_order INT DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
      `,
      `
      ALTER TABLE products
      ADD COLUMN IF NOT EXISTS is_on_sale BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS is_new BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS is_bestseller BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2),
      ADD COLUMN IF NOT EXISTS display_order INT DEFAULT 0;
      `,
      `
      ALTER TABLE categories
      ADD COLUMN IF NOT EXISTS display_order INT DEFAULT 0,
      ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE;
      `
    ];

    // Execute all statements in a single batch
    console.log('Executing table creation statements as a batch');
    const result = await executeSqlBatch(statements);
    
    if (!result) {
      console.warn('Warning: Some table creation statements may have failed');
    }

    return true;
  } catch (error) {
    console.error('Error creating tables:', error);
    return false;
  }
}

async function setupImages() {
  try {
    console.log('Setting up image directories...');

    // Create directories
    const directories = [
      'public/images/banners',
      'public/images/categories',
      'public/images/products',
      'public/images/partners'
    ];

    for (const dir of directories) {
      const fullPath = path.join(process.cwd(), dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
    }

    console.log('Image directories created successfully');
    return true;
  } catch (error) {
    console.error('Error setting up images:', error);
    return false;
  }
}

async function createSampleBanner() {
  try {
    console.log('Проверка таблицы баннеров...');

    // First, ensure the banners table exists with all required columns
    await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS banners (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          subtitle TEXT,
          image_url TEXT NOT NULL,
          link_url TEXT,
          position INTEGER DEFAULT 0,
          active BOOLEAN DEFAULT true,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
      `
    });

    console.log('Структура таблицы баннеров проверена');

    // Wait a moment for Supabase to update its schema cache
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return true;
  } catch (error) {
    console.error('Ошибка при проверке таблицы баннеров:', error);
    return false;
  }
}

async function updateProductFlags() {
  try {
    console.log('Updating product flags...');

    // Combine all product flag update queries into one batch
    const statements = [
      `
      UPDATE products 
      SET is_new = TRUE
      WHERE id IN (
        SELECT id FROM products ORDER BY RANDOM() LIMIT (SELECT CEIL(COUNT(*) * 0.3) FROM products)
      );
      `,
      `
      UPDATE products 
      SET is_on_sale = TRUE,
      original_price = price,
      price = ROUND(price * 0.8, 2)
      WHERE id IN (
        SELECT id FROM products ORDER BY RANDOM() LIMIT (SELECT CEIL(COUNT(*) * 0.2) FROM products)
      );
      `,
      `
      UPDATE products 
      SET is_bestseller = TRUE
      WHERE id IN (
        SELECT id FROM products ORDER BY RANDOM() LIMIT (SELECT CEIL(COUNT(*) * 0.15) FROM products)
      );
      `
    ];

    // Execute all statements in a single batch
    console.log('Executing product flag updates as a batch');
    const result = await executeSqlBatch(statements);
    
    if (!result) {
      console.warn('Warning: Some product flag updates may have failed');
    }

    return true;
  } catch (error) {
    console.error('Error updating product flags:', error);
    return false;
  }
}

async function setupStore() {
  try {
    // Run each step and check for success
    const migrationSuccess = await runMigrations();
    if (!migrationSuccess) {
      // If migrations failed, try direct table creation
      const tablesSuccess = await createTables();
      if (!tablesSuccess) {
        console.log('⚠️ Warning: Database schema updates had issues, but continuing...');
      }
    }

    const imagesSuccess = await setupImages();
    if (!imagesSuccess) {
      console.log('⚠️ Warning: Image setup had issues, but continuing...');
    }

    const bannerSuccess = await createSampleBanner();
    if (!bannerSuccess) {
      console.log('⚠️ Warning: Banner creation had issues, but continuing...');
    }

    const flagsSuccess = await updateProductFlags();
    if (!flagsSuccess) {
      console.log('⚠️ Warning: Product flag updates had issues, but continuing...');
    }

    console.log('✅ Store setup completed!');
  } catch (error) {
    console.error('Error setting up store:', error);
    process.exit(1);
  }
}

setupStore();
