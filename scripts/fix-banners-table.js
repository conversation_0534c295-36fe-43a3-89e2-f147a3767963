const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });
const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');

// Получаем переменные окружения
const SUPABASE_URL = process.env.REACT_APP_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.REACT_APP_SUPABASE_ANON_KEY;

// Проверяем наличие переменных окружения
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('Ошибка: Не заданы переменные окружения REACT_APP_SUPABASE_URL и/или REACT_APP_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Создаем клиент Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Функция для исправления структуры таблицы баннеров
async function fixBannersTable() {
  console.log('Исправляем структуру таблицы баннеров...');
  
  try {
    // Проверяем существование таблицы баннеров
    const { error: checkError } = await supabase
      .from('banners')
      .select('id')
      .limit(1)
      .maybeSingle();
    
    // Если таблицы не существует, выводим инструкцию для ручного создания
    if (checkError && checkError.code === 'PGRST116') {
      console.log('\n⛔️ Таблица banners не существует!');
      console.log('\nДля решения проблемы:');
      console.log('1. Войдите в панель управления Supabase: https://app.supabase.com/');
      console.log('2. Выберите ваш проект');
      console.log('3. Перейдите в SQL Editor');
      console.log('4. Создайте новый запрос');
      console.log('5. Вставьте содержимое файла scripts/fix-banners.sql');
      console.log('6. Выполните запрос');
      
      // Создаем SQL-файл, если его еще нет
      const sqlPath = path.resolve(__dirname, './fix-banners.sql');
      if (!fs.existsSync(sqlPath)) {
        const sql = `-- SQL-скрипт для исправления таблицы баннеров
-- Выполните этот скрипт в SQL Editor в панели управления Supabase

-- Сначала проверим существование таблицы баннеров и создадим её, если не существует
CREATE TABLE IF NOT EXISTS banners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  image_url TEXT NOT NULL,
  link_url TEXT,
  subtitle TEXT,
  active BOOLEAN DEFAULT TRUE,
  position INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Добавляем отсутствующие колонки
DO $$
BEGIN
  -- Проверяем link_url колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'link_url'
  ) THEN
    ALTER TABLE banners ADD COLUMN link_url TEXT;
  END IF;
  
  -- Проверяем subtitle колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'subtitle'
  ) THEN
    ALTER TABLE banners ADD COLUMN subtitle TEXT;
  END IF;
  
  -- Проверяем active колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'active'
  ) THEN
    ALTER TABLE banners ADD COLUMN active BOOLEAN DEFAULT TRUE;
  END IF;
  
  -- Проверяем position колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'position'
  ) THEN
    ALTER TABLE banners ADD COLUMN position INTEGER DEFAULT 0;
  END IF;
  
  -- Проверяем updated_at колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE banners ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
  END IF;
END $$;

-- Создаём индексы для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_banners_active ON banners(active);
CREATE INDEX IF NOT EXISTS idx_banners_position ON banners(position);
`;
        fs.writeFileSync(sqlPath, sql);
        console.log(`\nСоздан файл ${sqlPath} с необходимыми SQL-командами`);
      } else {
        console.log('\nФайл scripts/fix-banners.sql уже существует - используйте его содержимое в SQL Editor');
      }
      
      return;
    }
    
    // Таблица существует, пробуем добавить колонку link_url, если её нет
    try {
      // Попытаемся получить данные, чтобы проверить структуру таблицы
      const { data: banners, error } = await supabase
        .from('banners')
        .select('id, title, image_url, link_url, subtitle, active, position')
        .limit(1);
      
      if (error) {
        // Если ошибка связана с отсутствием колонки link_url или subtitle
        if (error.message && (error.message.includes("column \"link_url\" does not exist") || 
                             error.message.includes("column \"subtitle\" does not exist"))) {
          console.log('⚠️ Обнаружена проблема с колонками в таблице banners');
          console.log('\nДля решения проблемы:');
          console.log('1. Войдите в панель управления Supabase: https://app.supabase.com/');
          console.log('2. Выберите ваш проект');
          console.log('3. Перейдите в раздел Database -> Table Editor');
          console.log('4. Выберите таблицу banners');
          console.log('5. Нажмите "Edit" и добавьте следующие колонки:');
          console.log('   - link_url (тип: text, nullable)');
          console.log('   - subtitle (тип: text, nullable)');
          console.log('   - active (тип: boolean, default: true)');
          console.log('   - position (тип: integer, default: 0)');
          console.log('\nИли используйте SQL Editor и выполните скрипт из файла scripts/fix-banners.sql');
          
          return;
        } else {
          throw error;
        }
      }
      
      console.log('✅ Структура таблицы баннеров в порядке!');
      
    } catch (e) {
      throw e;
    }
    
  } catch (error) {
    console.error('Ошибка при исправлении таблицы баннеров:', error);
    
    // Рекомендуем использовать SQL-скрипт
    console.log('\n⚠️ Возникла ошибка при автоматическом исправлении таблицы.');
    console.log('Рекомендуем использовать SQL-скрипт вручную:');
    console.log('1. Войдите в панель управления Supabase: https://app.supabase.com/');
    console.log('2. Выберите ваш проект');
    console.log('3. Перейдите в SQL Editor');
    console.log('4. Создайте новый запрос');
    console.log('5. Вставьте содержимое файла scripts/fix-banners.sql');
    console.log('6. Выполните запрос');
  }
}

// Запускаем функцию исправления
fixBannersTable().then(() => {
  console.log('\n✅ Завершено.');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Критическая ошибка:', error);
  process.exit(1);
});
