import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import fetch from 'cross-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Use the working Supabase credentials from execute-migration.js
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  },
  global: {
    fetch: fetch
  }
});

async function fixVendorField() {
  try {
    console.log('Running vendor field fix migration...');
    
    // Read migration SQL
    const migrationSQL = fs.readFileSync(
      path.join(__dirname, 'migrations', 'fix-vendor-field.sql'),
      'utf8'
    );

    // Try using Supabase client
    try {
      const { error } = await supabase.rpc('exec_sql', {
        query: migrationSQL
      });

      if (error) throw error;
      console.log('Successfully updated vendor fields');
      return;
    } catch (supabaseError) {
      console.warn(`Supabase client approach failed: ${supabaseError.message}`);
      console.log('Trying alternative approach...');
    }

    // Fallback to manual instructions
    console.log('Unable to automatically apply the fix. Please follow these manual instructions:');
    console.log('\n1. Access your Supabase dashboard');
    console.log('2. Navigate to the SQL Editor');
    console.log('3. Paste and run the following SQL:\n');
    console.log(migrationSQL);
    
  } catch (error) {
    console.error('Error fixing vendor fields:', error);
    process.exit(1);
  }
}

fixVendorField(); 