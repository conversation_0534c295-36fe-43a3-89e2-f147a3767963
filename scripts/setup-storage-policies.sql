-- Create the brands bucket if it doesn't exist
insert into storage.buckets (id, name, public)
values ('brands', 'brands', true)
on conflict (id) do nothing;

-- Allow public read access to all files in the brands bucket
create policy "Public Access"
on storage.objects for select
using ( bucket_id = 'brands' );

-- Allow authenticated users to upload files to the brands bucket
create policy "Authenticated users can upload brand logos"
on storage.objects for insert
with check (
  bucket_id = 'brands'
  and auth.role() = 'authenticated'
);

-- Allow authenticated users to update their own files
create policy "Authenticated users can update brand logos"
on storage.objects for update
using (
  bucket_id = 'brands'
  and auth.role() = 'authenticated'
);

-- Allow authenticated users to delete their own files
create policy "Authenticated users can delete brand logos"
on storage.objects for delete
using (
  bucket_id = 'brands'
  and auth.role() = 'authenticated'
); 