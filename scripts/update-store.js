const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateProducts() {
  try {
    console.log('Updating products...');

    // Add new columns
    await supabase.rpc('exec_sql', {
      query: `
        ALTER TABLE products
        ADD COLUMN IF NOT EXISTS is_on_sale BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS is_new BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS is_bestseller BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2);
      `
    });

    // Update products
    await supabase.rpc('exec_sql', {
      query: `
        -- Mark new products
        UPDATE products
        SET is_new = TRUE
        WHERE created_at >= NOW() - INTERVAL '30 days';

        -- Set sale products
        UPDATE products
        SET is_on_sale = TRUE,
            original_price = price,
            price = price * 0.8
        WHERE id IN (
            SELECT id FROM products ORDER BY RANDOM() LIMIT (SELECT COUNT(*) / 10 FROM products)
        );

        -- Mark bestsellers
        UPDATE products
        SET is_bestseller = TRUE
        WHERE id IN (
            SELECT id FROM products ORDER BY RANDOM() LIMIT (SELECT COUNT(*) / 5 FROM products)
        );
      `
    });

    console.log('✅ Products updated successfully!');
  } catch (error) {
    console.error('Error updating products:', error);
    throw error;
  }
}

async function setupImages() {
  try {
    console.log('Setting up images...');

    // Create directories
    const directories = [
      'public/images/banners',
      'public/images/categories',
      'public/images/products',
      'public/images/partners'
    ];

    for (const dir of directories) {
      await fs.mkdir(dir, { recursive: true });
    }

    console.log('✅ Image directories created successfully!');
  } catch (error) {
    console.error('Error setting up images:', error);
    throw error;
  }
}

async function updateStore() {
  try {
    await setupImages();
    await updateProducts();
    console.log('🎉 Store update completed successfully!');
  } catch (error) {
    console.error('Error updating store:', error);
    process.exit(1);
  }
}

updateStore();
