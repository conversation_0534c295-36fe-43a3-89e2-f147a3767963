const { createClient } = require('@supabase/supabase-js');

// Настройка Supabase клиента
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

// Стандартное соответствие категорий для случая, если таблицы маппинга нет
// Здесь можно указать ID основных категорий вручную
const defaultCategoryIds = {
  kitchen_appliances: null, // ID категории кухонной техники
  sinks: null, // ID категории моек
  faucets: null // ID категории смесителей
};

async function ensureMappingTableExists() {
  try {
    // Проверим существует ли таблица
    const { error } = await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS category_id_mapping (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          mapping JSONB NOT NULL,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
      `
    });

    if (error) {
      // Проверим, нужно ли создать функцию exec_sql
      if (error.message.includes('function exec_sql') || error.code === '42883') {
        console.log('Создаем функцию exec_sql...');
        await supabase.from('_temp_migrations').insert([
          {
            query: `
              CREATE OR REPLACE FUNCTION exec_sql(query text)
              RETURNS void AS $$
              BEGIN
                EXECUTE query;
              END;
              $$ LANGUAGE plpgsql SECURITY DEFINER;
              
              GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
            `
          }
        ]);

        // Повторно пробуем создать таблицу
        await supabase.rpc('exec_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS category_id_mapping (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              mapping JSONB NOT NULL,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
          `
        });
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    console.error('Ошибка при создании таблицы маппинга:', error);
    return false;
  }
}

async function getRandomCategoryIds() {
  try {
    // Получаем ID всех категорий нижнего уровня (leaf categories)
    const { data, error } = await supabase
      .from('categories')
      .select('id')
      .order('created_at', { ascending: false });

    if (error) throw error;

    if (data && data.length > 0) {
      // Возвращаем случайно выбранные категории
      return data
        .map(cat => cat.id)
        .sort(() => 0.5 - Math.random())
        .slice(0, 5);
    }

    return [];
  } catch (error) {
    console.error('Ошибка при получении случайных категорий:', error);
    return [];
  }
}

async function linkProductsToCategories() {
  try {
    // Убедимся, что таблица маппинга существует
    await ensureMappingTableExists();

    console.log('Получение карты соответствия ID категорий...');

    // Получим карту соответствия ID категорий из таблицы category_id_mapping
    const { data: mappingData, error: mappingError } = await supabase
      .from('category_id_mapping')
      .select('mapping')
      .order('created_at', { ascending: false })
      .limit(1);

    let categoryMapping = {};

    if (mappingError || !mappingData || !mappingData.length) {
      console.log('Карта соответствия не найдена, используем случайное распределение товаров');

      // Получаем случайные категории
      const randomCategoryIds = await getRandomCategoryIds();
      if (randomCategoryIds.length === 0) {
        console.error('Не удалось получить категории для товаров');
        return;
      }

      console.log('Случайные категории для распределения:', randomCategoryIds);

      // Создадим временное поле old_category_id если его нет
      await supabase.rpc('exec_sql', {
        query: `
          ALTER TABLE products
          ADD COLUMN IF NOT EXISTS old_category_id TEXT;
        `
      });

      // Распределим товары случайным образом
      console.log('Распределяем товары по категориям случайным образом...');

      const { error: updateError } = await supabase.rpc('exec_sql', {
        query: `
          -- Распределяем товары случайным образом по категориям
          UPDATE products
          SET category_id = (
            SELECT id FROM categories 
            ORDER BY RANDOM() 
            LIMIT 1
          )
          WHERE category_id IS NULL OR TRUE;
        `
      });

      if (updateError) {
        console.error('Ошибка при распределении товаров:', updateError);
      } else {
        console.log('✅ Товары успешно распределены по категориям случайным образом');
      }

      return;
    }

    categoryMapping = mappingData[0].mapping;
    console.log('Карта соответствия ID категорий получена');

    // Создадим временное поле old_category_id если его нет
    await supabase.rpc('exec_sql', {
      query: `
        ALTER TABLE products
        ADD COLUMN IF NOT EXISTS old_category_id TEXT;
      `
    });

    // Сначала скопируем текущие category_id в old_category_id, если не задано
    await supabase.rpc('exec_sql', {
      query: `
        UPDATE products
        SET old_category_id = category_id
        WHERE old_category_id IS NULL AND category_id IS NOT NULL;
      `
    });

    // Обновим товары на основе карты соответствия
    console.log('Обновляем товары на основе карты соответствия...');
    let updatedCount = 0;
    let errorCount = 0;

    for (const [origCatId, newCatId] of Object.entries(categoryMapping)) {
      // Обновим товары, у которых old_category_id соответствует оригинальному ID категории
      const { data, error } = await supabase
        .from('products')
        .update({ category_id: newCatId })
        .eq('old_category_id', origCatId);

      if (error) {
        console.error(
          `Ошибка при обновлении категории для товаров с old_category_id=${origCatId}:`,
          error
        );
        errorCount++;
      } else {
        updatedCount++;
      }
    }

    // Распределим оставшиеся товары случайным образом
    await supabase.rpc('exec_sql', {
      query: `
        -- Распределяем товары без категорий случайным образом
        UPDATE products
        SET category_id = (
          SELECT id FROM categories 
          WHERE parent_id IS NOT NULL 
          ORDER BY RANDOM() 
          LIMIT 1
        )
        WHERE category_id IS NULL;
      `
    });

    console.log(`Обновлено категорий: ${updatedCount}, ошибок: ${errorCount}`);
    console.log('✅ Привязка товаров к категориям завершена!');
  } catch (error) {
    console.error('Произошла ошибка:', error);
  }
}

linkProductsToCategories();
