const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Files with formatting issues
const filesToFormat = [
  'src/components/filters/FilterPanel.js',
  'src/components/filters/SortDropdown.js',
  'src/pages/CategoryPage.js'
];

try {
  console.log('Fixing formatting issues...');

  // Check if prettier is already installed
  const prettierPath = path.join(process.cwd(), 'node_modules', '.bin', 'prettier');
  const hasPrettier = fs.existsSync(prettierPath);

  // Format each file using the project's prettier configuration
  for (const file of filesToFormat) {
    const fullPath = path.join(process.cwd(), file);
    console.log(`Formatting ${file}...`);

    if (hasPrettier) {
      // Use locally installed prettier
      execSync(`"${prettierPath}" --write "${fullPath}"`, { stdio: 'inherit' });
    } else {
      // Try using npx without installing
      execSync(`npx --no-install prettier --write "${fullPath}"`, { stdio: 'inherit' });
    }
  }

  console.log('✅ Formatting issues fixed successfully!');
} catch (error) {
  console.error('Error fixing formatting issues:', error);

  console.log('\nAlternative approach: Try running these commands manually:');
  for (const file of filesToFormat) {
    console.log(`npx prettier --write "${file}"`);
  }

  process.exit(1);
}
