const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Use the working Supabase credentials from execute-migration.js
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAddressesPolicy() {
  try {
    console.log('Running addresses policy fix migration...');
    
    // Read migration SQL
    const migrationSQL = fs.readFileSync(
      path.join(__dirname, 'migrations', 'fix-addresses-policy.sql'),
      'utf8'
    );

    // First approach - try using Supabase client
    try {
      const { error } = await supabase.rpc('exec_sql', {
        query: migrationSQL
      });

      if (error) throw error;
      console.log('Successfully updated addresses policy');
      return;
    } catch (supabaseError) {
      console.warn(`Supabase client approach failed: ${supabaseError.message}`);
      console.log('Trying alternative approach...');
    }

    // Second approach - try to use manual instructions
    console.log('Unable to automatically apply the fix. Please follow these manual instructions:');
    console.log('\n1. Access your Supabase dashboard');
    console.log('2. Navigate to the SQL Editor');
    console.log('3. Paste and run the following SQL:\n');
    console.log(migrationSQL);
    
  } catch (error) {
    console.error('Error fixing addresses policy:', error);
    process.exit(1);
  }
}

fixAddressesPolicy(); 