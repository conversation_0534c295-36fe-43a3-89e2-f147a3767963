import { resetAndPopulateStore } from '../src/utils/xmlParser';

(async () => {
  try {
    const xmlUrl = './public/data/products.xml'; // Путь к XML-файлу
    const result = await resetAndPopulateStore(xmlUrl);
    console.log(
      `Магазин успешно заполнен! Категорий: ${result.categories}, Товаров: ${result.products}`
    );
  } catch (error) {
    console.error('Ошибка при заполнении магазина:', error);
  }
})();
