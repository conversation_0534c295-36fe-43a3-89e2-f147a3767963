const axios = require('axios');
const { DOMParser } = require('@xmldom/xmldom');
const { supabase } = require('./supabaseClient'); // Ваш настроенный Supabase клиент

const FEED_URL = 'https://kuhteh.com.ua/price/ecommerce-ru.xml';

async function fetchXmlFeed(url) {
  try {
    console.log(`Fetching feed from ${url}...`);
    const response = await axios.get(url, {
      responseType: 'text',
      headers: { Accept: 'application/xml, text/xml' },
    });
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');
    return xmlDoc;
  } catch (error) {
    console.error(`Error fetching or parsing XML feed from ${url}:`, error.message);
    throw error;
  }
}

function extractCategoriesFromXml(xmlDoc) {
  const categoriesFromXml = [];
  const categoryNodes = xmlDoc.getElementsByTagName('category');
  console.log(`Found ${categoryNodes.length} categories in the XML feed.`);
  for (let i = 0; i < categoryNodes.length; i++) {
    const categoryNode = categoryNodes[i];
    const id = categoryNode.getAttribute('id');
    const name = categoryNode.textContent ? categoryNode.textContent.trim() : null;
    const parentId = categoryNode.getAttribute('parentId') || null;

    if (id && name) {
      categoriesFromXml.push({
        external_id: id,
        name: name,
        // parent_external_id: parentId // Если нужно будет связывать иерархию по external_id
      });
    }
  }
  return categoriesFromXml;
}

async function updateCategoryExternalIds() {
  console.log('Starting process to update external_ids for categories...');

  try {
    // 1. Добавляем колонку external_id, если ее нет
    // Это лучше сделать один раз вручную или через миграцию, но для полноты примера:
    // Важно: Этот DDL-запрос может потребовать повышенных прав, если их нет у вашего supabaseClient.
    // Рекомендуется выполнить ALTER TABLE через Supabase Studio.
    /*
    const { error: alterError } = await supabase.rpc('execute_sql', {
      sql: 'ALTER TABLE categories ADD COLUMN IF NOT EXISTS external_id TEXT;'
    });
    if (alterError) {
      console.warn(`Could not ensure external_id column exists (this might be a permissions issue or the column already exists): ${alterError.message}`);
    } else {
      console.log("Ensured 'external_id' column exists in 'categories' table.");
    }
    */
    console.log("Please ensure 'external_id' column (TEXT) exists in 'categories' table before running this script extensively.");


    // 2. Получаем категории из XML
    const xmlDoc = await fetchXmlFeed(FEED_URL);
    const categoriesFromXml = extractCategoriesFromXml(xmlDoc);

    if (categoriesFromXml.length === 0) {
      console.log('No categories found in XML feed. Exiting.');
      return;
    }
    console.log(`Extracted ${categoriesFromXml.length} categories from XML.`);

    // 3. Получаем все категории из базы данных
    const { data: dbCategories, error: dbError } = await supabase
      .from('categories')
      .select('id, name, external_id'); // Выбираем и текущий external_id, чтобы не обновлять без надобности

    if (dbError) {
      console.error('Error fetching categories from database:', dbError.message);
      return;
    }
    console.log(`Fetched ${dbCategories.length} categories from the database.`);

    let updatedCount = 0;
    let notFoundCount = 0;
    let alreadySetCount = 0;

    // 4. Обновляем external_id в базе
    for (const xmlCategory of categoriesFromXml) {
      const dbCategory = dbCategories.find(
        (cat) => cat.name && xmlCategory.name && cat.name.trim().toLowerCase() === xmlCategory.name.trim().toLowerCase()
      );

      if (dbCategory) {
        if (dbCategory.external_id === xmlCategory.external_id) {
          // console.log(`Category "${xmlCategory.name}" already has correct external_id: ${xmlCategory.external_id}`);
          alreadySetCount++;
        } else {
          console.log(`Updating category "${xmlCategory.name}" (DB ID: ${dbCategory.id}): setting external_id to ${xmlCategory.external_id} (was: ${dbCategory.external_id})`);
          const { error: updateError } = await supabase
            .from('categories')
            .update({ external_id: xmlCategory.external_id })
            .eq('id', dbCategory.id);

          if (updateError) {
            console.error(`Failed to update external_id for category "${xmlCategory.name}": ${updateError.message}`);
          } else {
            updatedCount++;
          }
        }
      } else {
        console.warn(`Category "${xmlCategory.name}" from XML not found in database by name. Skipping.`);
        notFoundCount++;
      }
    }

    console.log('\n--- Update Summary ---');
    console.log(`Categories processed from XML: ${categoriesFromXml.length}`);
    console.log(`Categories updated in DB: ${updatedCount}`);
    console.log(`Categories already had correct external_id: ${alreadySetCount}`);
    console.log(`Categories from XML not found in DB by name: ${notFoundCount}`);
    console.log('----------------------');

  } catch (error) {
    console.error('An error occurred during the update process:', error);
  }
}

updateCategoryExternalIds().then(() => console.log('Script finished.'));