const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function addIsAdminColumn() {
  try {
    console.log('Checking if profiles table exists...');
    
    // First check if profiles table exists
    const { data: profilesCheck, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
      
    if (profilesError && profilesError.code === '42P01') {
      console.error('Profiles table does not exist. Creating it first...');
      
      // Create profiles table if it doesn't exist
      const { error: createTableError } = await supabase.rpc('exec_sql', {
        query: `
          CREATE TABLE IF NOT EXISTS profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id),
            email TEXT,
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createTableError) {
        console.error('Error creating profiles table:', createTableError);
        return;
      }
      
      console.log('Profiles table created successfully');
    }
    
    console.log('Adding is_admin column to profiles table...');
    
    // SQL for adding is_admin column
    const sql = `
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 
          FROM information_schema.columns 
          WHERE table_name = 'profiles' 
          AND column_name = 'is_admin'
        ) THEN
          ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
          RAISE NOTICE 'The is_admin column has been added to the profiles table';
        ELSE
          RAISE NOTICE 'The is_admin column already exists in the profiles table';
        END IF;
      END $$;
    `;
    
    const { error } = await supabase.rpc('exec_sql', {
      query: sql
    });
    
    if (error) {
      console.error('Error adding is_admin column:', error);
      process.exit(1);
    }
    
    console.log('Successfully added is_admin column to profiles table');
    
    // Set the first user as admin (optional)
    console.log('Setting the first user as admin...');
    
    const setAdminSql = `
      UPDATE profiles 
      SET is_admin = TRUE 
      WHERE id IN (SELECT id FROM profiles ORDER BY created_at LIMIT 1);
    `;
    
    const { error: setAdminError } = await supabase.rpc('exec_sql', {
      query: setAdminSql
    });
    
    if (setAdminError) {
      console.error('Error setting admin user:', setAdminError);
    } else {
      console.log('Successfully set first user as admin');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

addIsAdminColumn(); 