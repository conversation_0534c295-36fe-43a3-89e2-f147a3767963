const express = require('express');
const cors = require('cors');
const { processFeed } = require('./feedUtils');
const { supabase } = require('./supabaseClient');
// Ensure service role key is provided, otherwise operations may be forbidden
if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY is missing. Please set it in .env before running the server.');
  process.exit(1);
}

const app = express();
app.use(cors());
app.use(express.json());

// List recent feed_jobs with feed name
app.get('/api/feeds', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('feed_jobs')
      .select(`id,feed_id,status,started_at,finished_at,items_processed,items_created,items_updated,items_failed,feeds(name)`)
      .order('created_at', { ascending: false })
      .limit(50);
    if (error) throw error;
    res.json(data);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Trigger processing of all active feeds
app.post('/api/feeds/process', async (req, res) => {
  try {
    const { feedId } = req.body;
    let feedsToProcess;
    if (feedId) {
      const { data, error: fetchError } = await supabase.from('feeds').select('id,name').eq('id', feedId);
      if (fetchError) throw fetchError;
      feedsToProcess = data;
    } else {
      const { data, error: fetchError } = await supabase.from('feeds').select('id,name').eq('active', true);
      if (fetchError) throw fetchError;
      feedsToProcess = data;
    }
    const results = [];
    for (const feed of feedsToProcess) {
      try {
        const result = await processFeed(feed.id);
        results.push({ feed: feed.name, stats: result.stats });
      } catch (err) {
        results.push({ feed: feed.name, error: err.message });
      }
    }
    res.json(results);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Start server with port fallback on EADDRINUSE
const DEFAULT_PORT = process.env.PORT ? parseInt(process.env.PORT, 10) : 5000;
function startServer(port) {
  const server = app.listen(port, () => console.log(`Feed API listening on port ${port}`));
  server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.warn(`Port ${port} is in use, trying ${port + 1}`);
      startServer(port + 1);
    } else {
      console.error('Server error:', err);
    }
  });
}
startServer(DEFAULT_PORT);