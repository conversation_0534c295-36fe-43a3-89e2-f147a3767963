#!/bin/bash

# Create necessary directories
mkdir -p public/images/{banners,categories,products,partners}

# Download sample banner
curl -o public/images/banners/main-banner.jpg "https://source.unsplash.com/1920x1080/?kitchen,modern"

# Download category images
for category in "cooking" "refrigerator" "dishwasher" "oven" "hood" "sink"; do
  curl -o "public/images/categories/${category}.jpg" "https://source.unsplash.com/800x600/?kitchen,${category}"
done

# Download partner logos
declare -a partners=("bosch" "siemens" "electrolux" "aeg" "miele" "gorenje")
for partner in "${partners[@]}"; do
  curl -o "public/images/partners/${partner}.png" "https://logo.clearbit.com/${partner}.com"
done

echo "✅ Images downloaded successfully!"
