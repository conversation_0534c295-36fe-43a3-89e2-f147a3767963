-- Fill empty vendor fields for all products
-- First create a temporary table to store products without vendor
CREATE TEMP TABLE products_without_vendor AS
SELECT id, brand FROM products 
WHERE 
  (vendor IS NULL OR vendor = '');

-- Update products' vendor field with brand if available, or 'Unknown' if not
UPDATE products
SET 
  vendor = COALESCE(brand, 'Unknown'),
  updated_at = NOW()
WHERE id IN (SELECT id FROM products_without_vendor);

-- Count and display updated records
SELECT COUNT(*) AS updated_products FROM products_without_vendor;

-- Drop temporary table
DROP TABLE products_without_vendor;

-- Create index on vendor field if not exists
CREATE INDEX IF NOT EXISTS idx_products_vendor ON products(vendor);

-- Grant necessary permissions
GRANT SELECT, UPDATE ON products TO authenticated, anon; 