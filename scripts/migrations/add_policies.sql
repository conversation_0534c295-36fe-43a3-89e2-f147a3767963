-- Политики для categories
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Enable read access for all users" ON categories FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users" ON categories FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users" ON categories FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users" ON categories FOR DELETE USING (true);

-- Политики для products
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Enable read access for all users" ON products FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users" ON products FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users" ON products FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users" ON products FOR DELETE USING (true);
