-- Создание таблицы reviews для хранения отзывов пользователей
CREATE TABLE IF NOT EXISTS public.reviews (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  product_id uuid REFERENCES public.products(id) ON DELETE CASCADE,
  name text NOT NULL,
  email text NOT NULL,
  rating integer NOT NULL CHECK (rating >= 1 AND rating <= 5),
  comment text NOT NULL,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now()
);

-- Добавляем политики безопасности RLS для таблицы reviews
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- Политика для анонимных пользователей: могут только читать approved отзывы
CREATE POLICY "Анонимные пользователи могут только видеть утвержденные отзывы" 
  ON public.reviews 
  FOR SELECT 
  TO anon 
  USING (status = 'approved');

-- Политика для authenticated пользователей: могут видеть утвержденные отзывы
CREATE POLICY "Authenticated пользователи могут видеть утвержденные отзывы" 
  ON public.reviews 
  FOR SELECT 
  TO authenticated 
  USING (status = 'approved');

-- Политика для authenticated пользователей: могут создавать отзывы
CREATE POLICY "Authenticated пользователи могут создавать отзывы" 
  ON public.reviews 
  FOR INSERT 
  TO authenticated 
  WITH CHECK (true);

-- Политика для service_role: полный доступ к таблице reviews
CREATE POLICY "Service role имеет полный доступ" 
  ON public.reviews 
  FOR ALL 
  TO service_role 
  USING (true) 
  WITH CHECK (true);

-- Создаем индекс для быстрого поиска по product_id
CREATE INDEX IF NOT EXISTS reviews_product_id_idx ON public.reviews (product_id);

-- Добавляем триггер для автоматического обновления рейтинга продукта при добавлении отзыва
CREATE OR REPLACE FUNCTION public.update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Обновляем среднюю оценку и количество отзывов для продукта
  UPDATE public.products
  SET 
    rating = (
      SELECT COALESCE(AVG(rating), 0)
      FROM public.reviews
      WHERE product_id = NEW.product_id AND status = 'approved'
    ),
    reviews_count = (
      SELECT COUNT(*)
      FROM public.reviews
      WHERE product_id = NEW.product_id AND status = 'approved'
    )
  WHERE id = NEW.product_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Создаем триггеры для автоматического обновления рейтинга продукта
DROP TRIGGER IF EXISTS update_product_rating_trigger ON public.reviews;
CREATE TRIGGER update_product_rating_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.reviews
FOR EACH ROW
EXECUTE FUNCTION public.update_product_rating();

-- Проверяем и добавляем столбцы rating и reviews_count в таблицу products, если они отсутствуют
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'products' AND column_name = 'rating') THEN
    ALTER TABLE public.products ADD COLUMN rating numeric DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'products' AND column_name = 'reviews_count') THEN
    ALTER TABLE public.products ADD COLUMN reviews_count integer DEFAULT 0;
  END IF;
END $$;