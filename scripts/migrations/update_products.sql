-- First add the columns
ALTER TABLE products
ADD COLUMN IF NOT EXISTS is_on_sale BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_new BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_bestseller BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2);

-- Update some products as featured
UPDATE products
SET is_new = TRUE
WHERE created_at >= NOW() - INTERVAL '30 days';

-- Set some products on sale
UPDATE products
SET is_on_sale = TRUE,
    original_price = price,
    price = price * 0.8
WHERE id IN (
    SELECT id
    FROM products
    ORDER BY RANDOM()
    LIMIT (SELECT COUNT(*) / 10 FROM products) -- 10% of products
);

-- Mark some products as bestsellers
UPDATE products
SET is_bestseller = TRUE
WHERE id IN (
    SELECT id
    FROM products
    ORDER BY RANDOM()
    LIMIT (SELECT COUNT(*) / 5 FROM products) -- 20% of products
);
