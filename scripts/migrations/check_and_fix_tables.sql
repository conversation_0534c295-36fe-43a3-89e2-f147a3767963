-- Ensure tables exist and have correct structure
DO $$ 
BEGIN
    -- Check and create required extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

    -- Add missing columns to products if they don't exist
    ALTER TABLE products 
    ADD COLUMN IF NOT EXISTS is_on_sale BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS is_new BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS is_bestseller BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS sale_price DECIMAL(10,2),
    ADD COLUMN IF NOT EXISTS original_price DECIMAL(10,2);

    -- Add missing columns to categories if they don't exist
    ALTER TABLE categories 
    ADD COLUMN IF NOT EXISTS image TEXT,
    ADD COLUMN IF NOT EXISTS display_order INT DEFAULT 0;

    -- Create indexes if they don't exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_products_is_on_sale') THEN
        CREATE INDEX idx_products_is_on_sale ON products(is_on_sale) WHERE is_on_sale = TRUE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_products_is_new') THEN
        CREATE INDEX idx_products_is_new ON products(is_new) WHERE is_new = TRUE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_products_is_bestseller') THEN
        CREATE INDEX idx_products_is_bestseller ON products(is_bestseller) WHERE is_bestseller = TRUE;
    END IF;
END $$;
