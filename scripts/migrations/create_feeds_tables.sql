-- Create feeds table for product feed sources
CREATE TABLE IF NOT EXISTS feeds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR NOT NULL,
    url TEXT NOT NULL,
    language VARCHAR(10) DEFAULT 'ru',
    active BOOLEA<PERSON> DEFAULT true,
    last_fetched TIM<PERSON><PERSON><PERSON> WITH TIME ZONE,
    fetch_interval_hours INTEGER DEFAULT 24,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create feed_jobs table to track feed processing jobs
CREATE TABLE IF NOT EXISTS feed_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    feed_id UUID REFERENCES feeds(id) ON DELETE CASCADE,
    status VARCHAR NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    started_at TIMESTAMP WITH TIME ZONE,
    finished_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    items_processed INTEGER,
    items_created INTEGER DEFAULT 0,
    items_updated INTEGER DEFAULT 0,
    items_failed INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable RLS so anon key can PATCH and SELECT
ALTER TABLE feeds DISABLE ROW LEVEL SECURITY;
ALTER TABLE feed_jobs DISABLE ROW LEVEL SECURITY;

-- Create policies for feeds table
CREATE POLICY "Feeds are viewable by authenticated users" 
    ON feeds FOR SELECT 
    USING (auth.role() = 'authenticated');

CREATE POLICY "Feeds are editable by authenticated users with admin access" 
    ON feeds FOR ALL 
    USING (auth.role() = 'authenticated' AND (auth.jwt() ->> 'app_role')::text = 'admin');

-- Create policies for feed_jobs table
CREATE POLICY "Feed jobs are viewable by authenticated users" 
    ON feed_jobs FOR SELECT 
    USING (auth.role() = 'authenticated');

CREATE POLICY "Feed jobs are editable by authenticated users with admin access" 
    ON feed_jobs FOR ALL 
    USING (auth.role() = 'authenticated' AND (auth.jwt() ->> 'app_role')::text = 'admin');