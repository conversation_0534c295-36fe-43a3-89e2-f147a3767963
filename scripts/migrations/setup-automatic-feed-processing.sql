-- Создание функции для автоматической обработки фидов в Supabase
-- Это SQL-функция будет вызываться через pg_cron каждые 6 часов

-- Функция для обработки всех активных фидов
CREATE OR REPLACE FUNCTION process_all_active_feeds()
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    feed_record RECORD;
    result_data jsonb := '[]'::jsonb;
    feed_result jsonb;
    total_processed integer := 0;
    total_errors integer := 0;
BEGIN
    -- Получаем все активные фиды, которые не обновлялись последние 6 часов
    FOR feed_record IN 
        SELECT id, name, url, language
        FROM feeds 
        WHERE active = true 
        AND (
            last_fetched IS NULL 
            OR last_fetched < NOW() - INTERVAL '6 hours'
        )
    LOOP
        BEGIN
            -- Вызываем Edge Function для обработки фида
            SELECT content::jsonb INTO feed_result
            FROM http((
                'POST',
                current_setting('app.edge_function_url') || '/process-feed',
                ARRAY[http_header('Authorization', 'Bearer ' || current_setting('app.service_role_key'))],
                'application/json',
                jsonb_build_object('feedId', feed_record.id)::text
            ));
            
            -- Добавляем результат в общий результат
            result_data := result_data || jsonb_build_object(
                'feed_id', feed_record.id,
                'feed_name', feed_record.name,
                'status', 'success',
                'result', feed_result
            );
            
            total_processed := total_processed + 1;
            
        EXCEPTION WHEN OTHERS THEN
            -- В случае ошибки записываем её
            result_data := result_data || jsonb_build_object(
                'feed_id', feed_record.id,
                'feed_name', feed_record.name,
                'status', 'error',
                'error', SQLERRM
            );
            
            total_errors := total_errors + 1;
        END;
    END LOOP;
    
    -- Возвращаем итоговый результат
    RETURN jsonb_build_object(
        'timestamp', NOW(),
        'total_processed', total_processed,
        'total_errors', total_errors,
        'feeds', result_data
    );
END;
$$;

-- Настройка pg_cron для автоматического запуска каждые 6 часов
-- Примечание: pg_cron должен быть включен в Supabase проекте
SELECT cron.schedule(
    'process-feeds-every-6-hours',    -- название задания
    '0 */6 * * *',                    -- каждые 6 часов
    'SELECT process_all_active_feeds();'  -- SQL команда
);

-- Проверить существующие cron задания
-- SELECT * FROM cron.job;

-- Удалить задание если нужно (для отладки)
-- SELECT cron.unschedule('process-feeds-every-6-hours');
