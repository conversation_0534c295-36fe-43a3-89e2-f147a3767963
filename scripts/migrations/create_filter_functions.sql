-- Функция для получения всех названий параметров товаров в категории
CREATE OR REPLACE FUNCTION get_category_params(category_id UUID)
RETURNS TABLE (
  name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT pp.name
  FROM product_params pp
  JOIN products p ON p.id = pp.product_id
  WHERE p.category_id = $1
  ORDER BY pp.name;
END;
$$ LANGUAGE plpgsql;

-- Функция для получения всех значений для конкретного параметра в категории
CREATE OR REPLACE FUNCTION get_param_values(category_id UUID, param_name TEXT)
RETURNS TABLE (
  value TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT pp.value
  FROM product_params pp
  JOIN products p ON p.id = pp.product_id
  WHERE p.category_id = $1 AND pp.name = $2 AND pp.value IS NOT NULL
  ORDER BY pp.value;
END;
$$ LANGUAGE plpgsql;
