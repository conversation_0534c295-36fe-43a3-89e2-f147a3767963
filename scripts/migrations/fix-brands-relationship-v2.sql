-- Enable UUID extension if not enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

DO $$ 
BEGIN
    -- Create brands table if it doesn't exist
    CREATE TABLE IF NOT EXISTS brands (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        logo TEXT,
        description TEXT,
        website_url TEXT,
        display_order INTEGER DEFAULT 0,
        is_featured BOOLEAN DEFAULT false,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
    );

    -- Check if products table needs UUID migration
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'id' 
        AND data_type = 'bigint'
    ) THEN
        -- Products table has bigint IDs, need to migrate
        -- First, create a temporary UUID column
        ALTER TABLE products ADD COLUMN new_id UUID DEFAULT uuid_generate_v4();
        
        -- Update all existing rows with UUIDs
        UPDATE products SET new_id = uuid_generate_v4() WHERE new_id IS NULL;
        
        -- Drop the old primary key constraint
        ALTER TABLE products DROP CONSTRAINT products_pkey;
        
        -- Rename columns
        ALTER TABLE products DROP COLUMN id;
        ALTER TABLE products RENAME COLUMN new_id TO id;
        
        -- Add primary key constraint
        ALTER TABLE products ADD PRIMARY KEY (id);
    END IF;

    -- Now we can safely add the brand_id column
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'brand_id'
    ) THEN
        ALTER TABLE products 
        ADD COLUMN brand_id UUID REFERENCES brands(id);
        
        -- Create index for better performance
        CREATE INDEX IF NOT EXISTS idx_products_brand_id ON products(brand_id);
    END IF;

    -- Grant necessary permissions
    GRANT SELECT ON brands TO authenticated, anon;
    GRANT SELECT, UPDATE ON products TO authenticated;
END $$;