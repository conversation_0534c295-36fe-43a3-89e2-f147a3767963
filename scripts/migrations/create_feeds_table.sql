-- Create extension if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> feeds table
CREATE TABLE IF NOT EXISTS feeds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  url TEXT NOT NULL,
  language TEXT NOT NULL DEFAULT 'ru',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_fetched TIMESTAMP WITH TIME ZONE
);

-- Add index on language
CREATE INDEX IF NOT EXISTS feeds_language_idx ON feeds (language);

-- Create feed jobs table
CREATE TABLE IF NOT EXISTS feed_jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  feed_id UUID NOT NULL REFERENCES feeds(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  finished_at TIMESTAMP WITH TIME ZONE,
  items_processed INTEGER DEFAULT 0,
  items_created INTEGER DEFAULT 0,
  items_updated INTEGER DEFAULT 0,
  items_failed INTEGER DEFAULT 0,
  error_message TEXT
);

-- Add indexes for feed jobs
CREATE INDEX IF NOT EXISTS feed_jobs_feed_id_idx ON feed_jobs (feed_id);
CREATE INDEX IF NOT EXISTS feed_jobs_status_idx ON feed_jobs (status);
CREATE INDEX IF NOT EXISTS feed_jobs_created_at_idx ON feed_jobs (created_at);

-- Add row level security to feeds
ALTER TABLE feeds ENABLE ROW LEVEL SECURITY;

-- Policies for authenticated users on feeds
DROP POLICY IF EXISTS "authenticated_select_feeds" ON feeds;
CREATE POLICY "authenticated_select_feeds" ON feeds FOR SELECT TO authenticated USING (true);

DROP POLICY IF EXISTS "authenticated_insert_feeds" ON feeds;
CREATE POLICY "authenticated_insert_feeds" ON feeds FOR INSERT TO authenticated WITH CHECK (true);

DROP POLICY IF EXISTS "authenticated_update_feeds" ON feeds;
CREATE POLICY "authenticated_update_feeds" ON feeds FOR UPDATE TO authenticated USING (true);

DROP POLICY IF EXISTS "authenticated_delete_feeds" ON feeds;
CREATE POLICY "authenticated_delete_feeds" ON feeds FOR DELETE TO authenticated USING (true);

-- Policies for anonymous users on feeds
CREATE POLICY "anon_select_feeds" ON feeds FOR SELECT TO anon USING (true);
CREATE POLICY "anon_insert_feeds" ON feeds FOR INSERT TO anon WITH CHECK (true);

-- Add row level security to feed_jobs
ALTER TABLE feed_jobs ENABLE ROW LEVEL SECURITY;

-- Policies for authenticated users on feed_jobs
DROP POLICY IF EXISTS "authenticated_select_feed_jobs" ON feed_jobs;
CREATE POLICY "authenticated_select_feed_jobs" ON feed_jobs FOR SELECT TO authenticated USING (true);

DROP POLICY IF EXISTS "authenticated_insert_feed_jobs" ON feed_jobs;
CREATE POLICY "authenticated_insert_feed_jobs" ON feed_jobs FOR INSERT TO authenticated WITH CHECK (true);

DROP POLICY IF EXISTS "authenticated_update_feed_jobs" ON feed_jobs;
CREATE POLICY "authenticated_update_feed_jobs" ON feed_jobs FOR UPDATE TO authenticated USING (true);

DROP POLICY IF EXISTS "authenticated_delete_feed_jobs" ON feed_jobs;
CREATE POLICY "authenticated_delete_feed_jobs" ON feed_jobs FOR DELETE TO authenticated USING (true);

-- Policies for anonymous users on feed_jobs
CREATE POLICY "anon_select_feed_jobs" ON feed_jobs FOR SELECT TO anon USING (true);
CREATE POLICY "anon_insert_feed_jobs" ON feed_jobs FOR INSERT TO anon WITH CHECK (true);

-- Add comments for documentation
COMMENT ON TABLE feeds IS 'External product feeds to be imported';
COMMENT ON TABLE feed_jobs IS 'Feed processing history';

-- Create view for feed statistics
CREATE OR REPLACE VIEW feed_stats AS
SELECT
  f.id,
  f.name,
  f.language,
  f.is_active,
  f.last_fetched,
  COUNT(j.id) AS total_jobs,
  SUM(CASE WHEN j.status = 'completed' THEN 1 ELSE 0 END) AS successful_jobs,
  SUM(CASE WHEN j.status = 'failed' THEN 1 ELSE 0 END) AS failed_jobs,
  SUM(j.items_created) AS total_items_created,
  SUM(j.items_updated) AS total_items_updated,
  SUM(j.items_failed) AS total_items_failed,
  MAX(j.created_at) AS last_job_date
FROM
  feeds f
LEFT JOIN
  feed_jobs j ON f.id = j.feed_id
GROUP BY
  f.id, f.name, f.language, f.is_active, f.last_fetched;