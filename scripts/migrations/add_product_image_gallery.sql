-- Add image gallery support to products table
ALTER TABLE IF EXISTS public.products 
ADD COLUMN IF NOT EXISTS image_gallery TEXT[] DEFAULT '{}';

-- Add is_main_image flag
COMMENT ON COLUMN public.products.image IS 'Main product image URL';

-- <PERSON>reate function to get main image
CREATE OR REPLACE FUNCTION get_main_product_image(product_id UUID) 
RETURNS TEXT AS $$
DECLARE
  main_image TEXT;
BEGIN
  SELECT image INTO main_image FROM products WHERE id = product_id;
  RETURN main_image;
END;
$$ LANGUAGE plpgsql;

-- Create function to add image to gallery
CREATE OR REPLACE FUNCTION add_image_to_gallery(product_id UUID, image_url TEXT) 
RETURNS VOID AS $$
BEGIN
  UPDATE products 
  SET image_gallery = array_append(image_gallery, image_url)
  WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate function to set main image
CREATE OR REPLACE FUNCTION set_main_product_image(product_id UUID, image_url TEXT) 
RETURNS VOID AS $$
DECLARE
  current_main_image TEXT;
  gallery TEXT[];
BEGIN
  -- Get current main image
  SELECT image, image_gallery INTO current_main_image, gallery 
  FROM products 
  WHERE id = product_id;
  
  -- If current main image exists and is not in gallery, add it to gallery
  IF current_main_image IS NOT NULL AND current_main_image != image_url THEN
    IF NOT array_position(gallery, current_main_image) > 0 THEN
      gallery = array_append(gallery, current_main_image);
    END IF;
  END IF;
  
  -- Remove new main image from gallery if it's there
  IF array_position(gallery, image_url) > 0 THEN
    gallery = array_remove(gallery, image_url);
  END IF;
  
  -- Update product with new main image and updated gallery
  UPDATE products 
  SET image = image_url, 
      image_gallery = gallery
  WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to remove image from gallery
CREATE OR REPLACE FUNCTION remove_image_from_gallery(product_id UUID, image_url TEXT) 
RETURNS VOID AS $$
BEGIN
  UPDATE products 
  SET image_gallery = array_remove(image_gallery, image_url)
  WHERE id = product_id;
END;
$$ LANGUAGE plpgsql;