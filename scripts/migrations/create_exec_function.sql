-- Function to execute arbitrary SQL
-- Note: In real production environments, this function should have proper security restrictions

-- Drop old signatures
DROP FUNCTION IF EXISTS public.exec_sql(json, text);
DROP FUNCTION IF EXISTS public.exec_sql(text, json);
DROP FUNCTION IF EXISTS public.exec_sql(text);

-- Create exec_sql(params, query) for Supabase RPC
CREATE OR REPLACE FUNCTION public.exec_sql(
  params json DEFAULT '{}'::json,
  query text
)
RETURNS void AS $$
BEGIN
  -- Currently ignoring params, execute the query
  EXECUTE query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
