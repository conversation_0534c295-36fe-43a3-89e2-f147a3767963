-- Создаем таблицу параметров товаров
CREATE TABLE IF NOT EXISTS product_params (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  value TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Создаем индекс для быстрого поиска по товару
CREATE INDEX IF NOT EXISTS idx_product_params_product_id ON product_params(product_id);
