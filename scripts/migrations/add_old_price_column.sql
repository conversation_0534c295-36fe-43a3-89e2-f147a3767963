-- Add old_price column to products table
CREATE OR REPLACE FUNCTION add_old_price_column()
R<PERSON>URNS void AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'old_price'
    ) THEN
        ALTER TABLE products ADD COLUMN old_price DECIMAL(10, 2);
        
        -- Update old_price based on discount if it exists
        UPDATE products 
        SET old_price = price * (100 + COALESCE(discount, 0)) / 100
        WHERE discount IS NOT NULL AND discount > 0;
    END IF;

    RAISE NOTICE 'old_price column check completed';
END;
$$ LANGUAGE plpgsql;

SELECT add_old_price_column();