-- Fix for the banners table structure
-- Adds missing columns which are causing banner creation errors

-- First check if the column already exists to avoid errors
DO $$
BEGIN
    -- Check if the active column exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'active'
    ) THEN
        -- Add the active column with default value true
        ALTER TABLE banners ADD COLUMN active BOOLEAN DEFAULT TRUE;
        RAISE NOTICE 'The active column has been added to the banners table.';
    ELSE
        RAISE NOTICE 'The active column already exists in the banners table.';
    END IF;
    
    -- Similarly check and add other potentially missing columns
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'display_order'
    ) THEN
        ALTER TABLE banners ADD COLUMN display_order INTEGER DEFAULT 0;
        RAISE NOTICE 'The display_order column has been added to the banners table.';
    END IF;
    
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'image_url'
    ) THEN
        ALTER TABLE banners ADD COLUMN image_url TEXT;
        RAISE NOTICE 'The image_url column has been added to the banners table.';
    END IF;
    
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'link_url'
    ) THEN
        ALTER TABLE banners ADD COLUMN link_url TEXT;
        RAISE NOTICE 'The link_url column has been added to the banners table.';
    END IF;
    
    -- Add description column which is currently missing
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'description'
    ) THEN
        ALTER TABLE banners ADD COLUMN description TEXT;
        RAISE NOTICE 'The description column has been added to the banners table.';
    END IF;
    
    -- Add subtitle column which might be needed
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'subtitle'
    ) THEN
        ALTER TABLE banners ADD COLUMN subtitle TEXT;
        RAISE NOTICE 'The subtitle column has been added to the banners table.';
    END IF;
    
    -- Add button_text column which might be needed
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'button_text'
    ) THEN
        ALTER TABLE banners ADD COLUMN button_text TEXT;
        RAISE NOTICE 'The button_text column has been added to the banners table.';
    END IF;
    
    -- Make sure we have a created_at column for timestamps
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE banners ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        RAISE NOTICE 'The created_at column has been added to the banners table.';
    END IF;
    
    -- Position column for sorting (alternative to display_order in some queries)
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'banners' AND column_name = 'position'
    ) THEN
        ALTER TABLE banners ADD COLUMN position INTEGER DEFAULT 0;
        RAISE NOTICE 'The position column has been added to the banners table.';
    END IF;
END $$;