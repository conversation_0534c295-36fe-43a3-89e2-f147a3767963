// CommonJS-compatible Supabase client for scripts
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from .env.local file if present
dotenv.config({ path: path.resolve(__dirname, '..', '.env.local') });

// Use environment variables (SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY)
const SUPABASE_URL = process.env.SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;

// Ensure URL and keys are provided
if (!SUPABASE_URL) throw new Error('Missing SUPABASE_URL env var');
if (!SUPABASE_ANON_KEY) throw new Error('Missing SUPABASE_ANON_KEY env var');
if (!SUPABASE_SERVICE_ROLE_KEY) console.warn('⚠️ SUPABASE_SERVICE_ROLE_KEY not set; backend operations will use anon key');

// Select key: prefer service role if available
const SUPABASE_KEY = SUPABASE_SERVICE_ROLE_KEY || SUPABASE_ANON_KEY;

// Warn if service role key not provided (migrations and admin operations may be blocked)
if (!SUPABASE_SERVICE_ROLE_KEY) {
 console.error('⚠️ REACT_APP_SUPABASE_SERVICE_ROLE_KEY is not set! Admin operations and migrations will be blocked.');
}

// Create and export the Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

module.exports = { supabase };