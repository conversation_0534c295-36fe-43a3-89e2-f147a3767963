DO $$ 
BEGIN
  -- Add brand_id column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'products' 
    AND column_name = 'brand_id'
  ) THEN
    ALTER TABLE products 
    ADD COLUMN brand_id UUID REFERENCES brands(id);
    
    CREATE INDEX IF NOT EXISTS idx_products_brand_id ON products(brand_id);
    
    GRANT SELECT ON brands TO authenticated, anon;
    GRANT SELECT, UPDATE ON products TO authenticated;
  END IF;
END $$;
