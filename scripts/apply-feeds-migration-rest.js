#!/usr/bin/env node
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function for direct SQL execution (using POST to the REST API)
async function executeSql(sql) {
  try {
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'count=exact'
      },
      body: JSON.stringify({
        query: sql
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`SQL execution failed: ${errorData.message || response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`SQL Execution error: ${error.message}`);
    throw error;
  }
}

async function applyFeedsMigration() {
  try {
    console.log('Starting feed system migration...');
    
    // Create the feeds table
    console.log('Creating feeds table...');
    await executeSql(`
      CREATE TABLE IF NOT EXISTS feeds (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        url TEXT NOT NULL,
        language TEXT NOT NULL DEFAULT 'ru',
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_fetched TIMESTAMP WITH TIME ZONE
      );
    `);
    
    // Create index on language
    await executeSql(`
      CREATE INDEX IF NOT EXISTS feeds_language_idx ON feeds (language);
    `);
    
    // Create feeds_jobs table
    console.log('Creating feed_jobs table...');
    await executeSql(`
      CREATE TABLE IF NOT EXISTS feed_jobs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        feed_id UUID NOT NULL REFERENCES feeds(id) ON DELETE CASCADE,
        status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        started_at TIMESTAMP WITH TIME ZONE,
        finished_at TIMESTAMP WITH TIME ZONE,
        items_processed INTEGER DEFAULT 0,
        items_created INTEGER DEFAULT 0,
        items_updated INTEGER DEFAULT 0,
        items_failed INTEGER DEFAULT 0,
        error_message TEXT
      );
    `);
    
    // Create indexes for feed jobs
    await executeSql(`
      CREATE INDEX IF NOT EXISTS feed_jobs_feed_id_idx ON feed_jobs (feed_id);
      CREATE INDEX IF NOT EXISTS feed_jobs_status_idx ON feed_jobs (status);
      CREATE INDEX IF NOT EXISTS feed_jobs_created_at_idx ON feed_jobs (created_at);
    `);
    
    console.log('Tables created successfully!');
    console.log('Setting up initial feeds...');
    
    // Set up initial feeds
    const { error: feedsError } = await supabase.from('feeds').insert([
      {
        name: 'Russian Products',
        url: 'https://kuhteh.com.ua/price/ecommerce-ru.xml',
        language: 'ru',
        is_active: true
      },
      {
        name: 'Ukrainian Products',
        url: 'https://kuhteh.com.ua/price/ecommerce-ua.xml',
        language: 'uk',
        is_active: true
      }
    ]);
    
    if (feedsError) {
      // If error is because feeds already exist, just log it
      if (feedsError.code === '23505') {
        console.log('Initial feeds already exist, skipping...');
      } else {
        console.error(`Failed to create initial feeds: ${feedsError.message}`);
        // Continue execution instead of throwing
      }
    } else {
      console.log('Initial feeds created successfully!');
    }
    
    // Set up row level security policies
    console.log('Setting up security policies...');
    
    await executeSql(`
      ALTER TABLE feeds ENABLE ROW LEVEL SECURITY;
      DROP POLICY IF EXISTS "Allow full access to authenticated users" ON feeds;
      CREATE POLICY "Allow full access to authenticated users" 
        ON feeds FOR ALL TO authenticated 
        USING (true)
        WITH CHECK (true);
        
      ALTER TABLE feed_jobs ENABLE ROW LEVEL SECURITY;
      DROP POLICY IF EXISTS "Allow full access to authenticated users" ON feed_jobs;
      CREATE POLICY "Allow full access to authenticated users" 
        ON feed_jobs FOR ALL TO authenticated 
        USING (true)
        WITH CHECK (true);
    `);
    
    console.log('Feed system setup complete.');
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Execute the function
applyFeedsMigration();