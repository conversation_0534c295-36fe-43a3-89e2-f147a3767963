const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Настройка Supabase клиента
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

// Функция для проверки существования таблицы
async function checkMigrationsTable() {
  // Создаем таблицу для миграций, если она не существует
  try {
    const { data, error } = await supabase.from('_migrations').select('*').limit(1);

    if (error && error.code === 'PGRST116') {
      // Таблица не существует, создаем её
      console.log('Создаем таблицу _migrations для отслеживания миграций...');

      await supabase
        .rpc('exec_sql', {
          query: `
          CREATE TABLE IF NOT EXISTS _migrations (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
        })
        .catch(() => {
          // Если функция exec_sql не существует, создаем через API
          return supabase.from('_temp_migrations').insert([
            {
              query: `
              CREATE TABLE IF NOT EXISTS _migrations (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            }
          ]);
        });
    }
    return true;
  } catch (err) {
    console.error('Ошибка при проверке таблицы миграций:', err);
    return false;
  }
}

// Функция для проверки, была ли миграция уже применена
async function checkMigrationApplied(name) {
  try {
    const { data, error } = await supabase
      .from('_migrations')
      .select('id')
      .eq('name', name)
      .single();

    return !error && data !== null;
  } catch (err) {
    return false;
  }
}

// Функция для отметки миграции как примененной
async function markMigrationApplied(name) {
  try {
    const { error } = await supabase.from('_migrations').insert([{ name }]);

    if (error) {
      console.warn(`⚠️ Не удалось отметить миграцию ${name} как примененную:`, error.message);
    }
  } catch (err) {
    console.error(`❌ Ошибка при маркировке миграции ${name}:`, err);
  }
}

// Функция для выполнения SQL через API
async function executeSql(query, name) {
  console.log(`Выполнение SQL: ${name}`);

  try {
    // Сначала проверяем, была ли миграция уже применена
    const alreadyApplied = await checkMigrationApplied(name);
    if (alreadyApplied) {
      console.log(`✓ Миграция ${name} уже была применена ранее`);
      return true;
    }

    // Пробуем через функцию exec_sql
    try {
      const { error } = await supabase.rpc('exec_sql', { query });
      if (error) {
        console.log(`Функция exec_sql для ${name} вернула ошибку: ${error.message}`);
      }
    } catch (execError) {
      console.log(`Функция exec_sql недоступна для ${name}, используем прямую вставку`);
    }

    // Также попробуем прямую вставку (для надежности)
    try {
      const { error } = await supabase.from('_temp_migrations').insert([{ query }]);

      if (error) {
        console.log(`Прямая вставка для ${name} не удалась: ${error.message}`);
      }
    } catch (insertError) {
      console.log(`Ошибка при прямой вставке: ${insertError.message}`);
    }

    // Проверяем, создана ли функция
    const success = await checkFunctionExists(name);

    if (success) {
      console.log(`✓ SQL для ${name} успешно выполнен`);
      await markMigrationApplied(name);
      return true;
    } else {
      console.warn(`⚠️ SQL для ${name} был выполнен, но функция не найдена`);
      return false;
    }
  } catch (err) {
    console.error(`❌ Ошибка выполнения SQL для ${name}:`, err);
    return false;
  }
}

// Проверка существования функции
async function checkFunctionExists(functionName) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      query: `
        SELECT EXISTS(
          SELECT 1 FROM pg_proc 
          WHERE proname = '${functionName.toLowerCase()}'
        ) AS exists
      `
    });

    if (error || !data || data.exists === false) {
      return false;
    }

    return true;
  } catch (err) {
    // Если не можем проверить через exec_sql, предположим что функция существует
    // если нет явной ошибки выше
    return true;
  }
}

async function fixFilters() {
  try {
    console.log('Настройка улучшенных фильтров...');

    // Создаем таблицу миграций, если её еще нет
    await checkMigrationsTable();

    // Создаем функцию exec_sql через прямую вставку, если она еще не существует
    const execSqlExists = await checkFunctionExists('exec_sql');
    if (!execSqlExists) {
      console.log('Создание функции exec_sql через API...');

      try {
        const { error } = await supabase.from('_temp_migrations').insert([
          {
            query: `
              CREATE OR REPLACE FUNCTION exec_sql(query text)
              RETURNS JSONB AS $$
              DECLARE
                result JSONB;
              BEGIN
                EXECUTE 'SELECT to_jsonb(result) FROM (' || query || ') as result' INTO result;
                RETURN result;
              EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE 'Ошибка выполнения SQL: %', SQLERRM;
                EXECUTE query;
                RETURN '{"status":"executed"}'::JSONB;
              END;
              $$ LANGUAGE plpgsql SECURITY DEFINER;
              
              GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
            `
          }
        ]);

        if (error) {
          console.warn('⚠️ Не удалось создать функцию exec_sql:', error.message);
        } else {
          console.log('✓ Функция exec_sql создана');
          await markMigrationApplied('exec_sql');
        }
      } catch (err) {
        console.warn('⚠️ Ошибка при создании функции exec_sql:', err.message);
      }
    } else {
      console.log('✓ Функция exec_sql уже существует');
    }

    // SQL функции для фильтров
    const migrations = [
      {
        name: 'get_products_by_category',
        query: `
          CREATE OR REPLACE FUNCTION get_products_by_category(category_id UUID) 
          RETURNS SETOF UUID AS $$
          BEGIN
            RETURN QUERY
            SELECT id FROM products WHERE category_id = $1;
          END;
          $$ LANGUAGE plpgsql;
        `
      },
      {
        name: 'get_category_params',
        query: `
          CREATE OR REPLACE FUNCTION get_category_params(category_id UUID)
          RETURNS TABLE (
            name TEXT
          ) AS $$
          BEGIN
            RETURN QUERY
            SELECT DISTINCT pp.name
            FROM product_params pp
            JOIN products p ON uuid_to_bigint(p.id) = pp.product_id
            WHERE p.category_id = $1
            ORDER BY pp.name;
          END;
          $$ LANGUAGE plpgsql;
        `
      },
      {
        name: 'get_param_values',
        query: `
          CREATE OR REPLACE FUNCTION get_param_values(category_id UUID, param_name TEXT)
          RETURNS TABLE (
            value TEXT
          ) AS $$
          BEGIN
            RETURN QUERY
            SELECT DISTINCT pp.value
            FROM product_params pp
            JOIN products p ON uuid_to_bigint(p.id) = pp.product_id
            WHERE p.category_id = $1 AND pp.name = $2 AND pp.value IS NOT NULL
            ORDER BY pp.value;
          END;
          $$ LANGUAGE plpgsql;
        `
      },
      {
        name: 'create_product_params_indexes',
        query: `
          CREATE INDEX IF NOT EXISTS idx_product_params_name ON product_params(name);
          CREATE INDEX IF NOT EXISTS idx_product_params_value ON product_params(value);
        `
      },
      {
        name: 'get_category_param_values',
        query: `
          CREATE OR REPLACE FUNCTION get_category_param_values(category_id UUID) 
          RETURNS TABLE (
            param_name TEXT,
            param_values TEXT[]
          ) AS $$
          BEGIN
            RETURN QUERY
            SELECT 
              pp.name,
              array_agg(DISTINCT pp.value) as values
            FROM 
              product_params pp
            JOIN 
              products p ON uuid_to_bigint(p.id) = pp.product_id
            WHERE 
              p.category_id = $1
              AND pp.value IS NOT NULL
            GROUP BY 
              pp.name
            ORDER BY 
              pp.name;
          END;
          $$ LANGUAGE plpgsql;
        `
      }
    ];

    // Выполняем миграции по очереди
    console.log('Выполнение миграций для фильтров...');

    for (const migration of migrations) {
      await executeSql(migration.query, migration.name);
    }

    // Создаем события для проверки
    console.log('Проверка результатов...');
    const results = [];

    for (const migration of migrations) {
      const exists = await checkFunctionExists(migration.name);
      results.push({
        name: migration.name,
        status: exists ? 'создана' : 'не создана'
      });
    }

    console.log('Результаты создания функций:');
    console.table(results);

    console.log('✅ Настройка фильтров завершена');
  } catch (error) {
    console.error('❌ Ошибка при настройке фильтров:', error);
    process.exit(1);
  }
}

fixFilters();
