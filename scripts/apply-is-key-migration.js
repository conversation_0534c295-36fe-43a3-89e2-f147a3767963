require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.REACT_APP_SUPABASE_SERVICE_KEY
);

async function applyMigration() {
  try {
    console.log('Applying is_key migration to product_params table...');
    
    // Read migration SQL file
    const sqlFilePath = path.join(__dirname, 'add-is-key-migration.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf-8');
    
    // Execute SQL with Supabase
    const { data, error } = await supabase.rpc('exec_sql', {
      query: sqlContent
    });
    
    if (error) {
      throw error;
    }
    
    console.log('Migration completed successfully!');
    console.log('Added is_key column to product_params table and set defaults');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

applyMigration();