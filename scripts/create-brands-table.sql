-- Drop existing table and related objects
drop table if exists public.brands cascade;
drop function if exists public.handle_updated_at() cascade;

-- Create the brands table
create table public.brands (
    id uuid default gen_random_uuid() primary key,
    name text not null,
    logo_url text,
    website_url text,
    position integer default 0,
    active boolean default true,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create indexes for better performance
create index brands_active_idx on public.brands(active);
create index brands_position_idx on public.brands(position);

-- Enable Row Level Security
alter table public.brands enable row level security;

-- Create policies
create policy "Public can view active brands"
    on public.brands for select
    using (active = true);

create policy "Authenticated users can manage brands"
    on public.brands for all
    using (auth.role() = 'authenticated')
    with check (auth.role() = 'authenticated');

-- Create function to automatically update updated_at
create function public.handle_updated_at()
returns trigger as $$
begin
    new.updated_at = timezone('utc'::text, now());
    return new;
end;
$$ language plpgsql;

-- Create trigger to automatically update updated_at
create trigger set_updated_at
    before update on public.brands
    for each row
    execute function public.handle_updated_at(); 