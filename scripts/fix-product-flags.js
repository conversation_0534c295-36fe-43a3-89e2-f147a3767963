const { createClient } = require('@supabase/supabase-js');

// Instead of importing from config, define the values directly in this script
// since Node.js scripts use CommonJS and can't directly import ES modules
const SUPABASE_URL = 'https://smkkodldxjnthospnapv.supabase.co';
const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function fixProductFlags() {
  try {
    console.log('Checking and updating product flags...');

    // Check if exec_sql function exists and create if not
    try {
      // Try to execute the function
      await supabase.rpc('exec_sql', {
        query: 'SELECT 1'
      });
      console.log('SQL execution function exists');
    } catch (execError) {
      // Create the function if needed
      console.log('Creating SQL execution function...');
      try {
        // Use raw SQL insert for creating the function
        await supabase.from('_temp_migrations').insert([
          {
            query: `
            CREATE OR REPLACE FUNCTION exec_sql(query text)
            RETURNS void AS $$
            BEGIN
              EXECUTE query;
            END;
            $$ LANGUAGE plpgsql SECURITY DEFINER;
            GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
          `
          }
        ]);
        console.log('SQL execution function created successfully');
      } catch (createFuncError) {
        console.error('Error creating SQL function:', createFuncError);
      }
    }

    // Check if flags exist and create if not
    const { error: colError } = await supabase.rpc('exec_sql', {
      query: `
        ALTER TABLE products
        ADD COLUMN IF NOT EXISTS is_on_sale BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS is_new BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS is_bestseller BOOLEAN DEFAULT FALSE;
      `
    });

    if (colError) {
      console.error('Error adding columns:', colError);
      return;
    }

    // Get current product counts
    const { data, error } = await supabase
      .from('products')
      .select('id', { count: 'exact', head: true });

    if (error) {
      console.error('Error counting products:', error);
      return;
    }

    const totalProducts = data.count || 0;
    console.log(`Total products found: ${totalProducts}`);

    if (totalProducts === 0) {
      console.log('No products found in the database. Nothing to update.');
      return;
    }

    // Update at least 10% of products with each flag
    const batchSize = Math.max(5, Math.floor(totalProducts * 0.1));

    console.log(`Updating product flags (batch size: ${batchSize})...`);

    await supabase.rpc('exec_sql', {
      query: `
        -- Set random products as on sale
        UPDATE products
        SET is_on_sale = TRUE
        WHERE id IN (
          SELECT id FROM products
          WHERE is_on_sale = FALSE OR is_on_sale IS NULL
          ORDER BY RANDOM()
          LIMIT ${batchSize}
        );

        -- Set random products as new
        UPDATE products
        SET is_new = TRUE
        WHERE id IN (
          SELECT id FROM products
          WHERE is_new = FALSE OR is_new IS NULL
          ORDER BY RANDOM()
          LIMIT ${batchSize}
        );

        -- Set random products as bestseller
        UPDATE products
        SET is_bestseller = TRUE
        WHERE id IN (
          SELECT id FROM products
          WHERE is_bestseller = FALSE OR is_bestseller IS NULL
          ORDER BY RANDOM()
          LIMIT ${batchSize}
        );
      `
    });

    console.log('✅ Product flags updated successfully');
  } catch (error) {
    console.error('Error fixing product flags:', error);
  }
}

fixProductFlags();
