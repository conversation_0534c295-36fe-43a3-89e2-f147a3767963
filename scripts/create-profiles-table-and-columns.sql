-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add is_admin column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'is_admin'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'The is_admin column has been added to the profiles table';
  ELSE
    RAISE NOTICE 'The is_admin column already exists in the profiles table';
  END IF;
END $$;

-- Set the specific user as admin
UPDATE profiles 
SET is_admin = TRUE 
WHERE id = '2aef44bd-57c3-4b0e-a8bb-193cd5ba476e';

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Public profiles are viewable by everyone"
  ON profiles FOR SELECT
  USING (true);

CREATE POLICY "Users can insert their own profile"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

-- Add RLS exception for admin users for all operations
CREATE POLICY "Admin users can perform all operations"
  ON profiles
  USING (
    auth.uid() IN (
      SELECT p.id FROM profiles p WHERE p.is_admin = TRUE
    )
  );

-- Notify about completion
DO $$
BEGIN
  RAISE NOTICE 'Profiles table and is_admin column setup completed';
END $$; 