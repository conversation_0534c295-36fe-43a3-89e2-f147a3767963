-- SQL-скрипт для исправления таблицы баннеров
-- Выполните этот скрипт в SQL Editor в панели управления Supabase

-- Сначала проверим существование таблицы баннеров и создадим её, если не существует
CREATE TABLE IF NOT EXISTS banners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  image_url TEXT NOT NULL,
  link_url TEXT,
  subtitle TEXT,
  active BOOLEAN DEFAULT TRUE,
  position INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- До<PERSON>авляем отсутствующие колонки
DO $$
BEGIN
  -- Проверяем link_url колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'link_url'
  ) THEN
    ALTER TABLE banners ADD COLUMN link_url TEXT;
  END IF;
  
  -- Проверяем subtitle колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'subtitle'
  ) THEN
    ALTER TABLE banners ADD COLUMN subtitle TEXT;
  END IF;
  
  -- Проверяем active колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'active'
  ) THEN
    ALTER TABLE banners ADD COLUMN active BOOLEAN DEFAULT TRUE;
  END IF;
  
  -- Проверяем position колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'position'
  ) THEN
    ALTER TABLE banners ADD COLUMN position INTEGER DEFAULT 0;
  END IF;
  
  -- Проверяем updated_at колонку
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'banners' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE banners ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
  END IF;
END $$;

-- Создаём индексы для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_banners_active ON banners(active);
CREATE INDEX IF NOT EXISTS idx_banners_position ON banners(position);

-- Добавляем демо-баннер, если таблица пустая
INSERT INTO banners (title, image_url, link_url, active, position)
SELECT 'Демо баннер', 'https://via.placeholder.com/1200x400?text=Demo+Banner', '#', TRUE, 1
WHERE NOT EXISTS (SELECT 1 FROM banners);
