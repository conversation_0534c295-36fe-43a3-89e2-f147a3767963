-- Add discount column to products table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'products' AND column_name = 'discount'
    ) THEN
        ALTER TABLE products ADD COLUMN discount INTEGER DEFAULT 0;
        -- Update existing products to have a default discount of 0
        UPDATE products SET discount = 0 WHERE discount IS NULL;
    END IF;
END $$;