const { createClient } = require('@supabase/supabase-js');

// Setup Supabase client
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

async function createParamIndexes() {
  console.log('Creating indexes for product parameters...');

  try {
    // Check if product_params table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('product_params')
      .select('id')
      .limit(1);

    if (tableError && tableError.code === 'PGRST116') {
      console.error('The product_params table does not exist. Please create it first.');
      return;
    }

    console.log('Creating name index...');
    await supabase.from('_temp_migrations').insert({
      query: 'CREATE INDEX IF NOT EXISTS idx_product_params_name ON product_params(name)'
    });

    console.log('Creating value index...');
    await supabase.from('_temp_migrations').insert({
      query: 'CREATE INDEX IF NOT EXISTS idx_product_params_value ON product_params(value)'
    });

    console.log('Creating product_id index...');
    await supabase.from('_temp_migrations').insert({
      query:
        'CREATE INDEX IF NOT EXISTS idx_product_params_product_id ON product_params(product_id)'
    });

    console.log('Creating compound index for name+value...');
    await supabase.from('_temp_migrations').insert({
      query:
        'CREATE INDEX IF NOT EXISTS idx_product_params_name_value ON product_params(name, value)'
    });

    console.log('✅ Indexes created successfully');
  } catch (err) {
    console.error('Error creating indexes:', err);
  }
}

createParamIndexes();
