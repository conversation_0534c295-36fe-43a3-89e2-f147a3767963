// Используем path для правильного построения пути к файлу .env.local
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

const { createClient } = require('@supabase/supabase-js');

// Получаем переменные окружения
const SUPABASE_URL = process.env.REACT_APP_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.REACT_APP_SUPABASE_ANON_KEY;

// Логируем информацию о переменных окружения (не показываем полный ключ)
console.log('Supabase URL:', SUPABASE_URL);
console.log('Supabase API Key (частично):', SUPABASE_ANON_KEY ? SUPABASE_ANON_KEY.substring(0, 10) + '...' : 'не задан');

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('Ошибка: Не заданы переменные окружения REACT_APP_SUPABASE_URL и/или REACT_APP_SUPABASE_ANON_KEY');
  console.error('Проверьте файл .env.local в корне проекта и убедитесь, что он содержит эти переменные.');
  process.exit(1);
}

// Создаем клиент Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Таблицы нашего приложения
const TABLES = {
  PRODUCTS: 'products',
  CATEGORIES: 'categories',
  BANNERS: 'banners',
  PRODUCT_PARAMS: 'product_params',
  ORDERS: 'orders',
  ORDER_ITEMS: 'order_items',
  USERS: 'users',
  PROFILES: 'profiles'
};

// Инициализация базы данных
async function setupDatabase() {
  console.log('Начинаем инициализацию базы данных...');
  
  try {
    // Проверка подключения к Supabase
    console.log('Проверка подключения к Supabase...');
    const { data, error } = await supabase.from('_tables').select().limit(1);
    
    if (error) {
      throw new Error(`Ошибка подключения к Supabase: ${error.message}`);
    }
    
    console.log('Подключение к Supabase успешно установлено');
    
    // Создаем таблицы напрямую через SQL запросы
    await createBannersTableSQL();
    await createProductsTableSQL();
    
    console.log('База данных успешно инициализирована!');
    process.exit(0);
  } catch (error) {
    console.error('Ошибка при инициализации базы данных:', error);
    process.exit(1);
  }
}

// Создаем таблицу баннеров через прямой SQL запрос
async function createBannersTableSQL() {
  console.log('Создаем таблицу баннеров...');
  
  try {
    // SQL для создания таблицы баннеров с ПРОВЕРКОЙ существования ВСЕХ КОЛОНОК
    const createTableSQL = `
      -- Создаем таблицу, если её нет
      CREATE TABLE IF NOT EXISTS ${TABLES.BANNERS} (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        title TEXT NOT NULL,
        image_url TEXT NOT NULL,
        link_url TEXT,
        active BOOLEAN DEFAULT TRUE,
        position INTEGER DEFAULT 0,
        subtitle TEXT,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );

      -- Добавляем колонки, если они отсутствуют (для совместимости)
      DO $$
      BEGIN
        -- Проверяем link_url колонку
        IF NOT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'banners' AND column_name = 'link_url'
        ) THEN
          ALTER TABLE banners ADD COLUMN link_url TEXT;
        END IF;
        
        -- Проверяем subtitle колонку
        IF NOT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'banners' AND column_name = 'subtitle'
        ) THEN
          ALTER TABLE banners ADD COLUMN subtitle TEXT;
        END IF;
        
        -- Другие колонки также проверяем
        IF NOT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'banners' AND column_name = 'active'
        ) THEN
          ALTER TABLE banners ADD COLUMN active BOOLEAN DEFAULT TRUE;
        END IF;
        
        IF NOT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'banners' AND column_name = 'position'
        ) THEN
          ALTER TABLE banners ADD COLUMN position INTEGER DEFAULT 0;
        END IF;
      END $$;
    `;
    
    // Выполняем SQL
    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      // Если функция exec_sql не существует, создаем её через REST API
      if (error.message && error.message.includes('function "exec_sql" does not exist')) {
        console.log('Функция exec_sql не найдена, пробуем создать таблицу через расширение PostgREST...');
        
        // Используем метод REST API для выполнения SQL
        const { error: restError } = await supabase.rest.tables.create({
          name: TABLES.BANNERS,
          columns: [
            { name: 'id', type: 'uuid', primaryKey: true, defaultValue: { expression: 'uuid_generate_v4()' } },
            { name: 'title', type: 'text', notNull: true },
            { name: 'image_url', type: 'text', notNull: true },
            { name: 'link_url', type: 'text' },
            { name: 'subtitle', type: 'text' },
            { name: 'active', type: 'boolean', defaultValue: { value: true } },
            { name: 'position', type: 'integer', defaultValue: { value: 0 } },
            { name: 'created_at', type: 'timestamptz', defaultValue: { expression: 'now()' } },
            { name: 'updated_at', type: 'timestamptz', defaultValue: { expression: 'now()' } }
          ]
        });
        
        if (restError) {
          throw new Error(`Ошибка создания таблицы через REST API: ${restError.message}`);
        }
      } else {
        throw error;
      }
    }
    
    console.log('Таблица баннеров создана успешно или уже существует');
    
  } catch (error) {
    console.error('Ошибка при создании таблицы баннеров:', error);
    throw error;
  }
}

// Создаем таблицу товаров через SQL
async function createProductsTableSQL() {
  console.log('Создаем таблицу товаров...');
  
  try {
    // SQL для создания таблицы товаров
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${TABLES.PRODUCTS} (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        price DECIMAL(10,2) DEFAULT 0,
        description TEXT,
        image TEXT,
        vendor TEXT,
        category_id UUID,
        stock INTEGER DEFAULT 0,
        min_stock INTEGER DEFAULT 5,
        created_at TIMESTAMPTZ DEFAULT NOW(),
        updated_at TIMESTAMPTZ DEFAULT NOW()
      );
    `;
    
    // Выполняем SQL
    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error && !error.message.includes('already exists')) {
      throw error;
    }
    
    console.log('Таблица товаров создана успешно или уже существует');
  } catch (error) {
    console.error('Ошибка при создании таблицы товаров:', error);
    throw error;
  }
}

// Запускаем функцию инициализации
setupDatabase();
