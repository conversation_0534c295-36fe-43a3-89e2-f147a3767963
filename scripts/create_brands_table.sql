-- Создание таблицы для брендов

-- Сначала проверим существование таблицы
CREATE TABLE IF NOT EXISTS brands (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  logo TEXT,
  description TEXT,
  website_url TEXT,
  display_order INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Создание индексов
CREATE INDEX IF NOT EXISTS idx_brands_name ON brands(name);
CREATE INDEX IF NOT EXISTS idx_brands_featured ON brands(is_featured);

-- Вставка демо-данных, если таблица пуста
INSERT INTO brands (name, logo, description, website_url, is_featured)
SELECT 
  unnest(ARRAY[
    'Samsung', 'Apple', 'LG', 'Philips', 'Sony', 'Bosch', 
    'Siemens', 'Electrolux', 'Tefal'
  ]) AS name,
  unnest(ARRAY[
    'https://via.placeholder.com/200x100?text=Samsung',
    'https://via.placeholder.com/200x100?text=Apple',
    'https://via.placeholder.com/200x100?text=LG',
    'https://via.placeholder.com/200x100?text=Philips',
    'https://via.placeholder.com/200x100?text=Sony',
    'https://via.placeholder.com/200x100?text=Bosch',
    'https://via.placeholder.com/200x100?text=Siemens',
    'https://via.placeholder.com/200x100?text=Electrolux',
    'https://via.placeholder.com/200x100?text=Tefal'
  ]) AS logo,
  'Описание бренда' AS description,
  '#' AS website_url,
  unnest(ARRAY[true, true, true, true, true, false, false, false, false]) AS is_featured
WHERE NOT EXISTS (SELECT 1 FROM brands LIMIT 1);
