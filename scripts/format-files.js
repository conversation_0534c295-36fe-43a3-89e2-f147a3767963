const { execSync } = require('child_process');
const path = require('path');

// Files with formatting issues
const filesToFormat = [
  'src/components/home/<USER>',
  'src/pages/CategoryPage.js',
  'src/pages/Home.js',
  'src/pages/ProductPage.js',
  'src/store/slices/productsSlice.js',
  'src/utils/categoryUtils.js',
  'src/utils/xmlParser.js'
];

try {
  console.log('Formatting files with Prettier...');

  // Format each file
  for (const file of filesToFormat) {
    const fullPath = path.join(process.cwd(), file);
    console.log(`Formatting ${file}...`);
    execSync(`npx prettier --write ${fullPath}`, { stdio: 'inherit' });
  }

  console.log('✅ All files formatted successfully!');
} catch (error) {
  console.error('Error formatting files:', error);
  process.exit(1);
}
