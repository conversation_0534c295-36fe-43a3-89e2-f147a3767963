#!/usr/bin/env node
const { createClient } = require('@supabase/supabase-js');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env.local file if present
dotenv.config({ path: path.resolve(__dirname, '..', '.env.local') });

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupFeeds() {
  try {
    console.log('Setting up feeds system...');

    // Check if feeds table exists first
    console.log('Checking for existing feeds table...');
    let { data: feeds, error } = await supabase.from('feeds').select('id').limit(1);
    
    if (!error) {
      console.log('Feeds table already exists!');
    } else {
      console.log('Creating feeds table via API call...');
      
      // NOTE: We need to use the admin key or SQL here to create the table,
      // but for now, we'll just display instructions for the user
      console.log('\nIMPORTANT: You need to execute this SQL in the Supabase dashboard:');
      console.log('------------------------------------------------');
      console.log(`
CREATE TABLE IF NOT EXISTS feeds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  url TEXT NOT NULL,
  language TEXT NOT NULL DEFAULT 'ru',
  active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_fetched TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS feed_jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  feed_id UUID NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  started_at TIMESTAMP WITH TIME ZONE,
  finished_at TIMESTAMP WITH TIME ZONE,
  items_processed INTEGER DEFAULT 0,
  items_created INTEGER DEFAULT 0,
  items_updated INTEGER DEFAULT 0,
  items_failed INTEGER DEFAULT 0,
  error_message TEXT
);

-- Add foreign key
ALTER TABLE feed_jobs 
ADD CONSTRAINT feed_jobs_feed_id_fkey 
FOREIGN KEY (feed_id) REFERENCES feeds(id) ON DELETE CASCADE;`);
      console.log('------------------------------------------------');
      console.log('\nAfter executing the SQL, run this script again to insert initial feeds.\n');
      return;
    }
    
    // Set up initial feeds
    console.log('Setting up initial feeds...');
    
    const { error: feedsError } = await supabase.from('feeds').insert([
      {
        name: 'Russian Products',
        url: 'https://kuhteh.com.ua/price/ecommerce-ru.xml',
        language: 'ru',
        active: true
      },
      {
        name: 'Ukrainian Products',
        url: 'https://kuhteh.com.ua/price/ecommerce-ua.xml',
        language: 'uk',
        active: true
      }
    ]);
    
    if (feedsError) {
      // If error is because feeds already exist, just log it
      if (feedsError.code === '23505') {
        console.log('Initial feeds already exist, skipping...');
      } else {
        console.error(`Failed to create initial feeds: ${feedsError.message}`);
      }
    } else {
      console.log('Initial feeds created successfully!');
    }
    
    console.log('Feed system setup complete!');
  } catch (error) {
    console.error('Error setting up feeds:', error);
    process.exit(1);
  }
}

// Execute the function
setupFeeds();