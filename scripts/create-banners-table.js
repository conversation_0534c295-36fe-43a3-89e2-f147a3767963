const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

// Use environment variables or default test credentials
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createBannersTable() {
  console.log('Creating banners table if it doesn\'t exist...');
  
  try {
    // Check if the table exists
    const { error: checkError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public'
          AND table_name = 'banners'
        )
      `
    });
    
    if (checkError) {
      console.error('Error checking if banners table exists:', checkError);
      return;
    }

    // Create the table if it doesn't exist
    const { error: createError } = await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS public.banners (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT,
          subtitle TEXT,
          button_text TEXT,
          button_link TEXT,
          image TEXT,
          position INTEGER DEFAULT 1,
          active BOOLEAN DEFAULT TRUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
        );
      `
    });
    
    if (createError) {
      console.error('Error creating banners table:', createError);
      return;
    }

    // Create some sample banners
    const topBanner = {
      title: 'Welcome to our Shop',
      subtitle: 'Find quality products at great prices',
      button_text: 'Shop Now',
      button_link: '/products',
      image: 'https://images.unsplash.com/photo-1555529771-7888783a18d3?ixlib=rb-4.0.3&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1950&q=80',
      position: 1,
      active: true
    };

    const bottomBanner = {
      title: 'Special Offers',
      subtitle: 'Limited time discounts on selected products',
      button_text: 'View Offers',
      button_link: '/sale',
      image: 'https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1950&q=80',
      position: 6,
      active: true
    };

    // Insert sample banners
    const { error: insertError } = await supabase.from('banners').insert([topBanner, bottomBanner]);
    
    if (insertError) {
      console.error('Error inserting sample banners:', insertError);
      return;
    }

    console.log('Banners table setup complete!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createBannersTable();
