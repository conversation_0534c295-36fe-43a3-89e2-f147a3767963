const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

// Use environment variables or default test credentials
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixBannersData() {
  console.log('Начинаю диагностику и исправление баннеров...');
  
  // 1. Получаем и выводим структуру таблицы
  try {
    console.log('1. Проверка структуры таблицы баннеров...');
    
    const { data: columns, error: columnsError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'banners'
        ORDER BY ordinal_position;
      `
    });
    
    if (columnsError) {
      console.error('Ошибка при получении структуры таблицы:', columnsError);
    } else {
      console.log('Структура таблицы banners:', columns);
    }
    
    // 2. Проверяем наличие баннеров
    console.log('2. Проверка существующих баннеров...');
    
    const { data: banners, error: bannersError } = await supabase
      .from('banners')
      .select('*')
      .order('position');
      
    if (bannersError) {
      console.error('Ошибка при получении баннеров:', bannersError);
    } else {
      console.log(`Найдено ${banners.length} баннеров:`);
      banners.forEach((banner, i) => {
        console.log(`Баннер #${i+1}:`, {
          id: banner.id,
          position: banner.position,
          active: banner.active,
          title: banner.title,
          image: banner.image && typeof banner.image === 'string' 
            ? `${banner.image.substring(0, 50)}...` 
            : banner.image
        });
      });
      
      // 3. Проверка и исправление проблемных баннеров
      console.log('3. Исправление проблемных баннеров...');
      
      for (const banner of banners) {
        let needsUpdate = false;
        const updates = {};
        
        // Исправляем недопустимые URL изображений
        if (!banner.image || typeof banner.image !== 'string' || banner.image.trim() === '') {
          needsUpdate = true;
          updates.image = banner.position <= 5 
            ? 'https://images.unsplash.com/photo-1555529771-7888783a18d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'
            : 'https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80';
          console.log(`Исправление изображения для баннера ${banner.id}`);
        }
        
        // Проверяем активность баннера
        if (banner.active === null || banner.active === undefined) {
          needsUpdate = true;
          updates.active = true;
          console.log(`Установка активности для баннера ${banner.id}`);
        }
        
        // Применяем обновления если нужно
        if (needsUpdate) {
          const { error: updateError } = await supabase
            .from('banners')
            .update(updates)
            .eq('id', banner.id);
            
          if (updateError) {
            console.error(`Ошибка обновления баннера ${banner.id}:`, updateError);
          } else {
            console.log(`Баннер ${banner.id} успешно обновлен`);
          }
        }
      }
    }
    
    // 4. Создание новых баннеров если их нет или они все неактивны
    if (!banners || banners.length === 0 || !banners.some(b => b.active)) {
      console.log('4. Создание новых тестовых баннеров...');
      
      const { error: deleteError } = await supabase
        .from('banners')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Удаляем все существующие баннеры
      
      if (deleteError) {
        console.error('Ошибка при удалении старых баннеров:', deleteError);
      }
      
      // Создаем новые тестовые баннеры
      const topBanner = {
        title: 'Добро пожаловать в наш магазин',
        subtitle: 'Качественные товары по доступным ценам',
        button_text: 'Смотреть товары',
        button_link: '/products',
        image: 'https://images.unsplash.com/photo-1555529771-7888783a18d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80',
        position: 1,
        active: true
      };
      
      const bottomBanner = {
        title: 'Специальные предложения',
        subtitle: 'Скидки на избранные товары',
        button_text: 'Смотреть акции',
        button_link: '/sale',
        image: 'https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80',
        position: 6,
        active: true
      };
      
      const { error: insertError } = await supabase
        .from('banners')
        .insert([topBanner, bottomBanner]);
        
      if (insertError) {
        console.error('Ошибка при создании тестовых баннеров:', insertError);
      } else {
        console.log('Тестовые баннеры успешно созданы!');
      }
    }
    
    console.log('Диагностика и исправление баннеров завершены!');
    
  } catch (error) {
    console.error('Непредвиденная ошибка:', error);
  }
}

fixBannersData();
