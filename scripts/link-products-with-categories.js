/**
 * Скрипт для связывания товаров с категориями
 * 
 * На основе анализа данных мы выяснили:
 * 1. В YML-файле товары имеют тег categoryId, который указывает на ID категории
 * 2. В базе данных товары имеют поле external_id, которое соответствует ID товара из YML
 * 3. Категории в базе данных имеют имена, которые совпадают с именами категорий в YML
 */

const { supabase } = require('./supabaseClient');
const fs = require('fs');
const { DOMParser } = require('@xmldom/xmldom');

async function linkProductsWithCategories() {
  try {
    console.log('Начинаю процесс связывания товаров с категориями...');

    // 1. Получаем все категории для создания карты соответствия
    console.log('Получение списка категорий из базы данных...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name');

    if (categoriesError) {
      throw new Error(`Ошибка загрузки категорий: ${categoriesError.message}`);
    }

    if (!categories || categories.length === 0) {
      throw new Error('Категории не найдены в базе данных');
    }

    console.log(`Загружено ${categories.length} категорий из базы данных`);

    // 2. Загружаем информацию из YML-файла
    console.log('Загрузка данных из YML-файла...');
    
    // Путь к YML файлу
    const ymlFilePath = './feed.yml';
    
    if (!fs.existsSync(ymlFilePath)) {
      throw new Error(`YML-файл не найден по пути: ${ymlFilePath}`);
    }
    
    const xmlData = fs.readFileSync(ymlFilePath, 'utf8');
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlData, 'text/xml');
    
    // 3. Извлекаем категории из YML-файла
    console.log('Извлечение информации о категориях из YML-файла...');
    const categoryNodes = xmlDoc.getElementsByTagName('category');
    
    if (!categoryNodes || categoryNodes.length === 0) {
      throw new Error('Категории не найдены в YML-файле');
    }
    
    console.log(`Найдено ${categoryNodes.length} категорий в YML-файле`);
    
    // 4. Создаем карту соответствия yml_category_id -> category_name
    const ymlCategoryMap = {};
    
    for (let i = 0; i < categoryNodes.length; i++) {
      const category = categoryNodes[i];
      const id = category.getAttribute('id');
      const name = category.textContent;
      
      if (id && name) {
        ymlCategoryMap[id] = name;
      }
    }
    
    console.log(`Создана карта соответствий для ${Object.keys(ymlCategoryMap).length} категорий из YML-файла`);
    
    // 5. Создаем карту соответствия yml_category_id -> db_category_uuid
    const categoryIdToUuidMap = {};
    
    for (const [ymlCategoryId, ymlCategoryName] of Object.entries(ymlCategoryMap)) {
      // Ищем категорию в базе данных по имени
      const dbCategory = categories.find(cat => 
        cat.name.toLowerCase() === ymlCategoryName.toLowerCase()
      );
      
      if (dbCategory) {
        categoryIdToUuidMap[ymlCategoryId] = dbCategory.id;
      }
    }
    
    console.log(`Сопоставлено ${Object.keys(categoryIdToUuidMap).length} категорий из ${Object.keys(ymlCategoryMap).length}`);
    
    // 6. Извлекаем товары и их категории из YML-файла
    console.log('Извлечение информации о товарах из YML-файла...');
    const offerNodes = xmlDoc.getElementsByTagName('offer');
    
    if (!offerNodes || offerNodes.length === 0) {
      throw new Error('Товары не найдены в YML-файле');
    }
    
    console.log(`Найдено ${offerNodes.length} товаров в YML-файле`);
    
    // 7. Создаем карту соответствия product_external_id -> category_id
    const productCategoryMap = {};
    
    for (let i = 0; i < offerNodes.length; i++) {
      const offer = offerNodes[i];
      const productId = offer.getAttribute('id');
      
      // Извлекаем categoryId из товара
      const categoryIdNodes = offer.getElementsByTagName('categoryId');
      
      if (categoryIdNodes && categoryIdNodes.length > 0) {
        const categoryId = categoryIdNodes[0].textContent;
        
        if (productId && categoryId) {
          productCategoryMap[productId] = categoryId;
        }
      }
    }
    
    console.log(`Создана карта соответствий для ${Object.keys(productCategoryMap).length} товаров из YML-файла`);
    
    // 8. Получаем все товары из базы данных
    console.log('Получение списка товаров из базы данных...');
    
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, external_id, category_id');
    
    if (productsError) {
      throw new Error(`Ошибка загрузки товаров: ${productsError.message}`);
    }
    
    console.log(`Загружено ${products.length} товаров из базы данных`);
    
    // 9. Обновляем товары, устанавливая правильные связи с категориями
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    console.log('Начинаю обновление товаров...');
    
    // Обновляем товары по одному, используя метод update вместо upsert
    for (const product of products) {
      try {
        // Товар должен иметь external_id, соответствующий ID товара из YML
        if (!product.external_id) {
          skippedCount++;
          continue;
        }
        
        // Получаем ID категории из YML по external_id товара
        const ymlCategoryId = productCategoryMap[product.external_id];
        
        if (!ymlCategoryId) {
          skippedCount++;
          continue;
        }
        
        // Получаем UUID категории в базе данных по ID категории из YML
        const dbCategoryUuid = categoryIdToUuidMap[ymlCategoryId];
        
        if (!dbCategoryUuid) {
          skippedCount++;
          continue;
        }
        
        // Обновляем товар
        const { error: updateError } = await supabase
          .from('products')
          .update({ 
            category_id: dbCategoryUuid, 
            updated_at: new Date().toISOString()
          })
          .eq('id', product.id);
        
        if (updateError) {
          console.error(`Ошибка обновления товара ${product.id}:`, updateError.message);
          errorCount++;
        } else {
          updatedCount++;
          
          // Показываем прогресс каждые 50 товаров
          if (updatedCount % 50 === 0) {
            console.log(`Обновлено ${updatedCount} товаров...`);
          }
        }
      } catch (error) {
        console.error(`Ошибка обработки товара ${product.id}:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n===== Результаты обновления =====');
    console.log(`Всего товаров: ${products.length}`);
    console.log(`Успешно связано с категориями: ${updatedCount}`);
    console.log(`Пропущено: ${skippedCount}`);
    console.log(`Ошибки при обновлении: ${errorCount}`);
    console.log('================================\n');
    
    // 10. Проверяем результаты обновления
    console.log('Проверка результатов связывания...');
    
    const { data: updatedProducts, error: checkError } = await supabase
      .from('products')
      .select('id')
      .not('category_id', 'is', null)
      .limit(1);
    
    if (checkError) {
      console.error(`Ошибка при проверке обновления: ${checkError.message}`);
    } else {
      const hasLinkedProducts = updatedProducts && updatedProducts.length > 0;
      console.log(`Результат: ${hasLinkedProducts ? 'Товары успешно связаны с категориями' : 'Товары НЕ связаны с категориями'}`);
    }
    
  } catch (error) {
    console.error('Ошибка выполнения скрипта:', error.message);
    process.exit(1);
  }
}

// Запускаем функцию
linkProductsWithCategories()
  .then(() => {
    console.log('Скрипт успешно завершен');
    process.exit(0);
  })
  .catch(error => {
    console.error('Ошибка выполнения скрипта:', error);
    process.exit(1);
  });