// ESM version of feedUtils
import axios from 'axios';
import { DOMParser } from 'xmldom';
import { createClient } from '@supabase/supabase-js';

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Fetches XML feed from the specified URL
 */
async function fetchXmlFeed(url) {
  try {
    console.log(`Fetching feed from ${url}...`);
    const response = await axios.get(url, {
      responseType: 'text',
      headers: { Accept: 'application/xml, text/xml' }
    });
    
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');
    
    return xmlDoc;
  } catch (error) {
    console.error(`Error fetching or parsing XML feed from ${url}:`, error.message);
    throw error;
  }
}

/**
 * Extract products from XML document
 */
function extractProductsFromXml(xmlDoc, language = 'ru') {
  const products = [];
  const productNodes = xmlDoc.getElementsByTagName('item');
  
  console.log(`Found ${productNodes.length} products in the feed`);
  
  for (let i = 0; i < productNodes.length; i++) {
    const item = productNodes[i];
    
    try {
      const product = {
        external_id: getNodeTextContent(item, 'g:id'),
        name: getNodeTextContent(item, 'g:title'),
        description: getNodeTextContent(item, 'g:description'),
        price: parseFloat(getNodeTextContent(item, 'g:price').replace(/[^0-9.]/g, '')),
        image: getNodeTextContent(item, 'g:image_link'),
        category: getNodeTextContent(item, 'g:product_type'),
        availability: getNodeTextContent(item, 'g:availability') === 'in stock',
        vendor: getNodeTextContent(item, 'vendor'),
        condition: getNodeTextContent(item, 'g:condition'),
        language,
        additional_images: extractAdditionalImages(item),
        attributes: extractAttributes(item)
      };

      // Extract old price if available
      const oldPriceStr = getNodeTextContent(item, 'g:sale_price');
      if (oldPriceStr) {
        product.old_price = parseFloat(oldPriceStr.replace(/[^0-9.]/g, ''));
      }
      
      products.push(product);
    } catch (err) {
      console.error(`Error processing product at index ${i}:`, err.message);
    }
  }
  
  return products;
}

/**
 * Helper functions for XML parsing
 */
function getNodeTextContent(parentNode, tagName) {
  const node = parentNode.getElementsByTagName(tagName)[0];
  return node ? node.textContent : '';
}

function extractAdditionalImages(itemNode) {
  const images = [];
  const additionalImageNodes = itemNode.getElementsByTagName('g:additional_image_link');
  
  for (let i = 0; i < additionalImageNodes.length; i++) {
    const url = additionalImageNodes[i].textContent;
    if (url) images.push(url);
  }
  
  return images;
}

function extractAttributes(itemNode) {
  const attributes = {};
  const customLabelNodes = itemNode.getElementsByTagName('g:custom_label_0');
  const customLabelNodes1 = itemNode.getElementsByTagName('g:custom_label_1');
  const customLabelNodes2 = itemNode.getElementsByTagName('g:custom_label_2');
  
  // Process basic attributes if available
  if (customLabelNodes.length > 0) {
    const attrString = customLabelNodes[0].textContent;
    if (attrString) {
      try {
        const parsed = JSON.parse(attrString);
        Object.assign(attributes, parsed);
      } catch (e) {
        attributes.custom_label_0 = attrString;
      }
    }
  }

  // Additional custom labels
  if (customLabelNodes1.length > 0) {
    attributes.custom_label_1 = customLabelNodes1[0].textContent;
  }
  
  if (customLabelNodes2.length > 0) {
    attributes.custom_label_2 = customLabelNodes2[0].textContent;
  }
  
  return attributes;
}

/**
 * Process a feed by ID
 */
async function processFeed(feedId) {
  try {
    // Get feed information
    console.log(`Processing feed ${feedId}`);
    const { data: feed, error } = await supabase
      .from('feeds')
      .select('*')
      .eq('id', feedId)
      .single();
    
    if (error) throw error;
    
    if (!feed) {
      throw new Error(`Feed with ID ${feedId} not found`);
    }

    console.log(`Processing feed: ${feed.name} (${feed.language})`);
    
    // Create a job entry to track processing
    const { data: jobData, error: jobError } = await supabase
      .from('feed_jobs')
      .insert([{
        feed_id: feedId,
        status: 'pending'
      }])
      .select();
    
    if (jobError) {
      throw jobError;
    }

    const jobId = jobData[0].id;
    console.log(`Created job with ID: ${jobId}`);
    
    // Fetch and process the XML feed
    const xmlDoc = await fetchXmlFeed(feed.url);
    const products = extractProductsFromXml(xmlDoc, feed.language);
    
    // Save products to the database
    console.log(`Saving ${products.length} products to database...`);
    const stats = await saveProductsToDatabase(products, feedId, jobId);
    
    return {
      feedId,
      jobId,
      stats
    };
  } catch (error) {
    console.error('Error processing feed:', error);
    
    // Try to update the job status if we have a job ID
    try {
      const { data: failedJobs } = await supabase
        .from('feed_jobs')
        .select('id')
        .eq('feed_id', feedId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (failedJobs && failedJobs.length > 0) {
        await supabase
          .from('feed_jobs')
          .update({
            status: 'failed',
            error_message: error.message || 'Unknown error',
            finished_at: new Date().toISOString()
          })
          .eq('id', failedJobs[0].id);
      }
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }
    
    throw error;
  }
}

/**
 * Save products to database
 */
async function saveProductsToDatabase(products, feedId, jobId) {
  const stats = {
    processed: products.length,
    created: 0,
    updated: 0,
    failed: 0
  };

  // Update job status to processing
  await supabase
    .from('feed_jobs')
    .update({ 
      status: 'processing',
      started_at: new Date().toISOString(),
      items_processed: products.length
    })
    .eq('id', jobId);
  
  // Process products in batches to avoid overwhelming the database
  const batchSize = 50;
  const batches = Math.ceil(products.length / batchSize);
  
  for (let i = 0; i < batches; i++) {
    const start = i * batchSize;
    const end = Math.min(start + batchSize, products.length);
    const batch = products.slice(start, end);
    
    console.log(`Processing batch ${i + 1}/${batches} (products ${start + 1}-${end})`);
    
    for (const product of batch) {
      try {
        // Check if product exists by external_id
        const { data: existingProduct } = await supabase
          .from('products')
          .select('id')
          .eq('external_id', product.external_id)
          .single();
        
        if (existingProduct) {
          // Update existing product
          await supabase
            .from('products')
            .update({
              name: product.name,
              description: product.description,
              price: product.price,
              old_price: product.old_price,
              image: product.image,
              is_available: product.availability,
              vendor: product.vendor,
              attributes: product.attributes,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingProduct.id);
          
          stats.updated++;
        } else {
          // Create a new product
          const { error } = await supabase.from('products').insert([{
            external_id: product.external_id,
            name: product.name,
            description: product.description,
            price: product.price,
            old_price: product.old_price,
            image: product.image,
            is_available: product.availability,
            vendor: product.vendor,
            attributes: product.attributes,
            language: product.language
          }]);
          
          if (error) throw error;
          stats.created++;
        }
      } catch (error) {
        console.error(`Error processing product ${product.external_id}:`, error.message);
        stats.failed++;
      }
    }
  }
  
  // Update feed with last fetched timestamp
  await supabase.from('feeds').update({ last_fetched: new Date().toISOString() })
    .eq('id', feedId);
  
  // Update job completion status
  await supabase
    .from('feed_jobs')
    .update({
      status: 'completed',
      finished_at: new Date().toISOString(),
      items_created: stats.created,
      items_updated: stats.updated,
      items_failed: stats.failed
    })
    .eq('id', jobId);
  
  return stats;
}

// Export for ESM
export {
  processFeed,
  fetchXmlFeed,
  extractProductsFromXml
};