import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixBrandsRelationship() {
  try {
    console.log('Checking database structure...');
    
    // Step 1: Check if brands table exists
    console.log('Checking brands table...');
    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('id, name')
      .limit(10);
    
    if (brandsError) {
      console.error('Error checking brands table:', brandsError);
      
      // Create brands table if it doesn't exist
      console.log('Creating brands table...');
      try {
        // This is a workaround since we can't run CREATE TABLE directly
        const { error: insertError } = await supabase
          .from('brands')
          .insert([
            { name: 'Samsung', logo: 'https://via.placeholder.com/200x100?text=Samsung', description: 'Brand description', website_url: '#', is_featured: true },
            { name: 'Apple', logo: 'https://via.placeholder.com/200x100?text=Apple', description: 'Brand description', website_url: '#', is_featured: true },
            { name: 'LG', logo: 'https://via.placeholder.com/200x100?text=LG', description: 'Brand description', website_url: '#', is_featured: true },
            { name: 'Philips', logo: 'https://via.placeholder.com/200x100?text=Philips', description: 'Brand description', website_url: '#', is_featured: true },
            { name: 'Sony', logo: 'https://via.placeholder.com/200x100?text=Sony', description: 'Brand description', website_url: '#', is_featured: true }
          ]);
          
        if (insertError) {
          throw new Error(`Failed to create brands: ${insertError.message}`);
        }
        
        console.log('Successfully created brands table with sample data');
      } catch (e) {
        console.error('Error creating brands table:', e);
      }
    } else {
      console.log(`Found ${brands.length} brands in database`);
    }
    
    // Step 2: Check if products table exists and if it has brand_id column
    console.log('Checking products table...');
    let { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, brand_id')
      .limit(10);
    
    if (productsError && productsError.message.includes('does not exist')) {
      console.error('Products table does not exist:', productsError);
      throw new Error('Products table not found');
    }
    
    // Check if products have brand_id column
    const hasBrandIdColumn = !productsError || !productsError.message.includes('brand_id');
    
    if (!hasBrandIdColumn) {
      console.log('Products table doesn\'t have brand_id column. Checking if it has a brand column...');
      
      // Check if there's a brand column
      const { data: brandColCheck, error: brandColError } = await supabase
        .from('products')
        .select('id, brand')
        .limit(1);
        
      const hasBrandColumn = !brandColError || !brandColError.message.includes('does not exist');
      
      if (hasBrandColumn) {
        console.log('Products table has a brand column but not brand_id');
        console.log('Please use the Supabase dashboard to rename the column or add brand_id column');
      } else {
        console.log('Products table doesn\'t have brand or brand_id column');
        console.log('Please use the Supabase dashboard to add the brand_id column');
      }
    } else {
      console.log('Products table already has brand_id column');
    }
    
    // Step 3: Update ProductEditPage.js to handle the case where there might not be a brand_id
    console.log('Brands relationship check complete');
    
    return {
      brandsExist: !brandsError,
      hasBrandIdColumn: hasBrandIdColumn
    };
  } catch (error) {
    console.error('Error:', error.message);
    return { error: error.message };
  }
}

// Run the function and log the result
fixBrandsRelationship().then(result => {
  console.log('Result:', result);
});