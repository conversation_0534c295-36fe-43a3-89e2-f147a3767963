const { execSync } = require('child_process');
const path = require('path');

console.log('🔍 Starting formatting check...');

try {
  // Run Prettier formatting command on all files
  console.log('✏️ Applying Prettier formatting...');
  execSync('npx prettier --write "src/**/*.{js,jsx,ts,tsx,css,scss,json}"', {
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });

  // Run ESLint fix
  console.log('🛠 Running ESLint auto-fix...');
  execSync('npx eslint --fix "src/**/*.{js,jsx,ts,tsx}"', {
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });

  console.log('✅ Formatting complete!');
} catch (error) {
  console.error('❌ Error during formatting:', error.message);
  process.exit(1);
}
