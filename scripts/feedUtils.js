const axios = require('axios');
const { DOMParser } = require('@xmldom/xmldom');
// Use shared Supabase client for scripts
const { supabase } = require('./supabaseClient');

/**
 * Fetches XML feed from the specified URL
 */
async function fetchXmlFeed(url) {
  try {
    console.log(`Fetching feed from ${url}...`);
    const response = await axios.get(url, {
      responseType: 'text',
      headers: { Accept: 'application/xml, text/xml' }
    });
    
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');
    
    return xmlDoc;
  } catch (error) {
    console.error(`Error fetching or parsing XML feed from ${url}:`, error.message);
    throw error;
  }
}

/**
 * Extract products from XML document, supporting both standard formats and YML (Yandex Market Language)
 */
function extractProductsFromXml(xmlDoc, language = 'ru') {
  const products = [];
  
  // First check if this is a YML (Yandex Market Language) format
  const isYml = xmlDoc.getElementsByTagName('yml_catalog').length > 0;
  
  if (isYml) {
    console.log('Detected YML (Yandex Market Language) format');
    // YML has offers inside the <offers> tag
    const offers = xmlDoc.getElementsByTagName('offer');
    console.log(`Found ${offers.length} products with tag <offer> in the YML feed`);
    
    // Process each offer
    for (let i = 0; i < offers.length; i++) {
      const offer = offers[i];
      
      try {
        // Get product attributes from the offer
        const id = offer.getAttribute('id') || '';
        const available = offer.getAttribute('available') === 'true';
        
        // Get other product details
        const name = getNodeTextContent(offer, 'name');
        const vendorCode = getNodeTextContent(offer, 'vendorCode');
        const url = getNodeTextContent(offer, 'url');
        const price = parseFloat(getNodeTextContent(offer, 'price').replace(/[^0-9.]/g, '')) || 0;
        const oldPrice = parseFloat(getNodeTextContent(offer, 'oldprice').replace(/[^0-9.]/g, '')) || 0;
        const description = getNodeTextContent(offer, 'description');
        const categoryId = getNodeTextContent(offer, 'categoryId');
        const vendor = getNodeTextContent(offer, 'vendor');
        
        // Get all images from <picture> tags - МОДИФИКАЦИЯ: теперь берем все изображения
        const pictureNodes = offer.getElementsByTagName('picture');
        const allImages = [];
        
        // Собираем все URL изображений из тегов <picture>
        for (let j = 0; j < pictureNodes.length; j++) {
          const url = pictureNodes[j].textContent;
          if (url) allImages.push(url);
        }
        
        // Основное изображение - первое в списке
        const image = allImages.length > 0 ? allImages[0] : '';
        // Дополнительные изображения - все остальные, начиная со второго
        const additionalImages = allImages.slice(1);
        
        // Collect parameters (if any)
        const params = {};
        const paramNodes = offer.getElementsByTagName('param');
        for (let j = 0; j < paramNodes.length; j++) {
          const name = paramNodes[j].getAttribute('name');
          const value = paramNodes[j].textContent;
          if (name && value) {
            params[name] = value;
          }
        }
        
        const product = {
          external_id: id || vendorCode,
          name: name,
          description: description,
          price: price,
          image: image,
          category_id: categoryId, // Переименовываем category в category_id для ясности
          is_available: available, // Сохраняем атрибут available как is_available
          availability: available, // Для обратной совместимости оставляем также availability
          brand: vendor,
          condition: 'new',
          language,
          additional_images: additionalImages,
          attributes: params,
          image_gallery: allImages, // ДОБАВЛЕНО: сохраняем все изображения для галереи
          parameters: params // ДОБАВЛЕНО: сохраняем параметры для импорта в product_params
        };
        
        // Add old price if available
        if (oldPrice > 0 && oldPrice !== price) {
          product.old_price = oldPrice;
        }
        
        // Log first product for debugging
        if (i === 0) {
          console.log('First product extracted:', JSON.stringify(product, null, 2));
        }
        
        // Basic validation - require at least id, name and price
        if (product.external_id && product.name && product.price > 0) {
          products.push(product);
        } else {
          console.log(`Skipping product at index ${i} due to missing required fields:`, 
            product.external_id ? '' : 'missing ID, ',
            product.name ? '' : 'missing name, ',
            product.price <= 0 ? 'invalid price' : ''
          );
        }
      } catch (err) {
        console.error(`Error processing YML offer at index ${i}:`, err.message);
      }
    }
    
    return products;
  }
  
  // If not YML, use the more generic approach for other formats
  // First try standard "item" nodes
  const productNodes = xmlDoc.getElementsByTagName('item');
  
  // If no items found, try other common product container tags
  let tagToUse = 'item';
  if (productNodes.length === 0) {
    const alternativeTags = ['product', 'offer', 'goods', 'Product', 'Offer'];
    for (const tag of alternativeTags) {
      const nodes = xmlDoc.getElementsByTagName(tag);
      if (nodes.length > 0) {
        tagToUse = tag;
        break;
      }
    }
  }
  
  // Get all product nodes using the identified tag
  const allProductNodes = xmlDoc.getElementsByTagName(tagToUse);
  console.log(`Found ${allProductNodes.length} products with tag <${tagToUse}> in the feed`);
  
  if (allProductNodes.length === 0) {
    // Debug the XML structure
    console.log('Examining XML structure...');
    const rootNode = xmlDoc.documentElement;
    console.log('Root node name:', rootNode.nodeName);
    
    // List all first-level child nodes to help identify structure
    console.log('First-level children:');
    for (let i = 0; i < rootNode.childNodes.length; i++) {
      const child = rootNode.childNodes[i];
      if (child.nodeName !== '#text' && child.nodeName !== '#comment') {
        console.log(`- ${child.nodeName}`);
        // Check second level
        if (child.childNodes && child.childNodes.length > 0) {
          console.log('  Second-level children:');
          for (let j = 0; j < Math.min(child.childNodes.length, 5); j++) {
            const subChild = child.childNodes[j];
            if (subChild.nodeName !== '#text' && subChild.nodeName !== '#comment') {
              console.log(`  - ${subChild.nodeName}`);
            }
          }
          if (child.childNodes.length > 5) {
            console.log(`  - ... and ${child.childNodes.length - 5} more`);
          }
        }
      }
    }
    
    return products; // Return empty array if no products found
  }
  
  for (let i = 0; i < allProductNodes.length; i++) {
    const item = allProductNodes[i];
    
    try {
      // Create a flexible node content extractor that tries multiple possible tag formats
      const getFlexibleNodeContent = (node, possibleNames) => {
        for (const name of possibleNames) {
          const content = getNodeTextContent(node, name);
          if (content) return content;
        }
        return '';
      };
      
      // Map of field names to possible XML tag names
      const fieldMappings = {
        external_id: ['g:id', 'id', 'code', 'article', 'sku'],
        name: ['g:title', 'title', 'name', 'product_name'],
        description: ['g:description', 'description', 'desc'],
        price: ['g:price', 'price', 'retail_price', 'cost'],
        sale_price: ['g:sale_price', 'sale_price', 'discount_price', 'promotional_price'],
        image: ['g:image_link', 'image_link', 'image', 'picture', 'picture_url'],
        category: ['g:product_type', 'product_type', 'category', 'cat'],
        availability: ['g:availability', 'availability', 'in_stock', 'available'],
        brand: ['g:brand', 'brand', 'manufacturer']
      };
      
      // Create product object with flexible field extraction
      const product = {
        external_id: getFlexibleNodeContent(item, fieldMappings.external_id),
        name: getFlexibleNodeContent(item, fieldMappings.name),
        description: getFlexibleNodeContent(item, fieldMappings.description),
        price: parseFloat(getFlexibleNodeContent(item, fieldMappings.price).replace(/[^0-9.]/g, '')) || 0,
        image: getFlexibleNodeContent(item, fieldMappings.image),
        category: getFlexibleNodeContent(item, fieldMappings.category),
        availability: getFlexibleNodeContent(item, fieldMappings.availability).toLowerCase().includes('stock') ||
                     getFlexibleNodeContent(item, fieldMappings.availability) === 'true' ||
                     getFlexibleNodeContent(item, fieldMappings.availability) === '1',
        brand: getFlexibleNodeContent(item, fieldMappings.brand),
        condition: getNodeTextContent(item, 'g:condition') || 'new',
        language,
        additional_images: extractAdditionalImages(item),
        attributes: extractAttributes(item)
      };

      // Extract old price if available
      const oldPriceStr = getFlexibleNodeContent(item, fieldMappings.sale_price);
      if (oldPriceStr) {
        product.old_price = parseFloat(oldPriceStr.replace(/[^0-9.]/g, ''));
      }
      
      // Log first product for debugging
      if (i === 0) {
        console.log('First product extracted:', JSON.stringify(product, null, 2));
      }
      
      // Basic validation - require at least id, name and price
      if (product.external_id && product.name && product.price > 0) {
        products.push(product);
      } else {
        console.log(`Skipping product at index ${i} due to missing required fields:`, 
          product.external_id ? '' : 'missing ID, ',
          product.name ? '' : 'missing name, ',
          product.price <= 0 ? 'invalid price' : ''
        );
      }
    } catch (err) {
      console.error(`Error processing product at index ${i}:`, err.message);
    }
  }
  
  return products;
}

/**
 * Extract categories from XML document (YML format)
 */
function extractCategoriesFromXml(xmlDoc) {
  const categories = [];
  
  // Check if this is a YML format
  const isYml = xmlDoc.getElementsByTagName('yml_catalog').length > 0;
  
  if (isYml) {
    console.log('Extracting categories from YML format');
    const categoryNodes = xmlDoc.getElementsByTagName('category');
    console.log(`Found ${categoryNodes.length} categories in the YML feed`);
    
    for (let i = 0; i < categoryNodes.length; i++) {
      const category = categoryNodes[i];
      const id = category.getAttribute('id') || '';
      const parentId = category.getAttribute('parentId') || null;
      const name = category.textContent;
      
      if (id && name) {
        categories.push({
          external_id: id,
          name: name,
          parent_id: parentId
        });
      }
    }
    
    // Log first few categories for debugging
    if (categories.length > 0) {
      console.log('First 3 categories extracted:', 
        JSON.stringify(categories.slice(0, 3), null, 2));
    }
    
    return categories;
  }
  
  console.log('No categories found or not a YML format');
  return categories;
}

/**
 * Helper functions for XML parsing
 */
function getNodeTextContent(parentNode, tagName) {
  const node = parentNode.getElementsByTagName(tagName)[0];
  return node ? node.textContent : '';
}

function extractAdditionalImages(itemNode) {
  const images = [];
  const additionalImageNodes = itemNode.getElementsByTagName('g:additional_image_link');
  
  for (let i = 0; i < additionalImageNodes.length; i++) {
    const url = additionalImageNodes[i].textContent;
    if (url) images.push(url);
  }
  
  return images;
}

function extractAttributes(itemNode) {
  const attributes = {};
  
  // Extract XML <param> tags - this handles the format <param name="Name">Value</param>
  const paramNodes = itemNode.getElementsByTagName('param');
  if (paramNodes && paramNodes.length > 0) {
    console.log(`Found ${paramNodes.length} parameter nodes`);
    
    for (let i = 0; i < paramNodes.length; i++) {
      const param = paramNodes[i];
      const name = param.getAttribute('name');
      const value = param.textContent;
      
      if (name && value) {
        attributes[name] = value;
      }
    }
  }
  
  // Continue with existing attribute extraction logic
  const customLabelNodes = itemNode.getElementsByTagName('g:custom_label_0');
  const customLabelNodes1 = itemNode.getElementsByTagName('g:custom_label_1');
  const customLabelNodes2 = itemNode.getElementsByTagName('g:custom_label_2');
  
  // Process basic attributes if available
  if (customLabelNodes.length > 0) {
    const attrString = customLabelNodes[0].textContent;
    if (attrString) {
      try {
        const parsed = JSON.parse(attrString);
        Object.assign(attributes, parsed);
      } catch (e) {
        attributes.custom_label_0 = attrString;
      }
    }
  }

  // Additional custom labels
  if (customLabelNodes1.length > 0) {
    attributes.custom_label_1 = customLabelNodes1[0].textContent;
  }
  
  if (customLabelNodes2.length > 0) {
    attributes.custom_label_2 = customLabelNodes2[0].textContent;
  }
  
  return attributes;
}

/**
 * Process a feed by ID
 */
async function processFeed(feedId) {
  let jobId = null;
  try {
    // Get feed information
    console.log(`Processing feed ${feedId}`);
    const { data: feed, error } = await supabase
      .from('feeds')
      .select('*')
      .eq('id', feedId)
      .single();
    if (error) throw error;
    if (!feed) throw new Error(`Feed with ID ${feedId} not found`);
    console.log(`Processing feed: ${feed.name} (${feed.language})`);

    // Check for existing unfinished job (pending/processing)
    const { data: existingJobs, error: jobCheckError } = await supabase
      .from('feed_jobs')
      .select('id,status')
      .eq('feed_id', feedId)
      .in('status', ['pending', 'processing'])
      .order('created_at', { ascending: false })
      .limit(1);
    if (jobCheckError) throw jobCheckError;
    if (existingJobs && existingJobs.length > 0) {
      jobId = existingJobs[0].id;
      // ВРЕМЕННО ОТКЛЮЧАЕМ ПРОВЕРКУ
      // Если job уже processing, не запускать повторно
      // if (existingJobs[0].status === 'processing') {
      //   throw new Error('Feed is already being processed');
      // }
      // Обновить статус на processing и started_at
      await supabase
        .from('feed_jobs')
        .update({
          status: 'processing',
          started_at: new Date().toISOString(),
          error_message: null
        })
        .eq('id', jobId);
      console.log(`Updated existing job ${jobId} to status: processing`);
    } else {
      // Создать новую job
      const { data: jobData, error: jobError } = await supabase
        .from('feed_jobs')
        .insert([{
          feed_id: feedId,
          status: 'processing',
          started_at: new Date().toISOString()
        }])
        .select();
      if (jobError) throw jobError;
      jobId = jobData[0].id;
      console.log(`Created job with ID: ${jobId}`);
    }

    // Fetch and process the XML feed
    const xmlDoc = await fetchXmlFeed(feed.url);
    
    // Extract and save categories first
    const categories = extractCategoriesFromXml(xmlDoc);
    const categoryStats = await saveCategoriesToDatabase(categories, feedId);
    console.log(`Categories processed: ${categoryStats.processed}, created: ${categoryStats.created}, updated: ${categoryStats.updated}, failed: ${categoryStats.failed}`);
    
    // Then extract and save products
    const products = extractProductsFromXml(xmlDoc, feed.language);
    console.log(`Saving ${products.length} products to database...`);
    const productStats = await saveProductsToDatabase(products, feedId, jobId);
    
    // Combine stats for return
    const stats = {
      products: productStats,
      categories: categoryStats
    };
    
    return { feedId, jobId, stats };
  } catch (error) {
    console.error('Error processing feed:', error);
    // Try to update the job status if we have a job ID
    if (jobId) {
      try {
        await supabase
          .from('feed_jobs')
          .update({
            status: 'failed',
            error_message: error.message || 'Unknown error',
            finished_at: new Date().toISOString()
          })
          .eq('id', jobId);
      } catch (updateError) {
        console.error('Failed to update job status:', updateError);
      }
    }
    throw error;
  }
}

/**
 * Save products to database
 */
async function saveProductsToDatabase(products, feedId, jobId) {
  const stats = {
    processed: products.length,
    created: 0,
    updated: 0,
    failed: 0,
    params_created: 0
  };

  // Update job status to processing
  await supabase
    .from('feed_jobs')
    .update({ 
      status: 'processing',
      started_at: new Date().toISOString(),
      items_processed: products.length
    })
    .eq('id', jobId);
  
  // Process products in batches to avoid overwhelming the database
  const batchSize = 50;
  const batches = Math.ceil(products.length / batchSize);
  
  // Используем жестко заданный список ожидаемых колонок для таблицы products
  const availableColumns = [
    'id', 'external_id', 'name', 'description', 'price', 'original_price', 'old_price',
    'image', 'image_gallery', 'is_available', 'brand', 'attributes', 'language',
    'category_id', 'moderation_status', 'is_active', 'created_at', 'updated_at',
    'is_new', 'is_bestseller', 'is_on_sale', 'discount_percent'
  ];

  // Загрузка категорий для сопоставления ID
  const { data: allCategories, error: catError } = await supabase.from('categories').select('id, external_id');
  const categoryMap = new Map();
  if (catError) {
      console.error('CRITICAL: Failed to fetch categories for mapping. Products might not be linked to categories correctly.', catError);
  } else if (allCategories) {
      allCategories.forEach(cat => {
          if (cat.external_id) {
              categoryMap.set(cat.external_id.toString(), cat.id);
          }
      });
      console.log(`Loaded ${categoryMap.size} categories into map for ID lookup.`);
  }

  let productParamsColumns = [];
  try {
    const { data: paramsColumnsData } = await supabase.rpc('get_table_columns', { table_name: 'product_params' });
    if (paramsColumnsData && paramsColumnsData.length > 0) {
      productParamsColumns = paramsColumnsData.map(col => col.column_name.toLowerCase());
      console.log('Available product_params table columns:', productParamsColumns);
    } else {
        console.warn('RPC get_table_columns for product_params returned no data. Using default list.');
        productParamsColumns = ['product_id', 'name', 'value', 'is_key', 'category', 'created_at', 'updated_at']; // Добавил 'category' на всякий случай
    }
  } catch (error) {
    console.error('Error getting product_params columns, using default list:', error.message);
    productParamsColumns = ['product_id', 'name', 'value', 'is_key', 'category', 'created_at', 'updated_at'];
  }
    
  for (let i = 0; i < batches; i++) {
    const start = i * batchSize;
    const end = Math.min(start + batchSize, products.length);
    const batch = products.slice(start, end);
    
    console.log(`Processing batch ${i + 1}/${batches} (products ${start + 1}-${end})`);
    
    for (const product of batch) {
      try {
        // Check if product exists by external_id without .single()
        const { data: existingData, error: existingError } = await supabase
          .from('products')
          .select('id')
          .eq('external_id', product.external_id)
          .limit(1);
        if (existingError) throw existingError;
        const existingProduct = existingData && existingData.length > 0 ? existingData[0] : null;
        
        // Create a data object with only the columns that exist in the database
        // Создаем productData только с теми полями, которые есть в availableColumns И в объекте product из фида
        const productData = {};
        // Собираем основные данные из фида, проверяя наличие полей в availableColumns
        for (const col of availableColumns) {
            if (col === 'is_available' && product.hasOwnProperty('availability')) {
                productData[col] = product.availability;
            } else if (product.hasOwnProperty(col)) {
                 // Пропускаем category_id здесь, обработаем его отдельно с маппингом
                if (col !== 'category_id') {
                    productData[col] = product[col];
                }
            }
        }
        productData.updated_at = new Date().toISOString();

        // Сопоставление category_id из фида с UUID из таблицы categories
        const feedCategoryId = product.category_id; // ID категории из фида (например, "63")
        const actualCategoryIdUUID = feedCategoryId ? categoryMap.get(feedCategoryId.toString()) : null;

        if (feedCategoryId && !actualCategoryIdUUID) {
            console.warn(`Product ${product.external_id} (name: "${product.name}") has categoryId "${feedCategoryId}" from feed, but no matching UUID found in categories table. Category will not be set.`);
        }
        
        if (actualCategoryIdUUID && availableColumns.includes('category_id')) {
            productData.category_id = actualCategoryIdUUID; // Используем найденный UUID
        } else {
            delete productData.category_id;
        }
        
        if (productData.hasOwnProperty('id') && productData.id !== product.external_id) {
            delete productData.id;
        }
        
        let productId = null;
        
        if (existingProduct) {
          productId = existingProduct.id;
          const updatePayload = { ...productData };
          delete updatePayload.external_id;
          delete updatePayload.language;
          delete updatePayload.moderation_status;
          delete updatePayload.is_active;
          delete updatePayload.created_at;
          delete updatePayload.id;

          const { error: updateError } = await supabase
            .from('products')
            .update(updatePayload)
            .eq('id', productId);
          
          if (updateError) throw updateError;
          stats.updated++;
        } else {
          // Create a new product
          // productData уже содержит category_id (UUID или undefined) и другие отфильтрованные поля
          const insertPayload = { ...productData };
          
          // Явно добавляем/переопределяем поля, специфичные для новой записи
          insertPayload.external_id = product.external_id;
          // language уже должен быть в insertPayload из productData, если он прошел фильтр availableColumns
          // category_id уже обработан выше и находится (или отсутствует) в insertPayload

          // Статусы для модерации
          insertPayload.moderation_status = 'pending_approval';
          insertPayload.is_active = false;
          // Убираем status из-за проблем с этой колонкой
          
          // created_at будет установлен Supabase автоматически
          // id (первичный ключ) также будет автогенерирован Supabase
          delete insertPayload.id; // На всякий случай, если 'id' пришел из фида и попал в productData

          console.log(`[NEW PRODUCT INSERT] Payload for external_id ${product.external_id}:`, JSON.stringify(insertPayload, null, 2));
          
          const { data: newProduct, error: insertError } = await supabase
            .from('products')
            .insert([insertPayload])
            .select('id');
          
          if (insertError) throw insertError;
          if (newProduct && newProduct.length > 0) {
            productId = newProduct[0].id;
          }
          stats.created++;
        }
        
        // Сохраняем параметры товара в таблицу product_params 
        // только если мы успешно создали или обновили товар и у нас есть его ID
        if (productId && product.parameters && Object.keys(product.parameters).length > 0) {
          try {
            // Попробуем автоматически определить нужный формат ID для product_params
            // Так как есть проблемы с определением типа колонок в таблице, попробуем
            // использовать непосредственно external_id в числовом виде
            const numericId = parseInt(product.external_id, 10);
            const productIdForParams = !isNaN(numericId) ? numericId : productId;
            
            console.log(`Using ID: ${productIdForParams} for product ${product.name}`);
            
            // Сначала удаляем старые параметры 
            // Если внешний ID является числовым, используем его, в противном случае используем UUID
            await supabase
              .from('product_params')
              .delete()
              .eq('product_id', productIdForParams);
              
            // Создаем массив новых параметров с необходимым минимумом полей
            const paramsToInsert = Object.entries(product.parameters).map(([name, value]) => {
              return {
                product_id: productIdForParams,
                name: name,
                value: value.toString() // Преобразуем в строку для безопасности
              };
            });
            
            // Вставляем данные, если есть что вставлять
            if (paramsToInsert.length > 0) {
              console.log(`Attempting to insert ${paramsToInsert.length} parameters for product ${productIdForParams}`);
              
              const { error: paramsError } = await supabase
                .from('product_params')
                .insert(paramsToInsert);
                
              if (!paramsError) {
                stats.params_created += paramsToInsert.length;
                console.log(`Successfully added ${paramsToInsert.length} parameters for product ${productIdForParams}`);
              } else {
                console.error(`Error inserting parameters for product ${productIdForParams}:`, paramsError.message);
              }
            }
          } catch (error) {
            console.error(`Error handling parameters for product ${productId}:`, error.message);
          }
        }
        
      } catch (error) {
        console.error(`Error processing product ${product.external_id}:`, error.message);
        stats.failed++;
      }
    }
  }
  
  // Update feed with last fetched timestamp
  await supabase.from('feeds').update({ last_fetched: new Date().toISOString() })
    .eq('id', feedId);
  
  // Update job completion status
  await supabase
    .from('feed_jobs')
    .update({
      status: 'completed',
      finished_at: new Date().toISOString(),
      items_created: stats.created,
      items_updated: stats.updated,
      items_failed: stats.failed
    })
    .eq('id', jobId);
  
  console.log(`Processing completed: ${stats.processed} products processed, ${stats.created} created, ${stats.updated} updated, ${stats.failed} failed, ${stats.params_created} parameters added`);
  
  return stats;
}

/**
 * Save categories to database
 */
async function saveCategoriesToDatabase(categories, feedId) {
  const stats = {
    processed: categories.length,
    created: 0,
    updated: 0,
    failed: 0
  };

  // Skip if no categories
  if (categories.length === 0) {
    console.log('No categories to save');
    return stats;
  }
  
  console.log(`Saving ${categories.length} categories to database...`);
  
  try {
    // Получаем все существующие категории для сверки
    console.log('Fetching existing categories...');
    const { data: existingCategories, error: fetchError } = await supabase
      .from('categories')
      .select('id, name, parent_id');
    
    if (fetchError) {
      console.error('Error fetching existing categories:', fetchError.message);
      return stats;
    }
    
    console.log(`Found ${existingCategories.length} existing categories`);
    
    // Создаем карту "имя_категории -> id" для быстрого поиска
    const categoryNameToId = {};
    const categoryIdToUuid = {};
    
    existingCategories.forEach(cat => {
      categoryNameToId[cat.name.toLowerCase()] = cat.id;
    });
    
    // Формируем карту соответствий ID категорий из фида к UUID в базе данных
    // и заранее готовим категории для создания, если их нет в базе
    const categoriesToCreate = [];
    const categoryMap = {}; // yml_id -> db_uuid
    
    // Первый проход - поиск существующих категорий по имени
    for (const category of categories) {
      const lowerCaseName = category.name.toLowerCase();
      if (categoryNameToId[lowerCaseName]) {
        // Если категория с таким именем уже существует, запоминаем соответствие ID
        categoryMap[category.external_id] = categoryNameToId[lowerCaseName];
        stats.updated++;
      } else {
        // Если категории нет, добавляем в список для создания
        categoriesToCreate.push(category);
      }
      
      // Запоминаем соответствие категорий для установки parent_id
      categoryIdToUuid[category.external_id] = categoryNameToId[lowerCaseName];
    }
    
    console.log(`Categories to create: ${categoriesToCreate.length}, to update: ${stats.updated}`);
    
    // Создаем новые категории
    if (categoriesToCreate.length > 0) {
      // Создаем категории порциями
      const batchSize = 10;
      const batches = Math.ceil(categoriesToCreate.length / batchSize);
      
      for (let i = 0; i < batches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, categoriesToCreate.length);
        const batch = categoriesToCreate.slice(start, end);
        
        console.log(`Creating categories batch ${i + 1}/${batches} (${start + 1}-${end} of ${categoriesToCreate.length})`);
        
        for (const category of batch) {
          try {
            // Создаем новую категорию
            const newCategory = {
              name: category.name,
              // parent_id будем устанавливать во втором проходе
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              display_order: 0,
              is_featured: false
            };
            
            const { data, error } = await supabase
              .from('categories')
              .insert([newCategory])
              .select('id');
            
            if (error) {
              console.error(`Error creating category "${category.name}":`, error.message);
              stats.failed++;
              continue;
            }
            
            if (data && data.length > 0) {
              // Запоминаем соответствие внешнего ID и созданного UUID
              categoryMap[category.external_id] = data[0].id;
              categoryIdToUuid[category.external_id] = data[0].id;
              stats.created++;
            }
          } catch (error) {
            console.error(`Error processing category "${category.name}":`, error.message);
            stats.failed++;
          }
        }
      }
    }
    
    // Второй проход - устанавливаем parent_id для категорий
    console.log('Setting parent-child relationships for categories...');
    let parentUpdates = 0;
    
    for (const category of categories) {
      // Если у категории есть родитель
      if (category.parent_id && categoryMap[category.external_id] && categoryMap[category.parent_id]) {
        try {
          // Обновляем категорию, устанавливая parent_id
          const { error } = await supabase
            .from('categories')
            .update({ 
              parent_id: categoryMap[category.parent_id],
              updated_at: new Date().toISOString()
            })
            .eq('id', categoryMap[category.external_id]);
          
          if (error) {
            console.error(`Error setting parent for category "${category.name}":`, error.message);
          } else {
            parentUpdates++;
          }
        } catch (error) {
          console.error(`Error updating parent relationship for "${category.name}":`, error.message);
        }
      }
    }
    
    console.log(`Updated parent-child relationships for ${parentUpdates} categories`);
    
    // Обновляем товары, связывая их с категориями
    console.log(`Linking products to categories...`);
    const { data: products, error: productError } = await supabase
      .from('products')
      .select('id, category_id')
      .limit(1);
      
    if (productError) {
      console.error('Error checking products table for category_id field:', productError.message);
    } else {
      // Проверяем, есть ли поле category_id в таблице товаров
      const hasProductCategoryId = products && products.length > 0 && 'category_id' in products[0];
      
      if (hasProductCategoryId) {
        console.log('Products table has category_id field, will link products to categories');
        
        // Будем обрабатывать товары небольшими партиями
        const { data: allProducts, error: productsError } = await supabase
          .from('products')
          .select('id, category_id');
          
        if (productsError) {
          console.error('Error fetching products:', productsError.message);
        } else if (allProducts && allProducts.length > 0) {
          console.log(`Found ${allProducts.length} products to check for category links`);
          
          let productsUpdated = 0;
          
          for (const product of allProducts) {
            // Если у товара есть category_id (из import) и для него есть соответствие в нашей карте
            if (product.category_id && categoryMap[product.category_id]) {
              try {
                const { error: updateError } = await supabase
                  .from('products')
                  .update({ 
                    category_id: categoryMap[product.category_id],
                    updated_at: new Date().toISOString()
                  })
                  .eq('id', product.id);
                
                if (!updateError) {
                  productsUpdated++;
                }
              } catch (error) {
                console.error(`Error updating product ${product.id} category:`, error.message);
              }
            }
          }
          
          console.log(`Updated category references for ${productsUpdated} products`);
        }
      } else {
        console.log('Products table does not have category_id field, skipping product-category linking');
      }
    }
    
  } catch (error) {
    console.error('Error working with categories:', error);
    return stats;
  }
  
  console.log('Categories import completed:', stats);
  return stats;
}

// Export for CommonJS
module.exports = {
  processFeed,
  fetchXmlFeed,
  extractProductsFromXml,
  extractCategoriesFromXml,
  saveCategoriesToDatabase
};