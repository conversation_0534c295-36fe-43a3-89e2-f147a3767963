/**
 * Скрипт для принудительного сброса статуса задачи обработки фида
 */
const { supabase } = require('./supabaseClient');

async function resetFeedJob(feedId) {
  try {
    console.log(`Forcing reset of processing status for feed ${feedId}...`);
    
    // Найдем все задачи с статусом processing для указанного фида
    const { data, error } = await supabase
      .from('feed_jobs')
      .select('id,status')
      .eq('feed_id', feedId)
      .in('status', ['processing', 'pending'])
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error('Error fetching feed jobs:', error.message);
      return;
    }
    
    if (!data || data.length === 0) {
      console.log(`No processing jobs found for feed ${feedId}`);
      return;
    }
    
    console.log(`Found ${data.length} jobs with status processing or pending`);
    
    // Обновим статус всех найденных задач на 'failed'
    for (const job of data) {
      const { error: updateError } = await supabase
        .from('feed_jobs')
        .update({ 
          status: 'failed',
          error_message: 'Force reset by admin',
          finished_at: new Date().toISOString() 
        })
        .eq('id', job.id);
        
      if (updateError) {
        console.error(`Error updating job ${job.id}:`, updateError.message);
      } else {
        console.log(`Reset job ${job.id} to failed status`);
      }
    }
    
    console.log('Reset completed');
    
  } catch (error) {
    console.error('Error in reset script:', error.message);
  }
}

// Получим ID фида из аргументов командной строки
const feedId = process.argv[2];

if (!feedId) {
  console.error('Please specify a feed ID');
  console.error('Usage: node force-reset-feed-job.js FEED_ID');
  process.exit(1);
}

resetFeedJob(feedId)
  .then(() => process.exit(0))
  .catch(err => {
    console.error(err);
    process.exit(1);
  });