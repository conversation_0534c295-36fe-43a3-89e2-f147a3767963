// Скрипт для проверки содержимого таблицы banners
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Чтение учетных данных Supabase из переменных окружения
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Ошибка: Ключ Supabase не найден в переменных окружения.');
  console.log('Убедитесь, что переменная окружения REACT_APP_SUPABASE_ANON_KEY установлена.');
  process.exit(1);
}

// Инициализация клиента Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkBanners() {
  try {
    console.log('Проверка структуры таблицы banners...');
    
    // Получаем информацию о столбцах таблицы
    const { data: columns, error: columnsError } = await supabase
      .from('banners')
      .select('*')
      .limit(0);
      
    if (columnsError) {
      console.error('Ошибка при проверке структуры таблицы:', columnsError);
    } else {
      console.log('Доступные столбцы в таблице banners:', Object.keys(columns));
    }

    // Получаем все баннеры
    console.log('\nЗагрузка всех баннеров...');
    const { data, error } = await supabase.from('banners').select('*');

    if (error) {
      console.error('Ошибка при загрузке баннеров:', error);
      return;
    }

    console.log(`Найдено баннеров: ${data.length}`);
    
    if (data.length === 0) {
      console.log('Таблица banners пуста. Нет баннеров для отображения.');
      return;
    }

    // Выводим детали каждого баннера
    data.forEach((banner, index) => {
      console.log(`\n--- Баннер #${index + 1} ---`);
      console.log(`ID: ${banner.id}`);
      console.log(`Заголовок: ${banner.title}`);
      console.log(`Подзаголовок: ${banner.subtitle || '(не указан)'}`);
      console.log(`Описание: ${banner.description || '(не указано)'}`);
      console.log(`URL изображения: ${banner.image_url || '(не указан)'}`);
      console.log(`URL ссылки: ${banner.link_url || '(не указан)'}`);
      console.log(`Активен: ${banner.active !== undefined ? banner.active : '(не указано)'}`);
      console.log(`Статус активности (is_active): ${banner.is_active !== undefined ? banner.is_active : '(не указано)'}`);
      console.log(`Порядок отображения: ${banner.display_order !== undefined ? banner.display_order : '(не указан)'}`);
      console.log(`Позиция: ${banner.position !== undefined ? banner.position : '(не указана)'}`);
      console.log(`Создан: ${banner.created_at || '(дата не указана)'}`);
      
      // Выводим все поля баннера для полного понимания структуры
      console.log('\nВсе поля баннера:');
      Object.entries(banner).forEach(([key, value]) => {
        console.log(`${key}: ${value !== null ? value : '(null)'}`);
      });
    });
    
    // Проверяем запросы, которые используются в приложении
    console.log('\n\nПроверка работы запросов...');
    // Запрос с фильтром по active=true
    const { data: activeData, error: activeError } = await supabase
      .from('banners')
      .select('*')
      .eq('active', true);
      
    console.log(`Баннеры с active=true: ${activeData?.length || 0}`);
    if (activeError) console.error('Ошибка запроса active=true:', activeError);
    
    // Запрос с фильтром по is_active=true
    const { data: isActiveData, error: isActiveError } = await supabase
      .from('banners')
      .select('*')
      .eq('is_active', true);
      
    console.log(`Баннеры с is_active=true: ${isActiveData?.length || 0}`);
    if (isActiveError) console.error('Ошибка запроса is_active=true:', isActiveError);
    
    // Запрос с фильтром по position
    const { data: topPositionData, error: topPositionError } = await supabase
      .from('banners')
      .select('*')
      .lte('position', 5);
      
    console.log(`Баннеры с position <= 5: ${topPositionData?.length || 0}`);
    if (topPositionError) console.error('Ошибка запроса position <= 5:', topPositionError);
    
  } catch (error) {
    console.error('Неожиданная ошибка:', error);
  }
}

checkBanners()
  .then(() => {
    console.log('\nПроверка баннеров завершена');
  })
  .catch(err => {
    console.error('Неперехваченная ошибка:', err);
    process.exit(1);
  });