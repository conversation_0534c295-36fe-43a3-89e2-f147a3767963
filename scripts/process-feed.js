/**
 * Скрипт для обработки XML-фидов
 * Импортирует товары и категории из фида
 */

// Импортируем функцию для обработки фида
const { processFeed } = require('./feedUtils');
const { supabase } = require('./supabaseClient');

async function main() {
  try {
    // Получить feedId из аргументов командной строки
    const feedId = process.argv[2];
    
    if (!feedId) {
      console.error('Пожалуйста, укажите ID фида для обработки');
      console.error('Использование: node process-feed.js FEED_ID');
      
      // Если ID не указан, выведем список доступных фидов
      console.log('Доступные фиды:');
      const { data: feeds, error } = await supabase
        .from('feeds')
        .select('id, name, url')
        .order('id');
        
      if (error) {
        console.error('Ошибка при получении списка фидов:', error.message);
      } else if (feeds && feeds.length > 0) {
        feeds.forEach(feed => {
          console.log(`ID: ${feed.id}, Название: ${feed.name}, URL: ${feed.url}`);
        });
      } else {
        console.log('Фиды не найдены, создайте их сначала');
      }
      
      process.exit(1);
    }
    
    console.log(`Начинаю обработку фида с ID: ${feedId}`);
    
    // Запускаем обработку фида
    const result = await processFeed(feedId);
    
    console.log('Обработка фида завершена успешно');
    console.log('---------------------------------');
    console.log('Статистика импорта товаров:');
    console.log(`- Обработано: ${result.stats.products.processed}`);
    console.log(`- Создано новых: ${result.stats.products.created}`);
    console.log(`- Обновлено существующих: ${result.stats.products.updated}`);
    console.log(`- Ошибок: ${result.stats.products.failed}`);
    
    console.log('---------------------------------');
    console.log('Статистика импорта категорий:');
    console.log(`- Обработано: ${result.stats.categories.processed}`);
    console.log(`- Создано новых: ${result.stats.categories.created}`);
    console.log(`- Обновлено существующих: ${result.stats.categories.updated}`);
    console.log(`- Ошибок: ${result.stats.categories.failed}`);
    
    process.exit(0);
  } catch (error) {
    console.error('Произошла ошибка при обработке фида:', error.message);
    process.exit(1);
  }
}

main();