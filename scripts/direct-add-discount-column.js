const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY; 
// Use service role key if available for admin operations
const serviceRoleKey = process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, serviceRoleKey || supabaseKey);

async function addDiscountColumn() {
  console.log('Starting to add discount column to products table using direct SQL...');

  try {
    // Execute a direct SQL query to add the column if it doesn't exist
    const { data, error } = await supabase.rpc('execute_sql', { 
      sql: "ALTER TABLE products ADD COLUMN IF NOT EXISTS discount INTEGER DEFAULT 0" 
    });

    if (error) {
      console.error('Error executing SQL:', error.message);
      
      // Try alternative approach through REST API
      console.log('Trying alternative approach...');
      const { error: alterError } = await supabase
        .from('_alter_table')
        .insert({
          table: 'products',
          operation: 'ADD COLUMN IF NOT EXISTS discount INTEGER DEFAULT 0'
        });
      
      if (alterError) {
        console.error('Alternative approach failed:', alterError.message);
        
        // Final attempt - try direct query with PostgreSQL function
        console.log('Trying final approach with raw query...');
        const { error: rawError } = await supabase.rpc('exec', { 
          command: "SELECT 1; ALTER TABLE products ADD COLUMN IF NOT EXISTS discount INTEGER DEFAULT 0;"
        });
        
        if (rawError) {
          console.error('All attempts failed:', rawError.message);
        } else {
          console.log('Successfully added discount column using raw query approach');
        }
      } else {
        console.log('Successfully added discount column using alternative approach');
      }
    } else {
      console.log('Successfully added discount column to products table');
    }

    // Regardless of column creation, ensure all products have a discount value
    console.log('Updating NULL discount values to 0...');
    const { error: updateError } = await supabase
      .from('products')
      .update({ discount: 0 })
      .filter('discount', 'is', null);
    
    if (updateError) {
      console.error('Failed to update null discount values:', updateError.message);
    } else {
      console.log('Successfully updated any NULL discount values to 0');
    }
    
  } catch (err) {
    console.error('An unexpected error occurred:', err.message || err);
  }
}

// Execute the function
addDiscountColumn()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Script failed:', err);
    process.exit(1);
  });