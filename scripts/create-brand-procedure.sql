CREATE OR R<PERSON>LACE FUNCTION add_brand_id_column()
R<PERSON>URNS void AS $$
BEGIN
  -- First check if 'brand' column exists and rename it if needed
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'products' 
    AND column_name = 'brand'
  ) THEN
    ALTER TABLE products RENAME COLUMN brand TO brand_id;
  END IF;

  -- Add brand_id column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'products' 
    AND column_name = 'brand_id'
  ) THEN
    ALTER TABLE products ADD COLUMN brand_id UUID REFERENCES brands(id);
    CREATE INDEX idx_products_brand_id ON products(brand_id);
    GRANT SELECT ON brands TO authenticated, anon;
    GRANT SELECT, UPDATE ON products TO authenticated;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION add_brand_id_column() TO authenticated;
