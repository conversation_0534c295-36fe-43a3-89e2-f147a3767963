const fs = require('fs').promises;
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
const { executeSqlBatch } = require('../src/utils/database');

// Supabase configuration
const SUPABASE_URL = 'https://smkkodldxjnthospnapv.supabase.co';
const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

// Init Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Helper function to split SQL content into statements while preserving PL/pgSQL blocks
function splitSqlStatements(sqlContent) {
  const statements = [];
  let currentStatement = '';
  let inDollarQuote = false;
  let dollarTag = '';
  
  // Split the content into lines to process
  const lines = sqlContent.split('\n');
  
  for (const line of lines) {
    // Skip empty lines and comments
    if (line.trim() === '' || line.trim().startsWith('--')) {
      currentStatement += line + '\n';
      continue;
    }
    
    // Check for dollar quoted blocks (like $$ or $BODY$)
    if (!inDollarQuote) {
      // Look for beginning of dollar quote
      const dollarQuoteMatch = line.match(/\$([a-zA-Z0-9_]*)\$/);
      if (dollarQuoteMatch) {
        inDollarQuote = true;
        dollarTag = dollarQuoteMatch[0];
      }
    } else {
      // Look for matching end of dollar quote
      if (line.includes(dollarTag)) {
        inDollarQuote = false;
        dollarTag = '';
      }
    }
    
    currentStatement += line + '\n';
    
    // If we're not in a dollar quote block and the line contains a semicolon
    if (!inDollarQuote && line.includes(';')) {
      statements.push(currentStatement.trim());
      currentStatement = '';
    }
  }
  
  // Add the last statement if there's anything left
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }
  
  return statements;
}

async function executeMigration(sqlFilePath) {
  try {
    console.log(`Reading SQL file: ${sqlFilePath}`);

    // Read SQL file
    const sqlContent = await fs.readFile(sqlFilePath, 'utf8');

    // Ensure exec_sql function exists
    try {
      await supabase.rpc('exec_sql', { query: 'SELECT 1' });
    } catch (error) {
      console.log('Creating exec_sql function first...');
      await supabase.from('_temp_migrations').insert([
        {
          query: `
          CREATE OR REPLACE FUNCTION exec_sql(query text)
          RETURNS void AS $$
          BEGIN
            EXECUTE query;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
          
          GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
        `
        }
      ]);
    }

    // Split SQL into individual statements preserving PL/pgSQL blocks
    const statements = splitSqlStatements(sqlContent);

    console.log(`Executing ${statements.length} SQL statements...`);

    if (statements.length > 1) {
      // For multiple statements, use batch execution in a single transaction
      console.log(`Using batch execution for improved performance`);
      const result = await executeSqlBatch(statements);
      
      if (!result) {
        console.error('Error executing migration batch');
        process.exit(1);
      }
    } else if (statements.length === 1) {
      // For single statements, use regular execution
      const { error } = await supabase.rpc('exec_sql', { query: statements[0] });
      if (error) {
        console.error(`Error executing statement: ${error.message}`);
        console.error(`Statement: ${statements[0].substring(0, 100)}...`);
        process.exit(1);
      }
    } else {
      console.warn('No valid SQL statements found in file');
      process.exit(1);
    }

    console.log('✅ Migration executed successfully!');
  } catch (error) {
    console.error(`Error executing migration: ${error.message}`);
    process.exit(1);
  }
}

// Get SQL file path from command line argument
const sqlFilePath = process.argv[2];
if (!sqlFilePath) {
  console.error('Please provide the path to the SQL file as an argument');
  console.error('Example: node execute-migration.js ./migrations/create_product_params.sql');
  process.exit(1);
}

executeMigration(sqlFilePath);
