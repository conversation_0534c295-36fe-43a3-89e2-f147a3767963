const { createClient } = require('@supabase/supabase-js');

// Инициализация клиента Supabase
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co'; // Замените на ваш URL Supabase
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E'; // Замените на действительный ключ API
const supabase = createClient(supabaseUrl, supabaseKey);

const addColumns = async () => {
  try {
    // Добавление колонок в таблицу categories
    console.log('Добавление колонок в таблицу categories...');
    let { error: categoriesError } = await supabase.rpc('add_column_if_not_exists', {
      table_name: 'categories',
      column_name: 'image',
      column_type: 'text'
    });
    if (categoriesError) throw categoriesError;
    console.log('Колонка "image" добавлена в таблицу categories.');

    // Добавление колонок в таблицу products
    console.log('Добавление колонок в таблицу products...');
    let { error: productsError } = await supabase.rpc('add_column_if_not_exists', {
      table_name: 'products',
      column_name: 'image',
      column_type: 'text'
    });
    if (productsError) throw productsError;

    console.log('Колонка "image" добавлена в таблицу products.');
    console.log('Все необходимые колонки успешно добавлены!');
  } catch (error) {
    console.error('Ошибка при добавлении колонок:', error.message);
  }
};

addColumns();
