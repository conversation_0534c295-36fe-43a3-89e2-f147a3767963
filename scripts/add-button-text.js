// Скрипт для добавления текста кнопки к баннеру
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Чтение учетных данных Supabase из переменных окружения
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Ошибка: Ключ Supabase не найден в переменных окружения.');
  console.log('Убедитесь, что переменная окружения REACT_APP_SUPABASE_ANON_KEY установлена.');
  process.exit(1);
}

// Инициализация клиента Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function addButtonText() {
  try {
    console.log('Загрузка существующих баннеров...');
    const { data, error } = await supabase.from('banners').select('*');

    if (error) {
      console.error('Ошибка при загрузке баннеров:', error);
      return;
    }

    console.log(`Найдено баннеров: ${data.length}`);
    
    if (data.length === 0) {
      console.log('Таблица banners пуста. Нет баннеров для обновления.');
      return;
    }

    // Обновляем каждый баннер, у которого отсутствует текст кнопки
    for (const banner of data) {
      if (!banner.button_text) {
        console.log(`Обновление баннера ID ${banner.id} "${banner.title}"...`);
        
        // Добавляем текст кнопки "Подробнее"
        const { data: updateData, error: updateError } = await supabase
          .from('banners')
          .update({
            button_text: 'Подробнее'  // Устанавливаем текст кнопки
          })
          .eq('id', banner.id);
        
        if (updateError) {
          console.error(`Ошибка при обновлении баннера ${banner.id}:`, updateError);
        } else {
          console.log(`Баннер ${banner.id} успешно обновлен: добавлен текст кнопки "Подробнее"`);
        }
      }
    }
    
    // Проверяем результаты обновления
    const { data: updatedData } = await supabase.from('banners').select('*');
    console.log('\nОбновленные данные баннеров:');
    updatedData.forEach(banner => {
      console.log(`ID: ${banner.id}, Заголовок: ${banner.title}, Текст кнопки: ${banner.button_text || '(отсутствует)'}, Ссылка: ${banner.link_url || banner.button_link || '(отсутствует)'}`);
    });
  } catch (error) {
    console.error('Неожиданная ошибка:', error);
  }
}

addButtonText()
  .then(() => {
    console.log('\nДобавление текста кнопки завершено');
  })
  .catch(err => {
    console.error('Неперехваченная ошибка:', err);
    process.exit(1);
  });