#!/usr/bin/env node

/**
 * Feed processor script
 * 
 * This script can be run manually or as a cron job to process all active feeds.
 * 
 * Usage:
 *   node process-feeds.js [--feed-id=UUID] [--force] [--dry-run]
 * 
 * Options:
 *   --feed-id=UUID  Process only the specified feed
 *   --force         Process feeds even if they were recently updated
 *   --dry-run       Show what would be processed without making changes
 */

const path = require('path');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local file if present
dotenv.config({ path: path.resolve(__dirname, '..', '.env.local') });

// Parse command-line arguments
const args = process.argv.slice(2);
const options = {
  feedId: null,
  force: false,
  dryRun: false
};

args.forEach(arg => {
  if (arg.startsWith('--feed-id=')) {
    options.feedId = arg.split('=')[1];
  } else if (arg === '--force') {
    options.force = true;
  } else if (arg === '--dry-run') {
    options.dryRun = true;
  }
});

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

// Import feed processing utilities
const { processFeed } = require('./feedUtils');

// Function to check if feed needs processing (last processed more than X hours ago)
const shouldProcessFeed = (feed, minHours = 6) => {
  if (options.force) return true;
  
  if (!feed.last_fetched) return true;
  
  const lastFetched = new Date(feed.last_fetched);
  const now = new Date();
  const hoursDiff = (now - lastFetched) / (1000 * 60 * 60);
  
  return hoursDiff >= minHours;
};

// Main function
const processFeeds = async () => {
  try {
    console.log('Starting feed processor...');
    
    let query = supabase.from('feeds').select('*');
    
    // If feed ID specified, only process that feed
    if (options.feedId) {
      console.log(`Targeting specific feed: ${options.feedId}`);
      query = query.eq('id', options.feedId);
    } else {
      // Otherwise only get active feeds
      // Using 'active' column as per the actual table structure
      query = query.eq('active', true);
    }
    
    const { data: feeds, error } = await query;
    
    if (error) {
      throw error;
    }
    
    console.log(`Found ${feeds.length} feeds`);
    
    if (feeds.length === 0) {
      console.log('No feeds to process. Exiting...');
      return;
    }
    
    // If dry run, just show what would be processed
    if (options.dryRun) {
      console.log('DRY RUN - would process the following feeds:');
      feeds.forEach(feed => {
        const shouldProcess = shouldProcessFeed(feed);
        console.log(`- ${feed.name} (${feed.id}) - ${shouldProcess ? 'WOULD PROCESS' : 'WOULD SKIP'}`);
      });
      return;
    }
    
    // Process all eligible feeds
    const results = [];
    
    for (const feed of feeds) {
      if (!shouldProcessFeed(feed)) {
        console.log(`Skipping feed ${feed.name} - recently updated`);
        continue;
      }
      
      try {
        console.log(`\n=== Processing feed: ${feed.name} ===`);
        const result = await processFeed(feed.id);
        results.push({ ...result, name: feed.name });
        
        console.log(`Successfully processed feed: ${feed.name}`);
        console.log(`Created: ${result.stats.created}, Updated: ${result.stats.updated}, Failed: ${result.stats.failed}`);
      } catch (err) {
        console.error(`Failed to process feed ${feed.name}:`, err.message);
      }
    }
    
    console.log('\nFeed processing complete!');
    console.log('Summary:');
    if (results.length === 0) {
      console.log('- No feeds were processed successfully');
    } else {
      results.forEach(result => {
        console.log(`- ${result.name}: Created ${result.stats.created}, Updated ${result.stats.updated}, Failed ${result.stats.failed}`);
      });
    }
    
  } catch (error) {
    console.error('Error in feed processor:', error);
    process.exit(1);
  }
};

// Run the main function
processFeeds().catch(err => {
  console.error('Error in feed processor:', err);
  process.exit(1);
});