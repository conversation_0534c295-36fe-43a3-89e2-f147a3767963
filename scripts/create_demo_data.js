const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });
const { createClient } = require('@supabase/supabase-js');

// Получаем переменные окружения
const SUPABASE_URL = process.env.REACT_APP_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('Ошибка: Не заданы переменные окружения REACT_APP_SUPABASE_URL и/или REACT_APP_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Создаем клиент Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Функция для добавления демо-данных
async function createDemoData() {
  try {
    console.log('Создание демо-данных...');
    
    // 1. Создаем категории
    const categories = [
      { name: 'Электроника', image: 'https://via.placeholder.com/300?text=Электроника', is_featured: true },
      { name: 'Одежда', image: 'https://via.placeholder.com/300?text=Одежда', is_featured: true },
      { name: 'Обувь', image: 'https://via.placeholder.com/300?text=Обувь', is_featured: true },
      { name: 'Аксессуары', image: 'https://via.placeholder.com/300?text=Аксессуары', is_featured: true },
      { name: 'Мебель', image: 'https://via.placeholder.com/300?text=Мебель', is_featured: true },
      { name: 'Книги', image: 'https://via.placeholder.com/300?text=Книги', is_featured: true }
    ];
    
    console.log('Добавление категорий...');
    for (let i = 0; i < categories.length; i++) {
      const category = categories[i];
      const { error } = await supabase.from('categories').insert({
        name: category.name,
        image: category.image,
        is_featured: category.is_featured,
        display_order: i
      });
      
      if (error) {
        console.warn(`Ошибка при добавлении категории ${category.name}: ${error.message}`);
      }
    }
    console.log('Категории добавлены');
    
    // 2. Создаем продукты
    console.log('Добавление продуктов...');
    
    // Получаем ID категорий
    const { data: categoryData } = await supabase.from('categories').select('id, name');
    const categoryMap = categoryData.reduce((map, cat) => {
      map[cat.name] = cat.id;
      return map;
    }, {});
    
    const products = [];
    
    // Создаем обычные продукты
    for (let i = 1; i <= 10; i++) {
      const categoryName = categories[Math.floor(Math.random() * categories.length)].name;
      products.push({
        name: `Обычный товар ${i}`,
        price: Math.floor(Math.random() * 10000) + 1000,
        image: `https://via.placeholder.com/300?text=Обычный+товар+${i}`,
        category_id: categoryMap[categoryName],
        stock: Math.floor(Math.random() * 100) + 10
      });
    }
    
    // Создаем новые продукты
    for (let i = 1; i <= 10; i++) {
      const categoryName = categories[Math.floor(Math.random() * categories.length)].name;
      products.push({
        name: `Новый товар ${i}`,
        price: Math.floor(Math.random() * 10000) + 1000,
        image: `https://via.placeholder.com/300?text=Новый+товар+${i}`,
        category_id: categoryMap[categoryName],
        stock: Math.floor(Math.random() * 100) + 10,
        is_new: true
      });
    }
    
    // Создаем продукты со скидкой
    for (let i = 1; i <= 10; i++) {
      const categoryName = categories[Math.floor(Math.random() * categories.length)].name;
      const original_price = Math.floor(Math.random() * 10000) + 5000;
      const discount = Math.floor(original_price * 0.2); // Скидка 20%
      products.push({
        name: `Товар со скидкой ${i}`,
        price: original_price - discount,
        original_price: original_price,
        image: `https://via.placeholder.com/300?text=Товар+со+скидкой+${i}`,
        category_id: categoryMap[categoryName],
        stock: Math.floor(Math.random() * 100) + 10,
        is_on_sale: true
      });
    }
    
    // Создаем хиты продаж
    for (let i = 1; i <= 10; i++) {
      const categoryName = categories[Math.floor(Math.random() * categories.length)].name;
      products.push({
        name: `Хит продаж ${i}`,
        price: Math.floor(Math.random() * 10000) + 1000,
        image: `https://via.placeholder.com/300?text=Хит+продаж+${i}`,
        category_id: categoryMap[categoryName],
        stock: Math.floor(Math.random() * 100) + 10,
        is_bestseller: true
      });
    }
    
    // Добавляем продукты порциями, чтобы избежать ошибок с большим запросом
    const batchSize = 10;
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      const { error } = await supabase.from('products').insert(batch);
      
      if (error) {
        console.warn(`Ошибка при добавлении партии продуктов: ${error.message}`);
      } else {
        console.log(`Добавлена партия продуктов ${i+1}-${Math.min(i+batchSize, products.length)}`);
      }
    }
    
    console.log('Демо-данные созданы успешно!');
  } catch (error) {
    console.error('Ошибка при создании демо-данных:', error);
  }
}

// Запускаем функцию
createDemoData().then(() => {
  console.log('Готово!');
  process.exit(0);
}).catch(error => {
  console.error('Критическая ошибка:', error);
  process.exit(1);
});
