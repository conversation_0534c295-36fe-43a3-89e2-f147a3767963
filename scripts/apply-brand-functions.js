import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_SERVICE_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyFunctions() {
  try {
    console.log('Reading SQL functions...');
    const sql = fs.readFileSync(path.join(__dirname, 'create-brand-functions.sql'), 'utf8');

    console.log('Applying SQL functions...');
    const { error } = await supabase.rpc('exec_sql', { query: sql });

    if (error) {
      throw error;
    }

    console.log('Successfully applied SQL functions');
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

applyFunctions();