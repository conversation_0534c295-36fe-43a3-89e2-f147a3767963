// <PERSON>ript to apply the fix for banner table structure
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Read supabase credentials from environment variables
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: Supabase key not found in environment variables.');
  console.log('Please make sure the REACT_APP_SUPABASE_ANON_KEY environment variable is set.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function applyBannersFix() {
  try {
    console.log('Reading SQL migration file...');
    const sqlFilePath = path.join(__dirname, 'fix-banners-active-column.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Applying migration to add the missing columns to the banners table...');
    const { error } = await supabase.rpc('exec_sql', { sql: sqlContent });

    if (error) {
      console.error('Error executing SQL: ', error);
      
      // Fallback to direct table modification if RPC fails
      console.log('Attempting direct table modification...');
      
      // First check if the column exists
      const { data: columnCheck, error: checkError } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'banners')
        .eq('column_name', 'active');
        
      if (checkError) {
        console.error('Error checking column existence:', checkError);
      } else {
        // If the column doesn't exist, add it
        if (!columnCheck || columnCheck.length === 0) {
          console.log('Adding active column directly...');
          // Using a raw SQL query as a last resort
          const { error: alterError } = await supabase.rpc('exec_sql', {
            sql: 'ALTER TABLE banners ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT TRUE;'
          });
          
          if (alterError) {
            console.error('Failed to add column directly:', alterError);
            console.log('\nPlease execute this SQL manually in your Supabase SQL editor:');
            console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT TRUE;');
            console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;');
            console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS image_url TEXT;');
            console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS link_url TEXT;');
          } else {
            console.log('Successfully added active column.');
          }
        } else {
          console.log('The active column already exists.');
        }
      }
      
      return;
    }

    console.log('Successfully applied banner table fixes.');
    console.log('You should now be able to create banners in the admin panel.');
  } catch (error) {
    console.error('An error occurred:', error);
    console.log('\nPlease execute this SQL manually in your Supabase SQL editor:');
    console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT TRUE;');
    console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;');
    console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS image_url TEXT;');
    console.log('ALTER TABLE banners ADD COLUMN IF NOT EXISTS link_url TEXT;');
  }
}

applyBannersFix()
  .then(() => {
    console.log('Script execution completed.');
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });