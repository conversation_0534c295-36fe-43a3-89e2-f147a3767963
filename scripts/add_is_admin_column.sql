-- Create function to add is_admin column to profiles table
CREATE OR <PERSON>EPLACE FUNCTION add_is_admin_column()
RETURNS void AS $$
BEGIN
  -- Check if the column already exists
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' 
    AND column_name = 'is_admin'
  ) THEN
    -- Add the is_admin column with default value false
    ALTER TABLE profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'The is_admin column has been added to the profiles table';
    
    -- Update first user to be admin (optional, enable if needed)
    -- UPDATE profiles SET is_admin = TRUE WHERE id IN (SELECT id FROM profiles ORDER BY created_at LIMIT 1);
  ELSE
    RAISE NOTICE 'The is_admin column already exists in the profiles table';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION add_is_admin_column() TO authenticated, anon; 