import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabaseSchema() {
  try {
    console.log('Checking database schema...');
    
    // Check products table structure
    const { data: productsInfo, error: productsError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'products'
        ORDER BY ordinal_position;
      `
    });
    
    if (productsError) {
      console.error('Error checking products table:', productsError.message);
    } else {
      console.log('Products table columns:');
      console.table(productsInfo);
    }

    // Check brands table structure
    const { data: brandsInfo, error: brandsError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'brands'
        ORDER BY ordinal_position;
      `
    });
    
    if (brandsError) {
      console.error('Error checking brands table:', brandsError.message);
    } else {
      console.log('Brands table columns:');
      console.table(brandsInfo);
    }
    
    // Check foreign key relationships
    const { data: fkInfo, error: fkError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM
          information_schema.table_constraints AS tc
          JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
          JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
          AND (tc.table_name = 'products' OR ccu.table_name = 'products');
      `
    });
    
    if (fkError) {
      console.error('Error checking foreign keys:', fkError.message);
    } else {
      console.log('Foreign key relationships:');
      console.table(fkInfo);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

checkDatabaseSchema();