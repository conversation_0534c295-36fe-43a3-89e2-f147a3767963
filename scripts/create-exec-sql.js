const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createExecSqlFunction() {
  try {
    console.log('Creating exec_sql function...');
    
    // Make a direct SQL query to create the function
    const { data, error } = await supabase.rpc('postgres', {
      sql: `
        -- Create the SQL execution function
        CREATE OR REPLACE FUNCTION exec_sql(query text)
        RETURNS void AS $$
        BEGIN
          EXECUTE query;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Grant execute permission
        GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
      `
    });
    
    if (error) {
      // If we get an error with rpc, try using REST API directly
      console.log('Direct RPC method failed, trying alternative approach...');
      
      const response = await fetch(`${supabaseUrl}/rest/v1/sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        },
        body: JSON.stringify({
          query: `
            -- Create the SQL execution function
            CREATE OR REPLACE FUNCTION exec_sql(query text)
            RETURNS void AS $$
            BEGIN
              EXECUTE query;
            END;
            $$ LANGUAGE plpgsql SECURITY DEFINER;

            -- Grant execute permission
            GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
          `
        })
      });
      
      const result = await response.json();
      
      if (response.ok) {
        console.log('Successfully created exec_sql function via REST API');
      } else {
        console.error('Error creating exec_sql function via REST API:', result);
        throw new Error('Failed to create exec_sql function');
      }
    } else {
      console.log('Successfully created exec_sql function');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
    console.log('You may need to create this function manually in the Supabase SQL editor.');
    console.log(`
      -- Create the SQL execution function
      CREATE OR REPLACE FUNCTION exec_sql(query text)
      RETURNS void AS $$
      BEGIN
        EXECUTE query;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Grant execute permission
      GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
    `);
    process.exit(1);
  }
}

createExecSqlFunction(); 