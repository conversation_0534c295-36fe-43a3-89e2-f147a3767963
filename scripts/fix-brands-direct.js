import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixBrandsRelationship() {
  try {
    console.log('Checking database structure...');
    
    // Step 1: Check if brands table exists and has data
    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('id')
      .limit(1);

    if (brandsError) {
      // Create brands table with initial data
      console.log('Creating brands table with initial data...');
      
      const { data: newBrands, error: createError } = await supabase
        .from('brands')
        .insert([
          { name: 'Samsung', logo: 'https://via.placeholder.com/200x100?text=Samsung', description: 'Brand description', website_url: '#', is_featured: true },
          { name: 'Apple', logo: 'https://via.placeholder.com/200x100?text=Apple', description: 'Brand description', website_url: '#', is_featured: true },
          { name: 'LG', logo: 'https://via.placeholder.com/200x100?text=LG', description: 'Brand description', website_url: '#', is_featured: true },
          { name: 'Philips', logo: 'https://via.placeholder.com/200x100?text=Philips', description: 'Brand description', website_url: '#', is_featured: true },
          { name: 'Sony', logo: 'https://via.placeholder.com/200x100?text=Sony', description: 'Brand description', website_url: '#', is_featured: true }
        ])
        .select();

      if (createError) {
        throw new Error('Error creating brands: ' + createError.message);
      }
      console.log('Created brands table with initial data');
    } else {
      console.log('Brands table exists');
    }

    // Step 2: Check products table for brand_id column
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('brand_id')
      .limit(1);

    if (productsError && productsError.message.includes('column "brand_id" does not exist')) {
      // Check if 'brand' column exists
      const { data: brandCheck, error: brandCheckError } = await supabase
        .from('products')
        .select('brand')
        .limit(1);

      if (!brandCheckError) {
        console.log('Found existing brand column, will be converted to brand_id');
        // We found the brand column exists, but we can't rename it through the API
        // We'll need to handle this case in the UI by using the brand column name
      }

      // Add brand_id column
      console.log('Adding brand_id column...');
      const { data: alterResult, error: alterError } = await supabase
        .from('products')
        .update({ brand_id: null })
        .eq('id', '00000000-0000-0000-0000-000000000000') // This will fail but create the column
        .select();

      if (alterError && !alterError.message.includes('not found')) {
        throw new Error('Error adding brand_id column: ' + alterError.message);
      }
    }

    console.log('Successfully verified brands relationship');
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

fixBrandsRelationship();