/**
 * <PERSON><PERSON><PERSON> to fix ESLint and Prettier formatting issues
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

const ROOT_DIR = path.resolve(__dirname, '..');

// Files with known ESLint issues
const FILES_TO_FIX = [
  'src/admin/pages/FeedManagement.js',
  'src/checkEnv.js',
  'src/components/admin/ProductForm.js',
  'src/components/filters/FilterPanel.js',
  'src/context/AuthContext.js',
  'src/index.js',
  'src/pages/CategoryPage.js',
  'src/pages/admin/CategoryCreatePage.js',
  'src/routes/AdminRoutes.js',
  'src/supabaseClient.js',
  'src/utils/xmlParser.js',
];

// Fix unused variables
function fixUnusedVariables() {
  console.log('\n🔍 Fixing unused variable declarations...');
  
  // Files with unused variables
  const filesToCheck = [
    'src/components/admin/ProductForm.js',
    'src/components/filters/FilterPanel.js',
    'src/utils/xmlParser.js'
  ];
  
  filesToCheck.forEach(filePath => {
    const fullPath = path.join(ROOT_DIR, filePath);
    if (!fs.existsSync(fullPath)) {
      console.warn(`⚠️ File does not exist: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Add underscore prefix to unused convertBigIntToUUID variable
    content = content.replace(
      /import\s+{([^}]*)convertBigIntToUUID([^}]*)}(\s+from\s+['"].*['"])/g,
      'import {$1_convertBigIntToUUID$2}$3'
    );
    
    // Update any remaining references to convertBigIntToUUID
    content = content.replace(/convertBigIntToUUID/g, '_convertBigIntToUUID');
    
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ Fixed unused variable in ${filePath}`);
  });
}

// Fix "data used before it was defined" in CategoryCreatePage.js
function fixUseBeforeDefine() {
  console.log('\n🔍 Fixing use-before-define issue...');
  
  const filePath = path.join(ROOT_DIR, 'src/pages/admin/CategoryCreatePage.js');
  if (!fs.existsSync(filePath)) {
    console.warn('⚠️ File does not exist: src/pages/admin/CategoryCreatePage.js');
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Look for the line with the issue and restructure the code
  // This is a basic fix - the actual implementation might need more context
  if (content.includes('data was used before it was defined')) {
    // Hoist the data variable declaration
    content = content.replace(
      /const\s+\{\s*data\s*\}\s*=/g,
      'let data;\n  const { data: fetchedData } ='
    );
    
    // Update references to use fetchedData
    content = content.replace(/data\./g, 'fetchedData.');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log('✅ Fixed use-before-define issue in CategoryCreatePage.js');
  }
}

// Run Prettier on files
function runPrettier() {
  console.log('\n🔍 Running Prettier to fix formatting issues...');
  
  const filePaths = FILES_TO_FIX.map(file => path.join(ROOT_DIR, file));
  const validFiles = filePaths.filter(file => fs.existsSync(file));
  
  if (validFiles.length === 0) {
    console.warn('⚠️ No files found to format');
    return;
  }
  
  try {
    const fileList = validFiles.join(' ');
    const cmd = `npx prettier --write ${fileList}`;
    console.log(`Running: ${cmd}`);
    execSync(cmd, { stdio: 'inherit' });
    console.log('✅ Prettier formatting completed');
  } catch (error) {
    console.error('❌ Error running Prettier:', error.message);
  }
}

// Main execution
console.log('🛠️ Starting ESLint error fix script...');

// Fix the issues in order
fixUnusedVariables();
fixUseBeforeDefine();
runPrettier();

console.log('\n✨ All fixes completed. Run ESLint again to verify fixes.');