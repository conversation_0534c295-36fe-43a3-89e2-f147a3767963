const { executeSqlBatch } = require('../src/utils/database');
const { supabase } = require('./supabaseClient');
const fs = require('fs').promises;
const path = require('path');

// Utility function to execute SQL statements
async function executeSqlFile(filePath) {
  try {
    console.log(`Executing SQL file: ${path.basename(filePath)}`);
    const sqlContent = await fs.readFile(filePath, 'utf8');

    // Split content into individual statements but preserve PL/pgSQL blocks
    // This handles both semicolon-separated statements and dollar-quoted blocks
    const statements = splitSqlStatements(sqlContent);
    
    if (statements.length <= 1) {
      // For single statements or files with complex PL/pgSQL, use the original approach
      const { error } = await supabase.rpc('exec_sql', { query: sqlContent });
      if (error && !error.message.includes('already exists')) {
        console.warn(`Warning in ${path.basename(filePath)}: ${error.message}`);
      }
    } else {
      // For multiple statements, use batch execution
      console.log(`File contains ${statements.length} SQL statements, using batch execution`);
      const result = await executeSqlBatch(statements);
      if (!result) {
        console.warn(`Warning: Some statements in ${path.basename(filePath)} may have failed`);
      }
    }

    console.log(`✅ Completed file: ${path.basename(filePath)}`);
    return true;
  } catch (error) {
    console.error(`Error executing SQL file ${path.basename(filePath)}:`, error);
    return false;
  }
}

// Helper function to split SQL content into statements while preserving PL/pgSQL blocks
function splitSqlStatements(sqlContent) {
  const statements = [];
  let currentStatement = '';
  let inDollarQuote = false;
  let dollarTag = '';
  
  // Split the content into lines to process
  const lines = sqlContent.split('\n');
  
  for (const line of lines) {
    // Skip empty lines and comments
    if (line.trim() === '' || line.trim().startsWith('--')) {
      currentStatement += line + '\n';
      continue;
    }
    
    // Check for dollar quoted blocks (like $$ or $BODY$)
    if (!inDollarQuote) {
      // Look for beginning of dollar quote
      const dollarQuoteMatch = line.match(/\$([a-zA-Z0-9_]*)\$/);
      if (dollarQuoteMatch) {
        inDollarQuote = true;
        dollarTag = dollarQuoteMatch[0];
      }
    } else {
      // Look for matching end of dollar quote
      if (line.includes(dollarTag)) {
        inDollarQuote = false;
        dollarTag = '';
      }
    }
    
    currentStatement += line + '\n';
    
    // If we're not in a dollar quote block and the line contains a semicolon
    if (!inDollarQuote && line.includes(';')) {
      statements.push(currentStatement.trim());
      currentStatement = '';
    }
  }
  
  // Add the last statement if there's anything left
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }
  
  return statements;
}

async function initializeDatabase() {
  try {
    // Read and execute init SQL
    const sqlPath = path.join(__dirname, 'migrations', 'init.sql');
    return await executeSqlFile(sqlPath);
  } catch (error) {
    console.error('Database initialization error:', error);
    return false;
  }
}

async function applyMigrations() {
  try {
    console.log('Starting database migration...');

    // Create RPC function exec_sql before any SQL execution
    const execFuncPath = path.join(__dirname, 'migrations', 'create_exec_function.sql');
    console.log('Creating RPC function exec_sql...');
    await executeSqlFile(execFuncPath);

    // Initialize database structure
    console.log('Creating basic database structure...');
    const initialized = await initializeDatabase();
    if (!initialized) {
      console.warn('Warning: Initial database structure might not be complete');
    }

    // Get all migration files except init.sql, sorted alphabetically
    const migrationsDir = path.join(__dirname, 'migrations');
    const files = await fs.readdir(migrationsDir);
    const migrationFiles = files
      .filter(file => file.endsWith('.sql') && file !== 'init.sql' && file !== 'create_exec_function.sql')
      .sort();

    console.log(`Found ${migrationFiles.length} migration files to apply`);
    
    // Apply each migration file
    for (const file of migrationFiles) {
      const filePath = path.join(migrationsDir, file);
      const success = await executeSqlFile(filePath);
      
      if (!success) {
        console.warn(`Warning: Migration ${file} might not have been applied completely`);
        // Continue with other migrations
      }
    }

    // Create sample records
    console.log('Setting up initial data...');
    const { error } = await supabase.from('categories').upsert([
      {
        id: '00000000-0000-0000-0000-000000000000',
        name: 'Root Category'
      }
    ]);

    if (error && !error.message.includes('duplicate key')) {
      console.warn('Warning: Could not create root category:', error.message);
    }

    console.log('✅ Database migration completed successfully!');
  } catch (error) {
    console.error('Error applying migrations:', error);
    process.exit(1);
  }
}

applyMigrations();
