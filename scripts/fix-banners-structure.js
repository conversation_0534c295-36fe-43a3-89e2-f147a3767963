const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../.env.local' });

// Use environment variables or default test credentials
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixBannersStructure() {
  console.log('Исправление структуры таблицы banners...');
  
  try {
    // Сначала получим список колонок для проверки
    console.log('Проверяем текущую структуру таблицы...');
    const { error: inspectError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'banners'
        ORDER BY ordinal_position;
      `
    });
    
    if (inspectError) {
      console.error('Ошибка при проверке структуры таблицы:', inspectError);
      return;
    }

    // Добавляем колонку image, если она отсутствует
    console.log('Добавляем колонку image в таблицу banners...');
    const { error: alterError } = await supabase.rpc('exec_sql', {
      query: `
        ALTER TABLE banners
        ADD COLUMN IF NOT EXISTS image TEXT;
      `
    });
    
    if (alterError) {
      console.error('Ошибка при добавлении колонки:', alterError);
      return;
    }
    
    // Проверяем, что колонка image успешно создана
    console.log('Проверяем, что колонка image создана...');
    const { data, error: checkError } = await supabase.rpc('exec_sql', {
      query: `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'banners' AND column_name = 'image';
      `
    });
    
    if (checkError) {
      console.error('Ошибка при проверке наличия колонки:', checkError);
      return;
    }
    
    console.log('Структура таблицы успешно обновлена!');
    
    // Обновляем баннеры с дефолтными изображениями
    console.log('Обновляем существующие баннеры...');
    
    // Получаем список баннеров
    const { data: banners, error: bannersError } = await supabase
      .from('banners')
      .select('*');
    
    if (bannersError) {
      console.error('Ошибка при получении баннеров:', bannersError);
      return;
    }
    
    console.log(`Найдено ${banners.length} баннеров для обновления.`);
    
    for (const banner of banners) {
      const defaultImage = banner.position <= 5 
        ? 'https://images.unsplash.com/photo-1555529771-7888783a18d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80'
        : 'https://images.unsplash.com/photo-1607082349566-187342175e2f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1950&q=80';
      
      console.log(`Обновляем баннер ID ${banner.id} с позицией ${banner.position}...`);
      
      const { error: updateError } = await supabase
        .from('banners')
        .update({ image: defaultImage })
        .eq('id', banner.id);
      
      if (updateError) {
        console.error(`Ошибка при обновлении баннера ${banner.id}:`, updateError);
      } else {
        console.log(`Баннер ${banner.id} успешно обновлен.`);
      }
    }
    
    console.log('Обновление баннеров завершено!');
    
  } catch (error) {
    console.error('Непредвиденная ошибка:', error);
  }
}

fixBannersStructure();
