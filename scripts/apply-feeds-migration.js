#!/usr/bin/env node
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyFeedsMigration() {
  try {
    console.log('Starting feed system migration...');
    
    // First create the exec_sql function
    console.log('Creating exec_sql function...');
    const execFunctionPath = path.join(__dirname, 'migrations', 'create_exec_function.sql');
    const execFunctionSQL = fs.readFileSync(execFunctionPath, 'utf8');
    
    // Execute the SQL directly since we can't use the exec_sql function yet
    const { error: execError } = await supabase.rpc('exec_sql', { sql: execFunctionSQL }).catch(async () => {
      // If the function doesn't exist, create it using raw SQL query
      console.log('Function not found, creating using direct SQL...');
      
      // Use direct SQL execution to create the function
      // For Supabase, we'll use the REST API to execute SQL directly
      const { error } = await supabase.from('_sql').select('*').eq('query', execFunctionSQL);
      
      if (error) {
        console.log('Trying alternative direct SQL method...');
        // Alternative approach: create tables directly
        // Read the SQL migration file
        const migrationPath = path.join(__dirname, 'migrations', 'create_feeds_table.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // Split into individual statements and execute them directly
        const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
        
        for (const stmt of statements) {
          if (stmt.trim()) {
            const { error } = await supabase.from('_sql').select('*').eq('query', stmt);
            if (error) {
              console.log(`Direct execution error: ${error.message}`);
            }
          }
        }
        
        return { error: null };
      }
      
      return { error: null };
    });
    
    if (execError) {
      console.log('Warning: Could not create exec_sql function, trying alternative approach');
    } else {
      console.log('exec_sql function created or already exists');
    
      // Now read and execute the feeds table migration
      console.log('Creating feeds table schema...');
      const migrationPath = path.join(__dirname, 'migrations', 'create_feeds_table.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      
      try {
        // Execute the SQL using Supabase's RPC
        const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
        
        if (error) {
          throw new Error(`Failed to execute migration: ${error.message}`);
        }
      } catch (error) {
        console.log('Error executing migration with exec_sql, falling back to direct method...');
        
        // Split into individual statements and execute them one by one
        const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
        
        for (const stmt of statements) {
          try {
            // Execute each statement separately
            if (stmt.trim().length > 0) {
              await supabase.from('_sql').select('*').eq('query', stmt);
            }
          } catch (stmtError) {
            console.log(`Warning: Could not execute statement: ${stmtError.message}`);
          }
        }
      }
    }
    
    console.log('Migration executed successfully!');
    console.log('Setting up initial feeds...');
    
    // Set up initial feeds
    const { error: feedsError } = await supabase.from('feeds').insert([
      {
        name: 'Russian Products',
        url: 'https://kuhteh.com.ua/price/ecommerce-ru.xml',
        language: 'ru',
        is_active: true
      },
      {
        name: 'Ukrainian Products',
        url: 'https://kuhteh.com.ua/price/ecommerce-ua.xml',
        language: 'uk',
        is_active: true
      }
    ]);
    
    if (feedsError) {
      // If error is because feeds already exist, just log it
      if (feedsError.code === '23505') {
        console.log('Initial feeds already exist, skipping...');
      } else {
        throw new Error(`Failed to create initial feeds: ${feedsError.message}`);
      }
    } else {
      console.log('Initial feeds created successfully!');
    }
    
    console.log('Feed system setup complete.');
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Execute the function
applyFeedsMigration();