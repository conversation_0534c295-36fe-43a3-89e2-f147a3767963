const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Чтение учетных данных Supabase из переменных окружения
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Ошибка: Ключ Supabase не найден в переменных окружения.');
  console.log('Убедитесь, что переменная окружения REACT_APP_SUPABASE_ANON_KEY установлена.');
  process.exit(1);
}

// Инициализация клиента Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateBanners() {
  try {
    console.log('Загрузка существующих баннеров...');
    const { data, error } = await supabase.from('banners').select('*');

    if (error) {
      console.error('Ошибка при загрузке баннеров:', error);
      return;
    }

    console.log(`Найдено баннеров: ${data.length}`);
    
    if (data.length === 0) {
      console.log('Таблица banners пуста. Нет баннеров для обновления.');
      return;
    }

    // Обновляем каждый баннер, где position отсутствует
    for (const banner of data) {
      if (banner.position === null) {
        console.log(`Обновление баннера ID ${banner.id} "${banner.title}"...`);
        
        // Устанавливаем position равной 1 для отображения в верхней части (top)
        const { data: updateData, error: updateError } = await supabase
          .from('banners')
          .update({
            position: 1  // Устанавливаем position = 1 для отображения на главной странице в верхней части
          })
          .eq('id', banner.id);
        
        if (updateError) {
          console.error(`Ошибка при обновлении баннера ${banner.id}:`, updateError);
        } else {
          console.log(`Баннер ${banner.id} успешно обновлен: position = 1`);
        }
      }
    }
    
    // Проверяем результаты обновления
    const { data: updatedData } = await supabase.from('banners').select('*');
    console.log('\nОбновленные данные баннеров:');
    updatedData.forEach(banner => {
      console.log(`ID: ${banner.id}, Заголовок: ${banner.title}, Позиция: ${banner.position}, Активен: ${banner.active || banner.is_active}`);
    });
  } catch (error) {
    console.error('Неожиданная ошибка:', error);
  }
}

updateBanners()
  .then(() => {
    console.log('\nОбновление баннеров завершено');
  })
  .catch(err => {
    console.error('Неперехваченная ошибка:', err);
    process.exit(1);
  });
