const { createClient } = require('@supabase/supabase-js');

// Setup Supabase client
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

async function createIndexes() {
  console.log('Creating indexes for product parameters...');

  try {
    // Create indexes directly with raw SQL insert
    const { error } = await supabase.from('_temp_migrations').insert({
      query: `
          -- Create indexes for product parameters
          CREATE INDEX IF NOT EXISTS idx_product_params_name 
          ON product_params(name);
          
          CREATE INDEX IF NOT EXISTS idx_product_params_value 
          ON product_params(value);
          
          CREATE INDEX IF NOT EXISTS idx_product_params_product_id 
          ON product_params(product_id);
        `
    });

    if (error) {
      console.error('Error creating indexes:', error.message);
    } else {
      console.log('✅ Indexes created successfully');
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

createIndexes();
