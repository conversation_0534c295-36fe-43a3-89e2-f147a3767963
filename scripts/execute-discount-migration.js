#!/usr/bin/env node
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;
const serviceRoleKey = process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, serviceRoleKey || supabaseKey);

async function applyDiscountMigration() {
  try {
    console.log('Starting discount column migration...');
    
    // Read the SQL migration file
    const sqlPath = path.join(__dirname, 'add-discount-column.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('Executing SQL migration...');
    
    // Try three different approaches to execute the SQL
    
    // Approach 1: Use the REST API
    try {
      console.log('Trying REST API approach...');
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        },
        body: JSON.stringify({
          query: sql
        })
      });
      
      if (response.ok) {
        console.log('Migration successful via REST API!');
        return;
      }
      
      console.log('REST API approach failed, trying next method...');
    } catch (err) {
      console.log('REST API error:', err.message);
    }
    
    // Approach 2: Try using stored procedure if available
    try {
      console.log('Trying stored procedure approach...');
      const { error } = await supabase.rpc('exec_sql', { sql });
      
      if (!error) {
        console.log('Migration successful via stored procedure!');
        return;
      }
      
      console.log('Stored procedure approach failed:', error.message);
    } catch (err) {
      console.log('Stored procedure error:', err.message);
    }
    
    // Approach 3: Use query builder as a fallback
    console.log('Trying plain query approach...');
    try {
      // Split into individual statements and execute them directly
      const statements = sql.split(';').filter(stmt => stmt.trim());
      
      for (const stmt of statements) {
        if (stmt.trim()) {
          await supabase.from('products').select('count(*)').then(() => {
            console.log('Verified products table exists');
          });
          
          // Try to directly add the column
          const alterStatement = "ALTER TABLE products ADD COLUMN IF NOT EXISTS discount INTEGER DEFAULT 0;";
          const { error } = await supabase.from('_vulnerable_statements')
            .insert({ query: alterStatement });
          
          if (error) {
            console.log(`Error executing statement: ${error.message}`);
          } else {
            console.log('Successfully executed statement');
          }
        }
      }
      
      // Verify if the column exists or attempt to update null values
      console.log('Setting default values for discount column...');
      const { error } = await supabase
        .from('products')
        .update({ discount: 0 })
        .is('discount', null);
      
      if (!error) {
        console.log('Successfully set default values for discount column');
      } else {
        console.log('Error setting default values:', error.message);
      }
    } catch (err) {
      console.log('Error in plain query approach:', err.message);
    }
    
    // Final approach: Try using Supabase SQL editor
    console.log('\n----------------------------------------');
    console.log('Attention: Automatic migration had issues.');
    console.log('Please execute the following SQL in the Supabase SQL editor:');
    console.log('----------------------------------------');
    console.log(sql);
    console.log('----------------------------------------');
    
  } catch (error) {
    console.error('Error applying migration:', error.message || error);
  }
}

// Execute the migration
applyDiscountMigration()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration script failed:', err);
    process.exit(1);
  });