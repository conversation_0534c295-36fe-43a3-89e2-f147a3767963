/**
 * <PERSON>ript to import product parameters from XML feed into product_params table
 * 
 * Usage: node import-product-params.js [feed_file_path]
 * If no feed file is specified, it will use the default feed.xml in the project root
 */

const fs = require('fs');
const path = require('path');
const { DOMParser } = require('@xmldom/xmldom');
const { supabase, convertUUIDToBigInt } = require('../src/supabaseClient');

// Helper function to extract text content from XML node
function getNodeTextContent(parentNode, tagName) {
  const node = parentNode.getElementsByTagName(tagName)[0];
  return node ? node.textContent : '';
}

// Extract parameters from XML offer node
function extractProductParams(offerNode) {
  const params = [];
  const paramNodes = offerNode.getElementsByTagName('param');
  
  if (paramNodes && paramNodes.length > 0) {
    for (let i = 0; i < paramNodes.length; i++) {
      const param = paramNodes[i];
      const name = param.getAttribute('name');
      const value = param.textContent;
      
      if (name && value) {
        params.push({ name, value });
      }
    }
  }
  
  return params;
}

// Main function to process the feed and import parameters
async function importProductParams(feedFilePath) {
  try {
    if (feedFilePath) {
      console.log(`Reading feed file from ${feedFilePath}`);
      
      // Read the XML file
      const xmlContent = fs.readFileSync(feedFilePath, 'utf8');
      
      // Parse XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
      
      // Check if it's a YML format (Yandex Market Language)
      const isYml = xmlDoc.getElementsByTagName('yml_catalog').length > 0;
      
      if (!isYml) {
        console.error('The feed is not in YML format. This script only supports YML format feeds.');
        return;
      }
      
      // Get all offers
      const offers = xmlDoc.getElementsByTagName('offer');
      console.log(`Found ${offers.length} products in the feed`);
      
      // Stats counters
      const stats = {
        processed: 0,
        paramsFound: 0,
        productsUpdated: 0,
        paramsImported: 0,
        failed: 0
      };
      
      // Process each offer
      for (let i = 0; i < offers.length; i++) {
        const offer = offers[i];
        stats.processed++;
        
        // Extract product identifiers
        const id = offer.getAttribute('id') || '';
        const vendorCode = getNodeTextContent(offer, 'vendorCode') || '';
        
        // Extract parameters
        const params = extractProductParams(offer);
        stats.paramsFound += params.length;
        
        if (params.length === 0) {
          continue; // Skip products with no parameters
        }
        
        // Find corresponding product in database
        const { data: products, error } = await supabase
          .from('products')
          .select('id, external_id')
          .or(`external_id.eq.${id},external_id.eq.${vendorCode}`);
        
        if (error) {
          console.error(`Error finding product with ID ${id} or vendor code ${vendorCode}:`, error.message);
          stats.failed++;
          continue;
        }
        
        if (!products || products.length === 0) {
          console.log(`Product not found for ID ${id} or vendor code ${vendorCode}`);
          continue;
        }
        
        const product = products[0];
        console.log(`Found product in database: ${product.id} (external_id: ${product.external_id})`);
        stats.productsUpdated++;
        
        // Delete existing parameters for this product
        const { error: deleteError } = await supabase
          .from('product_params')
          .delete()
          .eq('product_id', convertUUIDToBigInt(product.id));
        
        if (deleteError) {
          console.error(`Error deleting existing parameters for product ${product.id}:`, deleteError.message);
          stats.failed++;
          continue;
        }
        
        // Insert new parameters
        const paramsToInsert = params.map(param => ({
          product_id: convertUUIDToBigInt(product.id),
          name: param.name,
          value: param.value
        }));
        
        const { error: insertError } = await supabase
          .from('product_params')
          .insert(paramsToInsert);
        
        if (insertError) {
          console.error(`Error inserting parameters for product ${product.id}:`, insertError.message);
          stats.failed++;
        } else {
          stats.paramsImported += params.length;
          console.log(`Imported ${params.length} parameters for product ${product.id}`);
        }
        
        // Log progress every 10 products
        if (stats.processed % 10 === 0) {
          console.log(`Progress: ${stats.processed}/${offers.length} products processed`);
        }
      }
      
      // Print final stats
      console.log('\nImport completed:');
      console.log(`- Products processed: ${stats.processed}`);
      console.log(`- Parameters found: ${stats.paramsFound}`);
      console.log(`- Products updated: ${stats.productsUpdated}`);
      console.log(`- Parameters imported: ${stats.paramsImported}`);
      console.log(`- Failed operations: ${stats.failed}`);
    } else {
      // If no feed file is provided, import attributes from all products in the database
      await importAttributesFromAllProducts();
    }
  } catch (error) {
    console.error('Error importing product parameters:', error);
  }
}

// Function to import parameters from the attributes field of all products
async function importAttributesFromAllProducts() {
  console.log('Importing product parameters from the attributes field of all products...');
  
  // Get all products with attributes field
  const { data: products, error } = await supabase
    .from('products')
    .select('id, attributes')
    .not('attributes', 'is', null);
  
  if (error) {
    console.error('Error fetching products with attributes:', error);
    return;
  }
  
  console.log(`Found ${products.length} products with attributes.`);
  let processedCount = 0;
  let paramsImportedCount = 0;
  let failedCount = 0;
  
  // Process each product
  for (const product of products) {
    try {
      processedCount++;
      
      if (!product.attributes || Object.keys(product.attributes).length === 0) {
        continue;
      }
      
      // Convert attributes object to product_params format
      const params = [];
      for (const [name, value] of Object.entries(product.attributes)) {
        if (name && value) {
          params.push({
            product_id: convertUUIDToBigInt(product.id),
            name,
            value
          });
        }
      }
      
      if (params.length === 0) {
        continue;
      }
      
      // Delete existing parameters for this product
      const { error: deleteError } = await supabase
        .from('product_params')
        .delete()
        .eq('product_id', convertUUIDToBigInt(product.id));
      
      if (deleteError) {
        console.error(`Error deleting existing parameters for product ${product.id}:`, deleteError);
        failedCount++;
        continue;
      }
      
      // Insert new parameters
      const { error: insertError } = await supabase
        .from('product_params')
        .insert(params);
      
      if (insertError) {
        console.error(`Error inserting parameters for product ${product.id}:`, insertError);
        failedCount++;
      } else {
        paramsImportedCount += params.length;
        console.log(`Imported ${params.length} parameters for product ${product.id}`);
      }
      
      // Log progress every 10 products
      if (processedCount % 10 === 0) {
        console.log(`Progress: ${processedCount}/${products.length} products processed`);
      }
    } catch (err) {
      console.error(`Error processing product ${product.id}:`, err);
      failedCount++;
    }
  }
  
  // Print final stats
  console.log('\nImport completed:');
  console.log(`- Products processed: ${processedCount}`);
  console.log(`- Parameters imported: ${paramsImportedCount}`);
  console.log(`- Failed operations: ${failedCount}`);
}

// Get feed file path from command line arguments or use default
const feedFilePath = process.argv[2];

// Run the import - if no file path is provided, it will import from attributes field
importProductParams(feedFilePath);
