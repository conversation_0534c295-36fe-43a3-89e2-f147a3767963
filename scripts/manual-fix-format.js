const { execSync } = require('child_process');
const path = require('path');

// Files with formatting issues
const filesToFormat = [
  'src/components/filters/FilterPanel.js',
  'src/components/filters/MobileFilterDrawer.js',
  'src/utils/filterAnalytics.js' // Add this file
];

try {
  console.log('Attempting to format files directly with npx...');

  // Format specific files with known issues
  filesToFormat.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    console.log(`Formatting ${file}...`);
    try {
      execSync(`npx prettier --write "${fullPath}"`, { stdio: 'inherit' });
      console.log(`✓ Formatted ${file} successfully`);
    } catch (err) {
      console.error(`× Failed to format ${file}`);
    }
  });

  console.log('Manual formatting attempt complete.');
} catch (error) {
  console.error('Error:', error.message);
}
