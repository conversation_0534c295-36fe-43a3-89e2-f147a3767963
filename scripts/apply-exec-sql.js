import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyExecSqlFunction() {
  try {
    console.log('Applying exec_sql function to database...');
    
    // SQL for creating the exec_sql function
    const sqlFunction = `
      -- Create the SQL execution function
      CREATE OR REPLACE FUNCTION exec_sql(query text)
      RETURNS JSONB AS $$
      DECLARE
        result JSONB;
      BEGIN
        EXECUTE query INTO result;
        RETURN result;
      EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object('error', SQLERRM);
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Grant execute permission
      GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
    `;
    
    // Execute the SQL directly through REST API
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/postgres`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      },
      body: JSON.stringify({
        sql: sqlFunction
      })
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('Successfully applied exec_sql function');
    } else {
      console.error('Error applying exec_sql function:', result);
    }
    
    // Now let's try to run a test query using the Supabase REST API to confirm it works
    console.log('Testing exec_sql function...');
    
    const testResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      },
      body: JSON.stringify({
        query: `SELECT 'It works!' as message`
      })
    });
    
    const testResult = await testResponse.json();
    
    if (testResponse.ok) {
      console.log('Test successful:', testResult);
    } else {
      console.error('Test failed:', testResult);
    }

  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

applyExecSqlFunction();