/**
 * Скрипт для анализа структуры товаров в базе данных
 * Поможет определить, как именно связать товары с категориями
 */
const { supabase } = require('./supabaseClient');
const { DOMParser } = require('@xmldom/xmldom');

async function analyzeProducts() {
  try {
    console.log('Анализ структуры товаров...');
    
    // Получаем несколько первых товаров для анализа
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('Ошибка при получении товаров:', error.message);
      return;
    }
    
    if (!products || products.length === 0) {
      console.log('Товары не найдены');
      return;
    }
    
    console.log(`Найдено ${products.length} товаров для анализа`);
    
    // Анализируем структуру первого товара
    const firstProduct = products[0];
    console.log('Доступные поля товара:');
    console.log(Object.keys(firstProduct));
    
    // Выводим значения некоторых ключевых полей
    console.log('\nЗначения ключевых полей:');
    console.log('ID:', firstProduct.id);
    console.log('external_id:', firstProduct.external_id);
    console.log('name:', firstProduct.name);
    console.log('category_id:', firstProduct.category_id);
    
    // Проверяем, есть ли информация о категории в attributes
    if (firstProduct.attributes) {
      console.log('\nСодержимое attributes:');
      console.log(JSON.stringify(firstProduct.attributes, null, 2));
    }
    
    // Если категория не в основных полях и не в attributes, проверим все поля
    console.log('\nВсе поля товара:');
    for (const [key, value] of Object.entries(firstProduct)) {
      // Не выводим слишком большие объекты целиком
      if (typeof value === 'object' && value !== null) {
        console.log(`${key}: [объект/массив]`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }
    
    // Проверим все товары на наличие информации о категории
    console.log('\nАнализ всех товаров на наличие информации о категориях:');
    let hasCategory = 0;
    let noCategory = 0;
    
    // Составим статистику по первым 100 товарам
    const { data: allProducts, error: allError } = await supabase
      .from('products')
      .select('id, external_id, category_id, attributes')
      .limit(100);
    
    if (allError) {
      console.error('Ошибка при получении всех товаров:', allError.message);
      return;
    }
    
    // Создаем карту уникальных значений category_id
    const categoryIds = new Set();
    const externalToCategoryMap = {};
    
    for (const product of allProducts) {
      if (product.category_id) {
        hasCategory++;
        categoryIds.add(product.category_id);
        
        // Сопоставляем external_id с category_id для последующего анализа
        if (product.external_id) {
          externalToCategoryMap[product.external_id] = product.category_id;
        }
      } else {
        noCategory++;
      }
      
      // Проверяем, есть ли информация о категории в attributes
      if (product.attributes && typeof product.attributes === 'object') {
        for (const [key, value] of Object.entries(product.attributes)) {
          if (key.toLowerCase().includes('categ')) {
            console.log(`Найдено поле в attributes, связанное с категорией: ${key}: ${value}`);
          }
        }
      }
    }
    
    console.log(`\nИтоги анализа ${allProducts.length} товаров:`);
    console.log(`Товаров с заполненным category_id: ${hasCategory}`);
    console.log(`Товаров без category_id: ${noCategory}`);
    console.log(`Уникальных значений category_id: ${categoryIds.size}`);
    
    if (categoryIds.size > 0) {
      console.log('\nПримеры значений category_id:');
      const examples = Array.from(categoryIds).slice(0, 10);
      examples.forEach(id => console.log(id));
    }
    
    // Проверим наличие данных о категориях в YML-файле
    console.log('\nПроверяем данные о категориях в YML-файле...');
    const fs = require('fs');
    
    try {
      if (fs.existsSync('./feed.yml')) {
        const xmlData = fs.readFileSync('./feed.yml', 'utf8');
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlData, 'text/xml');
        
        // Извлекаем предложения (товары) из YML-фида
        const offerNodes = xmlDoc.getElementsByTagName('offer');
        console.log(`Найдено ${offerNodes.length} товаров в YML-файле`);
        
        // Анализируем первые 5 товаров из фида
        console.log('\nПример данных о категориях в товарах из YML-файле:');
        for (let i = 0; i < Math.min(5, offerNodes.length); i++) {
          const offer = offerNodes[i];
          const id = offer.getAttribute('id');
          
          // Получаем categoryId из товара в YML
          const categoryIdNodes = offer.getElementsByTagName('categoryId');
          const categoryId = categoryIdNodes.length > 0 ? categoryIdNodes[0].textContent : null;
          
          console.log(`Товар ID: ${id}, categoryId в YML: ${categoryId}`);
          
          // Если у нас есть информация о category_id в базе, сравним их
          if (externalToCategoryMap[id]) {
            console.log(`  - category_id в базе: ${externalToCategoryMap[id]}`);
          }
        }
      } else {
        console.log('Файл feed.yml не найден');
      }
    } catch (fileError) {
      console.error('Ошибка при чтении файла фида:', fileError.message);
    }
    
  } catch (error) {
    console.error('Ошибка анализа:', error);
  }
}

// Запускаем функцию
analyzeProducts()
  .then(() => {
    console.log('\nАнализ завершен');
    process.exit(0);
  })
  .catch(error => {
    console.error('Ошибка выполнения скрипта:', error);
    process.exit(1);
  });
