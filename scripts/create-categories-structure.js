const { createClient } = require('@supabase/supabase-js');

// Настройка Supabase клиента
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

// Структура категорий и их оригинальные ID
const categories = [
  // Основные категории (без parentId)
  {
    id: '59',
    name: '<PERSON>ух<PERSON>нн<PERSON> мийки',
    originalId: '59',
    parentId: null,
    level: 0,
    is_featured: true
  },
  { id: '63', name: 'Змішувачі', originalId: '63', parentId: null, level: 0, is_featured: true },
  {
    id: '96',
    name: '<PERSON>ух<PERSON><PERSON>на техніка',
    originalId: '96',
    parentId: null,
    level: 0,
    is_featured: true
  },
  { id: '71', name: 'Аксесуари', originalId: '71', parentId: null, level: 0, is_featured: true },
  {
    id: '80',
    name: 'Кухонний посуд',
    originalId: '80',
    parentId: null,
    level: 0,
    is_featured: true
  },
  {
    id: '97',
    name: 'Меблева фурнітура',
    originalId: '97',
    parentId: null,
    level: 0,
    is_featured: true
  },
  {
    id: '103',
    name: 'Меблеві ручки',
    originalId: '103',
    parentId: null,
    level: 0,
    is_featured: false
  },
  {
    id: '95',
    name: 'Меблеві розетки',
    originalId: '95',
    parentId: null,
    level: 0,
    is_featured: false
  },
  {
    id: '98',
    name: 'Меблеве світло',
    originalId: '98',
    parentId: null,
    level: 0,
    is_featured: false
  },
  {
    id: '102',
    name: 'Меблеві опори і ніжки',
    originalId: '102',
    parentId: null,
    level: 0,
    is_featured: false
  },
  {
    id: '93',
    name: 'Нестандартні вироби',
    originalId: '93',
    parentId: null,
    level: 0,
    is_featured: false
  },
  {
    id: '78',
    name: 'Кухни из нержавеющей стали',
    originalId: '78',
    parentId: null,
    level: 0,
    is_featured: false
  },

  // Подкатегории для "Кухонна техніка" (id="96")
  {
    id: '60',
    name: 'Варильні поверхні',
    originalId: '60',
    parentId: '96',
    level: 1,
    is_featured: true
  },
  {
    id: '61',
    name: 'Газові варильні поверхні',
    originalId: '61',
    parentId: '60',
    level: 2,
    is_featured: false
  },
  {
    id: '62',
    name: 'Електричні варильні поверхні',
    originalId: '62',
    parentId: '60',
    level: 2,
    is_featured: false
  },
  {
    id: '92',
    name: 'Індукційні варильні поверхні',
    originalId: '92',
    parentId: '60',
    level: 2,
    is_featured: false
  },
  { id: '64', name: 'Духові шафи', originalId: '64', parentId: '96', level: 1, is_featured: true },
  { id: '65', name: 'Витяжки', originalId: '65', parentId: '96', level: 1, is_featured: true },
  {
    id: '66',
    name: 'Мікрохвильові печі',
    originalId: '66',
    parentId: '96',
    level: 1,
    is_featured: false
  },
  { id: '67', name: 'Кавомашини', originalId: '67', parentId: '96', level: 1, is_featured: false },
  {
    id: '68',
    name: 'Посудомийні машини',
    originalId: '68',
    parentId: '96',
    level: 1,
    is_featured: true
  },
  {
    id: '69',
    name: 'Холодильники',
    originalId: '69',
    parentId: '96',
    level: 1,
    is_featured: false
  },
  {
    id: '70',
    name: 'Кухонні прилади',
    originalId: '70',
    parentId: '96',
    level: 1,
    is_featured: false
  },

  // Подкатегории для "Аксесуари" (id="71")
  {
    id: '73',
    name: 'Аксесуари для техніки',
    originalId: '73',
    parentId: '71',
    level: 1,
    is_featured: false
  },
  {
    id: '72',
    name: 'Аксесуари для мийок',
    originalId: '72',
    parentId: '71',
    level: 1,
    is_featured: false
  },
  {
    id: '101',
    name: 'Аксесуари для кухонь',
    originalId: '101',
    parentId: '71',
    level: 1,
    is_featured: false
  },

  // Подкатегории для "Меблева фурнітура" (id="97")
  { id: '99', name: 'Механізми', originalId: '99', parentId: '97', level: 1, is_featured: false },
  { id: '100', name: 'Фурнітура', originalId: '100', parentId: '97', level: 1, is_featured: false }
];

// Добавление изображений для категорий
const categoryImages = {
  59: '/images/categories/sinks.jpg',
  63: '/images/categories/faucets.jpg',
  96: '/images/categories/appliances.jpg',
  71: '/images/categories/accessories.jpg',
  80: '/images/categories/kitchenware.jpg',
  97: '/images/categories/furniture.jpg',
  60: '/images/categories/cooking.jpg',
  64: '/images/categories/ovens.jpg',
  65: '/images/categories/hood.jpg',
  68: '/images/categories/dishwasher.jpg',
  69: '/images/categories/refrigerator.jpg'
};

async function createCategoriesStructure() {
  try {
    // Создание таблицы для маппинга если она не существует
    console.log('Создание таблицы для хранения маппинга...');

    try {
      await supabase.rpc('exec_sql', {
        query: `
          CREATE TABLE IF NOT EXISTS category_id_mapping (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            mapping JSONB NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
        `
      });
    } catch (error) {
      if (error.message.includes('function exec_sql') || error.code === '42883') {
        console.log('Создаем функцию exec_sql первым...');
        await supabase.from('_temp_migrations').insert([
          {
            query: `
              CREATE OR REPLACE FUNCTION exec_sql(query text)
              RETURNS void AS $$
              BEGIN
                EXECUTE query;
              END;
              $$ LANGUAGE plpgsql SECURITY DEFINER;
              
              GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
            `
          }
        ]);

        // Пробуем снова создать таблицу
        await supabase.rpc('exec_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS category_id_mapping (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              mapping JSONB NOT NULL,
              created_at TIMESTAMPTZ DEFAULT NOW()
            );
          `
        });
      } else {
        console.warn('Ошибка при создании таблицы маппинга:', error);
      }
    }

    console.log('Обработка внешних ключей...');

    // Сначала обновляем все товары, устанавливая category_id в NULL
    console.log('Обновление товаров для удаления связей с категориями...');
    const { error: updateError } = await supabase
      .from('products')
      .update({ category_id: null })
      .not('id', 'is', null);

    if (updateError) {
      console.error('Ошибка при обновлении товаров:', updateError);
      return;
    }

    console.log('Очистка существующих категорий...');
    // Теперь можно удалить категории
    const { error: deleteError } = await supabase.from('categories').delete().not('id', 'is', null);

    if (deleteError) {
      console.error('Ошибка при удалении категорий:', deleteError);
      return;
    }

    console.log('Создание структуры категорий...');

    // Карта для хранения новых UUID
    const idMap = {};

    // Сначала создаем все категории с временными parent_id
    for (const category of categories) {
      const newCategory = {
        name: category.name,
        display_order: parseInt(category.id),
        is_featured: category.is_featured
      };

      // Добавляем изображение, если есть
      if (categoryImages[category.originalId]) {
        newCategory.image = categoryImages[category.originalId];
      }

      const { data, error } = await supabase.from('categories').insert(newCategory).select('id');

      if (error) {
        console.error(`Ошибка при создании категории ${category.name}:`, error);
        continue;
      }

      // Сохраняем соответствие оригинального ID и нового UUID
      idMap[category.originalId] = data[0].id;
      console.log(`Категория "${category.name}" создана с ID: ${data[0].id}`);
    }

    console.log('Обновление parent_id для подкатегорий...');

    // Теперь обновляем parent_id для всех подкатегорий
    for (const category of categories) {
      if (category.parentId) {
        const childId = idMap[category.originalId];
        const parentId = idMap[category.parentId];

        if (childId && parentId) {
          const { error } = await supabase
            .from('categories')
            .update({ parent_id: parentId })
            .eq('id', childId);

          if (error) {
            console.error(`Ошибка при обновлении parent_id для ${category.name}:`, error);
          } else {
            console.log(`Обновлен parent_id для "${category.name}"`);
          }
        }
      }
    }

    // Создание словаря для сопоставления оригинальных ID с новыми ID
    console.log('Создание карты соответствия новых ID категорий...');
    const oldToNewIdMap = {};
    for (const [origId, newId] of Object.entries(idMap)) {
      oldToNewIdMap[origId] = newId;
    }

    // Сохраняем карту соответствий для будущего использования
    const { error: insertError } = await supabase
      .from('category_id_mapping')
      .insert({ mapping: oldToNewIdMap });

    if (insertError) {
      console.error('Ошибка при сохранении карты соответствия:', insertError);
    } else {
      console.log('Карта соответствия успешно сохранена');
    }

    console.log('✅ Структура категорий успешно создана!');
  } catch (error) {
    console.error('Произошла ошибка:', error);
  }
}

createCategoriesStructure();
