#!/usr/bin/env node

/**
 * Reset Stuck Feed Jobs
 * 
 * This script resets any feed jobs that are stuck in "processing" or "pending" status
 * after a certain time threshold. This allows new jobs to be started.
 * 
 * Usage:
 *   node reset-feed-jobs.js [--feed-id=UUID] [--force] [--dry-run]
 * 
 * Options:
 *   --feed-id=UUID  Reset jobs only for the specified feed
 *   --force         Reset all jobs regardless of age
 *   --dry-run       Show what would be reset without making changes
 */

const path = require('path');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local file if present
dotenv.config({ path: path.resolve(__dirname, '..', '.env.local') });

// Parse command-line arguments
const args = process.argv.slice(2);
const options = {
  feedId: null,
  force: false,
  dryRun: false
};

args.forEach(arg => {
  if (arg.startsWith('--feed-id=')) {
    options.feedId = arg.split('=')[1];
  } else if (arg === '--force') {
    options.force = true;
  } else if (arg === '--dry-run') {
    options.dryRun = true;
  } else if (!isNaN(parseInt(arg))) {
    // Support for passing feed ID directly as a number
    options.feedId = parseInt(arg);
  }
});

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

// Main function
const resetStuckJobs = async () => {
  try {
    console.log('Starting feed job reset script...');
    
    // Calculate cutoff time (default: 1 hour ago)
    const cutoffTime = options.force 
      ? new Date() 
      : new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
    
    console.log(`Looking for jobs stuck since: ${cutoffTime.toISOString()}`);
    
    // Build the query to find stuck jobs
    let query = supabase
      .from('feed_jobs')
      .select('id, feed_id, status, started_at, created_at')
      .in('status', ['pending', 'processing'])
      .lt('started_at', cutoffTime.toISOString());
    
    // If feed ID specified, only process that feed
    if (options.feedId) {
      console.log(`Targeting specific feed: ${options.feedId}`);
      query = query.eq('feed_id', options.feedId);
    }
    
    // Execute the query
    const { data: stuckJobs, error } = await query;
    
    if (error) {
      throw error;
    }
    
    // Show found stuck jobs
    console.log(`Found ${stuckJobs.length} stuck jobs`);
    
    if (stuckJobs.length === 0) {
      console.log('No stuck jobs found. Exiting...');
      return;
    }
    
    // Print details
    stuckJobs.forEach(job => {
      const stuckTime = Math.round((new Date() - new Date(job.started_at)) / (1000 * 60));
      console.log(`- Job #${job.id} (Feed: ${job.feed_id}) - Status: ${job.status} - Stuck for ${stuckTime} minutes`);
    });
    
    // If dry run, exit here
    if (options.dryRun) {
      console.log('DRY RUN - would reset the jobs above. Run without --dry-run to actually reset them.');
      return;
    }
    
    // Reset the jobs
    console.log('\nResetting stuck jobs...');
    const jobIds = stuckJobs.map(job => job.id);
    
    const { error: updateError } = await supabase
      .from('feed_jobs')
      .update({
        status: 'failed',
        // Use a simpler update that doesn't rely on specific column names
        finished_at: new Date().toISOString()
      })
      .in('id', jobIds);
    
    if (updateError) {
      throw updateError;
    }
    
    console.log(`Successfully reset ${jobIds.length} jobs to 'failed' status.`);
    console.log('You can now run the feed processor again.');
    
  } catch (error) {
    console.error('Error in job reset script:', error);
    process.exit(1);
  }
};

// Run the main function
resetStuckJobs().catch(err => {
  console.error('Error in job reset script:', err);
  process.exit(1);
});