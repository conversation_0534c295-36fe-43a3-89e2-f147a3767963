const { createClient } = require('@supabase/supabase-js');

// Настройка Supabase клиента
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

async function createFilterFunctions() {
  try {
    console.log('Creating filter functions...');

    // Create get_category_params function
    console.log('Creating get_category_params function...');
    const { error: error1 } = await supabase.from('_temp_migrations').insert([
      {
        query: `
          CREATE OR REPLACE FUNCTION get_category_params(category_id UUID)
          RETURNS TABLE (
            name TEXT
          ) AS $$
          BEGIN
            RETURN QUERY
            SELECT DISTINCT pp.name
            FROM product_params pp
            JOIN products p ON uuid_to_bigint(p.id) = pp.product_id
            WHERE p.category_id = $1
            ORDER BY pp.name;
          END;
          $$ LANGUAGE plpgsql;
        `
      }
    ]);

    if (error1) {
      console.error('Error creating get_category_params function:', error1.message);
    } else {
      console.log('✓ get_category_params function created');
    }

    // Create get_param_values function
    console.log('Creating get_param_values function...');
    const { error: error2 } = await supabase.from('_temp_migrations').insert([
      {
        query: `
          CREATE OR REPLACE FUNCTION get_param_values(category_id UUID, param_name TEXT)
          RETURNS TABLE (
            value TEXT
          ) AS $$
          BEGIN
            RETURN QUERY
            SELECT DISTINCT pp.value
            FROM product_params pp
            JOIN products p ON uuid_to_bigint(p.id) = pp.product_id
            WHERE p.category_id = $1 AND pp.name = $2 AND pp.value IS NOT NULL
            ORDER BY pp.value;
          END;
          $$ LANGUAGE plpgsql;
        `
      }
    ]);

    if (error2) {
      console.error('Error creating get_param_values function:', error2.message);
    } else {
      console.log('✓ get_param_values function created');
    }

    // Create indexes for product_params table
    console.log('Creating indexes...');
    const { error: error3 } = await supabase.from('_temp_migrations').insert([
      {
        query: `
          CREATE INDEX IF NOT EXISTS idx_product_params_name ON product_params(name);
          CREATE INDEX IF NOT EXISTS idx_product_params_value ON product_params(value);
        `
      }
    ]);

    if (error3) {
      console.error('Error creating indexes:', error3.message);
    } else {
      console.log('✓ indexes created');
    }

    console.log('✅ Filter functions setup completed');
  } catch (error) {
    console.error('Error creating filter functions:', error);
  }
}

createFilterFunctions();
