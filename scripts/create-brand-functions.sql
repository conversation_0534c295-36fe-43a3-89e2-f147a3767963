-- Function to create brands table
CREATE OR REPLACE FUNCTION create_brands_table()
RETURNS void AS $$
BEGIN
  -- Enable UUID extension if not enabled
  CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

  -- Create brands table if it doesn't exist
  CREATE TABLE IF NOT EXISTS brands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    logo TEXT,
    description TEXT,
    website_url TEXT,
    display_order INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
  );

  -- Create indexes
  CREATE INDEX IF NOT EXISTS idx_brands_name ON brands(name);
  CREATE INDEX IF NOT EXISTS idx_brands_featured ON brands(is_featured);

  -- Insert demo data if table is empty
  INSERT INTO brands (name, logo, description, website_url, is_featured)
  SELECT 
    name, logo, 'Brand description', '#', is_featured
  FROM (
    SELECT 
      unnest(ARRAY['Samsung', 'Apple', 'LG', 'Philips', 'Sony']) AS name,
      unnest(ARRAY[
        'https://via.placeholder.com/200x100?text=Samsung',
        'https://via.placeholder.com/200x100?text=Apple',
        'https://via.placeholder.com/200x100?text=LG',
        'https://via.placeholder.com/200x100?text=Philips',
        'https://via.placeholder.com/200x100?text=Sony'
      ]) AS logo,
      unnest(ARRAY[true, true, true, true, true]) AS is_featured
  ) t
  WHERE NOT EXISTS (SELECT 1 FROM brands LIMIT 1);
END;
$$ LANGUAGE plpgsql;

-- Function to create the add_brand_id_column procedure
CREATE OR REPLACE FUNCTION create_add_brand_id_procedure()
RETURNS void AS $$
BEGIN
  -- Create or replace the function
  CREATE OR REPLACE FUNCTION add_brand_id_column()
  RETURNS void AS $proc$
  BEGIN
    -- First check if 'brand' column exists and rename it if needed
    IF EXISTS (
      SELECT 1 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      AND column_name = 'brand'
    ) THEN
      ALTER TABLE products RENAME COLUMN brand TO brand_id;
    END IF;

    -- Add brand_id column if it doesn't exist
    IF NOT EXISTS (
      SELECT 1 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      AND column_name = 'brand_id'
    ) THEN
      ALTER TABLE products ADD COLUMN brand_id UUID REFERENCES brands(id);
      CREATE INDEX IF NOT EXISTS idx_products_brand_id ON products(brand_id);
    END IF;

    -- Grant permissions
    GRANT SELECT ON brands TO authenticated, anon;
    GRANT SELECT, UPDATE ON products TO authenticated;
  END;
  $proc$ LANGUAGE plpgsql;

  -- Grant execute permissions
  GRANT EXECUTE ON FUNCTION add_brand_id_column() TO authenticated, anon;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions on the creator functions
GRANT EXECUTE ON FUNCTION create_brands_table() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION create_add_brand_id_procedure() TO authenticated, anon;