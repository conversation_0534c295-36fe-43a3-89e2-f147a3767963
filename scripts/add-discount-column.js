const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY; 
// Use service role key if available for admin operations
const serviceRoleKey = process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, serviceRoleKey || supabaseKey);

async function addDiscountColumn() {
  console.log('Starting to add discount column to products table...');

  try {
    // First check if the column already exists to avoid errors
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'products' });
    
    if (columnsError) {
      console.log('Error checking columns, attempting direct approach:', columnsError.message);
    } else {
      const columnExists = columns.some(col => col.column_name === 'discount');
      if (columnExists) {
        console.log('Discount column already exists. Skipping creation.');
        return;
      }
    }

    // Add the discount column to the products table
    const { error } = await supabase
      .rpc('add_column_if_not_exists', {
        table_name: 'products',
        column_name: 'discount',
        column_type: 'integer',
        column_default: '0'
      });

    if (error) {
      console.log('Error with RPC method, trying direct SQL approach:', error.message);
      
      // Fallback to direct SQL if RPC fails
      const { error: directError } = await supabase
        .from('products')
        .update({ discount: 0 })
        .is('discount', null);
      
      if (directError) {
        console.error('Failed to update null discount values:', directError.message);
      } else {
        console.log('Successfully updated products with null discount values to 0');
      }
    } else {
      console.log('Successfully added discount column to products table');
    }
  } catch (err) {
    console.error('An unexpected error occurred:', err.message);
  }
}

// Execute the function
addDiscountColumn()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Script failed:', err);
    process.exit(1);
  });