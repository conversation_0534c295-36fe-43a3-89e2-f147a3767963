const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Настройка Supabase клиента
const supabaseUrl = 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';
const supabase = createClient(supabaseUrl, supabaseKey);

async function runFilterMigrations() {
  try {
    console.log('Выполнение миграций для фильтров и сортировки...');

    // Чтение SQL файла с миграциями
    const sqlPath = path.join(__dirname, 'migrations', 'create_filter_functions.sql');
    const sql = await fs.readFile(sqlPath, 'utf8');

    // Создаем функцию exec_sql, если она не существует
    try {
      await supabase.rpc('exec_sql', { query: 'SELECT 1' });
      console.log('Функция exec_sql уже существует');
    } catch (error) {
      console.log('Создаем функцию exec_sql...');
      await supabase.from('_temp_migrations').insert([
        {
          query: `
          CREATE OR REPLACE FUNCTION exec_sql(query text)
          RETURNS void AS $$
          BEGIN
            EXECUTE query;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
          
          GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
        `
        }
      ]);
    }

    // Выполняем каждую SQL функцию отдельно, правильно разделяя их
    // Вместо простого split по точке с запятой
    const firstFunction = `
    CREATE OR REPLACE FUNCTION get_category_params(category_id UUID)
    RETURNS TABLE (
      name TEXT
    ) AS $$
    BEGIN
      RETURN QUERY
      SELECT DISTINCT pp.name
      FROM product_params pp
      JOIN products p ON uuid_to_bigint(p.id) = pp.product_id
      WHERE p.category_id = $1
      ORDER BY pp.name;
    END;
    $$ LANGUAGE plpgsql;
    `;

    const secondFunction = `
    CREATE OR REPLACE FUNCTION get_param_values(category_id UUID, param_name TEXT)
    RETURNS TABLE (
      value TEXT
    ) AS $$
    BEGIN
      RETURN QUERY
      SELECT DISTINCT pp.value
      FROM product_params pp
      JOIN products p ON uuid_to_bigint(p.id) = pp.product_id
      WHERE p.category_id = $1 AND pp.name = $2 AND pp.value IS NOT NULL
      ORDER BY pp.value;
    END;
    $$ LANGUAGE plpgsql;
    `;

    // Выполняем первую функцию
    console.log('Выполнение первой функции...');
    const { error: error1 } = await supabase.rpc('exec_sql', { query: firstFunction });

    if (error1) {
      console.warn(`Ошибка при выполнении первой функции: ${error1.message}`);
      // Попробуем прямым запросом через строку SQL
      const { error: directError1 } = await supabase
        .from('_temp_migrations')
        .insert([{ query: firstFunction }]);

      if (directError1) {
        console.error(`Ошибка при прямом запросе для первой функции: ${directError1.message}`);
      } else {
        console.log('Первая функция успешно создана через прямой запрос');
      }
    } else {
      console.log('Первая функция успешно создана');
    }

    // Выполняем вторую функцию
    console.log('Выполнение второй функции...');
    const { error: error2 } = await supabase.rpc('exec_sql', { query: secondFunction });

    if (error2) {
      console.warn(`Ошибка при выполнении второй функции: ${error2.message}`);
      // Попробуем прямым запросом через строку SQL
      const { error: directError2 } = await supabase
        .from('_temp_migrations')
        .insert([{ query: secondFunction }]);

      if (directError2) {
        console.error(`Ошибка при прямом запросе для второй функции: ${directError2.message}`);
      } else {
        console.log('Вторая функция успешно создана через прямой запрос');
      }
    } else {
      console.log('Вторая функция успешно создана');
    }

    console.log('✅ Миграции для фильтров успешно выполнены!');
  } catch (error) {
    console.error('Ошибка при выполнении миграций:', error);
  }
}

runFilterMigrations();
