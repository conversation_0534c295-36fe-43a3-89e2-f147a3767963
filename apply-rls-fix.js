const { getAdminClient } = require('./src/supabaseClient');

async function applyRLSFix() {
  console.log('🔧 Applying final RLS fix for product_params table...');
  
  const adminClient = getAdminClient();
  
  if (!adminClient) {
    console.log('❌ Admin client not available');
    return;
  }

  const sqlCommands = [
    // Drop existing policies
    `DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "dev_product_params_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "dev_all_access" ON product_params;`,
    
    // Create new permissive policies
    `CREATE POLICY "product_params_read_all" ON product_params FOR SELECT USING (true);`,
    
    `CREATE POLICY "product_params_admin_all" ON product_params FOR ALL 
     USING (auth.role() = 'service_role' OR auth.uid() IS NOT NULL)
     WITH CHECK (auth.role() = 'service_role' OR auth.uid() IS NOT NULL);`,
    
    // Grant permissions
    `GRANT ALL ON product_params TO authenticated;`,
    `GRANT ALL ON product_params TO service_role;`,
    `GRANT SELECT ON product_params TO anon;`
  ];

  let successCount = 0;
  
  for (const sql of sqlCommands) {
    try {
      const { error } = await adminClient.rpc('exec_sql', { query: sql });
      if (error) {
        console.log('⚠️  SQL command failed:', sql.substring(0, 60) + '...', error.message);
      } else {
        console.log('✅ Success:', sql.substring(0, 60) + '...');
        successCount++;
      }
    } catch (err) {
      console.log('⚠️  Exception:', sql.substring(0, 60) + '...', err.message);
    }
  }
  
  console.log(`\n📊 Applied ${successCount}/${sqlCommands.length} SQL commands successfully`);
  
  // Test the fix
  console.log('\n🧪 Testing the fix...');
  
  const testParam = {
    product_id: 123456,
    name: 'test_param_rls_fix',
    value: 'test_value'
  };
  
  const { data: insertData, error: insertError } = await adminClient
    .from('product_params')
    .insert(testParam)
    .select();
  
  if (insertError) {
    console.log('❌ RLS fix FAILED:', insertError.message);
    console.log('   403 errors will continue to occur');
  } else {
    console.log('✅ RLS fix SUCCESSFUL!');
    console.log('   403 errors should now be resolved');
    
    // Clean up test data
    if (insertData && insertData[0]) {
      await adminClient
        .from('product_params')
        .delete()
        .eq('id', insertData[0].id);
      console.log('🧹 Test data cleaned up');
    }
  }
}

applyRLSFix().catch(console.error);
