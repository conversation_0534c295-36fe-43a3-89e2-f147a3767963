#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://fijuwtwzugtmbggdmxjb.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE';

async function getProductStats() {
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Получаем статистику по статусам модерации
    const { data, error } = await supabase
      .from('products')
      .select('moderation_status, is_active')
      .in('moderation_status', ['pending_approval', 'approved', 'rejected']);
    
    if (error) {
      console.error('Error:', error);
      return;
    }
    
    // Группируем по статусам
    const stats = data.reduce((acc, product) => {
      const key = `${product.moderation_status}_${product.is_active ? 'active' : 'inactive'}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    console.log('=== Product Moderation Statistics ===');
    console.log('Status breakdown:');
    Object.entries(stats).forEach(([key, count]) => {
      console.log(`  ${key}: ${count}`);
    });
    
    const totalPending = stats['pending_approval_inactive'] || 0;
    const totalApproved = (stats['approved_active'] || 0) + (stats['approved_inactive'] || 0);
    const totalRejected = (stats['rejected_active'] || 0) + (stats['rejected_inactive'] || 0);
    
    console.log('\nSummary:');
    console.log(`  Pending: ${totalPending}`);
    console.log(`  Approved: ${totalApproved}`);
    console.log(`  Rejected: ${totalRejected}`);
    console.log(`  Total: ${totalPending + totalApproved + totalRejected}`);
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

getProductStats();
