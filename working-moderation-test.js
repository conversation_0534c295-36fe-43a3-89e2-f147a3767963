const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function testModerationFunctions() {
  try {
    console.log('=== Тестирование функций модерации ===\n');
    
    // 1. Получаем статистику до изменений
    console.log('📊 Статистика ДО тестирования:');
    await printModStats();
    console.log();
    
    // 2. Находим тестовые продукты для модерации
    const { data: testProducts, error } = await supabase
      .from('products')
      .select('id, name, external_id, moderation_status, is_active')
      .eq('moderation_status', 'pending_approval')
      .in('external_id', ['test_mod_2', 'test_mod_3'])
      .limit(2);
    
    if (error) {
      console.error('Ошибка при получении тестовых продуктов:', error);
      return;
    }
    
    if (!testProducts || testProducts.length === 0) {
      console.log('❌ Тестовые продукты не найдены');
      return;
    }
    
    console.log(`Найдено ${testProducts.length} тестовых продуктов для модерации`);
    
    // 3. Одобряем первый тестовый продукт
    if (testProducts.length > 0) {
      const productToApprove = testProducts.find(p => p.external_id === 'test_mod_2') || testProducts[0];
      console.log(`\n✅ ОДОБРЯЕМ: ${productToApprove.name}`);
      console.log(`   ID: ${productToApprove.id}`);
      
      const { error: approveError } = await supabase
        .from('products')
        .update({
          moderation_status: 'approved',
          is_active: true
        })
        .eq('id', productToApprove.id);
      
      if (approveError) {
        console.error('❌ Ошибка при одобрении:', approveError.message);
      } else {
        console.log('✅ Продукт успешно одобрен!');
      }
    }
    
    // 4. Отклоняем второй тестовый продукт
    if (testProducts.length > 1) {
      const productToReject = testProducts.find(p => p.external_id === 'test_mod_3') || testProducts[1];
      console.log(`\n❌ ОТКЛОНЯЕМ: ${productToReject.name}`);
      console.log(`   ID: ${productToReject.id}`);
      
      const { error: rejectError } = await supabase
        .from('products')
        .update({
          moderation_status: 'rejected',
          is_active: false
        })
        .eq('id', productToReject.id);
      
      if (rejectError) {
        console.error('❌ Ошибка при отклонении:', rejectError.message);
      } else {
        console.log('✅ Продукт успешно отклонен!');
      }
    }
    
    // 5. Показываем обновленную статистику
    console.log('\n📊 Статистика ПОСЛЕ тестирования:');
    await printModStats();
    
    console.log('\n🎉 Тестирование модерации завершено!');
    
  } catch (error) {
    console.error('❌ Общая ошибка:', error.message);
  }
}

async function printModStats() {
  const { data: allProducts } = await supabase
    .from('products')
    .select('moderation_status, is_active');
  
  const stats = {
    pending: 0,
    approved_active: 0,
    approved_inactive: 0,
    rejected: 0,
    other: 0
  };
  
  allProducts.forEach(product => {
    if (product.moderation_status === 'pending_approval') {
      stats.pending++;
    } else if (product.moderation_status === 'approved' && product.is_active) {
      stats.approved_active++;
    } else if (product.moderation_status === 'approved' && !product.is_active) {
      stats.approved_inactive++;
    } else if (product.moderation_status === 'rejected') {
      stats.rejected++;
    } else {
      stats.other++;
    }
  });
  
  console.log(`   📋 Ожидают модерации: ${stats.pending}`);
  console.log(`   ✅ Одобрены и активны: ${stats.approved_active}`);
  console.log(`   ⚠️  Одобрены но неактивны: ${stats.approved_inactive}`);
  console.log(`   ❌ Отклонены: ${stats.rejected}`);
  console.log(`   ❓ Другие статусы: ${stats.other}`);
  console.log(`   📦 Всего: ${allProducts.length}`);
}

// Запускаем тест
testModerationFunctions();
