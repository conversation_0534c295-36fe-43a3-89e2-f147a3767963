const { createClient } = require('@supabase/supabase-js');

// Use service role key for admin operations
const SUPABASE_URL = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTk3ODc4MiwiZXhwIjoyMDQ3NTU0NzgyfQ.Y1Q9l2NwayOcN_p8PN7GNvVfm5Ag_4nXMl8QkXvvgcE';

const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function fixProductParamsRLS() {
  console.log('🔧 Fixing product_params RLS policies...');
  
  const sqlCommands = [
    `DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;`, 
    `DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;`,
    
    `CREATE POLICY "product_params_select_policy" ON product_params FOR SELECT USING (true);`,
    
    `CREATE POLICY "product_params_insert_policy" ON product_params FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);`,
    
    `CREATE POLICY "product_params_update_policy" ON product_params FOR UPDATE USING (auth.uid() IS NOT NULL);`,
    
    `CREATE POLICY "product_params_delete_policy" ON product_params FOR DELETE USING (auth.uid() IS NOT NULL);`,
    
    `GRANT ALL ON product_params TO authenticated;`,
    `GRANT SELECT ON product_params TO anon;`
  ];
  
  for (const sql of sqlCommands) {
    try {
      const { error } = await supabaseAdmin.rpc('exec_sql', { query: sql });
      if (error) {
        console.log('⚠️  SQL command failed (might be expected):', sql.substring(0, 50) + '...', error.message);
      } else {
        console.log('✅ SQL command successful:', sql.substring(0, 50) + '...');
      }
    } catch (err) {
      console.log('⚠️  Exception with SQL command:', sql.substring(0, 50) + '...', err.message);
    }
  }
  
  // Test the fix
  console.log('\n🧪 Testing product_params access...');
  const { data, error } = await supabaseAdmin
    .from('product_params')
    .select('*')
    .limit(1);
    
  console.log('Admin access test:', error ? 'FAILED: ' + error.message : 'SUCCESS');
}

fixProductParamsRLS().catch(console.error);
