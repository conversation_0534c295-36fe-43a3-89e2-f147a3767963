const { getAdminClient } = require('./src/supabaseClient.js');

async function testAdminClient() {
  console.log('🧪 Testing Admin Client after JWT fix');
  console.log('======================================');

  const adminClient = getAdminClient();
  
  if (!adminClient) {
    console.log('❌ Admin client not available');
    return;
  }

  console.log('✅ Admin client available');

  try {
    // Test 1: Try to read products table
    console.log('\n1️⃣  Testing products table read...');
    const { data: products, error: readError } = await adminClient
      .from('products')
      .select('id, name, is_new, is_on_sale, is_bestseller')
      .limit(5);

    if (readError) {
      console.log('❌ Products read failed:', readError.message);
      return;
    }

    console.log(`✅ Successfully read ${products.length} products`);

    if (products.length > 0) {
      const testProduct = products[0];
      console.log(`   Using product: ${testProduct.name} (${testProduct.id})`);

      // Test 2: Try to update product flags
      console.log('\n2️⃣  Testing product flag update...');
      const currentFlags = {
        is_new: testProduct.is_new,
        is_on_sale: testProduct.is_on_sale,
        is_bestseller: testProduct.is_bestseller
      };

      // Toggle a flag temporarily
      const newFlags = {
        ...currentFlags,
        is_new: !currentFlags.is_new
      };

      const { data: updateData, error: updateError } = await adminClient
        .from('products')
        .update(newFlags)
        .eq('id', testProduct.id)
        .select();

      if (updateError) {
        console.log('❌ Product flag update failed:', updateError.message);
        console.log('   Error code:', updateError.code);
        console.log('   Error details:', updateError.details);
        return;
      }

      console.log('✅ Product flag update successful!');

      // Restore original flags
      console.log('\n3️⃣  Restoring original flags...');
      const { error: restoreError } = await adminClient
        .from('products')
        .update(currentFlags)
        .eq('id', testProduct.id);

      if (restoreError) {
        console.log('⚠️  Could not restore original flags:', restoreError.message);
      } else {
        console.log('✅ Original flags restored');
      }
    }

    console.log('\n🎯 ADMIN CLIENT TEST SUMMARY');
    console.log('============================');
    console.log('✅ Admin client working correctly');
    console.log('✅ Can read products');
    console.log('✅ Can update product flags');
    console.log('✅ JWT token is valid');
    console.log('');
    console.log('🚀 The "Invalid API key" error should now be fixed!');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testAdminClient();
