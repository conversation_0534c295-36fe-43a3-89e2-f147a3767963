const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/pages/CategoryPage.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Remove debug state
content = content.replace(/\s*const \[debug, setDebug\] = useState\({}\);/, '');

// Remove debugInfo initialization
content = content.replace(/\s*const debugInfo = {};/, '');

// Remove all debugInfo assignments
content = content.replace(/\s*debugInfo\.[^;]+;/g, '');

// Remove console.log statements with debugInfo
content = content.replace(/\s*console\.log\([^)]*debugInfo[^)]*\);/g, '');

// Remove setDebug calls
content = content.replace(/\s*setDebug\([^)]*\);/g, '');

// Remove renderDebugInfo function
content = content.replace(/\s*\/\/ Добавляем отображение информации для отладки[^}]+}\);/s, '');
content = content.replace(/\s*const renderDebugInfo = \(\) => \{[^}]+\{[^}]+\}[^}]+\};/s, '');

// Remove renderDebugInfo call
content = content.replace(/\s*\{\/\* Отладочная информация \*\/\}\s*\{renderDebugInfo\(\)\}/, '');

// Remove debug comment about production
content = content.replace(/\s*\/\/ Fetch total products count in the database for debugging[^}]+}/s, '');

// Remove console.error in catch blocks that use setDebug
content = content.replace(/\s*setDebug\(prev => \(\{ \.\.\.prev, error: error\.message \}\)\);/g, '');

// Clean up any remaining console.log statements
content = content.replace(/\s*console\.log\('Category debug info:'[^)]*\);/g, '');
content = content.replace(/\s*console\.log\('Root categories debug info:'[^)]*\);/g, '');
content = content.replace(/\s*console\.log\('Optimized \/categories debug info:'[^)]*\);/g, '');
content = content.replace(/\s*console\.log\('Debug info:'[^)]*\);/g, '');

// Clean up console.error statements with debug info
content = content.replace(/\s*console\.error\('Error fetching data:', error\);/g, '');

// Write the cleaned content back
fs.writeFileSync(filePath, content);

console.log('Debug cleanup completed for CategoryPage.js');
