const webpack = require('webpack');
const path = require('path');

module.exports = {
  // ...existing code...
  plugins: [
    // ...existing plugins...
    new webpack.DefinePlugin({
      'process.env.local': JSON.stringify(process.env.local)
    }),
    new webpack.ProgressPlugin()
  ],
  resolve: {
    fallback: {
      process: require.resolve('process/browser')
    },
    alias: {
      '@features': path.resolve(__dirname, 'src/features'),
      '@admin': path.resolve(__dirname, 'src/features/admin'),
      '@database': path.resolve(__dirname, 'src/features/database'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@pages': path.resolve(__dirname, 'src/pages'),
      '@assets': path.resolve(__dirname, 'src/assets'),
      '@utils': path.resolve(__dirname, 'src/utils'),
      '@hooks': path.resolve(__dirname, 'src/hooks'),
      '@services': path.resolve(__dirname, 'src/services'),
      '@scripts': path.resolve(__dirname, 'src/scripts')
    }
  },
  module: {
    rules: [
      // ...existing rules...
      {
        test: /\.m?js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      }
    ]
  }
};
