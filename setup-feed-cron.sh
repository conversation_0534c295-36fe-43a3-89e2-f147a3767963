#!/bin/bash

# Setup script for automatic feed processing every 6 hours
# This script sets up a cron job to run feed processing automatically

PROJECT_DIR="/Users/<USER>/e-com_new/online-store"
SCRIPT_PATH="$PROJECT_DIR/scripts/process-feeds.js"
LOG_FILE="$PROJECT_DIR/feed-processing.log"

echo "🔧 Setting up automatic feed processing every 6 hours..."

# Check if the script exists
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "❌ Error: Feed processing script not found at $SCRIPT_PATH"
    exit 1
fi

# Create the cron command
# Runs every 6 hours at minutes 0: 00:00, 06:00, 12:00, 18:00
CRON_COMMAND="0 0,6,12,18 * * * cd $PROJECT_DIR && node scripts/process-feeds.js >> $LOG_FILE 2>&1"

# Backup current crontab
echo "📋 Backing up current crontab..."
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "No existing crontab found"

# Check if the cron job already exists
if crontab -l 2>/dev/null | grep -q "process-feeds.js"; then
    echo "⚠️  Feed processing cron job already exists. Updating..."
    # Remove existing feed processing jobs
    crontab -l 2>/dev/null | grep -v "process-feeds.js" | crontab -
fi

# Add the new cron job
echo "➕ Adding new cron job..."
(crontab -l 2>/dev/null; echo "$CRON_COMMAND") | crontab -

# Verify the cron job was added
echo "✅ Cron job added successfully!"
echo ""
echo "📅 Current crontab:"
crontab -l | grep "process-feeds.js"
echo ""
echo "🕐 Feed processing will run every 6 hours at:"
echo "   - 00:00 (midnight)"
echo "   - 06:00 (morning)"
echo "   - 12:00 (noon)"
echo "   - 18:00 (evening)"
echo ""
echo "📝 Logs will be saved to: $LOG_FILE"
echo ""
echo "🔧 To remove the cron job later, run:"
echo "   crontab -e"
echo "   (then delete the line containing 'process-feeds.js')"
echo ""
echo "🧪 To test the feed processing manually, run:"
echo "   cd $PROJECT_DIR && node scripts/process-feeds.js"
echo ""
echo "✨ Setup complete! Feed processing will start automatically."
