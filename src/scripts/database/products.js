const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.REACT_APP_SUPABASE_ANON_KEY
);

async function setupProducts() {
  try {
    // Create products table if it doesn't exist
    const { error: tableError } = await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS public.products (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          price NUMERIC(10,2) NOT NULL,
          stock INTEGER DEFAULT 0,
          min_stock INTEGER DEFAULT 5,
          sku TEXT,
          category_id UUID REFERENCES categories(id),
          brand_id UUID REFERENCES brands(id),
          vendor TEXT,
          image TEXT,
          url TEXT,
          is_bestseller BOOLEAN DEFAULT false,
          is_new BOOLEAN DEFAULT false,
          is_on_sale BOOLEAN DEFAULT false,
          display_order INTEGER DEFAULT 0,
          original_price NUMERIC(10,2),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (tableError) throw tableError;
    return true;
  } catch (error) {
    console.error('Error setting up products:', error);
    throw error;
  }
}

module.exports = setupProducts;

if (require.main === module) {
  setupProducts();
}
