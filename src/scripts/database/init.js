const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.REACT_APP_SUPABASE_ANON_KEY
);

async function initDatabase() {
  try {
    // Create check_table_exists function
    const { error: functionError } = await supabase.rpc('exec_sql', {
      query: `
        CREATE OR REPLACE FUNCTION public.check_table_exists(table_name text)
        RETURNS boolean
        LANGUAGE plpgsql
        AS $$
        BEGIN
          RETURN EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        END;
        $$;
      `
    });

    if (functionError) {
      console.error('Error creating function:', functionError);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
}

module.exports = initDatabase;

if (require.main === module) {
  initDatabase();
}
