const { checkTableExists, executeSql, supabase } = require('../../utils/database');
const { handleError } = require('../../utils/errors');
require('dotenv').config();

async function setupBanners() {
  try {
    // Create banners table if it doesn't exist
    const { error: tableError } = await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS public.banners (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          title TEXT,
          image_url TEXT,
          link_url TEXT,
          active BOOLEAN DEFAULT true,
          position INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (tableError) throw tableError;
    return true;
  } catch (error) {
    console.error('Error setting up banners:', error);
    throw error;
  }
}

module.exports = setupBanners;

if (require.main === module) {
  setupBanners();
}
