const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.REACT_APP_SUPABASE_ANON_KEY
);

async function createStorageBuckets() {
  try {
    // Создаем хранилище для продуктов через API Supabase
    try {
      // Проверим существование корзины products
      const { data: buckets } = await supabase.storage.listBuckets();
      const productsBucketExists = buckets && buckets.some(b => b.name === 'products');

      // Если хранилище не существует, создаем его
      if (!productsBucketExists) {
        const { error } = await supabase.storage.createBucket('products', {
          public: true,
          fileSizeLimit: 5242880 // 5MB
        });

        if (error) {
          console.error('Ошибка при создании хранилища:', error);
        } else {
        }
      } else {
      }

      // Выведем список доступных хранилищ
      const { data: updatedBuckets } = await supabase.storage.listBuckets();
      if (updatedBuckets && updatedBuckets.length > 0) {
        console.log('Доступные хранилища:', updatedBuckets.map(b => b.name).join(', '));
      } else {
        console.log('Хранилища не найдены');
      }
    } catch (bucketError) {
      console.error('Ошибка при настройке хранилища:', bucketError);
    }
  } catch (error) {
    console.error('Ошибка при настройке хранилища:', error);
  }
}

module.exports = createStorageBuckets;

if (require.main === module) {
  createStorageBuckets();
}
