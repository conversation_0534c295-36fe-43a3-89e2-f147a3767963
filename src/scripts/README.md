# Admin Setup Scripts

This directory contains utility scripts for administrative tasks.

## Setting Up Admin User

If you're having trouble accessing the admin panel, you can use the `set-admin-user.js` script to make your user an admin.

### Prerequisites

Make sure you have the required environment variables set up in your `.env` file:

```
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

For more elevated privileges (optional, but recommended), add:

```
REACT_APP_SUPABASE_SERVICE_KEY=your_supabase_service_key
```

### Running the Script

To make a user an admin, run:

```bash
node src/scripts/set-admin-user.js <EMAIL>
```

Replace `<EMAIL>` with the email of the user you want to make an admin.

### What the Script Does

1. Finds the user by email
2. Ensures the `profiles` table exists with an `is_admin` column
3. Sets the user's `is_admin` status to `true`
4. Outputs the user's ID so you can add it to the hardcoded admin list in `src/context/AuthContext.js`

### After Running the Script

1. **Log out and log back in** to refresh your session with the new admin status
2. For extra security, add your user ID to the `adminUserIds` array in `src/context/AuthContext.js`

If you're still having trouble, you might need to rebuild your application after making these changes.

## Troubleshooting

If you encounter errors related to Supabase queries:

1. Check that your environment variables are correct
2. Make sure your Supabase instance is up and running
3. Check that you have the right permissions to edit the database 
4. Review your Supabase policies to ensure you can access and modify the `profiles` table 