const path = require('path');
require('dotenv').config();

async function setupDatabase() {
  try {
    // Use correct relative paths from setup.js location
    const initDb = require('./database/init');
    const setupBanners = require('./database/banners');
    const setupProducts = require('./database/products');
    const setupStorage = require('./database/create-storage');

    // Run setup steps
    await initDb();
    await setupBanners();
    await setupProducts();
    await setupStorage();
  } catch (error) {
    console.error('Ошибка при настройке:', error);
    process.exit(1);
  }
}

// Allow both direct execution and importing
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
