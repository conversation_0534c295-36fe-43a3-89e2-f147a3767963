const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Get email from command line arguments
const userEmail = process.argv[2];

if (!userEmail) {
  console.error('Please provide an email address: node set-admin-user.js <EMAIL>');
  process.exit(1);
}

// Setup Supabase client
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey =
  process.env.REACT_APP_SUPABASE_SERVICE_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function makeUserAdmin(email) {
  try {
    // 1. Get the user ID from auth.users
    const { data: userData, error: userError } = await supabase.auth.admin
      .getUserByEmail(email)
      .catch(() => {
        // Fallback if admin API isn't available
        return { data: null, error: { message: 'Admin API not available' } };
      });

    if (userError) {
      // Try alternative method to get user ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single();

      if (profileError) {
        throw new Error(`Failed to find user with email ${email}: ${profileError.message}`);
      }

      if (!profileData) {
        throw new Error(`No user found with email ${email}`);
      }

      return await updateUserAdminStatus(profileData.id, email);
    }

    const userId = userData?.user?.id;
    if (!userId) {
      throw new Error(`No user found with email ${email}`);
    }

    return await updateUserAdminStatus(userId, email);
  } catch (error) {
    console.error('Error setting admin status:', error.message);
    process.exit(1);
  }
}

async function updateUserAdminStatus(userId, email) {
  try {
    // 2. Check if profiles table exists and has is_admin column
    const { error: checkError } = await supabase.rpc('exec_sql', {
      query: `
        DO $$
        BEGIN
          -- Create profiles table if it doesn't exist
          IF NOT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name = 'profiles'
          ) THEN
            CREATE TABLE public.profiles (
              id UUID PRIMARY KEY REFERENCES auth.users(id),
              email TEXT,
              first_name TEXT,
              last_name TEXT,
              avatar_url TEXT,
              is_admin BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW()
            );
          END IF;
          
          -- Add is_admin column if it doesn't exist
          IF NOT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public'
            AND table_name = 'profiles'
            AND column_name = 'is_admin'
          ) THEN
            ALTER TABLE public.profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
          END IF;
        END $$;
      `
    });

    if (checkError) {
    }

    // 3. Update or insert the profile with is_admin=true
    const { data, error } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        email: email,
        is_admin: true,
        updated_at: new Date().toISOString()
      })
      .select();

    if (error) {
      // Try direct SQL if standard upsert fails
      const { error: sqlError } = await supabase.rpc('exec_sql', {
        query: `
          INSERT INTO public.profiles (id, email, is_admin, updated_at)
          VALUES ('${userId}', '${email}', TRUE, NOW())
          ON CONFLICT (id) 
          DO UPDATE SET 
            email = EXCLUDED.email,
            is_admin = TRUE,
            updated_at = NOW();
        `
      });

      if (sqlError) {
        throw new Error(`Failed to update profile: ${sqlError.message}`);
      }
      return true;
    }

    // 4. Add user ID to the list in AuthContext.js

    return true;
  } catch (error) {
    console.error('Error updating admin status:', error.message);
    process.exit(1);
  }
}

// Run the script
makeUserAdmin(userEmail);
