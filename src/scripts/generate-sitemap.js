// Скрипт для генерации динамического sitemap
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Инициализация Supabase клиента
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Базовый URL вашего сайта
const BASE_URL = 'https://yoursite.com';

// Функция для форматирования даты в формат YYYY-MM-DD
const formatDate = date => {
  const d = new Date(date || new Date());
  return d.toISOString().split('T')[0];
};

// Генерируем sitemap.xml динамически
async function generateSitemap() {
  const today = formatDate();

  // Получаем данные для sitemap из Supabase
  const { data: products, error: productsError } = await supabase
    .from('products')
    .select('id, created_at, updated_at')
    .eq('published', true);

  if (productsError) {
    console.error('Ошибка при получении продуктов:', productsError);
    return;
  }

  const { data: categories, error: categoriesError } = await supabase
    .from('categories')
    .select('slug, created_at, updated_at');

  if (categoriesError) {
    console.error('Ошибка при получении категорий:', categoriesError);
    return;
  }

  // Начало документа sitemap
  let sitemap = '<?xml version="1.0" encoding="UTF-8"?>\n';
  sitemap += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Статические страницы
  const staticPages = [
    { url: '/', priority: '1.0', changefreq: 'daily' },
    { url: '/products', priority: '0.8', changefreq: 'daily' },
    { url: '/bestsellers', priority: '0.7', changefreq: 'weekly' },
    { url: '/sale', priority: '0.7', changefreq: 'weekly' },
    { url: '/new-arrivals', priority: '0.7', changefreq: 'weekly' },
    { url: '/about', priority: '0.5', changefreq: 'monthly' },
    { url: '/contact', priority: '0.5', changefreq: 'monthly' }
  ];

  // Добавляем статические страницы
  staticPages.forEach(page => {
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${BASE_URL}${page.url}</loc>\n`;
    sitemap += `    <lastmod>${today}</lastmod>\n`;
    sitemap += `    <changefreq>${page.changefreq}</changefreq>\n`;
    sitemap += `    <priority>${page.priority}</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Добавляем страницы продуктов
  products.forEach(product => {
    const lastmod = formatDate(product.updated_at || product.created_at);
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${BASE_URL}/product/${product.id}</loc>\n`;
    sitemap += `    <lastmod>${lastmod}</lastmod>\n`;
    sitemap += `    <changefreq>weekly</changefreq>\n`;
    sitemap += `    <priority>0.6</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Добавляем страницы категорий
  categories.forEach(category => {
    const lastmod = formatDate(category.updated_at || category.created_at);
    sitemap += `  <url>\n`;
    sitemap += `    <loc>${BASE_URL}/category/${category.slug}</loc>\n`;
    sitemap += `    <lastmod>${lastmod}</lastmod>\n`;
    sitemap += `    <changefreq>weekly</changefreq>\n`;
    sitemap += `    <priority>0.7</priority>\n`;
    sitemap += `  </url>\n`;
  });

  // Закрываем документ sitemap
  sitemap += '</urlset>';

  // Сохраняем sitemap в файл
  const outputPath = path.join(__dirname, '../../public/sitemap.xml');
  fs.writeFileSync(outputPath, sitemap);
}

// Запускаем генерацию
generateSitemap().catch(err => {
  console.error('Ошибка при генерации sitemap:', err);
});
