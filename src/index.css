/* Base styles for the application */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom font settings */
@layer base {
  html {
    font-display: optional;
    -webkit-text-size-adjust: 100%;
  }

  body {
    @apply font-sans text-body min-h-screen;
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Fix iOS safe area issues */
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-first typography */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-semibold text-heading;
    margin-top: 0;
  }

  h1 {
    @apply text-2xl md:text-h1 leading-tight;
  }
  h2 {
    @apply text-xl md:text-h2 leading-tight;
  }
  h3 {
    @apply text-lg md:text-h3 leading-tight;
  }

  /* Fix mobile tap highlight */
  * {
    -webkit-tap-highlight-color: transparent;
  }

  /* Improve mobile scrolling */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* Mobile-first layout containers */
@layer components {
  .container {
    @apply px-4 md:px-6 mx-auto;
    max-width: 100%;
  }

  .page-container {
    @apply min-h-screen pt-16 pb-20 px-4 md:px-6;
    /* Account for mobile browser chrome */
    min-height: -webkit-fill-available;
  }

  .content-wrapper {
    @apply w-full max-w-7xl mx-auto;
  }

  /* Mobile navigation spacing */
  .nav-spacer {
    height: env(safe-area-inset-top);
  }

  .bottom-nav-spacer {
    height: calc(env(safe-area-inset-bottom) + 4rem);
  }
}

/* Primary colors */
:root {
  --color-primary: #d4b27d;
  --color-primary-dark: #c5a36e;
}

.bg-primary {
  background-color: var(--color-primary);
}

.bg-primary-dark {
  background-color: var(--color-primary-dark);
}

.text-primary {
  color: var(--color-primary);
}

.hover\:bg-primary-dark:hover {
  background-color: var(--color-primary-dark);
}

/* Utility classes */
.page-loading img:not([src^='data:']) {
  transition: none !important;
}

.page-loaded img {
  transition: opacity 0.3s ease-in-out;
}

/* Button components */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:ring-2 focus:ring-offset-2;
    /* Improve touch targets on mobile */
    min-height: 44px;
  }

  .btn-primary {
    @apply bg-primary hover:bg-primary-dark text-white focus:ring-primary/50;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-body focus:ring-gray-200;
  }

  .btn-outline {
    @apply border border-primary bg-transparent hover:bg-primary/10 text-primary focus:ring-primary/30;
  }

  .btn-sm {
    @apply text-sm px-3 py-1.5;
    min-height: 36px;
  }

  .btn-lg {
    @apply text-lg px-6 py-2.5;
    min-height: 52px;
  }
}

/* Admin sidebar styles */
.admin-sidebar {
  transition: width 0.3s;
  /* Fix mobile height */
  height: 100dvh;
}

.admin-sidebar.collapsed .admin-sidebar-title {
  display: none;
}

.admin-sidebar.collapsed .admin-menu li a span {
  display: none;
}

.admin-sidebar.collapsed .admin-menu li a i {
  margin-right: 0;
}

/* Mobile-optimized tooltips */
.admin-sidebar.collapsed .admin-menu li a:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  margin-left: 10px;
  white-space: nowrap;
  z-index: 1000;
  font-size: 14px;
}

/* Admin content responsive margins */
.admin-content {
  transition: margin-left 0.3s;
  min-height: 100dvh;
}

@media (min-width: 768px) {
  .admin-sidebar + .admin-content {
    margin-left: 240px;
  }

  .admin-sidebar.collapsed + .admin-content {
    margin-left: 60px;
  }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .admin-sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 40;
  }

  .admin-content {
    margin-left: 0;
    margin-bottom: calc(60px + env(safe-area-inset-bottom));
  }
}

/* Refined positioning for partners slider arrows */
.brands-slider {
  position: relative !important;
  overflow: visible !important;
}
.brands-slider .swiper-wrapper {
  padding: 0 60px !important;
}
.brands-slider .swiper-button-prev,
.brands-slider .swiper-button-next {
  top: 50% !important;
}
.brands-slider .swiper-button-prev {
  left: 0 !important;
  transform: translate(-150%, -50%) !important;
}
.brands-slider .swiper-button-next {
  right: 0 !important;
  transform: translate(150%, -50%) !important;
}

/* Ensure slide items appear above navigation arrows */
.brands-slider .swiper-slide {
  position: relative;
  z-index: 2;
}

/* Place navigation arrows behind slide items */
.brands-slider .swiper-button-prev,
.brands-slider .swiper-button-next {
  z-index: 1 !important;
}

/* Banner slider custom styles */
.slick-list {
  overflow: visible !important;
}
.slick-slider {
  position: relative !important;
}
.slick-prev,
.slick-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  width: 40px;
  height: 40px;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.slick-prev {
  left: -60px;
}
.slick-next {
  right: -60px;
}
.slick-prev:before,
.slick-next:before {
  font-size: 20px;
  color: #ffffff;
  opacity: 1 !important;
}
.slick-dots {
  position: absolute;
  bottom: 15px;
  width: 100%;
  display: flex !important;
  justify-content: center;
  padding: 0;
  margin: 0;
}
.slick-dots li {
  margin: 0 4px;
}
.slick-dots li button:before {
  font-size: 12px;
  color: #ffffff;
  opacity: 0.75;
}
.slick-dots li.slick-active button:before {
  opacity: 1;
}

/* Partners Slider Styles */
.partners-slider {
  position: relative;
  padding-bottom: 8px; /* Дополнительное место для теней */
}

.partners-slider .slider-container {
  overflow-x: hidden; /* Скрываем горизонтальное переполнение */
  overflow-y: visible; /* Показываем вертикальное переполнение для теней */
  border-radius: 12px;
  padding-bottom: 4px; /* Дополнительное место для теней */
}

.partners-slider .slider-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.partners-slider .partner-slide {
  flex-shrink: 0;
  padding: 0 12px;
}

.partners-slider .partner-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.partners-slider .partner-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

.partners-slider .partner-logo {
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partners-slider .partner-card:hover .partner-logo {
  filter: grayscale(0%);
}

/* Navigation buttons */
.partners-slider .nav-button {
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
}

.partners-slider .nav-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.partners-slider .nav-button:active {
  transform: scale(0.95);
}

/* Dots indicator */
.partners-slider .dots-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  gap: 6px;
}

.partners-slider .dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #d1d5db;
  transition: all 0.3s ease;
  cursor: pointer;
}

.partners-slider .dot.active {
  background: var(--color-primary);
  transform: scale(1.2);
}

.partners-slider .dot:hover {
  background: #9ca3af;
}

/* Auto-play indicator */
.partners-slider .autoplay-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  transition: color 0.3s ease;
}

.partners-slider .autoplay-indicator:hover {
  color: var(--color-primary);
}

.partners-slider .autoplay-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.partners-slider .autoplay-dot.active {
  background-color: #10b981;
}

.partners-slider .autoplay-dot.inactive {
  background-color: #9ca3af;
}

