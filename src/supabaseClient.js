import { createClient } from '@supabase/supabase-js';

console.log('🔥 Supabase client module loading...');

// Constants - hardcoded to avoid env issues
const SUPABASE_URL = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU';

console.log('📊 Environment check:', {
  url: SUPABASE_URL,
  hasKey: !!SUPABASE_ANON_KEY,
  keyLength: SUPABASE_ANON_KEY?.length || 0,
  isClient: typeof window !== 'undefined'
});

// Client options
const supabaseOptions = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: typeof window !== 'undefined',
    flowType: 'pkce',
    storageKey: 'sb-dmdijuuwnbwngerkbfak-auth-token'
  },
  global: {
    headers: {
      'X-Client-Info': 'online-store-v2'
    }
  }
};

// Add localStorage for browser environments
if (typeof window !== 'undefined') {
  supabaseOptions.auth.storage = window.localStorage;
  console.log('🌐 Browser environment detected, localStorage available');
}

console.log('⚡ Creating Supabase client...');

// Create the client with proper error handling
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, supabaseOptions);

// Validation
if (!supabase) {
  console.error('❌ CRITICAL: createClient returned null/undefined');
  throw new Error('Failed to create Supabase client');
}

if (!supabase.auth) {
  console.error('❌ CRITICAL: Supabase client missing auth property');
  throw new Error('Supabase client missing auth property');
}

if (!supabase.from) {
  console.error('❌ CRITICAL: Supabase client missing from method');
  throw new Error('Supabase client missing from method');
}

console.log('✅ Supabase client created successfully!');
console.log('🔧 Available methods:', {
  hasAuth: !!supabase.auth,
  hasFrom: !!supabase.from,
  authMethods: Object.keys(supabase.auth || {}),
  clientType: typeof supabase
});

export { supabase };

// Create a separate admin client with service role key if available
const SUPABASE_SERVICE_ROLE_KEY =
  process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8';

let supabaseAdminInstance = null;
export const supabaseAdmin = (() => {
  if (SUPABASE_SERVICE_ROLE_KEY && !supabaseAdminInstance) {
    try {
      supabaseAdminInstance = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });
      console.log('🔑 Supabase admin client created successfully!');
      console.log('🔧 Service role key length:', SUPABASE_SERVICE_ROLE_KEY.length);
    } catch (error) {
      console.error('❌ Failed to create Supabase admin client:', error);
    }
  }
  return supabaseAdminInstance;
})();

// Helper function to get appropriate client for admin operations
export const getAdminClient = () => {
  console.log('🔍 getAdminClient called - NODE_ENV:', process.env.NODE_ENV);
  console.log('🔍 supabaseAdmin available:', !!supabaseAdmin);

  // Always prefer admin client when available for admin operations
  if (supabaseAdmin) {
    console.log('✅ Using admin client (service role)');
    return supabaseAdmin;
  }
  // Fallback to regular client
  console.log('⚠️  Using regular client (user auth)');
  return supabase;
};

// UUID conversion helpers
export const convertUUIDToBigInt = uuid => {
  if (!uuid) return null;
  try {
    const cleanUuid = uuid.replace(/-/g, '');
    const part1 = parseInt(cleanUuid.substring(0, 8), 16);
    const part2 = parseInt(cleanUuid.substring(8, 16), 16);
    const part3 = parseInt(cleanUuid.substring(16, 24), 16);
    const part4 = parseInt(cleanUuid.substring(24, 32), 16);
    return Math.abs(part1 ^ part2 ^ part3 ^ part4);
  } catch (error) {
    console.error('Error converting UUID to BigInt:', error);
    return null;
  }
};

export const convertBigIntToUUID = bigInt => {
  if (!bigInt) return null;
  try {
    const hexValue = Math.abs(Number(bigInt)).toString(16).padStart(32, '0');
    return `${hexValue.slice(0, 8)}-${hexValue.slice(8, 12)}-${hexValue.slice(12, 16)}-${hexValue.slice(16, 20)}-${hexValue.slice(20, 32)}`;
  } catch (error) {
    console.error('Error converting BigInt to UUID:', error);
    return null;
  }
};

// Legacy aliases
export const convertToBigInt = convertUUIDToBigInt;
export const convertFromBigInt = convertBigIntToUUID;

// Helper function for product_params table queries
export const prepareProductParamsQuery = productIds => {
  if (!productIds) return null;

  // Handle single product ID
  if (typeof productIds === 'string') {
    return convertUUIDToBigInt(productIds);
  }

  // Handle array of product IDs
  if (Array.isArray(productIds)) {
    return productIds.map(id => convertUUIDToBigInt(id)).filter(id => id !== null);
  }

  return null;
};

export default supabase;
