// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import React from 'react';

// Mock IntersectionObserver
class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  unobserve() {
    return null;
  }
  disconnect() {
    return null;
  }
}

window.IntersectionObserver = IntersectionObserver;

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

// Для решения проблемы с Framer Motion
jest.mock('framer-motion', () => {
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      ...actual.motion,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      div: ({
        children,
        whileHover,
        whileTap,
        animate,
        initial,
        variants,
        transition,
        ...rest
      }) => <div {...rest}>{children}</div>,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      button: ({
        children,
        whileHover,
        whileTap,
        animate,
        initial,
        variants,
        transition,
        ...rest
      }) => <button {...rest}>{children}</button>
    }
  };
});

// Мокаем компоненты, которые могут вызывать проблемы в тестах
jest.mock('./components/home/<USER>', () => () => (
  <div data-testid="product-slider-mock">Product Slider Mock</div>
));

jest.mock('./components/home/<USER>', () => () => (
  <div data-testid="brand-slider-mock">Brand Slider Mock</div>
));

// Мокаем Supabase клиент
jest.mock('./supabaseClient', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    then: jest.fn().mockResolvedValue({ data: [], error: null })
  }
}));

// Создаем мок для Redux хуков
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: jest.fn().mockImplementation(() => ({
    wishlist: { items: [] },
    compare: { items: [] },
    cart: { items: [] }
  })),
  useDispatch: jest.fn().mockReturnValue(jest.fn())
}));
