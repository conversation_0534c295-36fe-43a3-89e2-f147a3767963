/* Banner CSS с выделенными стилями для баннеров */

.full-width-banner {
  width: 100vw !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
  z-index: 1;
  overflow: hidden;
  margin-top: -1px !important;
  margin-bottom: 0 !important;
}

/* Убираем отступы для верхнего баннера */
.banner-section-wrapper {
  margin-top: -1px !important;
  position: relative;
  z-index: 1;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: 0 !important;
  height: auto !important;
}

body.has-top-banner .banner-section-wrapper:first-child {
  margin-top: -60px !important;
}

/* Размеры баннеров */
.top-banner {
  height: 40rem;
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.bottom-banner {
  height: 24rem;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Контейнер для изображения с параллакс-эффектом */
.banner-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  background-color: #000;
  margin-bottom: 0 !important;
}

/* Базовые стили изображения */
.banner-image {
  position: absolute;
  width: 100%;
  height: 200%;
  object-fit: cover;
  will-change: transform;
}

/* Стили для верхнего баннера */
.top-banner-image {
  top: -50%;
}

/* Специфические стили для нижнего баннера - значительно увеличиваем покрытие */
.bottom-banner-image {
  top: -100%; /* Сдвигаем изображение еще выше */
  height: 400%; /* Значительно увеличиваем высоту для полного покрытия при скролле */
  object-position: center top; /* Фокусируем изображение строго на верхней части */
}

/* Остальные стили остаются без изменений */
.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.5) 100%);
}

.banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  margin-bottom: 0 !important;
}

.banner-title {
  color: white;
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.02em;
}

.banner-subtitle {
  color: white;
  font-size: 1.75rem;
  margin-bottom: 2rem;
  max-width: 85%;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
}

.banner-button {
  display: inline-block;
  background-color: var(--color-primary, #4f46e5);
  color: var(--color-primary-text, white);
  padding: 1rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.banner-button:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Медиа запросы для адаптивности */
@media (max-width: 768px) {
  .top-banner {
    height: 26rem;
  }

  .bottom-banner {
    height: 15rem;
  }

  .banner-title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .banner-subtitle {
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
    max-width: 90%;
  }

  .banner-button {
    padding: 0.75rem 1.75rem;
    font-size: 1rem;
  }
}

/* Дополнительные оптимизации для малых экранов */
@media (max-width: 480px) {
  .top-banner {
    height: 22rem;
  }

  .bottom-banner {
    height: 12rem;
  }

  .banner-title {
    font-size: 1.75rem;
    margin-bottom: 0.75rem;
  }

  .banner-subtitle {
    font-size: 1rem;
    max-width: 95%;
    margin-bottom: 1.25rem;
  }

  .banner-button {
    padding: 0.625rem 1.5rem;
    font-size: 0.875rem;
  }

  .banner-content {
    padding: 0 1rem;
  }
}

/* РАДИКАЛЬНОЕ РЕШЕНИЕ - закрываем любые отступы после баннера */
.banner-section-wrapper * {
  margin-bottom: 0 !important;
}

.bottom-banner-container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  border-bottom: 0 !important;
}

/* Для баннера в секции бестселлеров */
.banner-bestsellers-section .banner-section-wrapper {
  margin-bottom: -1px !important; /* Негативный отступ снизу для перекрытия */
}

/* Настройка прямого соединения баннера с бестселлерами */
.banner-bestsellers-section .banner-section-wrapper + .container {
  padding-top: 0 !important;
  margin-top: 30px !important;
}

/* Исправление для устранения пробела */
.banner-bestsellers-section .bottom-banner {
  border-bottom: none !important;
  margin-bottom: -1px !important;
}

@media (max-width: 768px) {
  .banner-bestsellers-section > .container {
    padding-top: 0 !important;
    margin-top: 30px !important;
  }
}

@media (max-width: 480px) {
  .banner-bestsellers-section > .container {
    padding-top: 0 !important;
    margin-top: 30px !important;
  }
}

/* Стили для улучшенных слайдеров */
.product-slider-with-external-arrows {
  position: relative;
}

.product-slider-with-external-arrows .slick-arrow {
  display: none !important; /* Скрываем стандартные стрелки */
}

.product-slider .slick-dots {
  bottom: -50px;
}

.product-slider .slick-dots li button:before {
  color: #4f46e5;
  font-size: 12px;
}

.product-slider .slick-dots li.slick-active button:before {
  color: #4f46e5;
  opacity: 1;
}

/* Адаптивные отступы для стрелок */
@media (max-width: 1024px) {
  .product-slider {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .product-slider .absolute.left-\[-50px\] {
    left: -30px;
  }

  .product-slider .absolute.right-\[-50px\] {
    right: -30px;
  }
}

@media (max-width: 768px) {
  .product-slider {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .product-slider .absolute.left-\[-50px\] {
    left: -15px;
    padding: 0.5rem;
  }

  .product-slider .absolute.right-\[-50px\] {
    right: -15px;
    padding: 0.5rem;
  }
}
