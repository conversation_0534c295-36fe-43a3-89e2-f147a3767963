/* Import the required fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  /* Градиенты */
  --primary-gradient: linear-gradient(135deg, #d4b27d 0%, #c5a36e 100%);
  --secondary-gradient: linear-gradient(135deg, #ede4d8 0%, #e5dac8 100%);
  --accent-gradient: linear-gradient(135deg, #d4b27d 0%, #c5a36e 100%);
  --glass-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

  /* Анимации */
  --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Transitions */
  --transition-speed: 0.3s;

  /* Colors */
  --color-background: #f5f5f5;
  --color-cardBackground: #ede4d8;
  --color-sectionBackground: #ede4d8;
  --color-primary: #d4b27d;
  --color-primaryDark: #c5a36e;
  --color-primaryText: #ffffff;
  --color-heading: #000000;
  --color-body: #333333;
  --color-muted: #666666;
  --color-star: #d4b27d;
  --color-footerBg: #f5f5f5;
  --color-footerText: #333333;
  --color-border: #cccccc;
  --color-shadow: rgba(0, 0, 0, 0.05);

  /* Font sizes */
  --font-size-h1: 3rem;
  --font-size-h2: 2.25rem;
  --font-size-h3: 1.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-sm: 0.875rem;
}

body {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  color: var(--color-body);
  background-color: var(--color-background);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family:
    'Poppins',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  color: var(--color-heading);
}

h1 {
  font-size: var(--font-size-h1);
}
h2 {
  font-size: var(--font-size-h2);
}
h3 {
  font-size: var(--font-size-h3);
}

/* Общие стили */
.btn {
  @apply px-6 py-2 rounded-lg transition-all duration-300;
  background-color: var(--color-primary);
  color: var(--color-primaryText);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: var(--color-primaryDark);
}

.card {
  @apply rounded-xl overflow-hidden transition-all duration-300;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

/* Dark mode styles */
.dark body {
  background-color: var(--background-dark);
  color: var(--text-dark);
}

/* Dark theme styles */
.dark {
  --color-background: #27241d;
  --color-cardBackground: #342f26;
  --color-sectionBackground: #342f26;
  --color-heading: #f5f5f5;
  --color-body: #e5e7eb;
  --color-muted: #9ca3af;
}

/* Микроанимации */
.hover-scale {
  transition: transform 0.2s var(--bounce);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-elevate {
  transition: all 0.3s var(--smooth);
}

.hover-elevate:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

/* Glass Effect */
.glass {
  background: var(--glass-gradient);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Skeleton Styles */
.skeleton-wrapper {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

/* Дополнительные стили для современного дизайна */
.bg-primary\/10 {
  background-color: rgba(var(--color-primary), 0.1);
}

/* Улучшенные стили кнопок */
.btn-primary {
  @apply bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg;
}

/* Button variants */
.btn-text {
  @apply text-primary hover:text-primary-dark font-medium transition-colors duration-300;
  text-decoration: none;
}

.btn-text:hover {
  text-decoration: underline;
}

.btn-icon {
  @apply inline-flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white hover:bg-primary-dark transition-colors duration-300;
}

/* Button with icon */
.btn-with-icon {
  @apply inline-flex items-center justify-center gap-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors duration-300;
}

/* Стилизация карточек товаров */
.product-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2;
}

/* Улучшенные стили для категорий */
.category-card {
  @apply relative overflow-hidden rounded-lg shadow-md transition-transform duration-500 hover:shadow-xl;
}

.category-card img {
  @apply transition-transform duration-700 w-full h-full object-cover;
}

.category-card:hover img {
  @apply transform scale-110;
}

.category-card-overlay {
  @apply absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end p-6;
}

/* Анимации для элементов */
.fade-in {
  animation: fadeIn 0.8s ease-in forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
