@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and Base Styles */
body {
  font-family: 'Inter', sans-serif;
  background-color: theme('colors.background');
  color: theme('colors.body');
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  max-width: 100%;
  padding-top: 76px; /* Высота хедера + дополнительный отступ */
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
}

/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: theme('colors.heading');
}

h1 {
  font-size: theme('fontSize.h1');
}
h2 {
  font-size: theme('fontSize.h2');
}
h3 {
  font-size: theme('fontSize.h3');
}

/* Links */
a {
  color: theme('colors.primary');
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: theme('colors.primary-dark');
}

/* Buttons */
.btn-primary {
  background-color: theme('colors.primary');
  color: theme('colors.primaryText');
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  font-weight: 600;
}

.btn-primary:hover {
  background-color: theme('colors.primary-dark');
  transform: translateY(-2px);
  box-shadow: 0 4px 6px theme('colors.shadow');
}

.btn-secondary {
  background-color: theme('colors.sectionBackground');
  color: theme('colors.body');
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  border: 1px solid theme('colors.border');
}

.btn-secondary:hover {
  background-color: #e5dac8;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px theme('colors.shadow');
}

/* Outline button */
.btn-outline {
  background-color: transparent;
  color: theme('colors.primary');
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  border: 1px solid theme('colors.primary');
}

.btn-outline:hover {
  background-color: rgba(212, 178, 125, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px theme('colors.shadow');
}

/* Small button variant */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Large button variant */
.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Inputs */
input,
textarea {
  border: none;
  border-bottom: 1px solid theme('colors.muted');
  padding: 0.5rem;
  background: transparent;
  width: 100%;
  transition: border-color 0.2s ease;
}

input:focus,
textarea:focus {
  outline: none;
  border-bottom-color: theme('colors.primary');
}

/* Cards */
.card {
  background-color: theme('colors.cardBackground');
  border: 1px solid theme('colors.border');
  border-radius: 0.375rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px theme('colors.shadow'); /* Now this will work with the updated config */
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

th {
  background-color: #f3f4f6;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 640px) {
  h1 {
    font-size: 1.5rem;
  }

  .container {
    padding: 1rem;
  }

  img {
    max-width: 100%;
    height: auto;
  }
}

/* В вашем CSS-файле (например, index.css) */
.modal-enter {
  opacity: 0;
}
.modal-enter-active {
  opacity: 1;
  transition: opacity 300ms ease-in;
}
.modal-exit {
  opacity: 1;
}
.modal-exit-active {
  opacity: 0;
  transition: opacity 300ms ease-out;
}

.modal-content-enter {
  transform: scale(0.9);
}
.modal-content-enter-active {
  transform: scale(1);
  transition: transform 300ms ease-in;
}
.modal-content-exit {
  transform: scale(1);
}
.modal-content-exit-active {
  transform: scale(0.9);
  transition: transform 300ms ease-out;
}

/* Стили для баннеров */
.banner-top,
.banner-bottom {
  position: relative;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.banner-top::before,
.banner-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  z-index: 1;
  pointer-events: none;
}

/* Улучшение шрифтов для баннеров */
.font-heading {
  font-family:
    'Poppins',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  letter-spacing: -0.02em;
  font-weight: 700;
}

.font-body {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 400;
}

/* Эффект подсветки текста для лучшей читаемости */
.text-white {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.text-black {
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.3);
}

/* Стили для баннеров на всю ширину экрана */
.full-width-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

.full-width-banner {
  width: 100vw !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  box-sizing: border-box;
}

/* Обновленные стили для полноэкранных баннеров */
.full-width-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow: hidden;
}

/* Стили для баннеров по позициям */
.banner-top,
.banner-bottom {
  width: 100vw !important;
  max-width: 100vw !important;
  position: relative;
  background-size: cover;
  background-position: center;
  overflow: visible;
}

/* Линейный градиент поверх фонового изображения */
.banner-top::before,
.banner-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
  z-index: 5;
  pointer-events: none;
}

/* Специфичные стили для каждой позиции */
.banner-top {
  margin-bottom: 2rem;
}

.banner-bottom {
  margin-top: 2rem;
  margin-bottom: 0;
}

/* Улучшенные стили для заголовков баннера */
.banner-top h2,
.banner-bottom h2 {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  font-family: 'Montserrat', system-ui, sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Улучшенные стили для текста баннера */
.banner-top p,
.banner-bottom p {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
  font-family: 'Open Sans', system-ui, sans-serif;
  font-weight: 400;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Исправление для мобильных устройств */
@media (max-width: 640px) {
  .full-width-wrapper {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    left: 0;
    right: 0;
  }

  .banner-top,
  .banner-bottom {
    width: 100% !important;
    margin-left: 0;
    margin-right: 0;
  }
}

/* Усиленные стили для баннеров на всю ширину экрана */
.banner-container {
  position: relative;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.banner-top,
.banner-bottom {
  position: relative;
  width: 100vw !important;
  max-width: 100vw !important;
  margin-left: calc(50% - 50vw) !important;
  margin-right: calc(50% - 50vw) !important;
  box-sizing: border-box;
}

.banner-top::before,
.banner-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.2));
  z-index: 1;
  pointer-events: none;
}

/* Улучшенные шрифты для баннеров */
.font-heading {
  font-family:
    'Montserrat',
    'Roboto',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  letter-spacing: -0.02em;
  font-weight: 700;
}

.font-body {
  font-family:
    'Open Sans',
    'Roboto',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 400;
}

/* Улучшенная контрастность текста */
.text-white {
  color: #ffffff !important;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.7) !important;
}

.text-black {
  color: #000000 !important;
  text-shadow: 0 2px 6px rgba(255, 255, 255, 0.5) !important;
}

/* Дополнительный стиль для мобильных устройств */
@media (max-width: 640px) {
  .banner-top,
  .banner-bottom {
    margin-left: -1rem !important;
    margin-right: -1rem !important;
    width: calc(100% + 2rem) !important;
  }
}

/* Новые улучшенные стили для полноэкранных баннеров */
.full-screen-banner-container {
  display: block !important;
  width: 100vw !important;
  max-width: 100vw !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

.full-screen-banner {
  width: 100vw !important;
  max-width: 100vw !important;
  left: 50% !important;
  right: 50% !important;
  margin-left: -50vw !important;
  margin-right: -50vw !important;
  position: relative !important;
}

/* Специфические стили для верхнего и нижнего баннеров */
.top-banner {
  margin-top: -1px !important;
}

.bottom-banner {
  margin-bottom: 0 !important; /* Убираем отступ снизу у нижнего баннера */
}

/* Специфические стили для баннеров на мобильных устройствах */
@media (max-width: 767px) {
  .full-screen-banner {
    width: 100% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    left: 50% !important;
    right: 50% !important;
  }
}

/* Новое решение для гарантированной полной ширины */
.absolute-full-width-container {
  position: relative;
  width: 100%;
  overflow-x: visible;
  z-index: 1;
}

.full-width-banner {
  position: relative;
  width: 100vw;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow: hidden;
  box-sizing: border-box;
  z-index: 1;
}

.banner-outer-container {
  position: relative;
  overflow-x: visible;
  width: 100%;
  margin: 0;
  padding: 0;
  z-index: 1;
}

/* Full-width banner styles - new enhanced version */
.full-width-banner-wrapper {
  position: relative;
  width: 100vw;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow-x: hidden;
  z-index: 1;
}

.top-banner {
  position: relative;
  width: 100vw;
  margin-bottom: 2rem;
}

.top-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3));
  z-index: 1;
  pointer-events: none;
}

/* Make banner text more dramatic for the taller banner */
.top-banner h2 {
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  letter-spacing: -0.02em;
}

.top-banner p {
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

/* Updated styles for both top and bottom banners */
.top-banner,
.bottom-banner {
  position: relative;
  width: 100vw;
  margin-bottom: 2rem;
}

.top-banner::before,
.bottom-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3));
  z-index: 1;
  pointer-events: none;
}

/* Different styling for each banner type */
.top-banner {
  /* Taller banner for top position */
  margin-bottom: 3rem;
}

.bottom-banner {
  /* Slightly different gradient for bottom banner */
  margin-top: 3rem;
  margin-bottom: 0; /* Убираем отступ снизу у нижнего баннера */
}

.bottom-banner::before {
  /* Slightly less dark gradient for bottom banner */
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
}

/* Make banner text more dramatic for both banners */
.top-banner h2,
.bottom-banner h2 {
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  letter-spacing: -0.02em;
}

.top-banner p,
.bottom-banner p {
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

/* Custom Toastify Styles */
.custom-toast-container .Toastify__toast {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-family: inherit;
  padding: 10px 16px;
  margin-top: 10px;
}

.custom-toast-container .Toastify__toast--success {
  background-color: rgba(16, 185, 129, 0.9);
  color: white;
}

.custom-toast-container .Toastify__toast--error {
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
}

.custom-toast-container .Toastify__toast--info {
  background-color: rgba(59, 130, 246, 0.9);
  color: white;
}

.custom-toast-container .Toastify__toast--warning {
  background-color: rgba(245, 158, 11, 0.9);
  color: white;
}

.custom-toast-container .Toastify__toast-body {
  font-size: 0.875rem;
  line-height: 1.3;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
}

.custom-toast-container .Toastify__toast-icon {
  margin-right: 8px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.custom-toast-container .Toastify__progress-bar {
  height: 3px;
}

.custom-toast-container .Toastify__close-button {
  color: white;
  opacity: 0.6;
  align-self: center;
  padding: 0 4px;
  margin-left: 8px;
}

.custom-toast-container .Toastify__close-button:hover {
  opacity: 0.9;
}

/* Ensure the container itself has some padding from the edge if toasts are edge-aligned */
.Toastify__toast-container {
  padding: 10px;
}

/* If you are using a dark theme, you might need specific overrides */
/* Example for dark theme text color on info/warning toasts if they were light */
/*
.dark .custom-toast-container .Toastify__toast--info,
.dark .custom-toast-container .Toastify__toast--warning {
  background-color: #374151; 
  color: #f3f4f6; 
}
.dark .custom-toast-container .Toastify__close-button {
  color: #f3f4f6;
}
*/

/* Глобальные стили для хлебных крошек и отступов верхней части страницы */
.breadcrumb-container,
.bg-gray-100,
.bg-gray-50 {
  padding-top: 0.5rem;
}

/* Верхний отступ для всех основных контейнеров страниц, кроме главной */
.container {
  padding-top: 2rem;
}

/* Для главной страницы убираем отступ между хедером и баннером */
.home-page .container:first-of-type {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* Убираем отступы для содержимого на главной странице */
.home-page {
  margin-top: -60px !important; /* Компенсируем отступ body для главной страницы */
  padding-top: 0 !important;
}

/* Убираем отступ для всех баннеров на главной странице */
.banner-section-wrapper,
.full-width-banner,
.top-banner,
.banner-image-container {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Стили для работы с фиксированным хедером */
header.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 50;
}

/* Центровка контента в хедере по вертикали */
header.fixed-header .container {
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  height: 60px;
}

/* Дополнительный отступ под фиксированным хедером */
body {
  padding-top: 60px; /* Высота хедера */
}

/* Специальный стиль для страницы с баннером наверху */
body.has-top-banner {
  padding-top: 60px; /* Сохраняем отступ только для контента, но не для баннера */
}

/* Дополнительный отступ на страницах для мобильных устройств */
@media (max-width: 767px) {
  /* Меньший отступ на мобильных устройствах */
  body {
    padding-top: 60px;
  }

  /* Убираем отступ для главной страницы */
  .home-page .container:first-of-type {
    padding-top: 0 !important;
  }

  /* Уменьшенная высота хедера на мобильных устройствах */
  header.fixed-header {
    min-height: 60px;
  }

  /* Уменьшенные размеры в мобильном хедере */
  header.fixed-header .container {
    padding-top: 0;
    padding-bottom: 0;
    height: 60px;
  }

  header.fixed-header .font-heading {
    font-size: 1.25rem;
  }

  .container {
    padding-top: 1.5rem;
  }

  /* Специфичный селектор для страницы продукта */
  .product-page-container {
    padding-top: 3.5rem !important;
    padding-bottom: 5rem !important; /* Отступ снизу для sticky-кнопки */
  }

  /* Специфичный селектор для страницы категорий */
  .category-page-container {
    padding-top: 1.5rem !important;
  }

  /* Специальные стили для хлебных крошек на странице категорий */
  .category-breadcrumbs {
    margin-top: 1rem !important;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    background-color: transparent !important; /* Убираем фон у хлебных крошек */
  }

  /* Улучшенные стили для хлебных крошек */
  div[aria-label='Breadcrumb'],
  .breadcrumbs,
  .flex.items-center.text-sm.text-gray-600 {
    padding-top: 1.2rem;
    padding-bottom: 0.8rem;
    background-color: transparent; /* Убираем фон у хлебных крошек */
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    z-index: 5;
    position: relative;
    font-size: 0.875rem;
  }

  /* Скрыть скроллбар для хлебных крошек на Webkit браузерах */
  div[aria-label='Breadcrumb']::-webkit-scrollbar,
  .breadcrumbs::-webkit-scrollbar,
  .flex.items-center.text-sm.text-gray-600::-webkit-scrollbar {
    display: none;
  }
}

/* Утилитарные классы для текста */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.break-words {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Стили для страницы заказа */
@media (max-width: 767px) {
  /* Улучшенные стили для страницы заказа на мобильных устройствах */
  .order-item {
    display: flex;
    flex-direction: column;
  }

  .order-item-content {
    flex: 1;
    min-width: 0;
  }

  .order-item-title {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    word-break: break-word;
  }

  .order-item-price {
    margin-top: 0.5rem;
    font-weight: 600;
  }

  /* Улучшенные стили для главной страницы на мобильных устройствах */
  .home-page h2 {
    font-size: 1.5rem;
    line-height: 1.3;
    margin-bottom: 0.5rem;
  }

  .home-page h3 {
    font-size: 1.125rem;
    line-height: 1.4;
  }

  .home-page .section-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  /* Оптимизация отступов для секций на главной странице */
  .home-page section {
    margin-bottom: 2rem;
  }

  .home-page .py-16 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .home-page .my-12 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  /* Уменьшение размера иконок на мобильных */
  .home-page svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  /* Улучшение отображения карточек преимуществ */
  .home-page .bg-gray-50 {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }

  /* Подгонка высоты контейнеров баннеров */
  .home-page .banner-section-wrapper {
    margin-bottom: 1.5rem;
  }
}

/* Упрощенное решение для отступов между баннером и бестселлерами */
.banner-bestsellers-section {
  position: relative;
}

.banner-bestsellers-section .bottom-banner-container {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}


