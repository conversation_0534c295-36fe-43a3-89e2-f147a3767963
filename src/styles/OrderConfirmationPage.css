.cart-item-summary {
  display: flex;
  align-items: flex-start; /* Выравнивание по верху, если контент разной высоты */
}

.cart-item-details {
  flex-grow: 1; /* Позволяет этому блоку занимать доступное пространство */
  margin-right: 16px; /* Отступ справа от цены */
  max-width: calc(
    100% - 100px
  ); /* Ограничение ширины, чтобы цена не выталкивалась. 100px - примерная ширина блока с ценой и отступов */
}

.item-name {
  word-break: break-word; /* Перенос длинных слов */
  white-space: normal; /* Разрешает перенос текста на новую строку */
  font-size: 1rem; /* Уменьшим немного шрифт для лучшего вида */
  line-height: 1.4;
}

.cart-item-price {
  flex-shrink: 0; /* Предотвращает сжатие блока с ценой */
  white-space: nowrap; /* Чтобы цена не переносилась */
  margin-left: auto; /* Прижимает цену к правому краю, если .cart-item-details не занимает всё место */
  text-align: right;
}

/* Стили для формы и других элементов страницы, если нужно */
.order-confirmation-container {
  max-width: 900px; /* Ограничиваем общую ширину контейнера страницы */
  margin: 0 auto;
}

/* Адаптивность для маленьких экранов */
@media (max-width: 600px) {
  .cart-item-summary {
    flex-direction: column; /* На маленьких экранах элементы в столбик */
    align-items: stretch; /* Растягиваем элементы на всю ширину */
  }

  .cart-item-details {
    margin-right: 0;
    margin-bottom: 8px; /* Добавляем отступ снизу, когда элементы в столбик */
    max-width: 100%; /* На маленьких экранах детали могут занимать всю ширину */
  }

  .cart-item-price {
    text-align: left; /* На маленьких экранах цена слева */
    margin-left: 0;
  }

  .item-name {
    font-size: 0.9rem; /* Еще немного уменьшим шрифт на мобильных */
  }
}
