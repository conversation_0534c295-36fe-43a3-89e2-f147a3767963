/* Hero Banner Slider Styles */

.hero-banner-slider {
  position: relative;
  width: 100vw;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 1;
}

.top-banner-slider {
  height: 100vh;
  min-height: 600px;
  max-height: 800px;
}

.bottom-banner-slider {
  height: 60vh;
  min-height: 400px;
  max-height: 600px;
}

.hero-banner-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Фоновое изображение с эффектами */
.hero-banner-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  z-index: 1;
  will-change: transform;
}

.hero-banner-image {
  position: absolute;
  top: -10%;
  left: 0;
  width: 100%;
  height: 120%;
  object-fit: cover;
  object-position: center;
  filter: brightness(0.8) contrast(1.1);
  transition: all 0.3s ease;
}

/* Эффект затемнения */
.hero-banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 2;
}

/* Контент баннера */
.hero-banner-content {
  position: relative;
  z-index: 3;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.hero-banner-text {
  text-align: center;
  color: white;
  max-width: 800px;
  margin: 0 auto;
}

.hero-banner-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
  letter-spacing: -0.02em;
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
    transform: scale(1);
  }
  100% {
    text-shadow: 3px 3px 12px rgba(255, 255, 255, 0.3);
    transform: scale(1.02);
  }
}

.hero-banner-subtitle {
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 1.4;
  margin-bottom: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  opacity: 0.95;
}

.hero-banner-button {
  display: inline-block;
  padding: 1rem 2.5rem;
  background: linear-gradient(45deg, #6366f1, #8b5cf6);
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  text-decoration: none;
  border-radius: 50px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.hero-banner-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
  z-index: -1;
}

.hero-banner-button:hover::before {
  left: 100%;
}

.hero-banner-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 32px rgba(99, 102, 241, 0.6);
  background: linear-gradient(45deg, #7c3aed, #6366f1);
}

/* Навигационные стрелки */
.hero-banner-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  z-index: 4;
  opacity: 0.8;
}

.hero-banner-nav:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
}

.hero-banner-nav-prev {
  left: 2rem;
}

.hero-banner-nav-next {
  right: 2rem;
}

/* Индикаторы слайдов */
.hero-banner-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 4;
}

.hero-banner-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.hero-banner-indicator.active {
  background: #6366f1;
  border-color: white;
  transform: scale(1.3);
}

.hero-banner-indicator::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.hero-banner-indicator.active::after {
  border-color: rgba(99, 102, 241, 0.4);
}

/* Кнопка воспроизведения/паузы */
.hero-banner-play-pause {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  z-index: 4;
  opacity: 0.8;
}

.hero-banner-play-pause:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
  opacity: 1;
}

/* Эффект прилипания (sticky) */
.hero-banner-slider.sticky {
  position: sticky;
  top: 0;
  z-index: 50;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Состояние загрузки */
.hero-banner-loading {
  width: 100vw;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  height: 400px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.loading-spinner {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.loading-spinner::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Адаптивность для планшетов */
@media (max-width: 1024px) {
  .top-banner-slider {
    height: 80vh;
    min-height: 500px;
  }
  
  .bottom-banner-slider {
    height: 50vh;
    min-height: 350px;
  }

  .hero-banner-title {
    font-size: 2.8rem;
  }

  .hero-banner-subtitle {
    font-size: 1.3rem;
  }

  .hero-banner-nav {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .hero-banner-nav-prev {
    left: 1rem;
  }

  .hero-banner-nav-next {
    right: 1rem;
  }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .top-banner-slider {
    height: 70vh;
    min-height: 400px;
  }
  
  .bottom-banner-slider {
    height: 40vh;
    min-height: 300px;
  }

  .hero-banner-content {
    padding: 1rem;
  }

  .hero-banner-title {
    font-size: 2.2rem;
    margin-bottom: 1rem;
  }

  .hero-banner-subtitle {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .hero-banner-button {
    padding: 0.875rem 2rem;
    font-size: 1rem;
  }

  .hero-banner-nav {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .hero-banner-indicators {
    bottom: 1rem;
    gap: 0.75rem;
  }

  .hero-banner-indicator {
    width: 10px;
    height: 10px;
  }

  .hero-banner-play-pause {
    width: 40px;
    height: 40px;
    bottom: 1rem;
    right: 1rem;
    font-size: 0.875rem;
  }
}

/* Адаптивность для маленьких экранов */
@media (max-width: 480px) {
  .top-banner-slider {
    height: 60vh;
    min-height: 350px;
  }
  
  .bottom-banner-slider {
    height: 35vh;
    min-height: 250px;
  }

  .hero-banner-title {
    font-size: 1.8rem;
  }

  .hero-banner-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .hero-banner-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .hero-banner-nav-prev {
    left: 0.5rem;
  }

  .hero-banner-nav-next {
    right: 0.5rem;
  }
}

/* Дополнительные стили для улучшенных эффектов */

/* Sticky эффект */
.hero-banner-sticky {
  position: sticky !important;
  top: 0 !important;
  z-index: 50 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Анимация появления элементов */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Улучшенный параллакс эффект */
.hero-banner-background {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Плавные переходы для всех интерактивных элементов */
.hero-banner-slider * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Эффект при наведении на слайдер */
.hero-banner-slider:hover .hero-banner-nav {
  opacity: 1;
  transform: translateY(-50%) scale(1.05);
}

.hero-banner-slider:hover .hero-banner-indicators {
  opacity: 1;
}

.hero-banner-slider:hover .hero-banner-play-pause {
  opacity: 1;
}

/* Улучшенная адаптивность для сенсорных устройств */
@media (hover: none) and (pointer: coarse) {
  .hero-banner-nav {
    opacity: 1;
  }
  
  .hero-banner-indicators {
    opacity: 1;
  }
  
  .hero-banner-play-pause {
    opacity: 1;
  }
}

/* Оптимизация для высокочастотных дисплеев */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-banner-image {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Темная тема */
@media (prefers-color-scheme: dark) {
  .hero-banner-overlay {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.7) 0%,
      rgba(0, 0, 0, 0.5) 50%,
      rgba(0, 0, 0, 0.6) 100%
    );
  }
  
  .hero-banner-nav {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .hero-banner-indicator {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }
}

/* Reduced motion для пользователей с особыми потребностями */
@media (prefers-reduced-motion: reduce) {
  .hero-banner-slider *,
  .hero-banner-slider *::before,
  .hero-banner-slider *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .hero-banner-background {
    will-change: auto;
  }
}

/* Улучшенная поддержка RTL */
[dir="rtl"] .hero-banner-nav-prev {
  right: 2rem;
  left: auto;
}

[dir="rtl"] .hero-banner-nav-next {
  left: 2rem;
  right: auto;
}

/* Печать */
@media print {
  .hero-banner-slider {
    height: auto !important;
    min-height: auto !important;
    max-height: 300px !important;
    page-break-inside: avoid;
  }
  
  .hero-banner-nav,
  .hero-banner-indicators,
  .hero-banner-play-pause {
    display: none !important;
  }
  
  .hero-banner-overlay {
    background: rgba(0, 0, 0, 0.3) !important;
  }
}
