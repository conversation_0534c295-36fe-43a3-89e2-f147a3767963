import React, { lazy, Suspense, useEffect, useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom'; // Changed Route to Outlet
import { AuthProvider } from './context/AuthContext';
import { CartProvider } from './context/CartContext';
import { WishlistProvider } from './context/WishlistContext';
import { CompareProvider } from './context/CompareContext';
import { ThemeProvider } from './context/ThemeContext';
import { LoadingProvider } from './context/LoadingContext';
import Header from './components/Header';
// import LoadingSpinner from './components/LoadingSpinner'; // Unused
import SiteLoader from './components/ui/SiteLoader';
import { _ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './styles/theme.css';
import { HelmetProvider } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Slide, toast } from 'react-toastify';
import { ScrollToTop } from './router';

// Use lazy loading for heavy components
const Footer = lazy(() => import('./components/Footer'));

function AppContent() {
  const location = useLocation();
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [isRouteChanging, setIsRouteChanging] = useState(false);
  const [prevLocation, setPrevLocation] = useState('');

  // Отслеживание изменения маршрута
  useEffect(() => {
    if (prevLocation !== '' && prevLocation !== location.pathname) {
      setIsRouteChanging(true);
      // Увеличиваем время показа прелоадера при переключении страниц
      // Особенно для страниц категорий, которые требуют больше времени для загрузки
      const routeDelay = location.pathname.includes('/category/') ? 1500 : 800;

      setTimeout(() => {
        setIsRouteChanging(false);
      }, routeDelay);
    }

    setPrevLocation(location.pathname);
  }, [location.pathname, prevLocation]);

  useEffect(() => {
    // Функция для отложенного скрытия прелоадера
    const hidePreloader = () => {
      // Добавляем небольшую задержку чтобы прелоадер не исчезал слишком быстро
      setTimeout(() => {
        setIsPageLoaded(true);
        // Дополнительная задержка перед полным удалением прелоадера для анимации исчезновения
        setTimeout(() => {
          setInitialLoadComplete(true);
        }, 300);
      }, 800);
    };

    // Используем requestIdleCallback для загрузки в фоновом режиме, если он доступен
    const idleCallback = window.requestIdleCallback || (cb => setTimeout(cb, 1));

    // Ждем загрузки шрифтов и основных ресурсов
    if ('fonts' in document) {
      document.fonts.ready
        .then(() => {
          idleCallback(hidePreloader);
        })
        .catch(() => {
          idleCallback(hidePreloader);
        });
    } else {
      window.addEventListener('load', () => idleCallback(hidePreloader));
    }

    return () => {
      if (!('fonts' in document)) {
        window.removeEventListener('load', hidePreloader);
      }
    };
  }, []);

  // Определяем, нужно ли показывать прелоадер
  const showPreloader = !initialLoadComplete || isRouteChanging;

  // Проверяем, находимся ли мы на главной странице
  const isHomePage = location.pathname === '/';

  // Определяем класс padding-top в зависимости от страницы
  const mainPaddingTopClass = isHomePage ? 'pt-16' : 'pt-20';

  return (
    <>
      <ScrollToTop />
      {/* Показываем SiteLoader при начальной загрузке или при смене страниц */}
      {showPreloader && <SiteLoader />}

      <div
        className={`app ${isPageLoaded ? 'page-loaded' : 'page-loading'} flex flex-col min-h-screen`}
      >
        <Header />
        <main className={`flex-grow container mx-auto px-4 ${mainPaddingTopClass}`}>
          <Suspense fallback={<div style={{ display: 'none' }} />}>
            <Outlet /> {/* Replaced Routes with Outlet */}
          </Suspense>
        </main>
        <Suspense fallback={<div style={{ display: 'none' }} />}>
          <Footer />
        </Suspense>
      </div>
    </>
  );
}

function App() {
  return (
    <HelmetProvider>
      <LoadingProvider>
        <AuthProvider>
          <CartProvider>
            <WishlistProvider>
              <CompareProvider>
                <ThemeProvider>
                  <AppContent />
                </ThemeProvider>
              </CompareProvider>
            </WishlistProvider>
          </CartProvider>
        </AuthProvider>
      </LoadingProvider>
    </HelmetProvider>
  );
}

export default App;
