import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { I18nextProvider } from 'react-i18next';
import { store } from './store/store';
import i18n from './i18n';
import './styles/index.css'; // Import Tailwind directives
import './styles/custom.css'; // Import custom styles separately
// Import environment check early
import './checkEnv';
import { ErrorBoundary } from 'react-error-boundary';
import DataError from './components/DataError';
import { useTranslation } from 'react-i18next'; // Already here, but useTranslation is only used in ErrorFallback
import { RouterProvider } from 'react-router-dom';
import { router } from './router'; // Import the router from router.js instead

// Added imports from index.tsx
import { AuthProvider } from './context/AuthContext';
import { CartProvider } from './context/CartContext';
import { WishlistProvider } from './context/WishlistContext';
import { CompareProvider } from './context/CompareContext';
import { ToastContainer, Slide } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { HelmetProvider } from 'react-helmet-async';
import { supabase } from './supabaseClient';

// Регистрация сервис-воркера с улучшенной обработкой ошибок
const registerServiceWorker = () => {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker
        .register('/serviceWorker.js')
        .then(registration => {
          console.log('Service Worker зарегистрирован успешно:', registration.scope);

          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              console.log('Новая версия Service Worker устанавливается...');
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  console.log('Новая версия Service Worker готова');
                  // Можно показать уведомление пользователю о обновлении
                }
              });
            }
          });
        })
        .catch(error => {
          console.error('Ошибка регистрации сервис-воркера:', error);
          // Приложение должно работать и без service worker
        });

      // Слушаем изменения контроллера
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker контроллер изменился');
        // Можно перезагрузить страницу если нужно
      });

      // Слушаем ошибки от service worker
      navigator.serviceWorker.addEventListener('error', error => {
        console.error('Service Worker ошибка:', error);
      });
    });
  } else {
    console.log('Service Worker не поддерживается в этом браузере');
  }
};

// Immediately attempt to parse hash parameters from the URL (from index.tsx)
if (window.location.hash && window.location.hash.includes('access_token')) {
  (async () => {
    try {
      const { error } = await supabase.auth.getSession();
      if (error) {
        console.error('Error retrieving session:', error);
      }
    } catch (err) {
      console.error('Error processing authentication:', err);
    }
  })();
}

// Skip rendering the React app completely for API routes
// This prevents React Router from trying to handle API requests
if (window.location.pathname.startsWith('/api/')) {
  // Create a placeholder to avoid errors
  const placeholder = document.createElement('div');
  placeholder.innerHTML = 'API Route - Processing...';
  document.body.appendChild(placeholder);
} else {
  // Add font display optimization
  const fontDisplayStyle = document.createElement('style');
  fontDisplayStyle.textContent = `
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: local('Poppins Regular'), local('Poppins-Regular'), 
           url(https://fonts.gstatic.com/s/poppins/v22/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 500;
      font-display: swap;
      src: local('Poppins Medium'), local('Poppins-Medium'),
           url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLGT9Z1xlFd2JQEk.woff2) format('woff2');
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 600;
      font-display: swap;
      src: local('Poppins SemiBold'), local('Poppins-SemiBold'),
           url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLEj6Z1xlFd2JQEk.woff2) format('woff2');
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      font-display: swap;
      src: local('Poppins Bold'), local('Poppins-Bold'),
           url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }
  `;
  document.head.appendChild(fontDisplayStyle);

  const ErrorFallback = ({ error }) => {
    const { t } = useTranslation(); // t is defined here
    return (
      <DataError
        message={error.message || t('unexpected_error', 'Произошла неожиданная ошибка.')}
      />
    );
  };

  const container = document.getElementById('root');
  if (!container) {
    throw new Error('Failed to find the root element');
  }

  const root = ReactDOM.createRoot(container);
  root.render(
    <React.StrictMode>
      <HelmetProvider>
        {' '}
        {/* Added from index.tsx */}
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <Provider store={store}>
            <I18nextProvider i18n={i18n}>
              <AuthProvider>
                {' '}
                {/* Added from index.tsx */}
                <CartProvider>
                  {' '}
                  {/* Added from index.tsx */}
                  <WishlistProvider>
                    {' '}
                    {/* Added from index.tsx */}
                    <CompareProvider>
                      {' '}
                      {/* Added from index.tsx */}
                      <RouterProvider router={router} />
                      <ToastContainer
                        position="top-right"
                        autoClose={1750}
                        hideProgressBar={false}
                        newestOnTop={false}
                        closeOnClick
                        rtl={false}
                        pauseOnFocusLoss
                        draggable
                        pauseOnHover
                        theme="colored"
                        transition={Slide}
                        className="custom-toast-container"
                        bodyClassName="custom-toast-body"
                        progressClassName="custom-toast-progress"
                      />
                      {/* Added from index.tsx */}
                    </CompareProvider>
                  </WishlistProvider>
                </CartProvider>
              </AuthProvider>
            </I18nextProvider>
          </Provider>
        </ErrorBoundary>
      </HelmetProvider>
    </React.StrictMode>
  );

  // Регистрируем сервис-воркер (from index.tsx)
  registerServiceWorker();
}

// Add offline/online detection
window.addEventListener('online', function () {
  document.body.classList.remove('offline');
  // Notify user they're back online
  if (window.toastify) {
    window.toastify.success('Подключение к интернету восстановлено');
  }
});

window.addEventListener('offline', function () {
  document.body.classList.add('offline');
  // Notify user they're offline
  if (window.toastify) {
    window.toastify.warning('Отсутствует подключение к интернету');
  }
});
