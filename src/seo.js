// Утилиты для генерации SEO метатегов в компонентах
import React from 'react';
import { Helmet } from 'react-helmet-async';

/**
 * Объект с основными SEO настройками сайта
 */
export const siteSettings = {
  siteName: 'Интернет-магазин',
  domain: 'yoursite.com',
  defaultTitle: 'Интернет-магазин - широкий ассортимент товаров',
  defaultDescription:
    'Интернет-магазин с широким ассортиментом товаров для дома, техники и аксессуаров. Доставка по всей стране.',
  defaultKeywords: 'интернет-магазин, товары, доставка, купить онлайн',
  defaultImage: '/logo512.png',
  themeColor: '#ffffff',
  twitterHandle: '@yourstore'
};

/**
 * Компонент для добавления основных мета-тегов SEO
 * @param {Object} props - SEO параметры
 * @returns {JSX.Element} - Helmet компонент с метатегами
 */
export const SEO = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  noindex = false,
  nofollow = false,
  structuredData = null,
  children
}) => {
  const siteUrl = `https://${siteSettings.domain}`;
  const pageUrl = url ? `${siteUrl}${url}` : siteUrl;
  const pageTitle = title ? `${title} - ${siteSettings.siteName}` : siteSettings.defaultTitle;
  const pageDescription = description || siteSettings.defaultDescription;
  const pageKeywords = keywords || siteSettings.defaultKeywords;
  const pageImage = image ? `${siteUrl}${image}` : `${siteUrl}${siteSettings.defaultImage}`;

  const robots = `${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}`;

  return (
    <Helmet>
      {/* Основные метатеги */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={pageKeywords} />
      <meta name="robots" content={robots} />

      {/* Канонический URL */}
      <link rel="canonical" href={pageUrl} />

      {/* Open Graph метатеги для социальных сетей */}
      <meta property="og:site_name" content={siteSettings.siteName} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:image" content={pageImage} />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:type" content={type} />

      {/* Twitter метатеги */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={siteSettings.twitterHandle} />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta name="twitter:image" content={pageImage} />

      {/* Микроразметка для поисковых систем */}
      {structuredData && (
        <script type="application/ld+json">{JSON.stringify(structuredData)}</script>
      )}

      {/* Дополнительные метатеги при необходимости */}
      {children}
    </Helmet>
  );
};

/**
 * Готовые шаблоны для микроразметки schema.org
 */
export const SchemaTemplates = {
  /**
   * Шаблон микроразметки для организации
   */
  organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteSettings.siteName,
    url: `https://${siteSettings.domain}`,
    logo: `https://${siteSettings.domain}/logo.png`,
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+123456789',
      contactType: 'customer service',
      availableLanguage: ['Russian', 'English']
    },
    sameAs: [
      'https://facebook.com/yourstore',
      'https://twitter.com/yourstore',
      'https://instagram.com/yourstore'
    ]
  },

  /**
   * Шаблон микроразметки для товара
   * @param {Object} product - Данные о товаре
   */
  product: product => ({
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.images && product.images[0] ? product.images[0] : siteSettings.defaultImage,
    sku: product.sku || product.id,
    mpn: product.mpn || product.id,
    brand: {
      '@type': 'Brand',
      name: product.brand || siteSettings.siteName
    },
    offers: {
      '@type': 'Offer',
      url: `https://${siteSettings.domain}/product/${product.id}`,
      priceCurrency: 'UAH',
      price: product.price,
      priceValidUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
        .toISOString()
        .split('T')[0],
      availability: product.in_stock
        ? 'https://schema.org/InStock'
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: siteSettings.siteName
      }
    }
  }),

  /**
   * Шаблон микроразметки для поисковой страницы
   * @param {string} query - Поисковый запрос
   */
  searchResults: query => ({
    '@context': 'https://schema.org',
    '@type': 'SearchResultsPage',
    mainEntity: {
      '@type': 'ItemList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          url: `https://${siteSettings.domain}/search?q=${encodeURIComponent(query)}`
        }
      ]
    }
  }),

  /**
   * Шаблон микроразметки для категории
   * @param {Object} category - Данные о категории
   */
  category: category => ({
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: category.name,
    description: category.description || `${category.name} - каталог товаров`,
    url: `https://${siteSettings.domain}/category/${category.slug}`
  }),

  /**
   * Шаблон микроразметки для хлебных крошек
   * @param {Array} breadcrumbs - Массив хлебных крошек
   */
  breadcrumbs: breadcrumbs => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: `https://${siteSettings.domain}${crumb.path}`
    }))
  })
};

export default SEO;
