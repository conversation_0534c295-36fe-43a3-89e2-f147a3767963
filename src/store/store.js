import { configureStore } from '@reduxjs/toolkit';
import productsReducer from './slices/productsSlice';
import cartReducer from './slices/cartSlice';
import wishlistReducer from './slices/wishlistSlice';
import compareReducer from './slices/compareSlice';
import authReducer from './slices/authSlice';

// Middleware для синхронизации wishlist с localStorage
const wishlistLocalStorageMiddleware = store => next => action => {
  const result = next(action);

  // Если действие относится к wishlist, сохраняем обновленное состояние в localStorage
  if (action.type.startsWith('wishlist/')) {
    try {
      const { wishlist } = store.getState();
      localStorage.setItem('wishlist', JSON.stringify(wishlist.items));
    } catch (error) {
      console.error('Error saving wishlist to localStorage:', error);
    }
  }

  return result;
};

// Загружаем wishlist из localStorage при запуске приложения
const loadWishlistFromLocalStorage = () => {
  try {
    const savedWishlist = localStorage.getItem('wishlist');
    if (savedWishlist) {
      return { items: JSON.parse(savedWishlist) };
    }
  } catch (error) {
    console.error('Error loading wishlist from localStorage:', error);
  }
  return { items: [] };
};

export const store = configureStore({
  reducer: {
    products: productsReducer,
    cart: cartReducer,
    wishlist: wishlistReducer,
    compare: compareReducer,
    auth: authReducer
  },
  preloadedState: {
    wishlist: loadWishlistFromLocalStorage()
  },
  middleware: getDefaultMiddleware => getDefaultMiddleware().concat(wishlistLocalStorageMiddleware),
  devTools: process.env.NODE_ENV !== 'production'
});
