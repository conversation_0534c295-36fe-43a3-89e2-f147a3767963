import { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-toastify';

const CompareContext = createContext();

export const CompareProvider = ({ children }) => {
  const [compareItems, setCompareItems] = useState(() => {
    try {
      const savedCompare = localStorage.getItem('compare');
      return savedCompare ? JSON.parse(savedCompare) : [];
    } catch (error) {
      console.error('Error parsing compare from localStorage:', error);
      return [];
    }
  });

  useEffect(() => {
    try {
      localStorage.setItem('compare', JSON.stringify(compareItems));
    } catch (error) {
      console.error('Error saving compare to localStorage:', error);
    }
  }, [compareItems]);

  // Добавляем прослушивание событий storage для синхронизации между вкладками
  useEffect(() => {
    const handleStorageChange = event => {
      if (event.key === 'compare') {
        try {
          const newCompareItems = JSON.parse(event.newValue);
          if (newCompareItems && Array.isArray(newCompareItems)) {
            // Избегаем циклических обновлений, сравнивая текущее состояние
            if (JSON.stringify(newCompareItems) !== JSON.stringify(compareItems)) {
              setCompareItems(newCompareItems);
            }
          }
        } catch (error) {
          console.error('Error parsing compare data from storage event:', error);
        }
      }
    };

    // Добавляем прослушиватель события хранилища
    window.addEventListener('storage', handleStorageChange);

    // Очищаем прослушиватель при размонтировании компонента
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [compareItems]);

  const addToCompare = product => {
    setCompareItems(prevItems => {
      if (!product.id) {
        toast.warn('Не удалось добавить товар в сравнение: отсутствует ID');
        return prevItems;
      }
      if (prevItems.find(item => item.id === product.id)) {
        toast.info(`${product.name} уже в списке сравнения`);
        return prevItems;
      }
      toast.success(`${product.name} добавлен в список сравнения`);
      return [...prevItems, product];
    });
  };

  const removeFromCompare = productId => {
    setCompareItems(prevItems => {
      const itemToRemove = prevItems.find(item => item.id === productId);
      const newItems = prevItems.filter(item => item.id !== productId);
      if (itemToRemove && newItems.length < prevItems.length) {
        toast.error(`${itemToRemove.name} удален из списка сравнения`);
      }
      return newItems;
    });
  };

  const clearCompare = () => {
    if (compareItems.length > 0) {
      toast.info('Список сравнения очищен');
    }
    setCompareItems([]);
  };

  const isInCompare = productId => {
    return compareItems.some(item => item.id === productId);
  };

  return (
    <CompareContext.Provider
      value={{ compareItems, addToCompare, removeFromCompare, clearCompare, isInCompare }}
    >
      {children}
    </CompareContext.Provider>
  );
};

export const useCompare = () => {
  const context = useContext(CompareContext);
  if (!context) {
    throw new Error('useCompare must be used within a CompareProvider');
  }
  return context;
};
