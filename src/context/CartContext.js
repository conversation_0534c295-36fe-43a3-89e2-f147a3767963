import { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { toast } from 'react-toastify';

const CartContext = createContext();

export const CartProvider = ({ children }) => {
  const { user } = useAuth();
  const [cart, setCart] = useState(() => {
    try {
      const savedCart = localStorage.getItem('cart');
      return savedCart ? JSON.parse(savedCart) : [];
    } catch (error) {
      console.error('Error parsing cart from localStorage:', error);
      return [];
    }
  });

  useEffect(() => {
    const syncCartWithFirestore = async () => {
      if (user) {
        try {
          // Removed all Firebase-related logic in syncCartWithFirestore
        } catch (error) {
          console.error('Error syncing cart with Firestore:', error);
        }
      } else {
        const savedCart = JSON.parse(localStorage.getItem('cart') || '[]');
        setCart(savedCart);
      }
    };

    syncCartWithFirestore();
  }, [user]);

  useEffect(() => {
    try {
      localStorage.setItem('cart', JSON.stringify(cart));
      if (user) {
        try {
          // Removed all Firebase-related logic in syncCartWithFirestore
        } catch (error) {
          console.error('Error saving cart:', error);
        }
      }
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  }, [cart, user]);

  const addToCart = item => {
    setCart(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        toast.info(`${item.name} количество обновлено в корзине`);
        return prev.map(cartItem =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + (item.quantity || 1) }
            : cartItem
        );
      }
      toast.success(`${item.name} добавлен в корзину`);
      return [...prev, { ...item, quantity: item.quantity || 1 }];
    });
  };

  const updateQuantity = (id, quantity) => {
    if (quantity <= 0) {
      removeFromCart(id);
    } else {
      setCart(prev =>
        prev.map(item => {
          if (item.id === id) {
            toast.info(`Количество ${item.name} обновлено до ${quantity}`);
            return { ...item, quantity };
          }
          return item;
        })
      );
    }
  };

  const removeFromCart = id => {
    setCart(prev => {
      const itemToRemove = prev.find(item => item.id === id);
      if (itemToRemove) {
        toast.error(`${itemToRemove.name} удален из корзины`);
      }
      return prev.filter(item => item.id !== id);
    });
  };

  // Очистить корзину после оформления
  const clearCart = () => {
    setCart([]);
    toast.info('Корзина очищена');
  };

  return (
    <CartContext.Provider value={{ cart, addToCart, updateQuantity, removeFromCart, clearCart }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
