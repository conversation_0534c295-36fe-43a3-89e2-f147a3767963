import { createContext, useContext, useState, useEffect } from 'react';
import { theme as baseTheme } from '../config/theme';

const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme || 'light';
  });

  const applyTheme = themeName => {
    const root = document.documentElement;

    // Apply theme class
    if (themeName === 'dark') {
      root.classList.add('dark');
      root.style.colorScheme = 'dark';
    } else {
      root.classList.remove('dark');
      root.style.colorScheme = 'light';
    }

    // Apply CSS variables for colors and fonts
    const themeVars = baseTheme.toCssVars();
    Object.entries(themeVars).forEach(([varName, value]) => {
      root.style.setProperty(varName, value);
    });
  };

  useEffect(() => {
    localStorage.setItem('theme', theme);
    applyTheme(theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  return <ThemeContext.Provider value={{ theme, toggleTheme }}>{children}</ThemeContext.Provider>;
};

export const useTheme = () => useContext(ThemeContext);
