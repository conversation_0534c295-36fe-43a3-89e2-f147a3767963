import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '../supabaseClient';

console.log('AuthContext loading. Supabase client:', supabase);
console.log('AuthContext loading. Supabase client type:', typeof supabase);
console.log('AuthContext loading. Supabase client has auth:', !!(supabase && supabase.auth));

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [authInitialized, setAuthInitialized] = useState(false);
  const [sessionChecked, setSessionChecked] = useState(false);

  // Функция для проверки, является ли пользователь админом
  const checkAdminStatus = async userId => {
    if (!userId) {
      setIsAdmin(false);
      return;
    }

    // Guard against undefined supabase client
    if (!supabase || !supabase.from) {
      console.error('❌ Supabase client not available in checkAdminStatus');
      setIsAdmin(false);
      return;
    }

    // Hardcoded admin user IDs - add your ID here after running the set-admin-user.js script
    const adminUserIds = [
      '2aef44bd-57c3-4b0e-a8bb-193cd5ba476e', // Default admin ID
      '6cc2c928-b7eb-4f60-98dc-3b9e89784928'
      // Add your user ID here
    ];

    // First check if user ID is in our hardcoded list (most reliable method)
    if (adminUserIds.includes(userId)) {
      setIsAdmin(true);
      return;
    }

    try {
      // Simplified admin check using profiles table
      const { data, error } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', userId)
        .single();

      if (error) {
        console.log('Admin status check failed (fallback to hardcoded list):', error.message);
        setIsAdmin(false);
        return;
      }

      setIsAdmin(data?.is_admin || false);
    } catch (error) {
      console.error('Exception checking admin status:', error);
      setIsAdmin(false);
    }
  };

  useEffect(() => {
    // Prevent multiple initialization
    if (authInitialized || sessionChecked) {
      return;
    }

    let subscription;

    // Check for active session on mount
    const initializeAuth = async () => {
      try {
        setLoading(true);

        console.log('Initializing auth. Supabase client check:', {
          hasSupabase: !!supabase,
          hasAuth: !!supabase?.auth,
          authMethods: supabase?.auth ? Object.keys(supabase.auth) : 'N/A'
        });

        if (!supabase || !supabase.auth) {
          throw new Error('Supabase client or auth not available');
        }

        const {
          data: { session },
          error
        } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          setUser(null);
          setIsAdmin(false);
        } else {
          const currentUser = session?.user || null;
          setUser(currentUser);

          // Check admin status if user is logged in
          if (currentUser?.id) {
            await checkAdminStatus(currentUser.id);
          } else {
            setIsAdmin(false);
          }
        }

        // Guard check before auth state listener
        if (!supabase || !supabase.auth) {
          console.error('❌ Supabase client not available for auth state listener');
          setLoading(false);
          setSessionChecked(true);
          setAuthInitialized(true);
          return;
        }

        // Listen for auth changes
        const {
          data: { subscription: authSubscription }
        } = supabase.auth.onAuthStateChange(async (event, session) => {
          console.log('Auth state changed:', event, session?.user?.id);

          try {
            const currentUser = session?.user || null;
            setUser(currentUser);

            // Check admin status if user is logged in
            if (currentUser?.id) {
              await checkAdminStatus(currentUser.id);
            } else {
              setIsAdmin(false);
            }
          } catch (error) {
            console.error('Error handling auth state change:', error);
            setUser(null);
            setIsAdmin(false);
          }
        });

        subscription = authSubscription;
      } catch (error) {
        console.error('Error initializing auth:', error);
        setUser(null);
        setIsAdmin(false);
      } finally {
        setLoading(false);
        setAuthInitialized(true);
        setSessionChecked(true);
      }
    };

    initializeAuth();

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  // Sign out function
  const signOut = async () => {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.auth) {
        console.error('❌ Supabase client not available in signOut');
        setUser(null);
        setIsAdmin(false);
        return;
      }

      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setIsAdmin(false); // Сбрасываем статус админа при выходе
    } catch (error) {
      console.error('Error signing out:', error.message);
    }
  };

  // Get user profile data
  const getUserProfile = async () => {
    try {
      if (!user) return null;

      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in getUserProfile');
        return null;
      }

      // Try to get the full profile
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Handle 406 Not Acceptable or other profile errors
      if (error) {
        console.log('Profile not found, using fallback profile:', error.message);

        // Create a fallback profile with basic user data
        const fallbackProfile = {
          id: user.id,
          email: user.email,
          first_name: '',
          last_name: '',
          avatar_url: user.user_metadata?.avatar_url || null,
          is_admin: isAdmin, // Use the admin status from context
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Try to create the profile record silently (without SQL execution)
        if (supabase && supabase.from) {
          supabase
            .from('profiles')
            .upsert(
              {
                id: user.id,
                email: user.email,
                is_admin: isAdmin
              },
              { onConflict: 'id' }
            )
            .then(({ error: upsertError }) => {
              if (upsertError) {
                console.log('Could not create profile record:', upsertError.message);
              }
            });
        }

        return fallbackProfile;
      }

      return data;
    } catch (error) {
      console.error('Error in getUserProfile:', error);
      return null;
    }
  };

  // Update user profile
  const updateUserProfile = async updates => {
    try {
      if (!user) throw new Error('User not authenticated');

      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in updateUserProfile');
        throw new Error('Database connection not available');
      }

      const { data, error } = await supabase.from('profiles').update(updates).eq('id', user.id);

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  };

  // Update user password
  const updatePassword = async newPassword => {
    try {
      if (!user) throw new Error('User not authenticated');
      if (!newPassword) throw new Error('Password cannot be empty');

      // Guard against undefined supabase client
      if (!supabase || !supabase.auth) {
        console.error('❌ Supabase client not available in updatePassword');
        throw new Error('Authentication service not available');
      }

      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;
      // Optionally, re-fetch user or session if needed, though Supabase handles this
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signOut,
    getUserProfile,
    updateUserProfile,
    updatePassword, // Added updatePassword to context
    isLoggedIn: !!user,
    isAdmin // Добавляем статус админа в контекст
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
