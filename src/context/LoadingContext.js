import React, { createContext, useContext, useState } from 'react';

// Создаем контекст для управления состоянием загрузки
const LoadingContext = createContext();

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export const LoadingProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('');

  // Функция для старта загрузки с опциональным сообщением
  const startLoading = (message = '') => {
    setIsLoading(true);
    setLoadingMessage(message);
  };

  // Функция для остановки загрузки
  const stopLoading = () => {
    setIsLoading(false);
    setLoadingMessage('');
  };

  return (
    <LoadingContext.Provider value={{ isLoading, loadingMessage, startLoading, stopLoading }}>
      {children}
    </LoadingContext.Provider>
  );
};

export default LoadingContext;
