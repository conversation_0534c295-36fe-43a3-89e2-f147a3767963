import { createContext, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { useSelector, useDispatch } from 'react-redux';
import {
  addToWishlist as addToWishlistAction,
  removeFromWishlist as removeFromWishlistAction
} from '../store/slices/wishlistSlice';
import { toast } from 'react-toastify';

const WishlistContext = createContext();

export const WishlistProvider = ({ children }) => {
  const { user } = useAuth();
  const wishlistItems = useSelector(state => state.wishlist.items);
  const dispatch = useDispatch();

  useEffect(() => {
    const syncWishlistWithFirestore = async () => {
      if (user) {
        try {
          // Removed all Firebase-related logic in syncWishlistWithFirestore
        } catch (error) {
          console.error('Error syncing wishlist with Firestore:', error);
        }
      } else {
        // Если нет пользователя, в Redux уже будут загружены данные из localStorage
      }
    };

    syncWishlistWithFirestore();
  }, [user]);

  const addToWishlist = item => {
    dispatch(addToWishlistAction(item));
    toast.success(`${item.name} добавлен в список желаемого`);
  };

  const removeFromWishlist = id => {
    const itemToRemove = wishlistItems.find(item => item.id === id);
    dispatch(removeFromWishlistAction(id));
    if (itemToRemove) {
      toast.error(`${itemToRemove.name} удален из списка желаемого`);
    }
  };

  const isInWishlist = id => {
    return wishlistItems.some(item => item.id === id);
  };

  return (
    <WishlistContext.Provider
      value={{ wishlist: wishlistItems, addToWishlist, removeFromWishlist, isInWishlist }}
    >
      {children}
    </WishlistContext.Provider>
  );
};

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
