import { supabase } from '../supabaseClient';

/**
 * API для получения товаров для внешнего использования
 * Этот API можно использовать на странице-заглушке основного домена
 */

// Базовая функция для получения товаров с фильтрацией
export const getProducts = async (options = {}) => {
  try {
    const {
      limit = 50,
      offset = 0,
      category = null,
      search = null,
      isActive = true,
      isAvailable = true,
      sortBy = 'created_at',
      sortOrder = 'desc',
      includeCategories = false,
      includeBrands = false
    } = options;

    // Базовый запрос
    let query = supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        price,
        original_price,
        image,
        vendor,
        brand,
        external_id,
        is_on_sale,
        is_new,
        is_bestseller,
        is_available,
        is_active,
        created_at,
        updated_at,
        url,
        category_id,
        ${includeCategories ? 'categories(id, name, slug),' : ''}
        ${includeBrands ? 'brands(id, name, logo_url),' : ''}
        stock
      `);

    // Применяем фильтры
    if (isActive !== null) {
      query = query.eq('is_active', isActive);
    }

    if (isAvailable !== null) {
      query = query.eq('is_available', isAvailable);
    }

    if (category) {
      query = query.eq('category_id', category);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,vendor.ilike.%${search}%`);
    }

    // Сортировка
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Пагинация
    if (limit) {
      query = query.range(offset, offset + limit - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      success: true,
      data: data || [],
      count,
      pagination: {
        limit,
        offset,
        total: count
      }
    };

  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
};

// Получить все товары (для заглушки)
export const getAllProducts = async (limit = 100) => {
  return await getProducts({
    limit,
    offset: 0,
    isActive: true,
    isAvailable: true,
    includeCategories: true,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });
};

// Получить товары по категории
export const getProductsByCategory = async (categoryId, limit = 20) => {
  return await getProducts({
    category: categoryId,
    limit,
    isActive: true,
    isAvailable: true,
    includeCategories: true
  });
};

// Получить новые товары
export const getNewProducts = async (limit = 20) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        price,
        original_price,
        image,
        vendor,
        brand,
        is_on_sale,
        is_new,
        is_bestseller,
        created_at,
        categories(name)
      `)
      .eq('is_active', true)
      .eq('is_available', true)
      .eq('is_new', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error fetching new products:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
};

// Получить товары со скидкой
export const getSaleProducts = async (limit = 20) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        price,
        original_price,
        image,
        vendor,
        brand,
        is_on_sale,
        created_at,
        categories(name)
      `)
      .eq('is_active', true)
      .eq('is_available', true)
      .eq('is_on_sale', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error fetching sale products:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
};

// Получить хиты продаж
export const getBestsellerProducts = async (limit = 20) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        price,
        original_price,
        image,
        vendor,
        brand,
        is_bestseller,
        created_at,
        categories(name)
      `)
      .eq('is_active', true)
      .eq('is_available', true)
      .eq('is_bestseller', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return {
      success: true,
      data: data || []
    };
  } catch (error) {
    console.error('Error fetching bestseller products:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
};

// Получить статистику товаров
export const getProductsStats = async () => {
  try {
    const [totalResult, activeResult, newResult, saleResult, bestsellerResult] = await Promise.all([
      supabase.from('products').select('id', { count: 'exact', head: true }),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_new', true),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_on_sale', true),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_bestseller', true)
    ]);

    return {
      success: true,
      data: {
        total: totalResult.count || 0,
        active: activeResult.count || 0,
        new: newResult.count || 0,
        onSale: saleResult.count || 0,
        bestsellers: bestsellerResult.count || 0
      }
    };
  } catch (error) {
    console.error('Error fetching products stats:', error);
    return {
      success: false,
      error: error.message,
      data: {}
    };
  }
};

// Поиск товаров
export const searchProducts = async (searchTerm, limit = 20) => {
  return await getProducts({
    search: searchTerm,
    limit,
    isActive: true,
    isAvailable: true,
    includeCategories: true
  });
};
