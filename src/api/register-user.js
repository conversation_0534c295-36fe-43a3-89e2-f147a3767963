const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseServiceKey =
  process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8';

// Create admin client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function registerUserWithServiceRole(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { email, password, firstName, lastName } = req.body;

  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required' });
  }

  try {
    // Create user using admin client
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        first_name: firstName || '',
        last_name: lastName || ''
      }
    });

    if (userError) {
      console.error('User creation failed:', userError.message);
      return res.status(400).json({ error: userError.message });
    }

    // Create profile manually
    const { error: profileError } = await supabaseAdmin.from('profiles').insert({
      id: userData.user.id,
      email: email,
      first_name: firstName || '',
      last_name: lastName || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    if (profileError) {
      // Don't fail the registration if profile creation fails
    } else {
    }

    return res.status(200).json({
      success: true,
      user: {
        id: userData.user.id,
        email: userData.user.email
      },
      message: 'User registered successfully'
    });
  } catch (error) {
    console.error('Registration error:', error.message);
    return res.status(500).json({ error: 'Internal server error: ' + error.message });
  }
}

module.exports = registerUserWithServiceRole;
