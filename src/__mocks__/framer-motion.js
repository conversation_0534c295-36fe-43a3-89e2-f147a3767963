// Mock для framer-motion
const mockMotion = {
  div: 'div',
  section: 'section',
  span: 'span',
  ul: 'ul',
  li: 'li',
  button: 'button',
  a: 'a',
  header: 'header',
  footer: 'footer',
  nav: 'nav',
  p: 'p',
  h1: 'h1',
  h2: 'h2',
  h3: 'h3'
  // Добавьте другие элементы по мере необходимости
};

export const motion = mockMotion;
export const AnimatePresence = ({ children }) => children;
export const useAnimation = () => ({ start: jest.fn() });
export const useCycle = () => [false, jest.fn()];

const framerMotionMock = {
  motion,
  AnimatePresence,
  useAnimation,
  useCycle
};

export default framerMotionMock;
