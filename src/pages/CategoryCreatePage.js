import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { toast } from 'react-toastify';

const CategoryCreatePage = () => {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [image, setImage] = useState(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleCreate = async e => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('categories')
        .insert([{ name, description, image }])
        .single();

      if (error) {
        throw error;
      }

      toast.success(t('category_created_successfully'));
      navigate('/admin/categories');
    } catch (error) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('create_category', 'Создать категорию')}
          </h2>
        </div>
        <form className="space-y-6" onSubmit={handleCreate}>
          <div>
            <label className="text-sm font-bold text-gray-600 block" htmlFor="name">
              {t('category_name', 'Название категории')}
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>
          <div>
            <label className="text-sm font-bold text-gray-600 block" htmlFor="description">
              {t('description', 'Описание')}
            </label>
            <textarea
              id="description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          <div>
            <label className="text-sm font-bold text-gray-600 block" htmlFor="image">
              {t('image_url', 'URL изображения')}
            </label>
            <input
              id="image"
              type="text"
              value={image}
              onChange={e => setImage(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          <div>
            <button
              type="submit"
              disabled={loading}
              className="w-full py-2 px-4 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-dark disabled:opacity-50"
            >
              {loading ? t('creating', 'Создание...') : t('create', 'Создать')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CategoryCreatePage;
