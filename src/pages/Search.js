import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import ProductList from '../components/ProductList';
import { ProductListSkeleton } from '../components/SkeletonLoader';
import { supabase } from '../supabaseClient';

const SearchPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const searchQuery = searchParams.get('query') || '';

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const resultsPerPage = 12;

  // Функция для поиска продуктов
  const searchProducts = async () => {
    if (!searchQuery.trim()) {
      setProducts([]);
      setTotalResults(0);
      setLoading(false);
      setTotalPages(1);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Стандартный поиск по имени
      const offset = (currentPage - 1) * resultsPerPage;

      const {
        data,
        error: searchError,
        count
      } = await supabase
        .from('products')
        .select('*', { count: 'exact' })
        .ilike('name', `%${searchQuery}%`)
        .order('created_at', { ascending: false })
        .range(offset, offset + resultsPerPage - 1);

      if (searchError) {
        console.error('Ошибка при поиске по имени:', searchError);
        throw searchError;
      }

      if (data && data.length > 0) {
        setProducts(data);
        setTotalResults(count || 0);
        setTotalPages(Math.ceil((count || 0) / resultsPerPage));
      } else {
        // Поиск по описанию, если не нашли по имени
        const { data: descData, count: descCount } = await supabase
          .from('products')
          .select('*', { count: 'exact' })
          .ilike('description', `%${searchQuery}%`)
          .order('created_at', { ascending: false })
          .range(0, resultsPerPage - 1);

        if (descData && descData.length > 0) {
          setProducts(descData);
          setTotalResults(descCount || 0);
          setTotalPages(Math.ceil((descCount || 0) / resultsPerPage));
        } else {
          // Если ничего не нашли
          setProducts([]);
          setTotalResults(0);
          setTotalPages(1);
        }
      }
    } catch (err) {
      console.error('Ошибка при выполнении поиска:', err);

      // Если была ошибка, попробуем последний шанс - получить все продукты и отфильтровать их
      try {
        const { data: allProducts } = await supabase
          .from('products')
          .select('id, name, description, price, image')
          .limit(50);

        if (allProducts && allProducts.length > 0) {
          // Фильтруем на стороне клиента
          const filteredProducts = allProducts.filter(
            product =>
              product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
              product.description?.toLowerCase().includes(searchQuery.toLowerCase())
          );

          if (filteredProducts.length > 0) {
            setProducts(filteredProducts);
            setTotalResults(filteredProducts.length);
            setTotalPages(Math.ceil(filteredProducts.length / resultsPerPage));
            setError(null);
            setLoading(false);
            return;
          }
        }
      } catch (fallbackError) {
        console.error('Ошибка при запасном поиске:', fallbackError);
      }

      setError(t('error_searching', 'Произошла ошибка при поиске. Пожалуйста, попробуйте позже.'));
    } finally {
      setLoading(false);
    }
  };

  // Запускаем поиск при изменении запроса или страницы
  useEffect(() => {
    searchProducts();
  }, [searchQuery, currentPage]); // eslint-disable-line react-hooks/exhaustive-deps

  // Перенаправляем на главную, если запрос пустой
  useEffect(() => {
    if (!searchQuery && searchParams.toString() === '') {
      navigate('/');
    }
  }, [searchQuery, searchParams, navigate]);

  // Обработчик изменения страницы
  const handlePageChange = pageNum => {
    setCurrentPage(pageNum);
    window.scrollTo(0, 0);
  };

  // Рендерим основной интерфейс
  return (
    <>
      <Helmet>
        <title>
          {t('search_results_title', 'Результаты поиска')} - {searchQuery}
        </title>
        <meta
          name="description"
          content={t('search_results_description', 'Результаты поиска товаров по вашему запросу.')}
        />
      </Helmet>

      <div className="container mx-auto px-4 py-8 mt-16">
        <h1 className="text-3xl font-semibold mb-6">
          {t('search_results', 'Результаты поиска')}: "{searchQuery}"
        </h1>

        {/* Состояние загрузки */}
        {loading ? (
          <ProductListSkeleton count={8} />
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={() => navigate('/')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {t('return_home', 'Вернуться на главную')}
            </button>
          </div>
        ) : products.length > 0 ? (
          <>
            <div className="mb-4 text-gray-600">
              {t('found_results', 'Найдено товаров')}: {totalResults}
            </div>

            <ProductList products={products} />

            {/* Пагинация */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center gap-2">
                  {/* Кнопка "Назад" */}
                  {currentPage > 1 && (
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      className="px-3 py-1 border rounded hover:bg-gray-100"
                    >
                      &laquo; {t('prev', 'Назад')}
                    </button>
                  )}

                  {/* Номера страниц */}
                  {Array.from({ length: Math.min(5, totalPages) }).map((_, idx) => {
                    // Логика расчета отображаемых номеров страниц
                    const pageNum =
                      totalPages <= 5
                        ? idx + 1
                        : idx + Math.max(1, Math.min(currentPage - 2, totalPages - 4));

                    return (
                      <button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        className={`px-3 py-1 border rounded ${
                          currentPage === pageNum ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  {/* Кнопка "Вперед" */}
                  {currentPage < totalPages && (
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      className="px-3 py-1 border rounded hover:bg-gray-100"
                    >
                      {t('next', 'Вперед')} &raquo;
                    </button>
                  )}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg mb-2">{t('no_results', 'Ничего не найдено')}</p>
            <p className="text-gray-500 mb-6">
              {t('try_different_keywords', 'Попробуйте изменить параметры поиска')}
            </p>
            <button
              onClick={() => navigate('/')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {t('return_home', 'Вернуться на главную')}
            </button>
          </div>
        )}
      </div>
    </>
  );
};

export default SearchPage;
