import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const NotFoundPage = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="max-w-md w-full px-6 py-8 bg-white shadow-lg rounded-lg text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">404</h1>
        <p className="text-xl font-semibold text-gray-600 mb-6">
          {t('page_not_found', 'Страница не найдена')}
        </p>
        <p className="text-gray-600 mb-8">
          {t(
            'page_not_found_message',
            'Страница, которую вы ищете, не существует или была перемещена.'
          )}
        </p>
        <Link
          to="/"
          className="inline-block bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
        >
          {t('back_to_home', 'Вернуться на главную')}
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;
