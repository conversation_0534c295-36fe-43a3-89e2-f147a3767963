import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ProductList from '../components/ProductList'; // Changed ProductGrid to ProductList
import { supabase } from '../supabaseClient'; // Corrected supabaseClient path
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';
import PageHeader from '../components/PageHeader';

const BestsellersPage = () => {
  const { t } = useTranslation();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBestsellers = async () => {
      try {
        setLoading(true);

        // Получаем товары с меткой "бестселлер" (is_bestseller = true)
        const { data, error } = await supabase
          .from('products')
          .select('*, old_price:original_price') // Select original_price and alias it as old_price
          .eq('is_bestseller', true)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setProducts(data || []);
      } catch (err) {
        console.error('Ошибка при загрузке бестселлеров:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchBestsellers();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <Helmet>
        <title>
          {t('bestsellers.pageTitle')} | {t('storeName')}
        </title>
        <meta name="description" content={t('bestsellers.pageDescription')} />
      </Helmet>

      <PageHeader
        title={t('bestsellers.pageTitle')}
        description={t('bestsellers.pageDescription')}
        breadcrumbs={[
          { name: t('home'), href: '/' },
          { name: t('bestsellers.pageTitle'), href: '/bestsellers' }
        ]}
      />

      {loading ? (
        <div className="flex justify-center py-10">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="text-red-500 text-center py-8">{error}</div>
      ) : products.length === 0 ? (
        <EmptyState
          title={t('bestsellers.noProductsTitle')}
          description={t('bestsellers.noProductsDescription')}
          icon="FireIcon"
        />
      ) : (
        <div className="pt-6">
          <ProductList products={products} /> {/* Changed ProductGrid to ProductList */}
        </div>
      )}
    </div>
  );
};

export default BestsellersPage;
