import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../supabaseClient';
import { useWishlist } from '../context/WishlistContext';
import {
  FaUser,
  FaShoppingBag,
  FaHeart,
  FaMapMarkerAlt,
  FaKey,
  FaSignOutAlt,
  FaBell,
  FaShieldAlt,
  FaCreditCard,
  FaHistory,
  FaChevronRight,
  FaSpinner,
  FaEdit,
  FaTrash
} from 'react-icons/fa';
import { toast } from 'react-toastify';

const AccountPage = () => {
  const { user, logout } = useAuth();
  const { wishlist } = useWishlist();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userData, setUserData] = useState(null);
  const [orders, setOrders] = useState([]);
  const [addresses, setAddresses] = useState([]);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [loginHistory, setLoginHistory] = useState([]);
  const [notifications, setNotifications] = useState({
    promotions: true,
    orderStatus: true,
    reviews: true,
    push: true
  });

  // State for profile editing
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileForm, setProfileForm] = useState({
    full_name: ''
    // phone: '', // Removed phone
    // Add other profile fields as needed
  });

  // State for address editing/adding
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null); // null for new, or address object for editing
  const [addressForm, setAddressForm] = useState({
    street: '',
    city: '',
    zip_code: '',
    house: '',
    nova_poshta_branch: '',
    is_default: false
  });

  useEffect(() => {
    if (!user) return;

    const fetchUserData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch user profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) throw profileError;
        setUserData(profile);
        // Initialize profileForm when userData is fetched
        if (profile) {
          setProfileForm({
            full_name: profile.full_name || ''
            // phone: profile.phone || '', // Removed phone
          });
        }

        // Fetch orders
        const { data: ordersData, error: ordersError } = await supabase
          .from('orders')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5);

        if (ordersError) throw ordersError;
        setOrders(ordersData || []);

        // Fetch addresses if they exist in a separate table
        const { data: addressesData, error: addressesError } = await supabase
          .from('addresses')
          .select('*')
          .eq('user_id', user.id);

        if (!addressesError) {
          setAddresses(addressesData || []);
        }

        // Fetch login history
        const { data: loginHistoryData, error: loginHistoryError } = await supabase
          .from('login_history')
          .select('*')
          .eq('user_id', user.id)
          .order('timestamp', { ascending: false })
          .limit(5);

        if (!loginHistoryError) {
          setLoginHistory(loginHistoryData || []);
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  const handlePasswordChange = async e => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      toast.error('Новые пароли не совпадают');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      toast.success('Пароль успешно изменен');
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (err) {
      console.error('Error changing password:', err);
      toast.error('Ошибка при изменении пароля');
    } finally {
      setLoading(false);
    }
  };

  const toggle2FA = async () => {
    try {
      // This is a placeholder for 2FA implementation
      // In a real application, you would integrate with a 2FA provider
      setIs2FAEnabled(!is2FAEnabled);

      const { error } = await supabase
        .from('profiles')
        .update({ two_factor_enabled: !is2FAEnabled })
        .eq('id', user.id);

      if (error) throw error;
    } catch (err) {
      console.error('Error toggling 2FA:', err);
      toast.error('Ошибка при изменении настроек 2FA');
    }
  };

  const handleDeleteAddress = async addressId => {
    if (!window.confirm('Вы уверены, что хотите удалить этот адрес?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('addresses')
        .delete()
        .eq('id', addressId)
        .eq('user_id', user.id);

      if (error) throw error;

      // Update addresses list
      setAddresses(addresses.filter(addr => addr.id !== addressId));
    } catch (err) {
      console.error('Error deleting address:', err);
      toast.error('Ошибка при удалении адреса');
    }
  };

  const toggleNotification = async type => {
    try {
      const newNotifications = {
        ...notifications,
        [type]: !notifications[type]
      };

      setNotifications(newNotifications);

      const { error } = await supabase.from('notification_preferences').upsert({
        user_id: user.id,
        preferences: newNotifications
      });

      if (error) throw error;
    } catch (err) {
      console.error('Error updating notification preferences:', err);
      // Revert the toggle if there was an error
      setNotifications(notifications);
      toast.error('Ошибка при обновлении настроек уведомлений');
    }
  };

  // Functions to handle profile form changes and submission
  const handleProfileInputChange = e => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileFormSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: profileForm.full_name
          // phone: profileForm.phone, // Removed phone
          // updated_at: new Date(), // Supabase handles this automatically
        })
        .eq('id', user.id);

      if (error) throw error;
      toast.success('Профиль успешно обновлен!');
      // Optionally refetch user data or update local state
      const { data: updatedProfile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      if (updatedProfile) setUserData(updatedProfile);
      setIsEditingProfile(false); // Close edit form after submission
      setActiveTab('overview'); // Go back to overview or stay
    } catch (err) {
      console.error('Error updating profile:', err);
      toast.error(`Ошибка при обновлении профиля: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Functions to handle address form changes and submission
  const handleAddressInputChange = e => {
    const { name, value, type, checked } = e.target;
    setAddressForm(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  const handleAddressFormSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    try {
      const addressData = {
        street: addressForm.street,
        city: addressForm.city,
        zip_code: addressForm.zip_code,
        house: addressForm.house,
        nova_poshta_branch: addressForm.nova_poshta_branch,
        is_default: addressForm.is_default,
        user_id: user.id
      };

      let response;
      if (editingAddress && editingAddress.id) {
        // Editing existing address
        response = await supabase
          .from('addresses')
          .update(addressData)
          .eq('id', editingAddress.id)
          .select();
      } else {
        // Adding new address
        response = await supabase.from('addresses').insert([addressData]).select();
      }

      const { data, error } = response;

      if (error) throw error;

      toast.success(editingAddress ? 'Адрес успешно обновлен!' : 'Адрес успешно добавлен!');

      // Refetch addresses or update local state
      if (data && data.length > 0) {
        const newAddress = data[0];
        if (editingAddress) {
          setAddresses(addresses.map(addr => (addr.id === newAddress.id ? newAddress : addr)));
        } else {
          setAddresses([...addresses, newAddress]);
        }
      }

      setIsEditingAddress(false);
      setEditingAddress(null);
    } catch (err) {
      console.error('Error saving address:', err);
      toast.error(`Ошибка при сохранении адреса: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const openAddAddressForm = () => {
    setEditingAddress(null);
    setAddressForm({
      street: '',
      city: '',
      zip_code: '',
      house: '',
      nova_poshta_branch: '',
      is_default: false
    });
    setIsEditingAddress(true);
  };

  const openEditAddressForm = address => {
    setEditingAddress(address);
    setAddressForm({
      street: address.street || '',
      city: address.city || '',
      zip_code: address.zip_code || '',
      house: address.house || '',
      nova_poshta_branch: address.nova_poshta_branch || '',
      is_default: address.is_default || false
    });
    setIsEditingAddress(true);
  };

  const renderEditProfileForm = () => (
    <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg transition-all duration-300 ease-in-out">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">Редактировать профиль</h2>
      <form onSubmit={handleProfileFormSubmit} className="space-y-6">
        <div>
          <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1">
            Полное имя
          </label>
          <input
            type="text"
            name="full_name"
            id="full_name"
            value={profileForm.full_name}
            onChange={handleProfileInputChange}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary transition-colors"
            placeholder="Иван Иванов"
          />
        </div>
        {/* <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Телефон</label>
          <input
            type="tel"
            name="phone"
            id="phone"
            value={profileForm.phone}
            onChange={handleProfileInputChange}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary transition-colors"
            placeholder="+380 XX XXX XX XX"
          />
        </div> */}
        {/* Add other fields like email (if changeable), avatar upload, etc. */}
        <div className="flex items-center justify-end space-x-3 pt-2">
          <button
            type="button"
            onClick={() => {
              setIsEditingProfile(false);
              setActiveTab('overview');
            }}
            className="px-5 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Отмена
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-5 py-2.5 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-lg transition-colors flex items-center"
          >
            {loading && <FaSpinner className="animate-spin mr-2" />}
            Сохранить изменения
          </button>
        </div>
      </form>
    </div>
  );

  const renderAddressForm = () => (
    <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg mt-6">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">
        {editingAddress ? 'Редактировать адрес' : 'Добавить новый адрес'}
      </h2>
      <form onSubmit={handleAddressFormSubmit} className="space-y-6">
        <div>
          <label htmlFor="street" className="block text-sm font-medium text-gray-700 mb-1">
            Улица
          </label>
          <input
            type="text"
            name="street"
            id="street"
            value={addressForm.street}
            onChange={handleAddressInputChange}
            required
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary"
          />
        </div>
        <div>
          <label htmlFor="house" className="block text-sm font-medium text-gray-700 mb-1">
            Номер дома
          </label>
          <input
            type="text"
            name="house"
            id="house"
            value={addressForm.house}
            onChange={handleAddressInputChange}
            required
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary"
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
              Город
            </label>
            <input
              type="text"
              name="city"
              id="city"
              value={addressForm.city}
              onChange={handleAddressInputChange}
              required
              className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary"
            />
          </div>
          <div>
            <label htmlFor="zip_code" className="block text-sm font-medium text-gray-700 mb-1">
              Почтовый индекс
            </label>
            <input
              type="text"
              name="zip_code"
              id="zip_code"
              value={addressForm.zip_code}
              onChange={handleAddressInputChange}
              required
              className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary"
            />
          </div>
        </div>
        <div>
          <label
            htmlFor="nova_poshta_branch"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Отделение Новой Почты
          </label>
          <input
            type="text"
            name="nova_poshta_branch"
            id="nova_poshta_branch"
            value={addressForm.nova_poshta_branch}
            onChange={handleAddressInputChange}
            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary"
            placeholder="Например, Отделение №15"
          />
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            name="is_default"
            id="is_default"
            checked={addressForm.is_default}
            onChange={handleAddressInputChange}
            className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
          />
          <label htmlFor="is_default" className="ml-2 block text-sm text-gray-900">
            Сделать адресом по умолчанию
          </label>
        </div>
        <div className="flex items-center justify-end space-x-3 pt-2">
          <button
            type="button"
            onClick={() => {
              setIsEditingAddress(false);
              setEditingAddress(null);
              setActiveTab('addresses');
            }}
            className="px-5 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
          >
            Отмена
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-5 py-2.5 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-lg flex items-center"
          >
            {loading && <FaSpinner className="animate-spin mr-2" />}
            {editingAddress ? 'Сохранить адрес' : 'Добавить адрес'}
          </button>
        </div>
      </form>
    </div>
  );

  const renderContent = () => {
    if (loading && activeTab === 'overview' && !userData) {
      // Show main loader only on initial overview load
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <FaSpinner className="w-8 h-8 text-primary animate-spin" />
        </div>
      );
    }

    // Updated conditions to render forms
    if (activeTab === 'edit-profile') {
      // Changed from 'edit-profile-form'
      return renderEditProfileForm();
    }

    if (isEditingAddress) {
      // This will cover add and edit address states
      return renderAddressForm();
    }

    switch (activeTab) {
      case 'overview':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Обзор аккаунта</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {/* Quick stats */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-blue-500/10 text-blue-600 mr-4">
                    <FaShoppingBag className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-blue-600/80">Заказы</p>
                    <p className="text-2xl font-bold text-blue-600">{orders.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-red-500/10 text-red-600 mr-4">
                    <FaHeart className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-red-600/80">Избранное</p>
                    <p className="text-2xl font-bold text-red-600">{wishlist.length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                <div className="flex items-center">
                  <div className="p-3 rounded-xl bg-green-500/10 text-green-600 mr-4">
                    <FaMapMarkerAlt className="h-6 w-6" />
                  </div>
                  <div>
                    <p className="text-sm text-green-600/80">Адреса</p>
                    <p className="text-2xl font-bold text-green-600">{addresses.length}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent orders */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold">Недавние заказы</h3>
                <Link
                  to="/orders"
                  className="text-primary hover:text-primary/80 font-medium flex items-center gap-2"
                >
                  Все заказы
                  <FaChevronRight className="w-4 h-4" />
                </Link>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Номер
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Дата
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Статус
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Сумма
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orders.length > 0 ? (
                      orders.map(order => (
                        <tr
                          key={order.id}
                          className="hover:bg-gray-50 transition-colors cursor-pointer"
                          onClick={() => navigate(`/orders/${order.id}`)}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary">
                            #{order.id.slice(0, 8)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {new Date(order.created_at).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getStatusBadge(order.status)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {order.total_amount} грн
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="4" className="px-6 py-4 text-center text-gray-500">
                          У вас пока нет заказов
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );
      case 'orders':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Мои заказы</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Номер
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Дата
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Статус
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Сумма
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {orders.length > 0 ? (
                    orders.map(order => (
                      <tr
                        key={order.id}
                        className="hover:bg-gray-50 transition-colors cursor-pointer"
                        onClick={() => navigate(`/orders/${order.id}`)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-primary">
                          #{order.id.slice(0, 8)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                          {new Date(order.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(order.status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {order.total_amount} грн
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="4" className="px-6 py-4 text-center text-gray-500">
                        У вас пока нет заказов
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        );
      case 'wishlist':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Избранное</h2>
            {wishlist.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {wishlist.map(item => (
                  <div
                    key={item.id}
                    className="flex items-center gap-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer"
                    onClick={() => navigate(`/product/${item.id}`)}
                  >
                    <img
                      src={item.image || '/placeholder.png'}
                      alt={item.name}
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                    <div className="flex-grow">
                      <h4 className="font-medium text-gray-900">{item.name}</h4>
                      <p className="text-primary font-medium">{item.price} грн</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">В избранном пока нет товаров</p>
                <Link to="/catalog" className="mt-4 inline-block text-primary hover:underline">
                  Перейти в каталог
                </Link>
              </div>
            )}
          </div>
        );
      case 'addresses':
        // If isEditingAddress is true, renderAddressForm will be shown by the condition above.
        // So, this case will only render the list of addresses when not actively editing/adding one.
        return (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Мои адреса</h2>
              <button onClick={openAddAddressForm} className="btn-primary px-4 py-2 rounded-lg">
                Добавить адрес
              </button>
            </div>

            {addresses.length > 0 ? (
              <div className="space-y-4">
                {addresses.map(address => (
                  <div
                    key={address.id}
                    className="border rounded-xl p-4 hover:border-primary transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-lg">{address.title || 'Адрес'}</h3>
                        <p className="text-gray-600 mt-1">
                          {address.street}, {address.house}
                          {address.apartment && `, кв. ${address.apartment}`}
                        </p>
                        <p className="text-gray-600">
                          {address.city}, {address.postal_code}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => openEditAddressForm(address)}
                          className="text-gray-600 hover:text-primary"
                        >
                          <FaEdit className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleDeleteAddress(address.id)}
                          className="text-gray-600 hover:text-red-600"
                        >
                          <FaTrash className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">У вас пока нет сохраненных адресов</p>
                <button onClick={openAddAddressForm} className="mt-4 text-primary hover:underline">
                  Добавить первый адрес
                </button>
              </div>
            )}
          </div>
        );
      case 'security':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Безопасность</h2>

            <div className="space-y-8">
              {/* Change Password Section */}
              <div className="border-b pb-8">
                <h3 className="text-lg font-medium mb-4">Изменить пароль</h3>
                <form onSubmit={handlePasswordChange} className="max-w-md space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Текущий пароль
                    </label>
                    <input
                      type="password"
                      value={currentPassword}
                      onChange={e => setCurrentPassword(e.target.value)}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Новый пароль
                    </label>
                    <input
                      type="password"
                      value={newPassword}
                      onChange={e => setNewPassword(e.target.value)}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Подтвердите новый пароль
                    </label>
                    <input
                      type="password"
                      value={confirmPassword}
                      onChange={e => setConfirmPassword(e.target.value)}
                      className="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    className="btn-primary px-6 py-2 rounded-lg"
                    disabled={loading}
                  >
                    {loading ? 'Сохранение...' : 'Сохранить новый пароль'}
                  </button>
                </form>
              </div>

              {/* Two-Factor Authentication */}
              <div className="border-b pb-8">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Двухфакторная аутентификация</h3>
                  <button
                    onClick={toggle2FA}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                      is2FAEnabled ? 'bg-primary' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                        is2FAEnabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
                <p className="text-gray-600 text-sm">
                  Добавьте дополнительный уровень безопасности для вашего аккаунта
                </p>
              </div>

              {/* Login History */}
              <div>
                <h3 className="text-lg font-medium mb-4">История входов</h3>
                <div className="space-y-3">
                  {loginHistory.map((login, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div>
                        <p className="font-medium">{login.device}</p>
                        <p className="text-sm text-gray-600">{login.location}</p>
                      </div>
                      <p className="text-sm text-gray-600">
                        {new Date(login.timestamp).toLocaleString()}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div>
            <h2 className="text-2xl font-bold mb-6">Уведомления</h2>

            <div className="space-y-6">
              {/* Email Notifications */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-medium mb-4">Уведомления по email</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Новости и акции</p>
                      <p className="text-sm text-gray-600">
                        Получать информацию о специальных предложениях
                      </p>
                    </div>
                    <button
                      onClick={() => toggleNotification('promotions')}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                        notifications.promotions ? 'bg-primary' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                          notifications.promotions ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Статус заказа</p>
                      <p className="text-sm text-gray-600">
                        Уведомления об изменении статуса заказа
                      </p>
                    </div>
                    <button
                      onClick={() => toggleNotification('orderStatus')}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                        notifications.orderStatus ? 'bg-primary' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                          notifications.orderStatus ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Отзывы</p>
                      <p className="text-sm text-gray-600">Ответы на ваши отзывы и комментарии</p>
                    </div>
                    <button
                      onClick={() => toggleNotification('reviews')}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                        notifications.reviews ? 'bg-primary' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                          notifications.reviews ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>

              {/* Push Notifications */}
              <div>
                <h3 className="text-lg font-medium mb-4">Push-уведомления</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Браузерные уведомления</p>
                      <p className="text-sm text-gray-600">Получать уведомления в браузере</p>
                    </div>
                    <button
                      onClick={() => toggleNotification('push')}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                        notifications.push ? 'bg-primary' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                          notifications.push ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <Helmet>
          <title>Личный кабинет</title>
        </Helmet>
        <div className="max-w-md w-full">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="bg-gradient-to-r from-primary to-primary/80 py-8 px-8">
              <h1 className="text-3xl font-bold text-white text-center">Личный кабинет</h1>
            </div>
            <div className="p-8 space-y-6">
              <p className="text-lg text-gray-600 text-center">
                Для доступа к личному кабинету необходимо авторизоваться.
              </p>
              <div className="space-y-4">
                <Link
                  to="/login"
                  className="btn-primary w-full inline-flex items-center justify-center py-3 rounded-xl text-lg font-medium transition-transform hover:scale-105"
                >
                  Войти в аккаунт
                </Link>
                <p className="text-gray-500 text-center">
                  Нет аккаунта?{' '}
                  <Link to="/register" className="text-primary font-medium hover:underline">
                    Зарегистрироваться
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const avatar =
    userData?.avatar_url ||
    user?.user_metadata?.avatar_url ||
    `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.email)}&background=ECECEC&color=31343C&size=200`;

  const menuItems = [
    { id: 'overview', label: 'Обзор', icon: <FaUser />, description: 'Основная информация' },
    { id: 'orders', label: 'Мои заказы', icon: <FaShoppingBag />, description: 'История заказов' },
    {
      id: 'edit-profile',
      label: 'Редактировать профиль',
      icon: <FaEdit />,
      description: 'Изменить личные данные'
    },
    { id: 'wishlist', label: 'Избранное', icon: <FaHeart />, description: 'Сохраненные товары' },
    {
      id: 'addresses',
      label: 'Мои адреса',
      icon: <FaMapMarkerAlt />,
      description: 'Адреса доставки'
    },
    {
      id: 'security',
      label: 'Безопасность',
      icon: <FaShieldAlt />,
      description: 'Настройки безопасности'
    },
    {
      id: 'notifications',
      label: 'Уведомления',
      icon: <FaBell />,
      description: 'Управление уведомлениями'
    }
  ];

  const getStatusBadge = status => {
    const badges = {
      delivered: { bg: 'bg-green-100', text: 'text-green-800', label: 'Доставлен' },
      shipped: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Отправлен' },
      processing: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'В обработке' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Отменен' },
      pending: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Ожидает обработки' }
    };

    const badge = badges[status] || { bg: 'bg-gray-100', text: 'text-gray-800', label: status };

    return (
      <span className={`px-3 py-1 text-sm font-medium rounded-full ${badge.bg} ${badge.text}`}>
        {badge.label}
      </span>
    );
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-2">Произошла ошибка при загрузке данных</p>
          <button onClick={() => window.location.reload()} className="text-primary hover:underline">
            Попробовать снова
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Helmet>
        <title>Личный кабинет | {userData?.full_name || user?.email}</title>
      </Helmet>

      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header with user info */}
        <div className="bg-white rounded-2xl shadow-sm mb-6 overflow-hidden">
          <div className="bg-gradient-to-r from-primary to-primary/80 p-6">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              <div className="flex-shrink-0">
                <div className="relative">
                  <img
                    src={avatar}
                    alt={userData?.full_name || user?.email}
                    className="w-24 h-24 rounded-full object-cover border-4 border-white/20 shadow-lg"
                  />
                  <button
                    onClick={() => navigate('/profile/edit')}
                    className="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaUser className="w-4 h-4 text-primary" />
                  </button>
                </div>
              </div>
              <div className="flex-grow text-center md:text-left">
                <h1 className="text-2xl font-bold text-white">
                  {userData?.full_name || 'Пользователь'}
                </h1>
                <p className="text-white/80">{user?.email}</p>
                {userData?.phone && <p className="text-white/80">{userData.phone}</p>}
                <div className="mt-4 flex flex-wrap justify-center md:justify-start gap-3">
                  <button
                    className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
                    onClick={() => navigate('/profile/edit')}
                  >
                    Редактировать профиль
                  </button>
                  <button
                    className="px-4 py-2 bg-red-500/10 hover:bg-red-500/20 text-white rounded-lg transition-colors flex items-center gap-2"
                    onClick={logout}
                  >
                    <FaSignOutAlt /> Выйти
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Mobile Menu Button */}
          <button
            className="lg:hidden w-full bg-white rounded-xl shadow-sm p-4 flex items-center justify-between"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="font-medium">Меню</span>
            <FaChevronRight
              className={`transform transition-transform ${isMobileMenuOpen ? 'rotate-90' : ''}`}
            />
          </button>

          {/* Sidebar Navigation */}
          <div className={`lg:w-1/4 ${isMobileMenuOpen ? 'block' : 'hidden'} lg:block`}>
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <nav className="divide-y divide-gray-100">
                {menuItems.map(item => (
                  <button
                    key={item.id}
                    onClick={() => {
                      setActiveTab(item.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full flex items-center px-6 py-4 text-left transition-all ${
                      activeTab === item.id
                        ? 'bg-primary/5 text-primary border-l-4 border-primary'
                        : 'text-gray-700 hover:bg-gray-50 border-l-4 border-transparent'
                    }`}
                  >
                    <span
                      className={`mr-3 ${activeTab === item.id ? 'text-primary' : 'text-gray-500'}`}
                    >
                      {item.icon}
                    </span>
                    <div>
                      <span className="font-medium block">{item.label}</span>
                      <span className="text-sm text-gray-500">{item.description}</span>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            <div className="bg-white rounded-xl shadow-sm p-6">{renderContent()}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountPage;
