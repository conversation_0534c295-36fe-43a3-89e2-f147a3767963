import React, { useEffect, useState, useRef } from 'react';
import { useCart } from '../context/CartContext';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../supabaseClient';
import { EmailService } from '../services/emailService';
import { MockEmailService } from '../services/mockEmailService';
import { OrderCreationService } from '../utils/orderHelpers';
import {
  FaCheckCircle,
  FaHome,
  FaShoppingBag,
  FaPrint,
  FaAngleRight,
  FaTruck,
  FaMapMarkerAlt,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaMapMarked,
  FaBuilding,
  FaInfoCircle,
  FaShoppingCart,
  FaCreditCard,
  FaBarcode
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import '../styles/OrderConfirmationPage.css';
import placeholderImage from '../assets/placeholder-product.svg';

const OrderConfirmationPage = () => {
  const { cart, clearCart } = useCart();
  const { user, getUserProfile } = useAuth();
  const { t } = useTranslation();
  const orderProcessed = useRef(false);

  const [orderPlaced, setOrderPlaced] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    city: '',
    novaPoshtaOffice: '',
    notes: ''
  });
  const [errors, setErrors] = useState({});
  const [userAddresses, setUserAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState('');

  // Calculate total for the cart summary before order placement
  const total = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);

  useEffect(() => {
    document.title = `${t('order_confirmation', 'Оформление заказа')} | E-Store`;
  }, [t]);

  useEffect(() => {
    const loadUserProfileAndAddresses = async () => {
      setLoading(true);
      if (user) {
        try {
          const profileData = await getUserProfile();
          if (profileData) {
            // Заполняем все доступные данные из профиля
            setFormData(prev => ({
              ...prev,
              firstName: profileData.first_name || '',
              lastName: profileData.last_name || '',
              phone: profileData.phone || '',
              email: profileData.email || user.email || '',
              city: profileData.city || '' // Теперь заполняем и город
            }));
          }

          // Загрузка адресов пользователя
          const { data: addressesData, error: addressesError } = await supabase
            .from('addresses')
            .select('*')
            .eq('user_id', user.id);

          if (addressesError) {
            console.error('Error fetching user addresses:', addressesError);
            toast.error(t('error_loading_addresses', 'Ошибка загрузки адресов'));
          } else {
            setUserAddresses(addressesData || []);
            // Если есть адреса, выбираем первый по умолчанию
            if (addressesData && addressesData.length > 0) {
              setSelectedAddressId(addressesData[0].id);
              // Заполняем поля адреса из первого сохраненного адреса
              setFormData(prev => ({
                ...prev,
                city: addressesData[0].city || '',
                novaPoshtaOffice: addressesData[0].nova_poshta_branch || ''
              }));
            }
          }
        } catch (error) {
          console.error('Error fetching user profile or addresses:', error);
        }
      }
      setLoading(false);
    };

    loadUserProfileAndAddresses();
  }, [user, getUserProfile, t]);

  const handleAddressChange = event => {
    const addressId = event.target.value;
    setSelectedAddressId(addressId);

    if (addressId === 'new') {
      // Очищаем поля адреса в formData для ввода нового
      setFormData(prev => ({
        ...prev,
        city: '',
        novaPoshtaOffice: ''
        // Добавьте сюда другие поля адреса, если они есть в таблице addresses
        // street: '',
        // house: '',
        // apartment: '',
        // zip_code: '',
      }));
    } else {
      const selectedAddr = userAddresses.find(addr => addr.id === addressId);
      if (selectedAddr) {
        setFormData(prev => ({
          ...prev,
          city: selectedAddr.city || '',
          novaPoshtaOffice: selectedAddr.nova_poshta_branch || ''
          // Убедитесь, что имена полей соответствуют вашей таблице addresses
          // street: selectedAddr.street || '',
          // house: selectedAddr.house || '',
          // apartment: selectedAddr.apartment || '',
          // zip_code: selectedAddr.zip_code || '',
        }));
      }
    }
  };

  const handleChange = e => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) newErrors.firstName = t('required_field', 'Обязательное поле');
    if (!formData.lastName.trim()) newErrors.lastName = t('required_field', 'Обязательное поле');
    if (!formData.phone.trim()) newErrors.phone = t('required_field', 'Обязательное поле');
    if (!formData.email.trim()) newErrors.email = t('required_field', 'Обязательное поле');
    if (!formData.city.trim()) newErrors.city = t('required_field', 'Обязательное поле');
    if (!formData.novaPoshtaOffice.trim())
      newErrors.novaPoshtaOffice = t('required_field', 'Обязательное поле');

    if (formData.phone && !/^\+?[0-9]{10,15}$/.test(formData.phone)) {
      newErrors.phone = t('invalid_phone', 'Неверный формат телефона');
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('invalid_email', 'Неверный формат электронной почты');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async e => {
    e.preventDefault();

    if (!validateForm()) {
      window.scrollTo(0, 0);
      return;
    }

    // Prevent double submission
    if (orderProcessed.current) {
      console.log('Order already processed, skipping...');
      return;
    }

    setLoading(true);

    try {
      const total = cart.reduce((sum, i) => sum + i.price * i.quantity, 0);

      // Формируем данные о заказе
      const orderData = {
        customer_name: `${formData.firstName} ${formData.lastName}`,
        customer_email: formData.email || (user ? user.email : null),
        customer_phone: formData.phone,
        shipping_address: {
          city: formData.city,
          nova_poshta_office: formData.novaPoshtaOffice,
          notes: formData.notes
        },
        total_amount: parseFloat(total.toFixed(2)),
        status: 'pending',
        payment_method: 'cash_on_delivery',
        payment_status: 'pending',
        notes: formData.notes,
        user_id: user ? user.id : null
      };

      // Подготавливаем товары заказа
      const orderItems = cart.map(item => ({
        product_id: item.id,
        product_name: item.name,
        quantity: item.quantity,
        price: parseFloat(item.price.toFixed(2))
      }));

      // Валидация данных заказа
      OrderCreationService.validateOrderData(orderData);

      console.log('Creating order with data:', orderData);

      // Создаем заказ с улучшенной обработкой ошибок
      const order = await OrderCreationService.createOrder(orderData, orderItems);

      if (!order) {
        throw new Error('Order creation failed - no order returned');
      }

      console.log('Order created successfully:', order);

      // Форматируем данные заказа для отображения
      const details = {
        id: order.id,
        date: new Date(order.created_at).toLocaleDateString(),
        items: cart,
        total,
        customer: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone,
          email: formData.email
        },
        shippingAddress: {
          city: formData.city,
          novaPoshtaOffice: formData.novaPoshtaOffice
        },
        paymentMethod: t('cash_on_delivery', 'Наложенный платеж'),
        deliveryMethod: t('nova_poshta', 'Новая Почта'),
        notes: formData.notes,
        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()
      };

      // Обновляем состояние и показываем подтверждение заказа
      setOrderDetails(details);
      clearCart();
      setOrderPlaced(true);
      setLoading(false);
      orderProcessed.current = true;

      // Send order confirmation email via EmailService (with fallback to Mock)
      try {
        if (order.customer_email) {
          console.log('Sending order confirmation email...');

          try {
            // Попытка отправки через основной EmailService
            await EmailService.sendOrderConfirmation(order.id, order);
            console.log('✅ Order confirmation email sent via EmailService');
          } catch (emailServiceError) {
            console.warn(
              '⚠️ EmailService failed, trying MockEmailService:',
              emailServiceError.message
            );

            // Fallback к MockEmailService
            await MockEmailService.sendOrderConfirmation(order.id, order);
            console.log('✅ Order confirmation email sent via MockEmailService');

            // Показываем предупреждение пользователю
            toast.info('Заказ создан. Email-уведомление отправлено в тестовом режиме.');
          }
        }
      } catch (emailError) {
        console.error('❌ All email services failed:', emailError);
        // Don't fail the order creation if email fails - just log the error
        toast.warn('Заказ создан успешно, но возникла ошибка при отправке email-уведомления');
      }

      // Show success message
      toast.success(t('order_created_successfully', 'Заказ успешно создан!'));
    } catch (error) {
      console.error('Error creating order:', error);
      setLoading(false);
      orderProcessed.current = false;

      // Show specific error message based on error type
      let errorMessage = t('order_creation_error', 'Ошибка при создании заказа');

      if (error.message.includes('Missing required fields')) {
        errorMessage = t('missing_required_fields', 'Не заполнены обязательные поля');
      } else if (error.message.includes('permission') || error.message.includes('policy')) {
        errorMessage = t('permission_error', 'Ошибка доступа к базе данных');
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = t('network_error', 'Ошибка сети. Проверьте подключение к интернету');
      }

      toast.error(errorMessage);
    }
  };

  if (cart.length === 0 && !orderPlaced) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-semibold mb-4">{t('cart_empty', 'Корзина пуста')}</h1>
          <p className="text-gray-600 mb-6">
            {t('add_items_to_cart', 'Добавьте товары в корзину, чтобы оформить заказ')}
          </p>
          <Link
            to="/"
            className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            <FaHome className="mr-2" /> {t('continue_shopping', 'Продолжить покупки')}
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">
        <div className="flex flex-col items-center justify-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-gray-600 text-lg">{t('loading', 'Загрузка...')}</p>
        </div>
      </div>
    );
  }

  if (!orderPlaced) {
    return (
      <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12 max-w-6xl">
        <div className="flex items-center justify-center mb-8">
          <FaShoppingCart className="text-primary text-3xl mr-3" />
          <h1 className="text-3xl font-semibold text-center">
            {t('complete_your_order', 'Оформление заказа')}
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Contact Information Block */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div className="flex items-center mb-4">
                  <FaUser className="text-primary text-xl mr-2" />
                  <h2 className="text-xl font-medium">
                    {t('contact_information', 'Контактная информация')}
                  </h2>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('first_name', 'Имя')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleChange}
                        className={`w-full pl-10 p-2.5 border rounded-md ${
                          errors.firstName ? 'border-red-500' : 'border-gray-300'
                        } focus:ring-primary focus:border-primary`}
                      />
                      <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {errors.firstName && (
                      <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('last_name', 'Фамилия')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleChange}
                        className={`w-full pl-10 p-2.5 border rounded-md ${
                          errors.lastName ? 'border-red-500' : 'border-gray-300'
                        } focus:ring-primary focus:border-primary`}
                      />
                      <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {errors.lastName && (
                      <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('phone', 'Телефон')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="+380991234567"
                        className={`w-full pl-10 p-2.5 border rounded-md ${
                          errors.phone ? 'border-red-500' : 'border-gray-300'
                        } focus:ring-primary focus:border-primary`}
                      />
                      <FaPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('email', 'Email')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className={`w-full pl-10 p-2.5 border rounded-md ${
                          errors.email ? 'border-red-500' : 'border-gray-300'
                        } focus:ring-primary focus:border-primary`}
                      />
                      <FaEnvelope className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                  </div>
                </div>
              </div>

              {/* Shipping Address Selection Block */}
              {user && userAddresses && userAddresses.length > 0 && (
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                  <div className="flex items-center mb-4">
                    <FaMapMarked className="text-primary text-xl mr-2" />
                    <h2 className="text-xl font-medium">
                      {t('shipping_address_title', 'Адрес доставки')}
                    </h2>
                  </div>
                  <div className="mb-4">
                    <label
                      htmlFor="saved_address"
                      className="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {t('select_saved_address', 'Выберите сохраненный адрес')}
                    </label>
                    <select
                      id="saved_address"
                      name="saved_address"
                      value={selectedAddressId}
                      onChange={handleAddressChange}
                      className="w-full p-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary pl-10 appearance-none"
                    >
                      <option value="">
                        {t('select_address_placeholder', '-- Выберите адрес --')}
                      </option>
                      {userAddresses.map(address => (
                        <option key={address.id} value={address.id}>
                          {`${address.city}, ${address.street || ''} ${address.house || ''}${address.apartment ? ', кв. ' + address.apartment : ''} (НП: ${address.nova_poshta_branch || t('not_specified_short', 'не указано')})`}
                        </option>
                      ))}
                      <option value="new">
                        {t('add_new_address_option', '+ Добавить новый адрес')}
                      </option>
                    </select>
                  </div>
                </div>
              )}

              {/* Delivery Information Block */}
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div className="flex items-center mb-4">
                  <FaTruck className="text-primary text-xl mr-2" />
                  <h2 className="text-xl font-medium">
                    {t('delivery_information', 'Информация о доставке')}
                  </h2>
                </div>

                <div className="p-4 mb-4 border border-blue-200 bg-blue-50 rounded-md flex items-start">
                  <FaInfoCircle className="text-blue-500 mt-1 mr-3 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-blue-800">
                      {t('nova_poshta_delivery', 'Доставка Новой Почтой')}
                    </p>
                    <p className="text-sm text-blue-600">
                      {t('cash_on_delivery_message', 'Оплата при получении (наложенный платеж)')}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('city', 'Город')} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleChange}
                        placeholder={t('city_placeholder', 'Например: Киев')}
                        className={`w-full pl-10 p-2.5 border rounded-md ${
                          errors.city ? 'border-red-500' : 'border-gray-300'
                        } focus:ring-primary focus:border-primary`}
                      />
                      <FaBuilding className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('nova_poshta_office', 'Отделение Новой Почты')}{' '}
                      <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="novaPoshtaOffice"
                        value={formData.novaPoshtaOffice}
                        onChange={handleChange}
                        placeholder={t('nova_poshta_placeholder', 'Например: Отделение №12')}
                        className={`w-full pl-10 p-2.5 border rounded-md ${
                          errors.novaPoshtaOffice ? 'border-red-500' : 'border-gray-300'
                        } focus:ring-primary focus:border-primary`}
                      />
                      <FaMapMarkerAlt className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                    {errors.novaPoshtaOffice && (
                      <p className="text-red-500 text-sm mt-1">{errors.novaPoshtaOffice}</p>
                    )}
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('notes', 'Примечания к заказу')}
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    rows="3"
                    placeholder={t('notes_placeholder', 'Дополнительная информация о заказе')}
                    className="w-full p-2.5 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex items-center justify-center px-6 py-3 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-w-[200px]"
                >
                  {loading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      {t('processing', 'Обработка...')}
                    </>
                  ) : (
                    <>
                      <FaCreditCard className="mr-2" />
                      {t('place_order', 'Оформить заказ')}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <div className="flex items-center mb-6">
                  <FaShoppingBag className="text-primary text-xl mr-3" />
                  <h2 className="text-xl font-semibold text-gray-800">
                    {t('your_order', 'Ваш заказ')}
                  </h2>
                </div>

                <div className="space-y-5 mb-6">
                  {cart.map(item => (
                    <div
                      key={item.id}
                      className="flex items-start border-b border-gray-100 pb-5 last:border-b-0 last:pb-0"
                    >
                      <div className="relative flex-shrink-0 w-24 h-24 rounded-lg overflow-hidden border border-gray-200 bg-gray-50">
                        <img
                          src={
                            item.images?.[0]?.url || // если изображение в формате {url: "..."}
                            (typeof item.images?.[0] === 'string'
                              ? item.images[0] // если просто строка URL
                              : item.image || // если есть поле image
                                item.thumbnail || // если есть поле thumbnail
                                placeholderImage) // если ничего нет, используем плейсхолдер
                          }
                          alt={item.name}
                          className="w-full h-full object-contain mix-blend-multiply"
                          onError={e => {
                            e.target.onerror = null;
                            e.target.src = placeholderImage;
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0 ml-4">
                        <h3 className="text-base font-medium text-gray-900 mb-1 pr-4">
                          {item.name}
                        </h3>
                        <div className="flex items-center text-sm text-gray-500 mb-2">
                          <span className="flex items-center">
                            <FaShoppingCart className="mr-1.5 text-gray-400" size={14} />
                            {t('quantity', 'Количество')}: {item.quantity}
                          </span>
                          {item.sku && (
                            <span className="ml-4 flex items-center">
                              <FaBarcode className="mr-1.5 text-gray-400" size={14} />
                              {item.sku}
                            </span>
                          )}
                        </div>
                        <div className="text-primary font-semibold">
                          {(item.price * item.quantity).toLocaleString('uk-UA', {
                            style: 'currency',
                            currency: 'UAH'
                          })}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between py-3 border-t border-gray-100">
                    <span className="text-gray-600">{t('subtotal', 'Подытог')}:</span>
                    <span className="font-medium text-gray-900">
                      {total.toLocaleString('uk-UA', { style: 'currency', currency: 'UAH' })}
                    </span>
                  </div>

                  <div className="flex justify-between items-center py-3 border-t border-gray-100">
                    <div className="flex items-center">
                      <FaTruck className="text-gray-400 mr-2" />
                      <span className="text-gray-600">{t('shipping', 'Доставка')}:</span>
                    </div>
                    <span className="text-gray-900">
                      {t('cash_on_delivery', 'Наложенный платеж')}
                    </span>
                  </div>

                  <div className="flex justify-between items-center py-4 border-t border-gray-100">
                    <span className="text-lg font-semibold text-gray-900">
                      {t('total', 'Итого')}:
                    </span>
                    <div className="text-right">
                      <span className="block text-2xl font-bold text-primary">
                        {total.toLocaleString('uk-UA', { style: 'currency', currency: 'UAH' })}
                      </span>
                      <span className="text-sm text-gray-500">
                        {t('including_vat', 'Включая НДС')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12 max-w-6xl">
      <h1 className="text-3xl font-semibold text-center mb-8">
        {t('order_confirmation', 'Подтверждение заказа')}
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h2 className="text-lg font-medium mb-4">{t('order_details', 'Детали заказа')}</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('order_id', 'ID заказа')}
                </label>
                <p className="text-gray-500">{orderDetails.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('order_date', 'Дата заказа')}
                </label>
                <p className="text-gray-500">{orderDetails.date}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('total', 'Итого')}
                </label>
                <p className="text-gray-500">
                  {orderDetails.total.toLocaleString('uk-UA', {
                    style: 'currency',
                    currency: 'UAH'
                  })}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('payment_method', 'Метод оплаты')}
                </label>
                <p className="text-gray-500">{orderDetails.paymentMethod}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('delivery_method', 'Метод доставки')}
                </label>
                <p className="text-gray-500">{orderDetails.deliveryMethod}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('estimated_delivery', 'Оценка доставки')}
                </label>
                <p className="text-gray-500">{orderDetails.estimatedDelivery}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('customer_name', 'Имя покупателя')}
                </label>
                <p className="text-gray-500">
                  {orderDetails.customer.firstName} {orderDetails.customer.lastName}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('customer_phone', 'Телефон покупателя')}
                </label>
                <p className="text-gray-500">{orderDetails.customer.phone}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('customer_email', 'Электронная почта покупателя')}
                </label>
                <p className="text-gray-500">{orderDetails.customer.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('shipping_address', 'Адрес доставки')}
                </label>
                <p className="text-gray-500">
                  {orderDetails.shippingAddress.city},{' '}
                  {orderDetails.shippingAddress.novaPoshtaOffice}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('notes', 'Примечания')}
                </label>
                <p className="text-gray-500">{orderDetails.notes}</p>
              </div>
            </div>
          </div>
        </div>
        <div className="lg:col-span-1">
          <div className="bg-white p-6 md:p-8 rounded-lg shadow-lg w-full">
            <h2 className="text-3xl font-semibold mb-8 text-gray-800">
              {t('your_order', 'Ваш заказ')}
            </h2>
            <div className="space-y-6">
              {cart.map(item => (
                <div
                  key={item.id}
                  className="flex items-center justify-between pb-4 border-b border-gray-200 last:border-b-0 cart-item-summary"
                >
                  <div className="flex items-center space-x-4 cart-item-details">
                    <img
                      src={
                        item.images?.[0]?.url || // если изображение в формате {url: "..."}
                        (typeof item.images?.[0] === 'string'
                          ? item.images[0] // если просто строка URL
                          : item.image || // если есть поле image
                            item.thumbnail || // если есть поле thumbnail
                            placeholderImage) // если ничего нет, используем плейсхолдер
                      }
                      alt={item.name}
                      className="w-16 h-16 object-cover rounded-md shadow"
                    />
                    <div>
                      <h3 className="text-lg font-medium text-gray-800 item-name">{item.name}</h3>
                      <p className="text-sm text-gray-500">
                        {t('quantity', 'Количество')}: {item.quantity}
                      </p>
                    </div>
                  </div>
                  <div className="text-right cart-item-price">
                    <p className="text-lg font-semibold text-gray-800">
                      {(item.price * item.quantity).toLocaleString('uk-UA', {
                        style: 'currency',
                        currency: 'UAH'
                      })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-8 pt-6 border-t border-gray-300">
              <h3 className="text-2xl font-semibold mb-4 text-gray-800">{t('total', 'Итого')}</h3>
              <p className="text-lg font-semibold text-gray-800">
                {orderDetails.total.toLocaleString('uk-UA', { style: 'currency', currency: 'UAH' })}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
