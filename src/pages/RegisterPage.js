import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../supabaseClient';
import { toast } from 'react-toastify';
import { FaEnvelope, FaLock, FaUser } from 'react-icons/fa';

const RegisterPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: ''
  });

  const handleChange = e => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEmailRegister = async e => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      toast.error(t('passwords_dont_match', 'Пароли не совпадают'));
      return;
    }

    setLoading(true);

    try {
      // Step 1: Sign up the user without metadata to avoid trigger issues
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          // Keep emailRedirectTo for confirmation flow
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (authError) {
        // Handle specific auth errors if needed, otherwise rethrow
        if (authError.message.includes('User already registered')) {
          toast.error(
            t('user_already_registered', 'Пользователь с таким email уже зарегистрирован.')
          );
        } else {
          throw authError; // Rethrow for generic error handling
        }
        return; // Stop execution if auth error
      }

      // Step 2: If user creation is successful, manually create the profile
      if (authData.user) {
        try {
          const serviceRoleKey = process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;

          if (!serviceRoleKey) {
            toast.error(
              t('configuration_error', 'Ошибка конфигурации. Обратитесь к администратору.')
            );
            return;
          }
          const profileResponse = await fetch(
            'https://dmdijuuwnbwngerkbfak.supabase.co/rest/v1/profiles',
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${serviceRoleKey}`,
                apikey: serviceRoleKey, // Supabase requires apikey header for REST
                Prefer: 'return=minimal' // Don't return the created object
              },
              body: JSON.stringify({
                id: authData.user.id, // This is likely the PK of the profiles table
                user_id: authData.user.id, // This is the FK to auth.users and was missing
                email: formData.email,
                first_name: formData.firstName,
                last_name: formData.lastName,
                // username: formData.email, // Or generate a unique username if needed
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
            }
          );

          if (!profileResponse.ok) {
            const profileErrorText = await profileResponse.text();
            // Even if profile creation fails, the user is created.
            // You might want to inform the user or log this for admin attention.
            toast.warn(
              t(
                'profile_creation_failed_contact_support',
                'Аккаунт создан, но не удалось сохранить детали профиля. Обратитесь в поддержку.'
              )
            );
          } else {
          }
        } catch (profileInsertError) {
          toast.warn(
            t(
              'profile_creation_error_contact_support',
              'Аккаунт создан, но произошла ошибка при сохранении профиля. Обратитесь в поддержку.'
            )
          );
        }
      } else if (!authError) {
        // This case should ideally not be reached if authError is not thrown and user is null
        toast.error(t('registration_incomplete', 'Регистрация не завершена. Попробуйте снова.'));
        return;
      }

      // If we reach here and there was no authError, registration (user part) was successful
      if (!authError) {
        toast.success(
          t('register_success', 'Регистрация успешна! Проверьте свою почту для подтверждения.')
        );
        navigate('/login');
      }
    } catch (error) {
      console.error('Error signing up:', error);
      // Check for the specific database error to provide a more targeted message
      if (error.message && error.message.includes('Database error saving new user')) {
        toast.error(
          t(
            'register_failed_database_issue',
            'Ошибка регистрации из-за проблемы с базой данных. Пожалуйста, попробуйте позже.'
          )
        );
      } else if (error.message && error.message.includes('User already registered')) {
        // This is already handled above, but as a fallback
        toast.error(
          t('user_already_registered', 'Пользователь с таким email уже зарегистрирован.')
        );
      } else {
        toast.error(t('register_failed', 'Ошибка регистрации: ') + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>{t('register', 'Регистрация')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-semibold text-center mb-6">
            {t('create_account', 'Создать аккаунт')}
          </h1>

          <form onSubmit={handleEmailRegister} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('first_name', 'Имя')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaUser className="text-gray-400" />
                  </div>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleChange}
                    className="pl-10 w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary p-2"
                    required
                  />
                </div>
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('last_name', 'Фамилия')}
                </label>
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary p-2"
                />
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                {t('email', 'Email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaEnvelope className="text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="pl-10 w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary p-2"
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                {t('password', 'Пароль')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="pl-10 w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary p-2"
                  required
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {t('confirm_password', 'Подтвердите пароль')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="pl-10 w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary p-2"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              {loading ? t('registering', 'Регистрация...') : t('register', 'Зарегистрироваться')}
            </button>
          </form>

          <div className="text-center mt-6">
            <p className="text-sm text-gray-600">
              {t('already_have_account', 'Уже есть аккаунт?')}{' '}
              <Link to="/login" className="text-primary hover:text-primary-dark font-medium">
                {t('login', 'Войти')}
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default RegisterPage;
