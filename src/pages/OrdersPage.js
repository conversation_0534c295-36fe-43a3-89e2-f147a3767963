import React, { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../supabaseClient';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const OrdersPage = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(null);
  const [orderItems, setOrderItems] = useState({});

  // Функция для создания необходимых таблиц, если они не существуют
  const ensureTablesExist = async () => {
    try {
      // Проверяем и создаем таблицу orders
      await supabase.rpc('exec_sql', {
        query: `
          CREATE TABLE IF NOT EXISTS orders (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            customer_name TEXT NOT NULL,
            customer_email TEXT,
            customer_phone TEXT NOT NULL,
            shipping_address JSONB,
            total_amount DECIMAL(10, 2) NOT NULL,
            status TEXT DEFAULT 'pending',
            payment_method TEXT,
            payment_status TEXT DEFAULT 'pending',
            notes TEXT,
            user_id UUID,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
          );

          CREATE TABLE IF NOT EXISTS order_items (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            order_id UUID REFERENCES orders(id),
            product_id UUID,
            quantity INTEGER NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            product_name TEXT NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
        `
      });
    } catch (error) {
      console.error('Error ensuring tables exist:', error);
    }
  };

  useEffect(() => {
    // Проверяем и создаем таблицы при первой загрузке
    ensureTablesExist();

    const fetchOrders = async () => {
      if (!user) return;

      setLoading(true);
      try {
        // Загружаем заказы пользователя
        const { data, error } = await supabase
          .from('orders')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        if (data && data.length > 0) {
          setOrders(data);

          // Загружаем все товары для заказов
          const orderIds = data.map(order => order.id);
          const { data: itemsData, error: itemsError } = await supabase
            .from('order_items')
            .select('*')
            .in('order_id', orderIds);

          if (itemsError) throw itemsError;

          // Группируем товары по id заказа
          const itemsByOrderId = {};
          itemsData.forEach(item => {
            if (!itemsByOrderId[item.order_id]) {
              itemsByOrderId[item.order_id] = [];
            }
            itemsByOrderId[item.order_id].push(item);
          });

          setOrderItems(itemsByOrderId);
        } else {
          setOrders([]);
        }
      } catch (err) {
        console.error('Error fetching orders:', err);
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user]);

  // Функция для форматирования даты
  const formatDate = dateString => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Функция для отображения статуса заказа
  const getOrderStatusLabel = status => {
    switch (status) {
      case 'pending':
        return t('pending', 'Ожидает обработки');
      case 'processing':
        return t('processing', 'В обработке');
      case 'packed':
        return t('packed', 'Упакован');
      case 'shipped':
        return t('shipped', 'Отправлен');
      case 'delivered':
        return t('delivered', 'Доставлен');
      case 'cancelled':
        return t('cancelled', 'Отменен');
      default:
        return status || '—';
    }
  };

  // Функция для получения класса стиля статуса
  const getStatusClass = status => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'packed':
        return 'bg-indigo-100 text-indigo-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Функция для форматирования цены
  const formatPrice = price => {
    return typeof price === 'number' ? `${price.toFixed(2)} ₴` : '—';
  };

  if (!user) {
    return (
      <div className="max-w-3xl mx-auto py-12 px-4">
        <h1 className="text-3xl font-bold mb-6">Мои заказы</h1>
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <p className="text-lg mb-4">
            Пожалуйста,{' '}
            <Link to="/login" className="text-primary underline">
              войдите
            </Link>{' '}
            в аккаунт.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-6">Мои заказы</h1>
      <div className="bg-white rounded-lg shadow p-6">
        {loading ? (
          <div className="text-center py-8">Загрузка...</div>
        ) : orders.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">У вас пока нет заказов.</p>
            <Link to="/categories" className="btn-primary inline-block">
              Перейти в каталог
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {orders.map(order => (
              <div key={order.id} className="border rounded-lg overflow-hidden">
                <div
                  className="bg-gray-50 p-4 flex flex-wrap justify-between items-center cursor-pointer"
                  onClick={() => setExpanded(expanded === order.id ? null : order.id)}
                >
                  <div>
                    <p className="text-sm text-gray-500">
                      Заказ №:{' '}
                      <span className="font-medium text-black">{order.id.substring(0, 8)}</span>
                    </p>
                    <p className="text-sm text-gray-500">{formatDate(order.created_at)}</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusClass(order.status)}`}
                    >
                      {getOrderStatusLabel(order.status)}
                    </span>
                    <span className="font-medium">{formatPrice(order.total_amount)}</span>
                  </div>
                </div>

                {expanded === order.id && (
                  <div className="p-4">
                    <h3 className="font-medium mb-2">Товары:</h3>
                    {orderItems[order.id] && orderItems[order.id].length > 0 ? (
                      <div className="space-y-2 mb-4">
                        {orderItems[order.id].map((item, idx) => (
                          <div key={idx} className="flex justify-between border-b pb-2">
                            <div>
                              {item.product_name}{' '}
                              <span className="text-gray-500">× {item.quantity}</span>
                            </div>
                            <div>{formatPrice(item.price * item.quantity)}</div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 mb-4">Детали товаров недоступны</p>
                    )}

                    {order.shipping_address && (
                      <div className="mb-4">
                        <h3 className="font-medium mb-1">Адрес доставки:</h3>
                        <p className="text-gray-700">
                          {order.shipping_address.city}, {order.shipping_address.nova_poshta_office}
                        </p>
                      </div>
                    )}

                    {order.notes && (
                      <div className="mb-4">
                        <h3 className="font-medium mb-1">Примечания:</h3>
                        <p className="text-gray-700">{order.notes}</p>
                      </div>
                    )}

                    <div className="mt-4 pt-4 border-t flex justify-between items-center">
                      <div className="text-sm text-gray-500">
                        Способ оплаты:{' '}
                        <span className="font-medium">
                          {order.payment_method === 'cash_on_delivery'
                            ? 'Наложенный платеж'
                            : order.payment_method}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        Статус оплаты:{' '}
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${order.payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}
                        >
                          {order.payment_status === 'paid' ? 'Оплачен' : 'Ожидает оплаты'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default OrdersPage;
