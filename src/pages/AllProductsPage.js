import React, { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../supabaseClient';
import FilterPanel from '../components/filters/FilterPanel';
import MobileFilterDrawer from '../components/filters/MobileFilterDrawer';
import SortDropdown from '../components/filters/SortDropdown';
import AnimatedLoading from '../components/AnimatedLoading';
import { useLoading } from '../context/LoadingContext';
import PageHeader from '../components/PageHeader';

const AllProductsPage = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { startLoading, stopLoading } = useLoading();

  // State for products and filtering
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dataReady, setDataReady] = useState(false);
  const [extractedFilterParams, setExtractedFilterParams] = useState([]);
  const [error, setError] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({});
  const [sortOption, setSortOption] = useState('default');
  const [isFiltering, setIsFiltering] = useState(false);
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const [initialFiltersSet, setInitialFiltersSet] = useState(false);

  // Pagination
  const [displayLimit, setDisplayLimit] = useState(24);

  // Parse URL parameters on initial load
  useEffect(() => {
    if (initialFiltersSet) return;

    const searchParams = new URLSearchParams(location.search);
    const urlFilters = {};

    // Parse vendor/brand filter
    const vendors = searchParams.get('vendors');
    if (vendors) {
      urlFilters.vendors = vendors.split(',');
    }

    // Parse price filter
    const minPrice = searchParams.get('min_price');
    const maxPrice = searchParams.get('max_price');
    if (minPrice || maxPrice) {
      urlFilters.price = {
        min: minPrice || '',
        max: maxPrice || ''
      };
    }

    // Parse parameter filters
    const paramFilters = {};
    for (const [key, value] of searchParams.entries()) {
      if (!['vendors', 'min_price', 'max_price', 'sort'].includes(key)) {
        paramFilters[key] = value.split(',');
      }
    }

    if (Object.keys(paramFilters).length > 0) {
      urlFilters.params = paramFilters;
    }

    // Parse sort option
    const sort = searchParams.get('sort');
    if (sort) {
      setSortOption(sort);
    }

    // Set filters from URL
    if (Object.keys(urlFilters).length > 0) {
      setFilters(urlFilters);
    }

    setInitialFiltersSet(true);
  }, [location.search]);

  // Effect to manage loading state
  useEffect(() => {
    if (loading) {
      startLoading('Loading products...');
    } else {
      const timer = setTimeout(() => {
        stopLoading();
        setDataReady(true);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [loading, startLoading, stopLoading]);

  // Fetch all products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setDataReady(false);

        const { data: productsData, error: productsError } = await supabase
          .from('products')
          .select('*, attributes, vendor, brand, price')
          .eq('is_active', true)
          .order('created_at', { ascending: false });

        if (productsError) throw productsError;

        if (!productsData) {
          setProducts([]);
          setFilteredProducts([]);
          return;
        }

        if (productsData.length > 0) {
          // Process products and extract parameters
          const productsWithParams = await loadProductParams(productsData);

          // Update products state
          setProducts(productsWithParams);
          setFilteredProducts(productsWithParams);

          // Apply any existing filters from URL
          if (Object.keys(filters).length > 0) {
            applyFiltersAndSort(productsWithParams, filters, sortOption);
          }

          // Add artificial delay for better UX if there are many products
          const delay = productsData.length > 50 ? 1000 : 600;
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          setProducts([]);
          setFilteredProducts([]);
        }
      } catch (error) {
        console.error('Error in fetchProducts:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []); // Remove filters and sortOption from dependencies

  // Load product parameters
  const loadProductParams = useCallback(async products => {
    if (!products?.length) return [];

    const productsWithParams = [...products];

    try {
      // Create a map to store all unique parameter names and values
      const paramMap = new Map();

      productsWithParams.forEach(product => {
        product.params = [];

        if (product.attributes && typeof product.attributes === 'object') {
          Object.entries(product.attributes).forEach(([key, value]) => {
            if (value !== null && value !== undefined && key !== 'id' && key !== 'name') {
              const strValue = String(value).trim();

              if (strValue) {
                product.params.push({
                  name: key,
                  value: strValue,
                  product_id: product.id
                });

                if (!paramMap.has(key)) {
                  paramMap.set(key, new Set());
                }
                paramMap.get(key).add(strValue);
              }
            }
          });
        }
      });

      // Convert parameter map to the format expected by FilterPanel
      const filterParams = [];
      paramMap.forEach((values, name) => {
        if (values.size > 0) {
          filterParams.push({
            name,
            values: Array.from(values).sort()
          });
        }
      });

      // Sort filter parameters by name
      filterParams.sort((a, b) => a.name.localeCompare(b.name));
      setExtractedFilterParams(filterParams);

      return productsWithParams;
    } catch (error) {
      console.error('Error processing product parameters:', error);
      return productsWithParams;
    }
  }, []);

  // Handle filter changes
  const handleFilterChange = newFilters => {
    setFilters(newFilters);
    applyFiltersAndSort(products, newFilters, sortOption);

    // Update URL with filter parameters
    updateUrlWithFilters(newFilters, sortOption);
  };

  // Handle sort changes
  const handleSortChange = option => {
    setSortOption(option);
    applyFiltersAndSort(products, filters, option);

    // Update URL with sort parameter
    updateUrlWithFilters(filters, option);
  };

  // Update URL with current filters and sort
  const updateUrlWithFilters = (currentFilters, currentSort) => {
    const searchParams = new URLSearchParams();

    // Add vendors/brands filter
    if (currentFilters.vendors && currentFilters.vendors.length > 0) {
      searchParams.set('vendors', currentFilters.vendors.join(','));
    }

    // Add price filter
    if (currentFilters.price?.min) {
      searchParams.set('min_price', currentFilters.price.min);
    }
    if (currentFilters.price?.max) {
      searchParams.set('max_price', currentFilters.price.max);
    }

    // Add parameter filters
    if (currentFilters.params && Object.keys(currentFilters.params).length > 0) {
      Object.entries(currentFilters.params).forEach(([paramName, paramValues]) => {
        if (paramValues.length > 0) {
          searchParams.set(paramName, paramValues.join(','));
        }
      });
    }

    // Add sort option
    if (currentSort && currentSort !== 'default') {
      searchParams.set('sort', currentSort);
    }

    // Update URL without reloading the page
    const queryString = searchParams.toString();
    navigate(
      {
        pathname: location.pathname,
        search: queryString ? `?${queryString}` : ''
      },
      { replace: true }
    );
  };

  // Apply filters and sorting
  const applyFiltersAndSort = (products, filters, sortOpt) => {
    setIsFiltering(true);

    try {
      let result = [...products];

      // Filter by price
      if (filters.price?.min) {
        result = result.filter(p => p.price >= filters.price.min);
      }
      if (filters.price?.max) {
        result = result.filter(p => p.price <= filters.price.max);
      }

      // Filter by vendor/brand
      if (filters.vendors && filters.vendors.length > 0) {
        result = result.filter(p => {
          // Check both vendor and brand fields
          const productVendor = p.vendor || p.brand;
          return productVendor && filters.vendors.includes(productVendor);
        });
      }

      // Filter by parameters
      if (filters.params && Object.keys(filters.params).length > 0) {
        result = result.filter(product => {
          for (const [paramName, paramValues] of Object.entries(filters.params)) {
            if (!paramValues.length) continue;

            const productParams = product.params || [];
            const hasMatchingParam = productParams.some(
              param => param.name === paramName && paramValues.includes(param.value)
            );

            if (!hasMatchingParam) return false;
          }
          return true;
        });
      }

      // Sort results
      switch (sortOpt) {
        case 'price_asc':
          result.sort((a, b) => a.price - b.price);
          break;
        case 'price_desc':
          result.sort((a, b) => b.price - a.price);
          break;
        case 'name_asc':
          result.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case 'name_desc':
          result.sort((a, b) => b.name.localeCompare(a.name));
          break;
        case 'newest':
          result.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          break;
        default:
          // Default sorting by relevance or ID
          break;
      }

      setFilteredProducts(result);
    } catch (err) {
      console.error('Error applying filters:', err);
      setFilteredProducts(products); // Fallback to original products
    } finally {
      setIsFiltering(false);
    }
  };

  // Load more products
  const handleLoadMore = () => {
    setDisplayLimit(prev => prev + 24);
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({});
    handleFilterChange({ params: {}, price: {}, vendors: undefined });
  };

  // Get visible products for current page
  const visibleProducts = Array.isArray(filteredProducts)
    ? filteredProducts.slice(0, displayLimit)
    : [];

  // Show nothing while loading or waiting for transition
  if (loading || !dataReady) {
    return null;
  }

  return (
    <>
      <Helmet>
        <title>
          {t('all_products', 'All Products')} - {t('shop_name', 'Kitchen Shop')}
        </title>
        <meta
          name="description"
          content={t('all_products_description', 'Browse our complete catalog of products')}
        />
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <PageHeader
          title={t('all_products', 'All Products')}
          breadcrumbs={[
            { name: t('home', 'Home'), href: '/' },
            { name: t('all_products', 'All Products'), href: '/products' }
          ]}
        />

        {error ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {t('error_loading_products', 'Error loading products')}
                </h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => window.location.reload()}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    {t('try_again', 'Try Again')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="flex flex-col lg:flex-row justify-between items-center mb-6">
              <button
                onClick={() => setMobileFiltersOpen(true)}
                className="lg:hidden w-full mb-4 lg:mb-0 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                {t('show_filters', 'Show Filters')}
              </button>

              <div className="flex items-center">
                <span className="mr-2">{t('sort_by', 'Sort by:')}</span>
                <SortDropdown onSortChange={handleSortChange} initialSort={sortOption} />
              </div>

              <div className="w-full md:w-auto text-sm text-gray-500">
                {t('products_found', 'Products found')}: {filteredProducts.length}
              </div>
            </div>

            <div className="flex flex-col lg:flex-row gap-6">
              {/* Filters - desktop */}
              <div className="hidden lg:block lg:w-1/4">
                <FilterPanel
                  categoryId={null}
                  onFilterChange={handleFilterChange}
                  preloadedParams={extractedFilterParams}
                  initialFilters={filters}
                />
              </div>

              {/* Mobile filters */}
              <MobileFilterDrawer
                isOpen={mobileFiltersOpen}
                onClose={() => setMobileFiltersOpen(false)}
                categoryId={null}
                onFilterChange={handleFilterChange}
                preloadedParams={extractedFilterParams}
                initialFilters={filters}
              />

              {/* Product list */}
              <div className="lg:w-3/4">
                {isFiltering ? (
                  <div className="p-8">
                    <AnimatedLoading count={6} />
                  </div>
                ) : filteredProducts.length > 0 ? (
                  <>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {visibleProducts.map(product => (
                        <a href={`/product/${product.id}`} key={product.id}>
                          <div className="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow h-full flex flex-col">
                            <div className="relative h-60">
                              {product.is_on_sale && (
                                <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs">
                                  {t('sale', 'Sale')}
                                </div>
                              )}
                              {product.is_new && (
                                <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-md text-xs">
                                  {t('new', 'New')}
                                </div>
                              )}
                              <img
                                src={product.image || '/placeholder.png'}
                                alt={product.name}
                                className="w-full h-full object-contain"
                                onError={e => {
                                  e.target.src = '/placeholder.png';
                                }}
                              />
                            </div>
                            <div className="p-4 flex-grow flex flex-col">
                              <p className="font-medium text-lg mb-2 line-clamp-2 text-black">
                                {product.name}
                              </p>
                              {(product.vendor || product.brand) && (
                                <p className="text-sm text-gray-600 mt-auto mb-2">
                                  {product.vendor || product.brand}
                                </p>
                              )}
                              <div className="mt-auto">
                                <p className="text-lg font-bold text-primary">
                                  {new Intl.NumberFormat('uk-UA', {
                                    style: 'currency',
                                    currency: 'UAH'
                                  }).format(product.price)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </a>
                      ))}
                    </div>

                    {/* Load more button */}
                    {filteredProducts.length > displayLimit && (
                      <div className="text-center mt-8">
                        <button
                          onClick={handleLoadMore}
                          className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                        >
                          {t('load_more', 'Load more')}
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-gray-500 mb-4">
                      {t('no_products_found', 'No products found')}
                    </div>
                    {Object.keys(filters).length > 0 && (
                      <button
                        onClick={resetFilters}
                        className="text-primary hover:text-primary-dark transition-colors"
                      >
                        {t('clear_filters', 'Clear filters')}
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default AllProductsPage;
