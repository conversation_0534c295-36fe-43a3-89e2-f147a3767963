import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';

const AdminLayout = () => {
  const [isExpanded, setIsExpanded] = useState(true);
  const location = useLocation();

  const toggleMenu = () => {
    setIsExpanded(!isExpanded);
  };

  const isActive = path => {
    return location.pathname === path || location.pathname.startsWith(path);
  };

  // Массив с пунктами меню
  const menuItems = [
    {
      path: '/admin',
      icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
      text: 'dashboard'
    },
    {
      path: '/admin/products',
      icon: 'M20 7l-8-4-8 4m16 0v10a2 2 0 01-2 2H6a2 2 0 01-2-2V7m16 0H6',
      text: 'products'
    },
    {
      path: '/admin/categories',
      icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z',
      text: 'Categories'
    },
    {
      path: '/admin/orders',
      icon: 'M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z',
      text: 'Orders'
    },
    {
      path: '/admin/banners',
      icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
      text: 'banners'
    },
    {
      path: '/admin/feeds',
      icon: 'M6 5c7.18 0 13 5.82 13 13M6 11a7 7 0 017 7M6 17h.01',
      text: 'feeds'
    },
    {
      path: '/admin/moderation',
      icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01',
      text: 'product_moderation'
    },
    {
      path: '/admin/reviews',
      icon: 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z',
      text: 'Reviews'
    },
    {
      path: '/admin/inventory',
      icon: 'M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4',
      text: 'inventory'
    },
    {
      path: '/admin/users',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z',
      text: 'users'
    },
    {
      path: '/admin/store',
      icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
      text: 'store_settings'
    }
  ];

  return (
    <div className="flex min-h-screen">
      {/* Боковое меню администратора */}
      <div
        className={`bg-white shadow-md transition-all duration-300 ${isExpanded ? 'w-64' : 'w-20'}`}
      >
        <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
          {isExpanded ? (
            <h2 className="font-bold text-lg truncate">Меню администратора</h2>
          ) : (
            <div className="w-full flex justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h7"
                />
              </svg>
            </div>
          )}
          <button
            onClick={toggleMenu}
            className="p-1 rounded-full hover:bg-gray-200 transition-colors"
            aria-label={isExpanded ? 'Свернуть меню' : 'Развернуть меню'}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={isExpanded ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'}
              />
            </svg>
          </button>
        </div>
        <nav className="p-2">
          <ul className="space-y-1">
            {menuItems.map(item => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center gap-3 px-3 py-2.5 rounded-lg transition-colors ${
                    isActive(item.path) ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                  } ${!isExpanded && 'justify-center'}`}
                >
                  <span className="flex-shrink-0">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={item.icon}
                      />
                    </svg>
                  </span>
                  {isExpanded && <span className="flex-grow truncate">{item.text}</span>}
                  {!isExpanded && <span className="sr-only">{item.text}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Основной контент */}
      <div className="flex-1 p-6 transition-all duration-300">
        <Outlet />
      </div>

      {/* Стили для подсказок при наведении на иконки в свернутом режиме */}
      {!isExpanded && (
        <style jsx>{`
          @media (min-width: 768px) {
            .flex-shrink-0:hover::after {
              content: attr(data-tooltip);
              position: absolute;
              left: 100%;
              top: 50%;
              transform: translateY(-50%);
              margin-left: 8px;
              padding: 4px 8px;
              background-color: #333;
              color: white;
              border-radius: 4px;
              font-size: 14px;
              white-space: nowrap;
              z-index: 10;
            }
          }
        `}</style>
      )}
    </div>
  );
};

export default AdminLayout;
