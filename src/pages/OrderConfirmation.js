import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from 'react-i18next';

const OrderConfirmation = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { t } = useTranslation();
  const { cartItems, total } = location.state || { cartItems: [], total: 0 };

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (cartItems.length > 0) {
      const savedOrders = localStorage.getItem(`orders_${user.uid}`);
      const orders = savedOrders ? JSON.parse(savedOrders) : [];
      const newOrder = {
        id: orders.length + 1,
        date: new Date().toLocaleDateString(),
        total,
        items: cartItems
      };
      localStorage.setItem(`orders_${user.uid}`, JSON.stringify([...orders, newOrder]));
    }
  }, [cartItems, total, user, navigate]);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">{t('order_confirmation')}</h1>
      <div className="bg-white rounded-lg shadow-md p-6">
        <p className="text-gray-600 mb-4">{t('order_success')}</p>
        <p className="text-gray-600">{t('order_total', { total })}</p>
        <button onClick={() => navigate('/')} className="btn-primary mt-4">
          {t('back_to_home')}
        </button>
      </div>
    </div>
  );
};

export default OrderConfirmation;
