import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { FaChevronDown, FaChevronRight, FaStar, FaChevronLeft } from 'react-icons/fa';
import { Settings, ShoppingBag, Heart, Maximize2, Truck, Shield, Clock } from 'react-feather';
import { ChevronRightIcon } from '@heroicons/react/24/solid';
import 'react-medium-image-zoom/dist/styles.css';
import { useCart } from '../context/CartContext';
import { useCompare } from '../context/CompareContext';
import { useWishlist } from '../context/WishlistContext';
import { supabase, prepareProductParamsQuery } from '../supabaseClient';
import { Helmet } from 'react-helmet-async';
import ReviewModal from '../components/ReviewModal';
import ProductGallery from '../components/ProductGallery';
import RecommendationSlider from '../components/RecommendationSlider';

// Helper function to get numeric IDs for a UUID
const _getProductNumericIds = uuid => {
  if (!uuid) return [];
  const ids = prepareProductParamsQuery(uuid);
  if (Array.isArray(ids)) {
    return ids.filter(id => id != null);
  }
  return ids != null ? [ids] : [];
};

const ProductPage = () => {
  const { t } = useTranslation();
  const { productId } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [params, setParams] = useState([]);
  const [keyParams, setKeyParams] = useState([]);
  const [_otherParams, setOtherParams] = useState([]);
  const [paramGroups, setParamGroups] = useState({});
  const [loadingError, setLoadingError] = useState(false);
  const [_loading, setLoading] = useState(true);
  const { addToCart } = useCart();
  const { addToCompare, removeFromCompare, isInCompare } = useCompare();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const [selectedImage, setSelectedImage] = useState('');
  const [productImages, setProductImages] = useState([]);
  const [_galleryImages, setGalleryImages] = useState([]);
  const [relatedProducts, _setRelatedProducts] = useState([]);
  const [similarProducts, setSimilarProducts] = useState([]);
  const [recentlyViewed, setRecentlyViewed] = useState([]);

  // Enhanced image gallery with pagination
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const [reviews, setReviews] = useState([]);
  const [reviewsLoading, setReviewsLoading] = useState(true);
  const [reviewsError, setReviewsError] = useState(null);
  const [reviewsUpdated, setReviewsUpdated] = useState(false);

  // Состояние для модального окна отзывов
  const [showReviewModal, setShowReviewModal] = useState(false);

  // Дополнительные состояния для улучшенного UX
  const [quantity, setQuantity] = useState(1);
  const [selectedTab, setSelectedTab] = useState('description');
  const [isSticky, setIsSticky] = useState(false);
  const specificationsSectionRef = useRef(null); // Create a ref for the section

  // Scroll to top when a new product is loaded
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [productId]);

  // Увеличение и уменьшение количества товара
  const increaseQuantity = () => setQuantity(prev => prev + 1);
  const decreaseQuantity = () => setQuantity(prev => (prev > 1 ? prev - 1 : 1));

  // Effect to scroll to specifications when tab changes to it
  // THIS useEffect MUST be AFTER selectedTab is defined
  useEffect(() => {
    if (selectedTab === 'specifications' && specificationsSectionRef.current) {
      specificationsSectionRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [selectedTab]); // Re-run when selectedTab changes

  // Helper function to load XML feed - defined within component
  const loadXmlFeed = useCallback(async () => {
    const possiblePaths = ['/feed.xml', '/public/feed.xml', './feed.xml'];

    for (const path of possiblePaths) {
      try {
        const response = await fetch(path, {
          method: 'GET',
          headers: { Accept: 'application/xml, text/xml' },
          cache: 'no-cache'
        });

        if (!response.ok) continue;

        const xmlText = await response.text();
        if (!xmlText || xmlText.trim().length === 0) continue;

        // Parse XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

        if (xmlDoc.getElementsByTagName('parsererror').length > 0) continue;
        return xmlDoc;
      } catch (error) {}
    }
    return null;
  }, []); // No dependencies - this function is stable

  // Helper function to find product images in XML feed - defined within component
  const findProductImagesInFeed = useCallback((xmlDoc, externalId) => {
    if (!xmlDoc || !externalId) return [];

    const imageURLs = [];
    try {
      const offers = xmlDoc.getElementsByTagName('offer');

      // Find the offer with matching ID
      let targetOffer = null;
      for (let i = 0; i < offers.length; i++) {
        const offer = offers[i];
        const id = offer.getAttribute('id');

        if (id && id.trim() === String(externalId).trim()) {
          targetOffer = offer;
          break;
        }
      }

      if (!targetOffer) {
        return [];
      }

      // Extract images from <picture> tags
      const pictures = targetOffer.getElementsByTagName('picture');

      for (let i = 0; i < pictures.length; i++) {
        const pic = pictures[i];
        if (pic.textContent && pic.textContent.trim().length > 0) {
          const url = pic.textContent.trim();
          imageURLs.push(url);
        }
      }
      return [...new Set(imageURLs)]; // Remove duplicates
    } catch (error) {
      console.error('Ошибка при обработке XML:', error);
      return [];
    }
  }, []); // No dependencies - this function is stable

  // Function to handle image change
  const handleImageChange = image => {
    setSelectedImage(image);
    const index = productImages.findIndex(img => img === image);
    if (index !== -1) {
      setCurrentImageIndex(index);
    }
  };

  // New function to navigate through images
  const navigateImages = direction => {
    if (direction === 'next') {
      setCurrentImageIndex(prev => (prev + 1 >= productImages.length ? 0 : prev + 1));
    } else {
      setCurrentImageIndex(prev => (prev - 1 < 0 ? productImages.length - 1 : prev - 1));
    }
  };

  // Load images from XML feed with proper memoization to avoid recreation on every render
  const loadImagesFromFeed = useCallback(
    async externalId => {
      if (!externalId) return [];

      try {
        const feedXml = await loadXmlFeed();
        if (!feedXml) return [];

        const images = findProductImagesInFeed(feedXml, externalId);
        return images;
      } catch (error) {
        console.error('Error loading images from feed:', error);
        return [];
      }
    },
    [loadXmlFeed, findProductImagesInFeed]
  );

  // Функция для извлечения URL изображений из параметров товара
  const extractImagesFromParams = useCallback(
    params => {
      if (!params || !params.length) return;

      // Получаем текущие изображения
      const currentImages = [...productImages];
      const imageHash = new Set(currentImages);
      // Define newImagesFound outside the loop to fix the no-loop-func ESLint warning
      let newImagesFound = false;

      // Ищем параметры, которые могут содержать URL изображений
      const possibleImageParams = params.filter(param => {
        const name = param.name.toLowerCase();
        return (
          name.includes('фото') ||
          name.includes('изображен') ||
          name.includes('картин') ||
          name.includes('photo') ||
          name.includes('image') ||
          name.includes('gallery')
        );
      });

      // Ищем URL изображений во всех параметрах
      const urlRegex = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp)(\?[^\s]*)?)/gi;

      // Helper function to process matches to avoid creating functions in loops
      const processMatches = matches => {
        if (!matches) return;
        const foundMatches = [...matches];
        foundMatches.forEach(url => {
          if (!imageHash.has(url)) {
            imageHash.add(url);
            currentImages.push(url);
            newImagesFound = true;
          }
        });
      };

      // Сначала проверяем явно связанные с изображениями параметры
      for (const param of possibleImageParams) {
        const value = String(param.value);
        const matches = value.match(urlRegex);
        processMatches(matches);
      }

      // Затем проверяем все остальные параметры на наличие URL
      for (const param of params) {
        const value = String(param.value);
        const matches = value.match(urlRegex);
        processMatches(matches);
      }

      // Если найдены новые изображения, обновляем галерею
      if (newImagesFound) {
        setProductImages(currentImages);
      }
    },
    [productImages]
  );

  // Update selectedImage when image index changes
  useEffect(() => {
    if (productImages.length > 0 && currentImageIndex < productImages.length) {
      // Prevent unnecessary re-renders by checking if the image is actually changing
      if (selectedImage !== productImages[currentImageIndex]) {
        setSelectedImage(productImages[currentImageIndex]);
      }
    }
  }, [currentImageIndex, productImages, selectedImage]);

  // Helper function to process the parameters
  const processParameters = params => {
    if (!params || !Array.isArray(params) || params.length === 0) {
      return {
        keyParameters: [],
        technicalParameters: [],
        groupedParameters: {}
      };
    }

    try {
      // Ensure parameters have consistent property names
      const normalizedParams = params.map(param => ({
        id: param.id || `param-${Math.random()}`,
        name: param.name || param.param_name || 'Unknown',
        value: param.value || param.param_value || '-',
        group: param.group || param.param_group || 'Общие',
        is_key: param.is_key || false
      }));

      // Sort parameters to prioritize key parameters
      const sortedParams = [...normalizedParams].sort((a, b) => {
        // First priority: key parameters
        if (a.is_key && !b.is_key) return -1;
        if (!a.is_key && b.is_key) return 1;

        // Second priority: group name
        if (a.group < b.group) return -1;
        if (a.group > b.group) return 1;

        // Third priority: parameter name
        return a.name.localeCompare(b.name);
      });

      // Split parameters into key and technical
      const keyParameters = sortedParams.filter(param => param.is_key);
      const technicalParameters = sortedParams.filter(param => !param.is_key);

      // Group parameters by their group name
      const groupedParameters = {};
      technicalParameters.forEach(param => {
        const group = param.group || 'Общие';
        if (!groupedParameters[group]) {
          groupedParameters[group] = [];
        }
        groupedParameters[group].push(param);
      });

      return {
        keyParameters,
        technicalParameters,
        groupedParameters
      };
    } catch (e) {
      console.error('Error processing parameters:', e);
      return {
        keyParameters: [],
        technicalParameters: [],
        groupedParameters: {}
      };
    }
  };

  // Generate default parameters based on product information
  const generateDefaultParameters = product => {
    if (!product) return [];

    const defaultParams = [];
    const currentDate = new Date().toISOString();

    // Add basic parameters based on product properties
    if (product.name) {
      // Extract product type from name (e.g., "Kitchen Mixer" -> "Mixer")
      const productType = product.name.split(' ').pop();
      defaultParams.push({
        id: `default-type-${product.id}`,
        product_id: product.id,
        name: 'Type',
        value: productType,
        created_at: currentDate
      });
    }

    if (product.description) {
      // Try to extract material info from description
      const materialMatches = product.description.match(/(made of|using) ([a-zA-Z]+)/i);
      if (materialMatches && materialMatches[2]) {
        defaultParams.push({
          id: `default-material-${product.id}`,
          product_id: product.id,
          name: 'Material',
          value: materialMatches[2].trim(),
          created_at: currentDate
        });
      }
    }

    if (product.price) {
      // Add quality parameter based on price range
      let quality = 'Standard';
      if (product.price > 200) {
        quality = 'Premium';
      } else if (product.price < 50) {
        quality = 'Economy';
      }

      defaultParams.push({
        id: `default-quality-${product.id}`,
        product_id: product.id,
        name: 'Quality',
        value: quality,
        created_at: currentDate
      });
    }

    // Add common parameters for most products
    defaultParams.push(
      {
        id: `default-warranty-${product.id}`,
        product_id: product.id,
        name: 'Warranty',
        value: '12 months',
        created_at: currentDate
      },
      {
        id: `default-origin-${product.id}`,
        product_id: product.id,
        name: 'Country of Origin',
        value: 'Imported',
        created_at: currentDate
      }
    );

    // Add color parameter if it exists in product data
    if (product.color) {
      defaultParams.push({
        id: `default-color-${product.id}`,
        product_id: product.id,
        name: 'Color',
        value: product.color,
        created_at: currentDate
      });
    } else if (product.image_url) {
      // Try to guess color from product name
      const commonColors = [
        'black',
        'white',
        'red',
        'blue',
        'green',
        'silver',
        'gold',
        'gray',
        'brown'
      ];
      const colorMatch = commonColors.find(color => product.name.toLowerCase().includes(color));

      if (colorMatch) {
        defaultParams.push({
          id: `default-color-${product.id}`,
          product_id: product.id,
          name: 'Color',
          value: colorMatch.charAt(0).toUpperCase() + colorMatch.slice(1),
          created_at: currentDate
        });
      }
    }
    return defaultParams;
  };

  // Load product data
  useEffect(() => {
    if (!productId) return;

    const fetchProduct = async () => {
      setLoadingError(false);
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('products')
          .select('*, categories:category_id(id, name)')
          .eq('id', productId)
          .single();

        if (error) throw error;
        if (data) {
          setProduct(data);

          // Обновляем массив продуктовых изображений с основной картинкой товара
          const initialImages = [];
          if (data.image) {
            initialImages.push(data.image);
            setSelectedImage(data.image);
          }

          // Добавляем изображения из image_gallery, если они есть
          if (
            data.image_gallery &&
            Array.isArray(data.image_gallery) &&
            data.image_gallery.length > 0
          ) {
            setProductImages([...initialImages, ...data.image_gallery]);
            setGalleryImages(data.image_gallery);
          } else if (data.image_gallery && typeof data.image_gallery === 'string') {
            // Если gallery - это строка, пробуем распарсить её как JSON
            try {
              const parsedGallery = JSON.parse(data.image_gallery);
              if (Array.isArray(parsedGallery) && parsedGallery.length > 0) {
                setProductImages([...initialImages, ...parsedGallery]);
                setGalleryImages(parsedGallery);
              }
            } catch (e) {
              console.error('Ошибка парсинга image_gallery как JSON:', e);
            }
          } else {
            // Устанавливаем начальный массив с основной картинкой
            setProductImages(initialImages);
          }

          // Всегда загружаем изображения из фида, если у нас есть external_id
          if (data.external_id) {
            loadImagesFromFeed(data.external_id).then(images => {
              if (images && images.length > 0) {
                setGalleryImages(prev => {
                  // Объединяем текущие изображения с новыми из фида
                  const combined = [...(prev || [])];
                  images.forEach(img => {
                    if (!combined.includes(img)) {
                      combined.push(img);
                    }
                  });
                  return combined;
                });

                setProductImages(prev => {
                  // Создаем новый массив с учетом уже имеющихся изображений
                  const combinedImages = [...prev];
                  images.forEach(img => {
                    if (!combinedImages.includes(img)) {
                      combinedImages.push(img);
                    }
                  });
                  if (combinedImages.length > 0 && !selectedImage) {
                    setSelectedImage(combinedImages[0]);
                  }
                  return combinedImages;
                });
              }
            });
          }
        }
      } catch (err) {
        console.error('Error fetching product:', err);
        setLoadingError(true);
        // Only navigate to 404 for certain errors, not all errors
        if (err.code === 'PGRST116') {
          navigate('/404');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId, loadImagesFromFeed, navigate, selectedImage]);

  // Now extract the fetchParameters function from the previous useEffect to make it reusable
  const fetchParameters = useCallback(
    async productId => {
      if (!productId) {
        console.error('No product ID provided');
        return [];
      }
      try {
        // Сначала пробуем получить параметры по внешнему ID, который теперь используется в таблице product_params
        if (product && product.external_id) {
          const { data: paramsData, error: paramsError } = await supabase
            .from('product_params')
            .select('*')
            .eq('product_id', product.external_id);

          if (!paramsError && paramsData && paramsData.length > 0) {
            return paramsData.map(param => ({
              ...param,
              is_key: param.is_key || false,
              group: param.category || param.group || 'Общие'
            }));
          }
        }

        // Если по external_id не нашли, продолжаем искать другими способами
        let resolvedId = productId;
        // If a numeric ID is passed, resolve it to the UUID via external_id
        if (/^\d+$/.test(productId)) {
          try {
            const { data: prod, error: prodErr } = await supabase
              .from('products')
              .select('id')
              .eq('external_id', productId)
              .single();
            if (prodErr) {
              console.error('Error fetching product by external_id:', prodErr);
            } else if (prod && prod.id) {
              resolvedId = prod.id;
            }
          } catch (e) {
            console.error('Exception fetching by external_id:', e);
          }
        }
        // Attempt fetching parameters using numeric IDs equivalent to UUID
        const numericIds = _getProductNumericIds(resolvedId);
        if (numericIds.length > 0) {
          const { data: numericParams, error: numericError } = await supabase
            .from('product_params')
            .select('*')
            .in('product_id', numericIds);
          if (!numericError && numericParams && numericParams.length > 0) {
            return numericParams.map(param => ({
              ...param,
              is_key: param.is_key || false,
              group: param.category || param.group || 'Общие'
            }));
          } else if (numericError) {
            console.error('Error fetching parameters by numeric IDs:', numericError);
          }
        }

        // If we still don't have parameters, try finding a similar product and use its parameters
        if (product?.name) {
          try {
            // Create search terms from first two significant words of product name
            const words = product.name
              .split(' ')
              .filter(word => word.length > 3)
              .slice(0, 2);
            if (words.length > 0) {
              // Build a query that will work with Supabase
              const { data: similarProducts, error: similarError } = await supabase
                .from('products')
                .select('id, external_id')
                .or(words.map(word => `name.ilike.%${word}%`).join(','))
                .neq('id', productId)
                .limit(5);
              if (similarError) {
                console.error('Error fetching similar products:', similarError);
              } else if (similarProducts && similarProducts.length > 0) {
                for (const similarProduct of similarProducts) {
                  // Пробуем найти параметры по external_id
                  if (similarProduct.external_id) {
                    const { data: params, error: paramsError } = await supabase
                      .from('product_params')
                      .select('*')
                      .eq('product_id', similarProduct.external_id);

                    if (!paramsError && params && params.length > 0) {
                      return params.map(param => ({
                        ...param,
                        id: `similar-${param.id}`,
                        product_id: product.external_id || productId,
                        is_key: param.is_key || false,
                        group: param.category || param.group || 'Общие'
                      }));
                    }
                  }

                  // Если не нашли по external_id, пробуем через преобразование UUID
                  const numericIdsSim = _getProductNumericIds(similarProduct.id);
                  if (numericIdsSim.length === 0) continue;
                  const { data: similarParams, error: similarError2 } = await supabase
                    .from('product_params')
                    .select('*')
                    .in('product_id', numericIdsSim);
                  if (similarError2) {
                    console.error(
                      `Error fetching parameters for similar product ${similarProduct.id}:`,
                      similarError2
                    );
                    continue;
                  }
                  if (similarParams && similarParams.length > 0) {
                    return similarParams.map(param => ({
                      ...param,
                      id: `similar-${param.id}`,
                      product_id: productId,
                      is_key: param.is_key || false,
                      group: param.category || param.group || 'Общие'
                    }));
                  }
                }
              }
            } else {
            }
          } catch (e) {
            console.error('Error in similar product search:', e);
          }
        }

        // If still no parameters found, generate default ones based on product data
        const defaultParams = generateDefaultParameters(product);
        return defaultParams;
      } catch (e) {
        console.error('Error fetching product parameters:', e);
        return [];
      }
    },
    [product]
  );

  // Add a dedicated useEffect for parameter loading that depends on the product
  useEffect(() => {
    if (!product || !product.id) return;

    let isMounted = true;

    const loadParameters = async () => {
      try {
        const rawParams = await fetchParameters(product.id);

        if (!isMounted) return; // Don't update state if component unmounted

        const baseParams =
          rawParams && rawParams.length > 0 ? rawParams : generateDefaultParameters(product);
        const extraParams = [];
        if (product.material) {
          extraParams.push({
            id: `product-material-${product.id}`,
            name: 'Material',
            value: product.material,
            group: 'Общие',
            is_key: false
          });
        }
        if (product.color) {
          extraParams.push({
            id: `product-color-${product.id}`,
            name: 'Color',
            value: product.color,
            group: 'Общие',
            is_key: false
          });
        }
        if (product.size) {
          extraParams.push({
            id: `product-size-${product.id}`,
            name: 'Size',
            value: product.size,
            group: 'Общие',
            is_key: false
          });
        }
        if (product.weight) {
          extraParams.push({
            id: `product-weight-${product.id}`,
            name: 'Weight',
            value: `${product.weight} г`,
            group: 'Общие',
            is_key: false
          });
        }
        const mergedParams = [...baseParams, ...extraParams];
        const processed = processParameters(mergedParams);
        // Update parameter states
        setParams(mergedParams);
        setKeyParams(processed.keyParameters);
        setOtherParams(processed.technicalParameters);
        setParamGroups(processed.groupedParameters);

        // Извлекаем URL изображений из параметров товара (только один раз)
        if (mergedParams.length > 0) {
          extractImagesFromParams(mergedParams);
        }
      } catch (error) {
        console.error('Error loading parameters:', error);
        setParams([]);
        setKeyParams([]);
        setOtherParams([]);
        setParamGroups({});
      }
    };

    loadParameters();

    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    };
  }, [product, fetchParameters, extractImagesFromParams]);

  // Fetch reviews for the product
  const fetchReviews = useCallback(async () => {
    if (!productId) return;

    setReviewsLoading(true);
    try {
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('product_id', productId)
        .eq('status', 'approved')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setReviews(data || []);
      setReviewsError(null);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      setReviewsError(error.message);
    } finally {
      setReviewsLoading(false);
    }
  }, [productId]);

  // Add this to your existing useEffect dependencies
  useEffect(() => {
    if (reviewsUpdated) {
      fetchReviews();
      setReviewsUpdated(false);
    }
  }, [reviewsUpdated, fetchReviews]);

  // Add fetchReviews to your initial data loading useEffect
  useEffect(() => {
    if (productId) {
      fetchReviews();
    }
  }, [productId, fetchReviews]);

  // Эффект для отслеживания прокрутки страницы и создания "липкой" панели покупки
  useEffect(() => {
    const handleScroll = () => {
      const position = window.pageYOffset;
      setIsSticky(position > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Функция добавления в корзину с указанным количеством
  const handleAddToCartWithQuantity = () => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: quantity
    });
  };

  // Add function to fetch similar products
  const fetchSimilarProducts = useCallback(async () => {
    if (!product?.category_id) return;

    try {
      // Получаем товары из той же категории
      const { data: categoryProducts, error } = await supabase
        .from('products')
        .select('*, categories:category_id(*)')
        .eq('category_id', product.category_id)
        .neq('id', product.id)
        .limit(15);

      if (error) throw error;

      // Для каждого товара проверяем и обрабатываем изображения
      const processedProducts = await Promise.all(
        categoryProducts.map(async prod => {
          // Если у товара есть внешний ID, пробуем получить изображения из XML фида
          if (prod.external_id) {
            const feedImages = await loadImagesFromFeed(prod.external_id);
            if (feedImages && feedImages.length > 0) {
              return {
                ...prod,
                image: feedImages[0], // Используем первое изображение как основное
                image_gallery: feedImages // Сохраняем все изображения в галерею
              };
            }
          }

          // Если нет изображений из фида, используем существующие
          return {
            ...prod,
            image: prod.image || '/placeholder.png',
            image_gallery: Array.isArray(prod.image_gallery)
              ? prod.image_gallery
              : prod.image_gallery
                ? JSON.parse(prod.image_gallery)
                : []
          };
        })
      );

      // Случайно перемешиваем массив
      const shuffled = processedProducts.sort(() => 0.5 - Math.random());
      setSimilarProducts(shuffled);
    } catch (error) {
      console.error('Error fetching similar products:', error);
    }
  }, [product, loadImagesFromFeed]);

  // Add function to manage recently viewed products
  const updateRecentlyViewed = useCallback(() => {
    if (!product) return;

    // Get recently viewed from localStorage
    const recentlyViewedStr = localStorage.getItem('recentlyViewed');
    let recentlyViewedArr = recentlyViewedStr ? JSON.parse(recentlyViewedStr) : [];

    // Remove current product if it exists in the array
    recentlyViewedArr = recentlyViewedArr.filter(item => item.id !== product.id);

    // Add current product to the beginning with all necessary data
    recentlyViewedArr.unshift({
      id: product.id,
      name: product.name,
      price: product.price,
      image:
        product.image || (product.image_gallery && product.image_gallery[0]) || '/placeholder.png',
      old_price: product.old_price,
      discount: product.discount,
      category_id: product.category_id,
      external_id: product.external_id,
      image_gallery: product.image_gallery
    });

    // Keep only last 10 items
    recentlyViewedArr = recentlyViewedArr.slice(0, 10);

    // Save back to localStorage
    localStorage.setItem('recentlyViewed', JSON.stringify(recentlyViewedArr));

    // Update state
    setRecentlyViewed(recentlyViewedArr);
  }, [product]);

  // Add useEffect to fetch recommendations when product changes
  useEffect(() => {
    if (product) {
      fetchSimilarProducts();
      updateRecentlyViewed();
    }
  }, [product, fetchSimilarProducts, updateRecentlyViewed]);

  if (loadingError) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-2xl">
        <div className="text-center py-8 bg-red-50 rounded-lg border border-red-100">
          <svg
            className="w-16 h-16 text-red-500 mb-4 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            {t('product_error_title', 'Ошибка загрузки')}
          </h2>
          <p className="text-red-600 mb-4">
            {t('product_error', 'Не удалось загрузить информацию о товаре.')}
          </p>
          <div className="flex justify-center gap-4">
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              {t('reload', 'Перезагрузить страницу')}
            </button>
            <Link
              to="/"
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
            >
              {t('go_home', 'На главную')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    // Не показываем скелетон, вместо этого просто небольшое сообщение,
    // так как основной прелоадер уже отображается
    return null;
  }

  // Проверяем, добавлен ли товар в сравнение
  const productInCompare = product ? isInCompare(product.id) : false;

  // Функция для добавления или удаления товара из сравнения
  const handleAddToCompare = () => {
    // Создаем базовый объект товара для сравнения
    const productForCompare = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image
    };

    // Если товар уже в сравнении - удаляем, иначе добавляем
    if (productInCompare) {
      removeFromCompare(product.id);
    } else {
      addToCompare(productForCompare);
    }
  };

  // Проверяем, добавлен ли товар в избранное
  const productInWishlist = product ? isInWishlist(product.id) : false;

  // Функция для добавления/удаления из избранного
  const handleWishlistClick = () => {
    if (!product) return;
    if (productInWishlist) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  // Update the reviews tab section
  const renderReviewsTab = () => (
    <div className="bg-white rounded-lg p-6">
      <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between mb-8 border-b border-gray-100 pb-6">
        <div>
          <h3 className="text-xl font-semibold mb-2">Отзывы покупателей</h3>
          <div className="flex items-center gap-2">
            <div className="flex">
              {[1, 2, 3, 4, 5].map(star => (
                <svg
                  key={star}
                  className={`w-5 h-5 ${
                    (product.rating || 0) >= star ? 'text-amber-400' : 'text-gray-200'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.18c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.045 9.394c-.783-.57-.38-1.81.588-1.81h4.18a1 1 0 00.95-.69l1.286-3.967z" />
                </svg>
              ))}
            </div>
            <span className="text-gray-600">
              {product.rating || 0}/5 ({product.reviewsCount || 0} отзывов)
            </span>
          </div>
        </div>
        <button
          onClick={() => setShowReviewModal(true)}
          className="bg-primary text-white px-6 py-2.5 rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium shadow-sm"
        >
          Написать отзыв
        </button>
      </div>

      {reviewsLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : reviewsError ? (
        <div className="text-center py-8 text-red-600">
          Ошибка при загрузке отзывов: {reviewsError}
        </div>
      ) : reviews.length > 0 ? (
        <div className="space-y-6">
          {reviews.map(review => (
            <div key={review.id} className="border-b border-gray-100 py-6 last:border-b-0">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <div className="font-medium text-gray-900 mb-1">{review.name}</div>
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map(star => (
                        <svg
                          key={star}
                          className={`w-4 h-4 ${
                            star <= review.rating ? 'text-amber-400' : 'text-gray-200'
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.18c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.045 9.394c-.783-.57-.38-1.81.588-1.81h4.18a1 1 0 00.95-.69l1.286-3.967z" />
                        </svg>
                      ))}
                    </div>
                    <span className="text-sm text-gray-500">
                      {new Date(review.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">{review.comment}</p>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <svg
            className="mx-auto h-12 w-12 text-gray-400 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="text-gray-500 mb-4">У этого товара еще нет отзывов</p>
          <button
            onClick={() => setShowReviewModal(true)}
            className="bg-primary text-white px-6 py-2.5 rounded-lg hover:bg-primary-dark transition-colors text-sm font-medium shadow-sm"
          >
            Оставить первый отзыв
          </button>
        </div>
      )}
    </div>
  );

  return (
    <>
      <Helmet>
        <title>{`${product.name} - ${t('shop_name', 'Furniture Shop')}`}</title>
        <meta name="description" content={`${product.name} - ${product.short_description || ''}`} />
      </Helmet>

      {/* Модальное окно для добавления отзыва */}
      <ReviewModal
        isOpen={showReviewModal}
        onClose={() => setShowReviewModal(false)}
        productId={productId}
        productName={product.name}
        onSuccess={() => {
          setReviewsUpdated(true);
          fetchReviews();
        }}
      />

      {/* Sticky Bar для мобильной версии */}
      {isSticky && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg p-3 z-50 md:hidden animate-slideUp">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex flex-col">
              <span className="text-xs text-gray-500 truncate max-w-[150px]">{product.name}</span>
              <span className="text-lg font-bold text-gray-900">
                {new Intl.NumberFormat('uk-UA', {
                  style: 'currency',
                  currency: 'UAH',
                  minimumFractionDigits: 0
                }).format(product.price)}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center border border-gray-200 rounded-lg bg-gray-50">
                <button onClick={decreaseQuantity} className="px-2 py-1 text-gray-600">
                  -
                </button>
                <span className="w-8 text-center text-sm">{quantity}</span>
                <button onClick={increaseQuantity} className="px-2 py-1 text-gray-600">
                  +
                </button>
              </div>
              <button
                onClick={handleAddToCartWithQuantity}
                className="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg text-sm font-medium shadow-sm flex items-center"
              >
                <ShoppingBag size={16} className="mr-1" />
                {t('add_to_cart', 'В корзину')}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="container product-page-container mx-auto px-4 py-6 md:py-6 pt-10 max-w-[1400px]">
        {/* Основной блок с информацией о товаре */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-0">
            {/* Product Gallery */}
            <div className="lg:col-span-3 p-4 md:p-8 relative">
              <ProductGallery
                images={productImages}
                productName={product.name}
                productId={product.id}
              />
            </div>

            {/* Информация о товаре и действия */}
            <div className="lg:col-span-2 p-4 md:p-8 border-t md:border-t-0 md:border-l border-gray-100">
              {/* Название и артикул */}
              <div className="mb-6">
                <div className="flex items-center justify-between gap-2 mb-1">
                  <h1 className="text-2xl md:text-3xl font-bold text-gray-900">{product.name}</h1>
                </div>
                <div className="flex items-center gap-x-4 text-sm text-gray-500">
                  {product.article && (
                    <span>
                      Артикул: <span className="font-medium">{product.article}</span>
                    </span>
                  )}
                  {product.available && (
                    <span className="flex items-center text-emerald-600">
                      <span className="mr-1.5 inline-block w-2 h-2 rounded-full bg-emerald-500"></span>
                      В наличии
                    </span>
                  )}
                </div>
              </div>

              {/* Блок отзывов с кликабельной кнопкой */}
              <div className="flex items-center gap-3 mb-6">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map(star => (
                    <svg
                      key={star}
                      className={`w-4 h-4 ${
                        (product.rating || 0) >= star ? 'text-amber-400' : 'text-gray-200'
                      }`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.18c.969 0 1.371 1.24.588 1.81l-3.385 2.46a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.385-2.46a1 1 0 00-1.175 0l-3.385 2.46c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.045 9.394c-.783-.57-.38-1.81.588-1.81h4.18a1 1 0 00.95-.69l1.286-3.967z" />
                    </svg>
                  ))}
                </div>
                <span className="text-sm text-gray-500">
                  {product.rating || 0}/5 ({product.reviewsCount || 0} {t('reviews', 'отзывов')})
                </span>
                <button
                  onClick={() => setShowReviewModal(true)}
                  className="text-sm text-primary font-medium hover:text-primary-dark transition-colors underline"
                >
                  {t('write_review', 'Написать отзыв')}
                </button>
              </div>

              {/* Новая секция: Параметры продукта */}
              {params.length > 0 && (
                <div className="mb-6 border border-gray-100 rounded-lg bg-gray-50 p-4">
                  <h3 className="font-medium text-lg mb-3 flex items-center">
                    <Settings size={16} className="text-primary mr-2" />
                    {t('product_parameters', 'Параметры товара')}
                  </h3>
                  <div className="grid grid-cols-1 gap-y-2">
                    {params.slice(0, 5).map(param => (
                      <div key={param.id} className="flex items-center text-sm">
                        <div className="w-1/3 text-gray-600">{param.name}:</div>
                        <div className="w-2/3 font-medium">{param.value}</div>
                      </div>
                    ))}
                    {params.length > 5 && (
                      <button
                        onClick={() => {
                          setSelectedTab('specifications');
                        }}
                        className="text-primary hover:text-primary-dark text-sm mt-2 font-medium flex items-center"
                      >
                        {t('view_all_params', 'Все параметры')}
                        <ChevronRightIcon className="ml-1 w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Цена и счётчик количества */}
              <div className="mb-6">
                <div className="flex items-end gap-3 mb-3">
                  <div className="flex-grow">
                    <p className="text-3xl font-bold text-gray-900">
                      {new Intl.NumberFormat('uk-UA', {
                        style: 'currency',
                        currency: 'UAH',
                        minimumFractionDigits: 0
                      }).format(product.price)}
                    </p>
                    {(product.old_price || product.original_price) &&
                      ((product.old_price && Number(product.old_price) > Number(product.price)) ||
                        (product.original_price &&
                          Number(product.original_price) > Number(product.price))) && (
                        <p className="text-sm text-gray-500 line-through">
                          {new Intl.NumberFormat('uk-UA', {
                            style: 'currency',
                            currency: 'UAH',
                            minimumFractionDigits: 0
                          }).format(product.original_price || product.old_price)}
                        </p>
                      )}
                  </div>
                  <button
                    onClick={handleAddToCartWithQuantity}
                    className="bg-primary hover:bg-primary-dark text-white py-3.5 px-6 rounded-lg font-medium text-base shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-0.5 flex items-center justify-center"
                  >
                    <ShoppingBag size={18} className="mr-2" />
                    {t('add_to_cart', 'Додати до кошика')}
                  </button>
                </div>
                {product.old_price &&
                  Number(product.old_price) > Number(product.price) &&
                  product.discount > 0 && (
                    <p className="text-sm text-red-600 mt-1">
                      Экономия:{' '}
                      {new Intl.NumberFormat('uk-UA', {
                        style: 'currency',
                        currency: 'UAH',
                        minimumFractionDigits: 0
                      }).format(product.old_price - product.price)}
                    </p>
                  )}
              </div>

              {/* Ключевые параметры из таблицы product_params */}
              {params.filter(param => param.is_key).length > 0 && (
                <div className="mb-6 border-t border-gray-100 pt-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Settings size={16} className="text-primary" />
                    <h3 className="font-medium text-gray-900">Ключевые характеристики</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                    {params
                      .filter(param => param.is_key)
                      .slice(0, 6)
                      .map((param, idx) => (
                        <div key={idx} className="col-span-1">
                          <span className="text-xs text-gray-500 block">{param.name}</span>
                          <span className="text-sm font-medium">{param.value}</span>
                        </div>
                      ))}
                  </div>
                  {params.filter(param => param.is_key).length > 6 && (
                    <button
                      onClick={() => {
                        setSelectedTab('specifications');
                      }}
                      className="text-sm text-primary font-medium mt-3 hover:text-primary-dark underline inline-flex items-center"
                    >
                      {t('view_all_specs', 'Смотреть все характеристики')}
                      <svg
                        className="ml-1 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  )}
                </div>
              )}

              {/* Выбор количества и кнопки действий */}
              <div className="mb-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex items-center border border-gray-300 rounded-lg w-fit">
                    <button
                      onClick={decreaseQuantity}
                      className="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-l-lg"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M20 12H4"
                        />
                      </svg>
                    </button>
                    <input
                      type="number"
                      value={quantity}
                      onChange={e => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                      min="1"
                      className="w-12 text-center border-0 p-0 focus:ring-0 text-gray-900"
                    />
                    <button
                      onClick={increaseQuantity}
                      className="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-r-lg"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              {/* Преимущества */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm border-t border-gray-100 pt-6">
                <div className="flex items-start">
                  <Truck size={16} className="text-primary flex-shrink-0 mt-0.5" />
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">Быстрая доставка</p>
                    <p className="text-gray-500">2-3 рабочих дня</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Shield size={16} className="text-primary flex-shrink-0 mt-0.5" />
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">Гарантия качества</p>
                    <p className="text-gray-500">12 месяцев на все товары</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Settings size={16} className="text-primary flex-shrink-0 mt-0.5" />
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">Установка</p>
                    <p className="text-gray-500">Профессиональный монтаж</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Clock size={16} className="text-primary flex-shrink-0 mt-0.5" />
                  <div className="ml-3">
                    <p className="font-medium text-gray-900">Время работы</p>
                    <p className="text-gray-500">Пн-Пт: 9:00 - 18:00</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Similar Products Slider */}
        <RecommendationSlider title="Также вас могут заинтересовать" products={similarProducts} />

        {/* Product Description and Tabs */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-8">
          <div className="flex overflow-x-auto scrollbar-hide">
            <button
              onClick={() => setSelectedTab('description')}
              className={`px-6 py-3 font-medium text-sm whitespace-nowrap focus:outline-none ${
                selectedTab === 'description'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('description', 'Описание')}
            </button>
            <button
              onClick={() => setSelectedTab('specifications')}
              className={`px-6 py-3 font-medium text-sm whitespace-nowrap focus:outline-none ${
                selectedTab === 'specifications'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('specifications', 'Характеристики')}
            </button>
            <button
              onClick={() => setSelectedTab('reviews')}
              className={`px-6 py-3 font-medium text-sm whitespace-nowrap focus:outline-none ${
                selectedTab === 'reviews'
                  ? 'text-primary border-b-2 border-primary'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('reviews', 'Отзывы')} {product.reviewsCount > 0 && `(${product.reviewsCount})`}
            </button>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {selectedTab === 'description' && (
              <div className="prose max-w-none">
                <div
                  className="text-gray-700"
                  dangerouslySetInnerHTML={{ __html: product.description || 'Нет описания' }}
                ></div>
              </div>
            )}

            {selectedTab === 'specifications' && (
              <div id="specifications-section" ref={specificationsSectionRef} className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  {t('specifications', 'Технические характеристики')}
                </h2>

                {/* Ключевые параметры вверху, всегда видимы */}
                {keyParams && keyParams.length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                      <FaStar className="h-4 w-4 mr-2 text-primary" />
                      {t('keyFeatures', 'Ключевые характеристики')}
                    </h3>
                    <div className="bg-blue-50 border border-blue-100 rounded-lg overflow-hidden">
                      <table className="min-w-full">
                        <tbody>
                          {keyParams.map(param => (
                            <tr
                              key={param.id}
                              className="border-b border-blue-100 last:border-b-0 hover:bg-blue-100 transition-colors"
                            >
                              <td className="px-4 py-3 text-sm font-medium text-gray-700 w-1/3">
                                {param.name}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900 w-2/3">
                                {param.value}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Группировка параметров по категориям с аккордеоном */}
                {Object.keys(paramGroups).length > 0 ? (
                  <div className="space-y-4">
                    {Object.keys(paramGroups).map(groupName => {
                      return (
                        <div
                          key={groupName}
                          className="border border-gray-200 rounded-lg overflow-hidden"
                        >
                          <details className="group" open={groupName === 'Общие'}>
                            <summary className="flex justify-between items-center p-4 bg-gray-50 cursor-pointer">
                              <h3 className="text-md font-medium text-gray-900">{groupName}</h3>
                              <div className="transform group-open:rotate-180 transition-transform duration-200">
                                <FaChevronDown className="h-4 w-4 text-gray-500" />
                              </div>
                            </summary>
                            <div className="p-0">
                              <table className="min-w-full divide-y divide-gray-200">
                                <tbody className="bg-white">
                                  {paramGroups[groupName].map(param => (
                                    <tr
                                      key={param.id}
                                      className="hover:bg-gray-50 transition-colors"
                                    >
                                      <td className="px-4 py-3 text-sm text-gray-500 w-1/3 border-r border-gray-100">
                                        {param.name}
                                      </td>
                                      <td className="px-4 py-3 text-sm text-gray-900 w-2/3">
                                        {param.value}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </details>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="py-8 px-6 bg-gray-50 rounded-lg text-center">
                    <div className="text-gray-600 italic mb-2">
                      {t('noSpecifications', 'Нет дополнительных характеристик для этого товара')}
                    </div>
                    {!keyParams || keyParams.length === 0 ? (
                      <p className="text-sm text-gray-500">
                        {t(
                          'noSpecificationsAtAll',
                          'Технические характеристики для данного товара отсутствуют.'
                        )}
                      </p>
                    ) : (
                      <p className="text-sm text-gray-500">
                        {t(
                          'onlyKeySpecifications',
                          'Доступны только ключевые характеристики, указанные выше.'
                        )}
                      </p>
                    )}
                  </div>
                )}

                {/* Download specifications button */}
                {params.length > 0 && (
                  <div className="mt-8 flex justify-center">
                    <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                      <svg
                        className="mr-2 -ml-1 h-5 w-5 text-gray-500"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      {t('downloadSpecifications', 'Скачать спецификации')}
                    </button>
                  </div>
                )}
              </div>
            )}

            {selectedTab === 'reviews' && renderReviewsTab()}
          </div>
        </div>

        {/* Recently Viewed Products */}
        {recentlyViewed.length > 1 && (
          <RecommendationSlider
            title="Недавно просмотренные"
            products={recentlyViewed.filter(item => item.id !== product?.id)}
            withActions={false}
          />
        )}
      </div>
    </>
  );
};

export default ProductPage;
