import React, { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase /*, convertUUIDToBigInt */ } from '../supabaseClient'; // convertUUIDToBigInt is indeed unused and should be removed if not already.
import { Helmet } from 'react-helmet-async';
import FilterPanel from '../components/filters/FilterPanel';
import SortDropdown from '../components/filters/SortDropdown';
import MobileFilterDrawer from '../components/filters/MobileFilterDrawer';
import AnimatedLoading from '../components/AnimatedLoading';
import { useLoading } from '../context/LoadingContext';

const CategoryPage = () => {
  const { t } = useTranslation();
  const { categoryId } = useParams();
  const { startLoading, stopLoading } = useLoading(); // Используем контекст загрузки
  const [categories, setCategories] = useState([]); // For the main display grid on /categories
  // const [sidebarCategories, setSidebarCategories] = useState([]); // This state and its setter are unused.
  const [subcategories, setSubcategories] = useState([]); // For subcategories on a specific category page
  const [category, setCategory] = useState(null); // For the current specific category
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [dataReady, setDataReady] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [expandedCategories, setExpandedCategories] = useState({});

  // Состояние для фильтров и сортировки
  const [filters, setFilters] = useState({});
  const [sortOption, setSortOption] = useState('default');
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [isFiltering, setIsFiltering] = useState(false);

  // Добавить состояние для пагинации
  const [displayLimit, setDisplayLimit] = useState(12);
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);

  // Этот эффект управляет глобальным состоянием загрузки
  useEffect(() => {
    if (loading) {
      startLoading('Loading category data...');
    } else {
      // Используем таймер для плавного перехода
      const timer = setTimeout(() => {
        stopLoading();
        setDataReady(true);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [loading, startLoading, stopLoading]);

  // fetchCategories is now responsible for populating the sidebar category tree
  const fetchCategories = useCallback(
    async (allCategoriesWithCountsFromRpc = null) => {
      try {
        let categoriesToProcess = [];

        if (allCategoriesWithCountsFromRpc) {
          // Use pre-fetched data with counts if provided (for /categories route)
          categoriesToProcess = allCategoriesWithCountsFromRpc;
        } else if (categoryId) {
          // For a specific category page, fetch all categories and then determine product presence
          // This part still has N+1 if not careful, but is specific to /category/:id
          // For now, keeping its original logic for when categoryId is present
          const { data: allCats, error: allCatsError } = await supabase
            .from('categories')
            .select('*') // Consider selecting only 'id', 'name', 'parent_id'
            .order('name');
          if (allCatsError) throw allCatsError;

          // The N+1 helper function
          const categoryHasProductsRecursively = async (catId, allDbCategories) => {
            const { count, error } = await supabase
              .from('products')
              .select('id', { count: 'exact', head: true })
              .eq('category_id', catId);
            if (error) return false;
            if (count > 0) return true;

            const children = allDbCategories.filter(c => c.parent_id === catId);
            for (const child of children) {
              if (await categoryHasProductsRecursively(child.id, allDbCategories)) return true;
            }
            return false;
          };

          const activeCategories = [];
          for (const cat of allCats) {
            if (await categoryHasProductsRecursively(cat.id, allCats)) {
              activeCategories.push(cat);
            }
          }
          categoriesToProcess = activeCategories;
        } else {
          // Should not happen if fetchData provides data for /categories route
          return;
        }

        // Filter categories that have products (productCount > 0 if using pre-fetched data)
        // or use the recursive check if on a specific category page
        const categoriesForSidebar = allCategoriesWithCountsFromRpc
          ? categoriesToProcess.filter(cat => cat.productCount > 0)
          : categoriesToProcess; // categoriesToProcess is already filtered if categoryId was present

        // setSidebarCategories(categoriesForSidebar); // This call is related to the unused sidebarCategories state.

        if (categoryId) {
          setSelectedCategory(categoryId);
          const expandParents = catId => {
            const catToExpand = categoriesForSidebar.find(c => c.id === catId);
            if (catToExpand?.parent_id) {
              setExpandedCategories(prev => ({ ...prev, [catToExpand.parent_id]: true }));
              expandParents(catToExpand.parent_id);
            }
          };
          expandParents(categoryId);
        }
      } catch (error) {
        console.error('Error in fetchCategories (sidebar):', error);
      }
    },
    [categoryId]
  ); // Removed allCategoriesWithCountsFromRpc from deps as it's a param

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setDataReady(false); // Сбрасываем готовность данных при начале загрузки
        let fetchedProductsData = null; // Создаем переменную для хранения данных о продуктах

        if (categoryId) {
          // Fetch specific category
          const { data: categoryData, error: categoryError } = await supabase
            .from('categories')
            .select('*')
            .eq('id', categoryId)
            .single();

          if (categoryError) throw categoryError;
          setCategory(categoryData);

          // Fetch only subcategories that have products
          const { data: subcatsData, error: subcatsError } = await supabase
            .from('categories')
            .select('*')
            .eq('parent_id', categoryId);

          if (subcatsError) throw subcatsError;

          // Filter subcategories to only include those with products
          if (subcatsData) {
            // For each subcategory, check if it has products
            const subcatsWithProducts = [];
            for (const subcat of subcatsData) {
              const { count, error: countError } = await supabase
                .from('products')
                .select('id', { count: 'exact', head: true })
                .eq('category_id', subcat.id);

              if (!countError && count > 0) {
                subcatsWithProducts.push(subcat);
              }
            }

            setSubcategories(subcatsWithProducts);
          } else {
            setSubcategories([]);
          }

          // Get all subcategory IDs (including nested)
          const getAllSubcategoryIds = async parentId => {
            const result = [parentId];
            const { data, error } = await supabase
              .from('categories')
              .select('id')
              .eq('parent_id', parentId);

            if (!error && data) {
              for (const subcat of data) {
                const childIds = await getAllSubcategoryIds(subcat.id);
                result.push(...childIds);
              }
            }

            return result;
          };

          // Get all category IDs to fetch products from (current category + all subcategories)
          const categoryIds = await getAllSubcategoryIds(categoryId);

          // Fetch products for this category and all subcategories
          const { data: productsData, error: productsError } = await supabase
            .from('products')
            .select('*')
            .in('category_id', categoryIds)
            .eq('is_active', true);

          if (productsError) throw productsError;

          // Сохраняем данные о продуктах в локальную переменную
          fetchedProductsData = productsData || [];

          // Add logging to see if products are being returned from the database

          // Load product parameters and associate them with products
          if (fetchedProductsData && fetchedProductsData.length > 0) {
            const productsWithParams = await loadProductParams(fetchedProductsData);
            setProducts(productsWithParams);
            setFilteredProducts(productsWithParams);

            // Если продуктов много, добавляем искусственную задержку для лучшего UX
            const delay = fetchedProductsData.length > 20 ? 1000 : 600;
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            // No products found
            setProducts([]);
            setFilteredProducts([]);
          }
        } else {
          // Optimized logic for /categories route (no categoryId)

          // Step 1: Fetch all categories (basic info)
          const { data: allCategoriesData, error: categoriesError } = await supabase
            .from('categories')
            .select('id, name, image, parent_id') // Added parent_id for potential tree building
            .order('name');

          if (categoriesError) throw categoriesError;

          if (!allCategoriesData || allCategoriesData.length === 0) {
            setCategories([]); // For main display
            // Potentially set sidebar categories to empty as well if a separate state is used
            return;
          }

          // Step 2: Fetch product counts using the RPC function
          const { data: countsData, error: countsError } = await supabase.rpc(
            'get_category_product_counts_recursive' // Calling the new recursive function
          );

          if (countsError) throw countsError;

          const productCountsMap = (countsData || []).reduce((acc, item) => {
            // The new function returns 'total_products_in_branch'
            acc[item.category_id_fk] = item.total_products_in_branch;
            return acc;
          }, {});

          // Format categories for display: root categories that have products in their branch
          const formattedDisplayCategories = allCategoriesData
            .filter(cat => cat.parent_id === null && (productCountsMap[cat.id] || 0) > 0)
            .map(category => ({
              id: category.id,
              name: category.name,
              image: category.image,
              // This productCount now represents the total for the branch
              productCount: productCountsMap[category.id] || 0
            }));

          setCategories(formattedDisplayCategories); // This state is for the main grid

          // Prepare data for the sidebar category tree (all categories with their branch counts)
          const allCategoriesWithCounts = allCategoriesData.map(cat => ({
            ...cat,
            // This productCount also represents the total for the branch
            productCount: productCountsMap[cat.id] || 0
          }));

          // The existing fetchCategories function also needs to be refactored
          // to use these counts instead of its own N+1 queries.
          // For now, this optimized data contains allCategoriesWithCounts.
          // The `setCategories` above is for the main display.
          // The `fetchCategories` call later in useEffect might still cause issues if not updated.
          // Pass the efficiently fetched data to fetchCategories for the sidebar
          if (typeof fetchCategories === 'function') {
            // Ensure fetchCategories is defined
            fetchCategories(allCategoriesWithCounts);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
        // dataReady будет установлен в true через эффект с задержкой
      }
    };

    fetchData(); // fetchData will call fetchCategories with data if !categoryId
    if (categoryId) {
      // If categoryId is present, fetchCategories is called without pre-fetched counts
      // and will use its own (potentially still N+1 for this specific page) logic.
      fetchCategories();
    }
    // When !categoryId, fetchData now prepares allCategoriesWithCounts and calls fetchCategories(allCategoriesWithCounts)
    // So, no explicit call to fetchCategories() here for the !categoryId case.
  }, [categoryId, fetchCategories]); // fetchCategories is a dependency of useEffect

  // Fix the loadProductParams function to correctly attach parameters to products
  const [extractedFilterParams, setExtractedFilterParams] = useState([]);
  const loadProductParams = async products => {
    if (!products || !products.length) return [];

    // Make a copy of the products array to avoid mutation issues
    const productsWithParams = [...products];

    try {
      // Extract IDs from product objects and filter out any null or undefined IDs
      const productIds = products
        .map(product => product.id)
        .filter(id => id !== null && id !== undefined);

      if (!productIds.length) return productsWithParams;

      // const bigIntProductIds = productIds // This variable is unused since the block it was used in is removed.
      //   .map(id => {
      //     try {
      //       return convertUUIDToBigInt(id);
      //     } catch (error) {
      //       console.error(`Error converting UUID ${id} to BigInt:`, error);
      //       return null;
      //     }
      //   })
      //   .filter(id => id !== null);

      // Initialize parameters for all products from their attributes first
      // Create a map to store all unique parameter names and their values across products
      const paramMap = new Map();

      productsWithParams.forEach(product => {
        // Create a new params array for each product
        product.params = [];

        // If the product has attributes, convert them to params format
        if (product.attributes && typeof product.attributes === 'object') {
          Object.entries(product.attributes).forEach(([key, value]) => {
            if (value !== null && value !== undefined && key !== 'id' && key !== 'name') {
              // Normalize value to string for consistency
              const strValue = String(value).trim();

              if (strValue) {
                // Add attribute as a parameter
                product.params.push({
                  name: key,
                  value: strValue,
                  product_id: product.id
                });

                // Add to our parameter map for filter panel
                if (!paramMap.has(key)) {
                  paramMap.set(key, new Set());
                }
                paramMap.get(key).add(strValue);
              }
            }
          });
        }
      });

      // Only query database if we have valid IDs
      // The following block for fetching and merging product_params from the database
      // is removed because product.attributes already contains all necessary parameters.
      // if (bigIntProductIds.length > 0) {
      //);
      //   let allParams = [];
      //   const { data: paramsData, error: paramsError } = await supabase
      //     .from('product_params')
      //     .select('product_id, name, value')
      //     .in('product_id', bigIntProductIds);

      //   if (paramsError) {
      //     console.error('Error fetching parameters:', paramsError);
      //   } else if (paramsData && paramsData.length > 0) {
      //     allParams = paramsData;
      //     allParams.forEach(param => {
      //       if (param.name && param.value) {
      //         const strValue = String(param.value).trim();
      //         if (strValue) {
      //           if (!paramMap.has(param.name)) {
      //             paramMap.set(param.name, new Set());
      //           }
      //           paramMap.get(param.name).add(strValue);
      //         }
      //       }
      //     });
      //   }
      //

      //   if (allParams.length > 0) {
      //     const paramsByProductId = {};
      //     allParams.forEach(param => {
      //       try {
      //         const productUUID = convertBigIntToUUID(param.product_id);
      //         if (!paramsByProductId[productUUID]) {
      //           paramsByProductId[productUUID] = [];
      //         }
      //         paramsByProductId[productUUID].push(param);
      //       } catch (error) {
      //         console.error(`Error converting BigInt to UUID:`, error);
      //       }
      //     });

      //     productsWithParams.forEach(product => {
      //       const dbParams = paramsByProductId[product.id];
      //       if (dbParams && dbParams.length > 0) {
      //         const existingParamNames = new Set(product.params.map(p => p.name));
      //         dbParams.forEach(dbParam => {
      //           if (!existingParamNames.has(dbParam.name)) {
      //             product.params.push(dbParam);
      //           }
      //         });
      //       }
      //     });
      //   }
      // }
      // End of removed block

      // Convert our parameter map to the format expected by FilterPanel
      // FilterPanel expects an array of objects with name and values properties
      const filterParams = [];
      paramMap.forEach((values, name) => {
        // Only add parameters that have multiple values (useful for filtering)
        if (values.size > 0) {
          filterParams.push({
            name,
            values: Array.from(values).sort()
          });
        }
      });

      // Sort filter parameters by name for consistent display
      filterParams.sort((a, b) => a.name.localeCompare(b.name));

      // Store the extracted filter parameters in a state variable to pass to FilterPanel
      setExtractedFilterParams(filterParams);

      // Make filter parameters globally available to all components via window object
      window.preloadedFilterParams = filterParams;

      return productsWithParams;
    } catch (error) {
      console.error('Error processing product parameters:', error);
      return productsWithParams;
    }
  };

  // Обработчик изменения фильтров
  const handleFilterChange = newFilters => {
    setFilters(newFilters);
    applyFiltersAndSort(products, newFilters, sortOption);
  };

  // Обработчик изменения сортировки
  const handleSortChange = option => {
    setSortOption(option);
    applyFiltersAndSort(products, filters, option);
  };

  // Применение фильтров и сортировки
  const applyFiltersAndSort = (products, filters, sortOpt) => {
    setIsFiltering(true);

    try {
      // Фильтрация товаров
      let result = [...products];

      // Фильтрация по цене
      if (filters.price?.min) {
        result = result.filter(p => p.price >= filters.price.min);
      }
      if (filters.price?.max) {
        result = result.filter(p => p.price <= filters.price.max);
      }

      // Фильтрация по производителю
      if (filters.vendors && filters.vendors.length > 0) {
        result = result.filter(p => {
          // Проверяем наличие производителя в vendor или brand
          const productVendor = p.vendor || p.brand;
          return productVendor && filters.vendors.includes(productVendor);
        });
      }

      // Фильтрация по параметрам
      if (filters.params && Object.keys(filters.params).length > 0) {
        result = result.filter(product => {
          // Для каждого свойства в фильтре
          for (const [paramName, paramValues] of Object.entries(filters.params)) {
            if (!paramValues.length) continue;

            // Проверяем, есть ли у товара совпадающий параметр с нужным значением
            const productParams = product.params || [];
            const hasMatchingParam = productParams.some(
              param => param.name === paramName && paramValues.includes(param.value)
            );

            if (!hasMatchingParam) return false;
          }
          return true;
        });
      }

      // Сортировка результатов
      switch (sortOpt) {
        case 'price_asc':
          result.sort((a, b) => a.price - b.price);
          break;
        case 'price_desc':
          result.sort((a, b) => b.price - a.price);
          break;
        case 'name_asc':
          result.sort((a, b) => a.name.localeCompare(b.name));
          break;
        case 'name_desc':
          result.sort((a, b) => b.name.localeCompare(a.name));
          break;
        case 'newest':
          result.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          break;
        default:
          // По умолчанию сортировка по релевантности или ID
          break;
      }

      setFilteredProducts(result);
    } catch (err) {
      console.error('Error applying filters:', err);
      setFilteredProducts(products); // Fallback to original products
    } finally {
      setIsFiltering(false);
    }
  };

  // Добавить функцию для загрузки дополнительных товаров
  const handleLoadMore = () => {
    setDisplayLimit(prev => prev + 12);
  };

  // Изменить отображение товаров, чтобы показывать только ограниченное количество
  const visibleProducts = Array.isArray(filteredProducts)
    ? filteredProducts.slice(0, displayLimit)
    : [];

  // Function to toggle category expansion
  const toggleCategory = categoryId => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Function to build the category tree
  // const renderCategories = (parentId = null, level = 0) => {
  //   return categories
  //     .filter(category => category.parent_id === parentId)
  //     .map(category => {
  //       const hasChildren = categories.some(c => c.parent_id === category.id);
  //       const isExpanded = expandedCategories[category.id];
  //       const isSelected = category.id === selectedCategory;

  //       return (
  //         <div
  //           key={category.id}
  //           className="category-item"
  //           style={{ marginLeft: `${level * 20}px` }}
  //         >
  //           <div className={`flex items-center py-3 border-b ${isSelected ? 'bg-gray-100' : ''}`}>
  //             {hasChildren && (
  //               <button
  //                 onClick={() => toggleCategory(category.id)}
  //                 className="mr-2 w-6 h-6 flex items-center justify-center rounded-full bg-gray-200 text-gray-700"
  //               >
  //                 {isExpanded ? '-' : '+'}
  //               </button>
  //             )}

  //             <Link
  //               to={`/category/${category.id}`}
  //               className="flex-grow font-medium hover:text-primary"
  //               onClick={() => setSelectedCategory(category.id)}
  //             >
  //               {category.name}
  //             </Link>

  //             {category.image && (
  //               <img
  //                 src={category.image}
  //                 alt={category.name}
  //                 className="w-8 h-8 object-cover rounded ml-2"
  //                 onError={e => {
  //                   e.target.onerror = null;
  //                   e.target.src = 'https://placehold.co/50x50/EEE/31343C?text=Img';
  //                 }}
  //               />
  //             )}
  //           </div>

  //           {/* Show subcategories if expanded */}
  //           {hasChildren && isExpanded && renderCategories(category.id, level + 1)}
  //         </div>
  //       );
  //     });
  // };

  // Показываем "ничего" пока загружаются данные или не прошла задержка для красивого перехода
  if (loading || !dataReady) {
    return null;
  }

  // Отображение списка всех категорий
  if (!categoryId) {
    return (
      <>
        <Helmet>
          <title>{t('categories', 'Категории')}</title>
        </Helmet>
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold mb-8">{t('categories', 'Категории')}</h1>

          {/* Existing grid display (hierarchical category display removed) */}
          {categories.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {categories.map(category => (
                <Link key={category.id} to={`/category/${category.id}`} className="block group">
                  <div className="bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300 hover:shadow-lg border border-gray-200">
                    <div className="aspect-square relative overflow-hidden">
                      <img
                        src={
                          category.image || 'https://placehold.co/300x300/EEE/31343C?text=No+Image'
                        }
                        alt={category.name}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        loading="lazy"
                        onError={e => {
                          // Added onError for placeholder
                          e.target.onerror = null; // prevent infinite loop if placeholder also fails
                          e.target.src = 'https://placehold.co/300x300/EEE/31343C?text=No+Image';
                        }}
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent p-4">
                        <span
                          className="text-white text-sm font-medium"
                          style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}
                        >
                          {t('products_count', '{{count}} товаров', {
                            count: category.productCount
                          })}
                        </span>
                      </div>
                    </div>
                    <h2 className="p-3 text-lg font-medium text-left text-gray-700">
                      {category.name}
                    </h2>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {t('no_categories', 'Категории не найдены')}
            </div>
          )}
        </div>
      </>
    );
  }

  // Reset all filters - Move inside component to fix the undefined variables
  const resetFilters = () => {
    setFilters({});
    // Reset filter values
    handleFilterChange({ params: {}, price: {}, vendors: undefined });
  };

  return (
    <>
      <Helmet>
        <title>
          {category?.name
            ? `${category.name} - ${t('shop_name', 'Kitchen Shop')}`
            : t('categories')}
        </title>
      </Helmet>

      {/* Add breadcrumbs navigation */}
      <div className="category-breadcrumbs mt-2 md:mt-0">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center text-sm text-gray-600">
            <Link to="/" className="hover:text-primary">
              {t('home', 'Главная')}
            </Link>
            <span className="mx-2">/</span>
            {category ? (
              <>
                <Link to="/categories" className="hover:text-primary">
                  {t('categories', 'Категории')}
                </Link>
                <span className="mx-2">/</span>
                <span className="text-gray-900">{category.name}</span>
              </>
            ) : (
              <span className="text-gray-900">{t('categories', 'Категории')}</span>
            )}
          </div>
        </div>
      </div>

      <div className="container category-page-container mx-auto px-4 py-4 md:py-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-4 md:mb-8">
          {category?.name || t('category', 'Категория')}
        </h1>

        {/* Показываем подкатегории, если они есть */}
        {subcategories.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-semibold mb-4">{t('subcategories', 'Подкатегории')}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {subcategories.map(subcat => (
                <Link
                  key={subcat.id}
                  to={`/category/${subcat.id}`}
                  className="block p-4 border rounded-lg hover:shadow-md transition-all"
                >
                  {subcat.image ? (
                    <img
                      src={subcat.image}
                      alt={subcat.name}
                      className="w-full h-32 object-cover rounded mb-2"
                      onError={e => {
                        e.target.src = '/placeholder.png';
                      }}
                    />
                  ) : (
                    <div className="w-full h-32 bg-gray-100 rounded flex items-center justify-center mb-2">
                      <span className="text-gray-400">{t('no_image', 'Нет изображения')}</span>
                    </div>
                  )}
                  <h3 className="text-lg font-medium hover:text-primary">{subcat.name}</h3>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Добавляем информацию о количестве товаров */}
        <div className="mb-4 text-lg">
          {products.length > 0 ? (
            <p>
              {t('found_products', 'Найдено товаров')}: <strong>{products.length}</strong>
              {subcategories.length > 0 && (
                <span className="text-gray-500 ml-1">
                  ({t('including_subcategories', 'включая товары в подкатегориях')})
                </span>
              )}
            </p>
          ) : (
            subcategories.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {t('no_products_in_category', 'В данной категории нет товаров')}
              </div>
            )
          )}
        </div>

        {/* Добавляем фильтрацию и сортировку */}
        {products.length > 0 && (
          <div className="mb-6">
            <div className="sticky top-20 md:top-16 z-10 bg-white shadow-sm py-3 px-4 rounded-lg mb-4 flex flex-wrap gap-4 justify-between items-center">
              <button
                onClick={() => setMobileFiltersOpen(true)}
                className="lg:hidden flex items-center px-3 py-1.5 border rounded-lg hover:bg-gray-50"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                  />
                </svg>
                <span>{t('filters', 'Фильтры')}</span>
              </button>

              <div className="flex items-center">
                <span className="mr-2">{t('sort_by', 'Сортировать:')}</span>
                <SortDropdown onSortChange={handleSortChange} />
              </div>

              <div className="w-full md:w-auto text-sm text-gray-500">
                {t('products_found', 'Найдено товаров')}: {filteredProducts.length}
              </div>
            </div>

            <div className="flex flex-col lg:flex-row gap-6">
              {/* Фильтры - на мобильных в отдельной панели */}
              <div className="hidden lg:block lg:w-1/4">
                <FilterPanel
                  categoryId={categoryId}
                  onFilterChange={handleFilterChange}
                  preloadedParams={extractedFilterParams}
                />
              </div>

              {/* Показываем мобильную версию фильтров */}
              <MobileFilterDrawer
                isOpen={mobileFiltersOpen}
                onClose={() => setMobileFiltersOpen(false)}
                categoryId={categoryId}
                onFilterChange={handleFilterChange}
                preloadedParams={extractedFilterParams}
              />

              {/* Список товаров */}
              <div className="lg:w-3/4">
                {isFiltering ? (
                  <div className="p-8">
                    <AnimatedLoading count={6} />
                  </div>
                ) : filteredProducts.length > 0 ? (
                  <>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {visibleProducts.map(product => (
                        <Link to={`/product/${product.id}`} key={product.id}>
                          <div className="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow h-full flex flex-col">
                            <div className="relative h-60">
                              {product.is_on_sale && (
                                <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs">
                                  {t('sale', 'Акция')}
                                </div>
                              )}
                              {product.is_new && (
                                <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-md text-xs">
                                  {t('new', 'Новинка')}
                                </div>
                              )}
                              <img
                                src={product.image || '/placeholder.png'}
                                alt={product.name}
                                className="w-full h-full object-contain"
                                onError={e => {
                                  e.target.src = '/placeholder.png';
                                }}
                              />
                            </div>
                            <div className="p-4 flex-grow flex flex-col">
                              <p className="font-medium text-lg mb-2 line-clamp-2 text-black">
                                {product.name}
                              </p>
                              {product.vendor && (
                                <p className="text-sm text-gray-600 mt-auto mb-2">
                                  {product.vendor}
                                </p>
                              )}
                              <div className="mt-auto">
                                <p className="text-lg font-bold text-primary">
                                  {new Intl.NumberFormat('uk-UA', {
                                    style: 'currency',
                                    currency: 'UAH'
                                  }).format(product.price)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>

                    {/* Кнопка "Показать больше" */}
                    {filteredProducts.length > displayLimit && (
                      <div className="text-center mt-8">
                        <button
                          onClick={handleLoadMore}
                          className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                        >
                          {t('load_more', 'Показать больше')}
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-12 bg-white rounded-lg shadow">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-16 w-16 mx-auto text-gray-400 mb-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 9 9 0 11-18 0 9 9 9 0 0118 0z"
                      />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">
                      {t('no_products_found', 'Товары не найдены')}
                    </h3>
                    <p className="text-gray-500">
                      {t('try_different_filters', 'Попробуйте изменить параметры фильтра')}
                    </p>
                    <button
                      onClick={resetFilters}
                      className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
                    >
                      {t('reset_filters', 'Сбросить все фильтры')}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Если нет товаров в категории */}
        {products.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            {t('no_products_in_category', 'В данной категории нет товаров')}
          </div>
        )}
      </div>
    </>
  );
};

export default CategoryPage;
