import React from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import SEO, { SchemaTemplates } from '../seo';

// Импортируем изображения (эти пути нужно создать в вашем проекте)
import teamImage from '../assets/images/team.jpg';
import storeImage from '../assets/images/store.jpg';
import valuesImage from '../assets/images/values.jpg';

const AboutPage = () => {
  const { t } = useTranslation();

  // Анимация для секций при прокрутке
  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  };

  // Данные для микроразметки schema.org
  const aboutSchemaData = {
    '@context': 'https://schema.org',
    '@type': 'AboutPage',
    name: 'About Kitchen Shop',
    description: 'Learn more about Kitchen Shop, our history, mission and values.'
  };

  return (
    <>
      <SEO
        title={t('about_title', 'About Us')}
        description={t(
          'about_description',
          'Learn more about Kitchen Shop - our history, team, mission and values. Quality kitchen equipment since 1995.'
        )}
        keywords={t(
          'about_keywords',
          'about us, kitchen shop, history, mission, team, values, kitchen equipment'
        )}
        url="/about"
        structuredData={aboutSchemaData}
      />

      {/* Hero секция */}
      <div className="bg-gradient-to-r from-gray-100 to-gray-200 py-16 md:py-24">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800">
              {t('about_us', 'About Us')}
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8">
              {t(
                'about_hero_text',
                'Discover the story behind Kitchen Shop and our commitment to bringing quality and innovation to your kitchen since 1995.'
              )}
            </p>
            <div className="h-1 w-24 bg-primary mx-auto rounded-full"></div>
          </motion.div>
        </div>
      </div>

      {/* История компании */}
      <motion.section
        className="py-16"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                {t('our_story', 'Our Story')}
              </h2>
              <p className="mb-4 text-gray-600">
                {t(
                  'about_history_1',
                  'Kitchen Shop began in 1995 with a simple vision: to provide exceptional kitchen equipment that combines functionality, durability, and style.'
                )}
              </p>
              <p className="mb-4 text-gray-600">
                {t(
                  'about_history_2',
                  'What started as a small family business with a single store has grown into a respected retailer with locations nationwide and a robust online presence, serving both home cooks and professional chefs alike.'
                )}
              </p>
              <p className="mb-6 text-gray-600">
                {t(
                  'about_history_3',
                  "Throughout our journey, we've maintained our commitment to quality, customer service, and staying at the forefront of kitchen innovation."
                )}
              </p>
              <div className="flex flex-wrap gap-4">
                <div className="bg-gray-100 p-4 rounded-lg text-center flex-1">
                  <div className="text-3xl font-bold text-primary mb-2">28</div>
                  <div className="text-gray-600">{t('years_experience', 'Years Experience')}</div>
                </div>
                <div className="bg-gray-100 p-4 rounded-lg text-center flex-1">
                  <div className="text-3xl font-bold text-primary mb-2">15+</div>
                  <div className="text-gray-600">{t('retail_locations', 'Retail Locations')}</div>
                </div>
                <div className="bg-gray-100 p-4 rounded-lg text-center flex-1">
                  <div className="text-3xl font-bold text-primary mb-2">10k+</div>
                  <div className="text-gray-600">{t('products', 'Products')}</div>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="rounded-lg overflow-hidden shadow-xl">
                <img
                  src={storeImage}
                  alt={t('about_store_image_alt', 'Kitchen Shop store')}
                  className="w-full h-auto object-cover"
                  onError={e => {
                    e.target.onerror = null;
                    e.target.src = 'https://via.placeholder.com/800x600?text=Store+Image';
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Миссия и ценности */}
      <motion.section
        className="py-16 bg-gray-50"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row-reverse items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6 text-gray-800">
                {t('mission_values', 'Our Mission & Values')}
              </h2>
              <p className="mb-6 text-gray-600">
                {t(
                  'about_mission',
                  'Our mission is to enhance the cooking experience by providing exceptional kitchen equipment that inspires creativity and makes cooking more enjoyable for everyone.'
                )}
              </p>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-primary text-white p-2 rounded mr-4 mt-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800 mb-1">
                      {t('value_quality', 'Quality')}
                    </h3>
                    <p className="text-gray-600">
                      {t(
                        'value_quality_desc',
                        'We meticulously select products that meet our high standards for durability and performance.'
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-primary text-white p-2 rounded mr-4 mt-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800 mb-1">
                      {t('value_innovation', 'Innovation')}
                    </h3>
                    <p className="text-gray-600">
                      {t(
                        'value_innovation_desc',
                        'We stay at the forefront of kitchen technology to bring you the latest advancements.'
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-primary text-white p-2 rounded mr-4 mt-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800 mb-1">
                      {t('value_customer', 'Customer Service')}
                    </h3>
                    <p className="text-gray-600">
                      {t(
                        'value_customer_desc',
                        'We believe in building lasting relationships with our customers through exceptional service.'
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="rounded-lg overflow-hidden shadow-xl">
                <img
                  src={valuesImage}
                  alt={t('about_values_image_alt', 'Our values in action')}
                  className="w-full h-auto object-cover"
                  onError={e => {
                    e.target.onerror = null;
                    e.target.src = 'https://via.placeholder.com/800x600?text=Values+Image';
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Команда */}
      <motion.section
        className="py-16"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">{t('our_team', 'Our Team')}</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {t(
                'team_intro',
                'Meet the passionate professionals behind Kitchen Shop who work tirelessly to bring you the best kitchen equipment and customer experience.'
              )}
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {/* Член команды 1 */}
            <div className="bg-white rounded-lg overflow-hidden shadow-md transition-transform hover:shadow-lg hover:-translate-y-2">
              <div className="h-64 overflow-hidden">
                <img
                  src="https://via.placeholder.com/400x400?text=CEO"
                  alt="CEO"
                  className="w-full h-full object-cover object-center"
                />
              </div>
              <div className="p-4">
                <h3 className="font-bold text-lg text-gray-800 mb-1">Alex Johnson</h3>
                <p className="text-primary text-sm mb-2">{t('position_ceo', 'CEO & Founder')}</p>
                <p className="text-gray-600 text-sm">
                  {t(
                    'team_ceo_desc',
                    'With over 25 years of experience in kitchenware retail, Alex founded Kitchen Shop with a vision to revolutionize the industry.'
                  )}
                </p>
              </div>
            </div>

            {/* Член команды 2 */}
            <div className="bg-white rounded-lg overflow-hidden shadow-md transition-transform hover:shadow-lg hover:-translate-y-2">
              <div className="h-64 overflow-hidden">
                <img
                  src="https://via.placeholder.com/400x400?text=COO"
                  alt="COO"
                  className="w-full h-full object-cover object-center"
                />
              </div>
              <div className="p-4">
                <h3 className="font-bold text-lg text-gray-800 mb-1">Maria Rodriguez</h3>
                <p className="text-primary text-sm mb-2">
                  {t('position_coo', 'Chief Operations Officer')}
                </p>
                <p className="text-gray-600 text-sm">
                  {t(
                    'team_coo_desc',
                    'Maria ensures that all operations run smoothly, from supply chain management to customer delivery.'
                  )}
                </p>
              </div>
            </div>

            {/* Член команды 3 */}
            <div className="bg-white rounded-lg overflow-hidden shadow-md transition-transform hover:shadow-lg hover:-translate-y-2">
              <div className="h-64 overflow-hidden">
                <img
                  src="https://via.placeholder.com/400x400?text=Chef"
                  alt="Chef Consultant"
                  className="w-full h-full object-cover object-center"
                />
              </div>
              <div className="p-4">
                <h3 className="font-bold text-lg text-gray-800 mb-1">Pierre Dubois</h3>
                <p className="text-primary text-sm mb-2">
                  {t('position_chef', 'Head Chef Consultant')}
                </p>
                <p className="text-gray-600 text-sm">
                  {t(
                    'team_chef_desc',
                    'As a Michelin-starred chef, Pierre tests and approves all products before they enter our catalog.'
                  )}
                </p>
              </div>
            </div>

            {/* Член команды 4 */}
            <div className="bg-white rounded-lg overflow-hidden shadow-md transition-transform hover:shadow-lg hover:-translate-y-2">
              <div className="h-64 overflow-hidden">
                <img
                  src="https://via.placeholder.com/400x400?text=CTO"
                  alt="CTO"
                  className="w-full h-full object-cover object-center"
                />
              </div>
              <div className="p-4">
                <h3 className="font-bold text-lg text-gray-800 mb-1">David Kim</h3>
                <p className="text-primary text-sm mb-2">
                  {t('position_cto', 'Chief Technology Officer')}
                </p>
                <p className="text-gray-600 text-sm">
                  {t(
                    'team_cto_desc',
                    'David leads our digital transformation, ensuring a seamless online shopping experience.'
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* CTA секция */}
      <section className="bg-primary py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-white">
            {t('join_us', 'Join Our Kitchen Family')}
          </h2>
          <p className="text-white opacity-90 max-w-2xl mx-auto mb-8">
            {t(
              'cta_text',
              'Experience the difference that quality kitchen equipment can make in your culinary adventures. Browse our catalog and transform your kitchen today.'
            )}
          </p>
          <a
            href="/products"
            className="inline-block bg-white text-primary font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors"
          >
            {t('view_products', 'View Our Products')}
          </a>
        </div>
      </section>
    </>
  );
};

export default AboutPage;
