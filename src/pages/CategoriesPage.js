import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { ProductListSkeleton } from '../components/SkeletonLoader';
import { supabase } from '../supabaseClient';

const CategoriesPage = () => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);

        // Step 1: Fetch all categories (basic info)
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('categories')
          .select('id, name, image')
          .order('name');

        if (categoriesError) throw categoriesError;

        if (!categoriesData || categoriesData.length === 0) {
          setCategories([]);
          // No need to proceed further if no categories
          return;
        }

        // Step 2: Fetch product counts using the RPC function
        const { data: countsData, error: countsError } = await supabase.rpc(
          'get_category_product_counts'
        );

        if (countsError) throw countsError;

        const productCountsMap = (countsData || []).reduce((acc, item) => {
          // The RPC function returns category_id_fk and num_products
          acc[item.category_id_fk] = item.num_products;
          return acc;
        }, {});

        const formattedCategories = categoriesData.map(category => ({
          id: category.id,
          name: category.name,
          image: category.image,
          productCount: productCountsMap[category.id] || 0
        }));

        // Отображаем все категории, даже если у них нет товаров
        setCategories(formattedCategories);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(err.message || t('error_loading_data', 'Ошибка загрузки данных'));
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [t]);

  if (loading) {
    return <ProductListSkeleton />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          {t('try_again', 'Попробовать снова')}
        </button>
        <Link
          to="/"
          className="mt-4 inline-block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          {t('back_to_home', 'На главную')}
        </Link>
      </div>
    );
  }

  if (categories.length === 0) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          {t('no_categories', 'Категорий нет')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('add_categories_prompt', 'Добавьте категории для отображения.')}
        </p>
        <a
          href="/admin"
          className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          {t('go_to_admin', 'Перейти в админку')}
        </a>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{t('categories_page_title', 'Kitchen Shop - Categories')}</title>
        <meta
          name="description"
          content={t(
            'categories_page_description',
            'Browse our selection of kitchen product categories.'
          )}
        />
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">{t('categories', 'Категории')}</h1>

        {categories.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">
              {t('no_categories', 'Категории пока не добавлены.')}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {categories.map(category => (
              <Link key={category.id} to={`/category/${category.id}`} className="block group">
                <div className="bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300 hover:shadow-lg border border-gray-200">
                  <div className="aspect-square relative overflow-hidden">
                    <img
                      src={
                        category.image || 'https://placehold.co/300x300/EEE/31343C?text=No+Image'
                      }
                      alt={category.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      loading="lazy"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent p-4">
                      <span
                        className="text-white text-sm font-medium"
                        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}
                      >
                        {t('products_count', '{{count}} товаров', { count: category.productCount })}
                      </span>
                    </div>
                  </div>
                  <h2 className="p-3 text-lg font-medium text-left text-gray-700">
                    {category.name}
                  </h2>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default CategoriesPage;
