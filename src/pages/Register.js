import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';

const Register = () => {
  const { t } = useTranslation();
  const { register } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async e => {
    e.preventDefault();
    if (password !== confirmPassword) {
      setError(t('password_mismatch', 'Passwords do not match.'));
      return;
    }
    try {
      await register(email, password);
      navigate('/profile');
    } catch (err) {
      setError(t('register_error', 'Failed to register. Please try again.'));
    }
  };

  return (
    <div className="container mx-auto px-6 py-12">
      <h1 className="text-3xl font-semibold mb-8 text-center">{t('register', 'Register')}</h1>
      <div className="card max-w-md mx-auto">
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            type="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            placeholder={t('email', 'Email')}
            required
          />
          <input
            type="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            placeholder={t('password', 'Password')}
            required
          />
          <input
            type="password"
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            placeholder={t('confirm_password', 'Confirm Password')}
            required
          />
          {error && <p className="text-red-500">{error}</p>}
          <button type="submit" className="btn-primary w-full">
            {t('register', 'Register')}
          </button>
        </form>
        <p className="text-gray-600 mt-4 text-center">
          {t('have_account', 'Already have an account?')}{' '}
          <Link to="/login">{t('login', 'Login')}</Link>
        </p>
      </div>
    </div>
  );
};

export default Register;
