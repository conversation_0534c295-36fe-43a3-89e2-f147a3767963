import { useState } from 'react';
// Remove unused import or use it
// import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../supabaseClient';

const SeedPage = () => {
  // Remove unused variables or use them
  // const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const seedDatabase = async () => {
    setLoading(true);
    try {
      // Add your seeding logic here
      const sampleData = {
        products: [
          { name: 'Sample Product 1', price: 99.99, description: 'Description 1' },
          { name: 'Sample Product 2', price: 149.99, description: 'Description 2' }
        ],
        categories: [
          { name: 'Category 1', description: 'Category Description 1' },
          { name: 'Category 2', description: 'Category Description 2' }
        ]
      };

      // Insert sample products
      const { error: productsError } = await supabase.from('products').insert(sampleData.products);

      if (productsError) throw productsError;

      // Insert sample categories
      const { error: categoriesError } = await supabase
        .from('categories')
        .insert(sampleData.categories);

      if (categoriesError) throw categoriesError;

      setMessage('Database seeded successfully!');
    } catch (error) {
      setMessage(`Error seeding database: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Seed Database - Development Only</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Seed Database</h1>
        <button
          onClick={seedDatabase}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Seeding...' : 'Seed Database'}
        </button>
        {message && <div className="mt-4 p-4 rounded bg-gray-100">{message}</div>}
      </div>
    </>
  );
};

export default SeedPage;
