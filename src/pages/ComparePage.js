import { useState, useEffect, useMemo, useCallback } from 'react'; // Added useCallback
import { useTranslation } from 'react-i18next';
import { useCompare } from '../context/CompareContext';
import { Link } from 'react-router-dom';
import CompareParamGroup from '../components/compare/CompareParamGroup';
import CompareProductCard from '../components/compare/CompareProductCard';
import { useCart } from '../context/CartContext';
import { supabase } from '../supabaseClient';
import { Heading, Text } from '../components/ui/Typography';
import Button from '../components/Button';
import LoadingSpinner from '../components/LoadingSpinner'; // Corrected path

const ComparePage = () => {
  const { t } = useTranslation();
  const { compareItems, removeFromCompare, clearCompare } = useCompare();
  const { addToCart } = useCart();
  const [compareWithDetails, setCompareWithDetails] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showOnlyDifferences, setShowOnlyDifferences] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);
  const [paramGroups, setParamGroups] = useState([]);
  const [allParamNames, setAllParamNames] = useState([]);

  // Обработка изменения размера окна
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Функция для группировки параметров по категориям
  const organizeParametersIntoGroups = useCallback(
    allParams => {
      // Базовая группа для основных параметров
      const basicGroup = {
        id: 'main',
        title: t('main_params', 'Основные параметры'),
        params: ['name', 'price', 'brand']
      };

      // Создаем автоматически группы на основе параметров
      const groupedParams = {};

      // Группируем параметры по категориям
      allParams.forEach(param => {
        if (basicGroup.params.includes(param.name.toLowerCase())) return;

        // Определяем категорию параметра
        let category = 'other';

        // Примеры правил определения категории параметров
        if (
          /размер|габарит|длин|ширин|высот|диаметр|dimension|size|height|width|length/i.test(
            param.name
          )
        ) {
          category = 'dimensions';
        } else if (/материал|material|состав|ткань|textile|fabric/i.test(param.name)) {
          category = 'materials';
        } else if (/цвет|color|окрас|оттенок/i.test(param.name)) {
          category = 'appearance';
        } else if (/вес|weight|масса/i.test(param.name)) {
          category = 'physical';
        } else if (/энерго|мощност|power|consumption|емкость|capacity/i.test(param.name)) {
          category = 'power';
        } else if (/гарант|warranty|срок|period|service/i.test(param.name)) {
          category = 'service';
        } else if (param.is_key) {
          category = 'featured'; // Ключевые параметры
        }

        // Создаем группу, если еще не существует
        if (!groupedParams[category]) {
          groupedParams[category] = {
            params: []
          };
        }

        // Добавляем параметр в группу
        groupedParams[category].params.push(param.name);
      });

      // Перевод названий групп
      const categoryTranslations = {
        dimensions: t('dimensions_category', 'Размеры и габариты'),
        materials: t('materials_category', 'Материалы и состав'),
        appearance: t('appearance_category', 'Внешний вид'),
        physical: t('physical_category', 'Физические характеристики'),
        power: t('power_category', 'Энергопотребление и мощность'),
        service: t('service_category', 'Гарантия и сервис'),
        featured: t('featured_category', 'Ключевые характеристики'),
        other: t('other_params', 'Прочие характеристики')
      };

      // Преобразовываем в массив с ID и заголовками
      const groupsArray = Object.entries(groupedParams).map(([id, group]) => ({
        id,
        title: categoryTranslations[id] || t(`${id}_category`, id),
        params: group.params
      }));

      // Сортируем группы - сначала основные параметры, затем ключевые, затем остальные
      const orderedGroups = [
        basicGroup,
        ...groupsArray.sort((a, b) => {
          if (a.id === 'featured') return -1;
          if (b.id === 'featured') return 1;
          return a.title.localeCompare(b.title);
        })
      ];

      return orderedGroups;
    },
    [t]
  ); // Added t as a dependency

  useEffect(() => {
    const fetchProductDetails = async () => {
      setLoading(true);
      setError(null);

      if (!Array.isArray(compareItems) || compareItems.length === 0) {
        setCompareWithDetails([]);
        setLoading(false);
        return;
      }

      try {
        // Get product IDs from compareItems
        const productIds = compareItems.map(item => item.id);

        // Fetch detailed product data from Supabase
        const { data: productsData, error: productsError } = await supabase
          .from('products')
          .select('*')
          .in('id', productIds);

        if (productsError) throw productsError;

        // Create a map to store product data by ID
        const productMap = {};
        productsData.forEach(product => {
          productMap[product.id] = product;
        });

        // Создаем массив для всех имен параметров
        const allParams = new Set();

        // Массив для хранения всех уникальных параметров и их метаданных
        let allParamsWithMetadata = [];

        // Fetch parameters for each product
        const productsWithParams = await Promise.all(
          compareItems.map(async item => {
            if (!item.id) {
              return item;
            }

            // Get product details from the map, or use the original item as fallback
            const productDetails = productMap[item.id] || {
              id: item.id,
              name: item.name || `Product ${item.id}`,
              price: item.price || 0,
              image: item.image || 'https://placehold.co/100x100/EEE/31343C?text=Item'
            };

            // Fetch product parameters from product_params table
            const { data: paramsData, error: paramsError } = await supabase
              .from('product_params')
              .select('*')
              .eq('product_id', productDetails.external_id || productDetails.id);

            if (paramsError) {
              console.error('Error fetching product parameters:', paramsError);
            }

            // Базовый объект товара
            const productWithParams = {
              ...productDetails,
              inStock: productDetails.available ? 'В наличии' : 'Нет в наличии',
              params: {}
            };

            // Добавляем все параметры из базы данных
            if (paramsData && paramsData.length > 0) {
              // Добавляем параметры в общий список
              paramsData.forEach(param => {
                // Добавляем имя параметра в сет всех параметров
                allParams.add(param.name);

                // Добавляем параметр с метаданными в список
                if (!allParamsWithMetadata.find(p => p.name === param.name)) {
                  allParamsWithMetadata.push({
                    name: param.name,
                    is_key: param.is_key || false
                  });
                }

                // Заполняем значение параметра для текущего товара
                productWithParams.params[param.name] = param.value;
              });

              // Для обратной совместимости добавляем некоторые ключевые параметры и в корень объекта
              const brand = paramsData.find(
                p =>
                  p.name?.toLowerCase().includes('brand') || p.name?.toLowerCase().includes('бренд')
              );
              if (brand) {
                productWithParams.brand = brand.value;
              }

              const material = paramsData.find(
                p =>
                  p.name?.toLowerCase().includes('material') ||
                  p.name?.toLowerCase().includes('материал')
              );
              if (material) {
                productWithParams.material = material.value;
              }

              const dimensions = paramsData.find(
                p =>
                  p.name?.toLowerCase().includes('dimension') ||
                  p.name?.toLowerCase().includes('размер')
              );
              if (dimensions) {
                productWithParams.dimensions = dimensions.value;
              }

              const weight = paramsData.find(
                p =>
                  p.name?.toLowerCase().includes('weight') || p.name?.toLowerCase().includes('вес')
              );
              if (weight) {
                productWithParams.weight = weight.value;
              }

              const color = paramsData.find(
                p =>
                  p.name?.toLowerCase().includes('color') || p.name?.toLowerCase().includes('цвет')
              );
              if (color) {
                productWithParams.color = color.value;
              }

              const warranty = paramsData.find(
                p =>
                  p.name?.toLowerCase().includes('warranty') ||
                  p.name?.toLowerCase().includes('гарантия')
              );
              if (warranty) {
                productWithParams.warranty = warranty.value;
              }
            }

            return productWithParams;
          })
        );

        // Устанавливаем данные товаров
        setCompareWithDetails(productsWithParams);

        // Устанавливаем все имена параметров
        setAllParamNames(Array.from(allParams));

        // Организуем параметры в группы и устанавливаем их
        const groups = organizeParametersIntoGroups(allParamsWithMetadata);
        setParamGroups(groups);
      } catch (err) {
        console.error('Ошибка при загрузке данных сравнения:', err);
        setError(
          err.code === 'permission-denied'
            ? t('error_permission_denied', 'Нет доступа к данным. Проверьте права доступа.')
            : t(
                'error_fetching_data',
                'Ошибка при загрузке данных. Проверьте подключение к интернету.'
              )
        );
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [compareItems, t, organizeParametersIntoGroups]); // Добавили organizeParametersIntoGroups

  // Получаем уникальные категории для фильтрации
  const categories = useMemo(() => {
    return [
      { id: 'all', name: t('all_categories', 'Все категории') },
      ...paramGroups.map(group => ({ id: group.id, name: group.title }))
    ];
  }, [paramGroups, t]);

  // Функция проверки, имеют ли параметры одинаковые значения
  const areValuesEqual = paramName => {
    if (compareWithDetails.length <= 1) return false;

    // Проверяем сначала в корне объекта
    if (typeof compareWithDetails[0][paramName] !== 'undefined') {
      const firstValue = compareWithDetails[0][paramName];
      return compareWithDetails.every(item => item[paramName] === firstValue);
    }

    // Затем проверяем в params
    if (
      compareWithDetails[0].params &&
      typeof compareWithDetails[0].params[paramName] !== 'undefined'
    ) {
      const firstValue = compareWithDetails[0].params[paramName];
      return compareWithDetails.every(item => item.params && item.params[paramName] === firstValue);
    }

    return true; // Если параметр не найден ни у одного товара, считаем значения равными
  };

  // Функция определения лучшего значения
  const getBestValue = paramName => {
    if (compareWithDetails.length <= 1) return null;

    // Для цены лучшее - минимальное значение
    if (paramName === 'price') {
      const minPrice = Math.min(...compareWithDetails.map(item => parseFloat(item.price) || 0));
      return minPrice;
    }

    return null;
  };

  // Получение значения параметра для конкретного товара
  const getParamValue = (item, paramName) => {
    // Сначала проверяем в корне объекта
    if (typeof item[paramName] !== 'undefined') {
      return item[paramName];
    }

    // Затем проверяем в params
    if (item.params && typeof item.params[paramName] !== 'undefined') {
      return item.params[paramName];
    }

    return 'Не указано';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">
        <div className="flex justify-center">
          <LoadingSpinner size="large" />
        </div>
        <Text className="mt-4">{t('loading', 'Загрузка...')}</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-6 py-12">
        <Heading as="h1" variant="h1" className="mb-8">
          {t('compare', 'Сравнение')}
        </Heading>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <Text variant="error">{error}</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-6 py-12">
      <Heading as="h1" variant="h1" className="mb-8">
        {t('compare', 'Сравнение')}
      </Heading>

      {compareWithDetails.length === 0 ? (
        <div className="text-center bg-gray-50 p-10 rounded-lg shadow-sm">
          <svg
            className="w-16 h-16 mx-auto text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <Text variant="lead" className="my-4">
            {t('compare_empty', 'Список сравнения пуст.')}
          </Text>
          <Link to="/categories">
            <Button variant="primary" size="lg">
              {t('go_to_categories', 'Перейти к категориям')}
            </Button>
          </Link>
        </div>
      ) : (
        <>
          <div className="mb-8 flex flex-wrap justify-between items-center gap-4 bg-white rounded-lg shadow-sm p-4 border border-gray-100">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showOnlyDifferences"
                  checked={showOnlyDifferences}
                  onChange={e => setShowOnlyDifferences(e.target.checked)}
                  className="mr-2 h-5 w-5 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="showOnlyDifferences" className="font-medium">
                  {t('show_only_differences', 'Показывать только различия')}
                </label>
              </div>

              {!isMobileView && (
                <div className="ml-4">
                  <label htmlFor="categoryFilter" className="sr-only">
                    Выберите категорию
                  </label>
                  <select
                    id="categoryFilter"
                    value={selectedCategory}
                    onChange={e => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary font-medium"
                  >
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>

            <Button
              variant="outline"
              onClick={clearCompare}
              className="text-red-500 border-red-500 hover:bg-red-50"
            >
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                ></path>
              </svg>
              {t('clear_comparison', 'Очистить сравнение')}
            </Button>
          </div>

          {/* Мобильное представление с карточками */}
          {isMobileView ? (
            <div className="space-y-6">
              {compareWithDetails.map(product => (
                <CompareProductCard
                  key={product.id}
                  product={product}
                  getBestValue={getBestValue}
                  getParamValue={getParamValue}
                  allParamNames={allParamNames}
                  paramGroups={paramGroups}
                  t={t}
                />
              ))}
            </div>
          ) : (
            /* Десктопное представление с таблицей */
            <div className="overflow-x-auto bg-white rounded-lg shadow-md">
              <table className="w-full table-auto border-collapse">
                <thead>
                  <tr className="bg-gray-50 border-b">
                    <th className="p-4 text-left font-semibold text-heading"></th>
                    {compareWithDetails.map((item, index) => (
                      <th
                        key={`${item.id}-${index}`}
                        className="p-4 text-center min-w-[200px] border-l border-gray-100"
                      >
                        <Button
                          variant="ghost"
                          onClick={() => removeFromCompare(item.id)}
                          className="text-gray-500 hover:text-red-600 transition-colors duration-200"
                        >
                          <svg
                            className="w-4 h-4 mr-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M6 18L18 6M6 6l12 12"
                            ></path>
                          </svg>
                          {t('remove', 'Удалить')}
                        </Button>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {/* Изображения товаров */}
                  <tr className="border-b">
                    <td className="p-4 font-semibold text-heading">{t('image', 'Изображение')}</td>
                    {compareWithDetails.map((item, index) => (
                      <td
                        key={`${item.id}-${index}-image`}
                        className="p-6 text-center border-l border-gray-100"
                      >
                        <div className="flex justify-center">
                          <Link
                            to={`/product/${item.id}`}
                            className="cursor-pointer hover:opacity-90 transition-opacity"
                          >
                            <div className="relative w-32 h-32 bg-gray-50 rounded-md p-2 flex items-center justify-center">
                              <img
                                src={
                                  item.image || 'https://placehold.co/100x100/EEE/31343C?text=Item'
                                }
                                alt={item.name || 'Товар'}
                                className="max-w-full max-h-full object-contain"
                                onError={e => {
                                  e.target.onerror = null;
                                  e.target.src =
                                    'https://placehold.co/100x100/EEE/31343C?text=Item';
                                }}
                              />
                            </div>
                          </Link>
                        </div>
                      </td>
                    ))}
                  </tr>

                  {/* Основная информация и кнопки */}
                  <tr className="border-b">
                    <td className="p-4 font-semibold text-heading">{t('name', 'Название')}</td>
                    {compareWithDetails.map((item, index) => (
                      <td
                        key={`${item.id}-${index}-name`}
                        className="p-4 text-center border-l border-gray-100"
                      >
                        <Link
                          to={`/product/${item.id}`}
                          className="block font-medium text-primary mb-2 line-clamp-2 text-base hover:text-primary-dark hover:underline transition-colors"
                        >
                          {item.name || 'Неизвестный товар'}
                        </Link>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={() => addToCart(item)}
                          className="mt-2"
                        >
                          <svg
                            className="w-4 h-4 mr-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                            ></path>
                          </svg>
                          {t('add_to_cart', 'В корзину')}
                        </Button>
                      </td>
                    ))}
                  </tr>

                  {/* Цена */}
                  <tr className="border-b">
                    <td className="p-4 font-semibold text-heading">{t('price', 'Цена')}</td>
                    {compareWithDetails.map((item, index) => {
                      const isBestPrice = item.price === getBestValue('price');
                      return (
                        <td
                          key={`${item.id}-${index}-price`}
                          className={`p-4 text-center border-l border-gray-100 ${isBestPrice ? 'bg-green-50' : ''}`}
                        >
                          <Text
                            className={`text-lg ${isBestPrice ? 'font-bold text-green-600' : 'font-medium'}`}
                          >
                            {item.price ? `${item.price} ₴` : 'Цена недоступна'}
                          </Text>
                          {isBestPrice && (
                            <div className="flex items-center justify-center text-xs text-green-600 mt-1 font-medium">
                              <svg
                                className="w-4 h-4 mr-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                ></path>
                              </svg>
                              Лучшая цена
                            </div>
                          )}
                        </td>
                      );
                    })}
                  </tr>

                  {/* Вставляем группы параметров, используя компонент CompareParamGroup */}
                  {paramGroups
                    .filter(group => selectedCategory === 'all' || selectedCategory === group.id)
                    .map(group => (
                      <CompareParamGroup
                        key={group.id}
                        title={group.title}
                        params={group.params}
                        items={compareWithDetails}
                        showOnlyDifferences={showOnlyDifferences}
                        areValuesEqual={areValuesEqual}
                        getBestValue={getBestValue}
                        getParamValue={getParamValue}
                        t={t}
                      />
                    ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ComparePage;
