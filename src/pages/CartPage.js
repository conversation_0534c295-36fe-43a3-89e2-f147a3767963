import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useCart } from '../context/CartContext';
import { FaShoppingCart, FaTrash, FaArrowLeft, FaPlus, FaMinus } from 'react-icons/fa';
import { Helmet } from 'react-helmet-async';

const CartPage = () => {
  const { t } = useTranslation();
  const { cart, updateQuantity, removeFromCart } = useCart();
  const [cartWithDetails, setCartWithDetails] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchProductDetails = async () => {
      setLoading(true);
      setError(null);

      if (!Array.isArray(cart) || cart.length === 0) {
        setCartWithDetails([]);
        setLoading(false);
        return;
      }

      try {
        const updatedCart = await Promise.all(
          cart.map(async item => {
            if (!item.id) {
              return { ...item, quantity: item.quantity || 1 };
            }

            return { ...item, quantity: item.quantity || 1 };
          })
        );
        setCartWithDetails(updatedCart);
      } catch (err) {
        console.error('Ошибка при загрузке данных корзины:', err);
        setError(
          err.code === 'permission-denied'
            ? t('error_permission_denied', 'Нет доступа к данным. Проверьте права доступа.')
            : t(
                'error_fetching_data',
                'Ошибка при загрузке данных. Проверьте подключение к интернету.'
              )
        );
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [cart, t]);

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">
        <div className="flex flex-col items-center justify-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-gray-600 text-lg">{t('loading', 'Загрузка...')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-6 py-12">
        <h1 className="text-3xl font-semibold mb-8">{t('cart', 'Корзина')}</h1>
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
          <div className="flex">
            <div className="ml-3">
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const totalPrice = cartWithDetails.reduce(
    (sum, item) => sum + (item.price || 0) * (item.quantity || 1),
    0
  );

  return (
    <>
      <Helmet>
        <title>
          {cartWithDetails.length > 0
            ? `(${cartWithDetails.length}) ${t('cart', 'Корзина')}`
            : t('cart', 'Корзина')}{' '}
          | E-Store
        </title>
      </Helmet>
      <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12 max-w-6xl">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl sm:text-3xl font-semibold flex items-center gap-2">
            <FaShoppingCart className="text-primary" />
            {t('cart', 'Корзина')}
            {cartWithDetails.length > 0 && (
              <span className="bg-primary text-white text-xs w-6 h-6 inline-flex items-center justify-center rounded-full ml-2">
                {cartWithDetails.length}
              </span>
            )}
          </h1>
          <Link
            to="/"
            className="text-primary hover:text-primary-dark flex items-center gap-1 text-sm"
          >
            <FaArrowLeft />
            {t('continue_shopping', 'Продолжить покупки')}
          </Link>
        </div>

        {cartWithDetails.length === 0 ? (
          <div className="text-center bg-white rounded-lg shadow-sm p-10">
            <div className="flex justify-center mb-6">
              <FaShoppingCart className="text-gray-300 text-6xl" />
            </div>
            <h2 className="text-xl font-medium text-gray-700 mb-4">
              {t('cart_empty', 'Ваша корзина пуста')}
            </h2>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              {t(
                'cart_empty_message',
                'Похоже, вы ещё ничего не добавили в корзину. Самое время это исправить!'
              )}
            </p>
            <Link
              to="/categories"
              className="bg-primary text-white px-6 py-3 rounded-md hover:bg-primary-dark transition-colors duration-300"
            >
              {t('go_to_categories', 'Перейти к категориям')}
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-100 bg-gray-50 hidden sm:flex">
                  <div className="w-2/5 font-medium">{t('product', 'Товар')}</div>
                  <div className="w-1/5 text-center font-medium">{t('price', 'Цена')}</div>
                  <div className="w-1/5 text-center font-medium">{t('quantity', 'Количество')}</div>
                  <div className="w-1/5 text-right font-medium">{t('subtotal', 'Сумма')}</div>
                </div>

                <div className="divide-y divide-gray-100">
                  {cartWithDetails.map(item => (
                    <div
                      key={item.id}
                      className="p-4 sm:flex items-center hover:bg-gray-50 transition-colors"
                    >
                      <div className="sm:w-2/5 flex gap-4 mb-4 sm:mb-0">
                        <img
                          src={item.image || 'https://placehold.co/100x100/EEE/31343C?text=Item'}
                          alt={item.name || 'Товар'}
                          className="w-20 h-20 object-cover rounded"
                        />
                        <div>
                          <p className="font-medium">{item.name || 'Неизвестный товар'}</p>
                          <p className="text-gray-500 text-sm mt-1">{item.category || ''}</p>
                          <button
                            onClick={() => removeFromCart(item.id)}
                            className="text-red-500 hover:text-red-700 text-sm flex items-center gap-1 mt-2 sm:hidden"
                          >
                            <FaTrash size={12} />
                            {t('remove', 'Удалить')}
                          </button>
                        </div>
                      </div>

                      <div className="sm:w-1/5 text-center mb-4 sm:mb-0">
                        <div className="sm:hidden text-gray-500 text-sm mb-1">
                          {t('price', 'Цена')}:
                        </div>
                        <div className="font-medium">{item.price?.toFixed(2) || '0.00'} ₴</div>
                      </div>

                      <div className="sm:w-1/5 flex justify-center mb-4 sm:mb-0">
                        <div className="flex items-center border rounded-md">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="px-3 py-1 border-r hover:bg-gray-100"
                            disabled={item.quantity <= 1}
                          >
                            <FaMinus
                              size={12}
                              className={item.quantity <= 1 ? 'text-gray-300' : 'text-gray-600'}
                            />
                          </button>
                          <span className="px-4 py-1">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="px-3 py-1 border-l hover:bg-gray-100"
                            disabled={item.stock && item.quantity >= item.stock}
                          >
                            <FaPlus
                              size={12}
                              className={
                                item.stock && item.quantity >= item.stock
                                  ? 'text-gray-300'
                                  : 'text-gray-600'
                              }
                            />
                          </button>
                        </div>
                      </div>

                      <div className="sm:w-1/5 text-right">
                        <div className="sm:hidden text-gray-500 text-sm mb-1">
                          {t('subtotal', 'Сумма')}:
                        </div>
                        <div className="font-medium">
                          {((item.price || 0) * item.quantity).toFixed(2)} ₴
                        </div>
                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="text-red-500 hover:text-red-700 text-sm hidden sm:flex sm:items-center sm:gap-1 justify-end ml-auto mt-2"
                        >
                          <FaTrash size={12} />
                          {t('remove', 'Удалить')}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold mb-4">{t('order_summary', 'Сумма заказа')}</h2>

                <div className="space-y-2 mb-4">
                  <div className="py-2 border-b border-gray-100">
                    <span>Вартість доставки: за тарифами перевізника</span>
                  </div>
                </div>

                <div className="flex justify-between py-2 text-lg font-semibold">
                  <span>{t('total', 'Итого')}</span>
                  <span>{totalPrice.toFixed(2)} ₴</span>
                </div>

                <Link
                  to="/order-confirmation"
                  className="mt-6 bg-primary hover:bg-primary-dark text-white w-full py-3 rounded-md text-center block transition-colors"
                >
                  {t('proceed_to_checkout', 'Оформить заказ')}
                </Link>

                <p className="text-xs text-gray-500 text-center mt-4">
                  {t(
                    'taxes_included',
                    'Налог включен в стоимость. Доставка рассчитывается на странице оформления.'
                  )}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CartPage;
