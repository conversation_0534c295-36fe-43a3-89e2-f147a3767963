import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';
import { useCompare } from '../context/CompareContext';
import { supabase } from '../supabaseClient';
import { Heart, ShoppingCart, XCircle, Trash2, ArrowLeft } from 'react-feather';

const WishlistPage = () => {
  const { t } = useTranslation();
  const { wishlist, removeFromWishlist } = useWishlist();
  const { addToCart } = useCart();
  const { addToCompare, removeFromCompare, isInCompare } = useCompare();
  const [wishlistWithDetails, setWishlistWithDetails] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleAddToCart = product => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image_url || product.image,
      quantity: 1
    });
  };

  const handleAddToCompare = (e, product) => {
    e.preventDefault();
    e.stopPropagation();

    if (isInCompare(product.id)) {
      // Если товар уже в сравнении - удаляем его
      removeFromCompare(product.id);
    } else {
      // Если товара нет в сравнении - добавляем его
      addToCompare({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image_url || product.image
      });
    }
  };

  useEffect(() => {
    const fetchProductDetails = async () => {
      setLoading(true);
      setError(null);

      if (!Array.isArray(wishlist) || wishlist.length === 0) {
        setWishlistWithDetails([]);
        setLoading(false);
        return;
      }

      try {
        const productIds = wishlist.map(item => item.id);

        const { data: products, error } = await supabase
          .from('products')
          .select('*')
          .in('id', productIds);

        if (error) throw error;

        if (products && products.length > 0) {
          const productsWithImagesFixed = products.map(product => ({
            ...product,
            image_url: product.image_url || product.image || '/placeholder.png'
          }));
          setWishlistWithDetails(productsWithImagesFixed);
        } else {
          const updatedWishlist = await Promise.all(
            wishlist.map(async item => {
              return {
                id: item.id,
                name: `Товар ${item.id}`,
                image: '/placeholder.png',
                image_url: '/placeholder.png',
                price: Math.floor(Math.random() * 100) + 1
              };
            })
          );
          setWishlistWithDetails(updatedWishlist);
        }
      } catch (err) {
        console.error('Ошибка при загрузке данных списка желаемого:', err);
        setError(
          err.code === 'permission-denied'
            ? t('error_permission_denied', 'Нет доступа к данным. Проверьте права доступа.')
            : t(
                'error_fetching_data',
                'Ошибка при загрузке данных. Проверьте подключение к интернету.'
              )
        );
      } finally {
        setLoading(false);
      }
    };

    fetchProductDetails();
  }, [wishlist, t]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 py-12 text-center">
        <div className="flex justify-center items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
        <p className="mt-4 text-gray-600">{t('loading', 'Загрузка...')}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 sm:px-6 py-12">
        <div className="flex items-center mb-8">
          <Heart size={28} className="text-red-500 mr-3" />
          <h1 className="text-3xl font-semibold">{t('wishlist', 'Избранное')}</h1>
        </div>
        <div className="bg-red-50 border border-red-300 text-red-700 px-6 py-5 rounded-lg shadow-sm">
          <div className="flex items-start">
            <XCircle className="mr-3 mt-0.5 flex-shrink-0" size={20} />
            <p>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12">
      <div className="mb-10">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between">
          <div className="flex items-center mb-4 sm:mb-0">
            <div className="bg-red-50 p-3 rounded-full mr-3">
              <Heart size={24} className="text-red-500" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">{t('wishlist', 'Избранное')}</h1>
              {wishlistWithDetails.length > 0 && (
                <p className="text-gray-500 mt-1">
                  {t('items_in_wishlist', '{{count}} товаров', {
                    count: wishlistWithDetails.length
                  })}
                </p>
              )}
            </div>
          </div>

          {wishlistWithDetails.length > 0 && (
            <Link
              to="/"
              className="flex items-center text-primary hover:text-primary-dark transition-colors"
            >
              <ArrowLeft size={18} className="mr-1" />
              <span>{t('continue_shopping', 'Продолжить покупки')}</span>
            </Link>
          )}
        </div>
      </div>

      {wishlistWithDetails.length === 0 ? (
        <div className="bg-white rounded-2xl shadow-sm p-8 sm:p-12 text-center max-w-2xl mx-auto border border-gray-100">
          <div className="bg-red-50 h-24 w-24 rounded-full flex items-center justify-center mx-auto mb-6">
            <Heart size={36} className="text-red-400" />
          </div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            {t('wishlist_empty_title', 'Ваш список избранного пуст')}
          </h2>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            {t(
              'wishlist_empty_message',
              'Добавляйте товары в избранное, чтобы вернуться к ним позже или поделиться с друзьями'
            )}
          </p>
          <Link
            to="/categories"
            className="btn-primary bg-gradient-to-r from-primary to-primary-dark hover:from-primary-dark hover:to-primary px-6 py-3 inline-block rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-0.5"
          >
            {t('explore_products', 'Найти товары')}
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8 fade-in">
          {wishlistWithDetails.map((item, index) => (
            <div
              key={`${item.id}-${index}`}
              className="product-card group bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 flex flex-col h-full"
              style={{
                animation: `fadeIn 0.5s ease-in-out forwards ${index * 0.05}s`,
                minHeight: '400px'
              }}
            >
              <div className="relative" style={{ height: '240px' }}>
                <Link to={`/product/${item.id}`} className="block h-full">
                  <div className="overflow-hidden bg-gray-50 h-full relative">
                    <img
                      src={item.image_url || item.image || '/placeholder.png'}
                      alt={item.name || 'Товар'}
                      className="w-full h-full object-contain p-4 transition-transform duration-500 group-hover:scale-110"
                      onError={e => {
                        e.target.onerror = null;
                        e.target.src = '/placeholder.png';
                      }}
                    />
                    {item.is_new && (
                      <div className="absolute top-3 left-3 bg-green-500 text-white text-xs font-medium py-1 px-2 rounded-full">
                        {t('new', 'Новинка')}
                      </div>
                    )}
                    {item.discount_percent > 0 && (
                      <div className="absolute top-3 left-3 bg-red-500 text-white text-xs font-medium py-1 px-2 rounded-full">
                        -{item.discount_percent}%
                      </div>
                    )}
                  </div>
                </Link>

                <div className="absolute top-2 right-2 flex flex-col gap-2">
                  <button
                    onClick={() => removeFromWishlist(item.id)}
                    className="p-2 rounded-full bg-red-100 text-red-500 transition-colors shadow-sm hover:bg-red-200 group-hover:animate-pulse"
                    aria-label={t('remove_from_wishlist', 'Убрать из избранного')}
                  >
                    <Heart size={16} fill="currentColor" />
                  </button>
                  <button
                    onClick={e => handleAddToCompare(e, item)}
                    className={`p-2 rounded-full ${
                      isInCompare(item.id)
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-white hover:bg-blue-50 hover:text-blue-500 text-gray-500'
                    } transition-colors shadow-sm`}
                    aria-label={
                      isInCompare(item.id)
                        ? t('in_compare', 'В сравнении')
                        : t('compare', 'Сравнить')
                    }
                  >
                    <svg
                      width="16"
                      height="16"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      viewBox="0 0 24 24"
                    >
                      <rect x="2" y="9" width="7" height="13" rx="2" />
                      <rect x="15" y="4" width="7" height="18" rx="2" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="p-5 flex flex-col h-full">
                <Link to={`/product/${item.id}`} className="block flex-grow">
                  <h2 className="text-base sm:text-lg font-semibold mb-2 text-gray-800 hover:text-primary transition-colors line-clamp-2">
                    {item.name || 'Неизвестный товар'}
                  </h2>
                </Link>

                {item.brand && <p className="text-sm text-gray-500 mb-3">{item.brand}</p>}

                <div className="mt-auto">
                  <div className="flex justify-between items-end mb-4">
                    <div>
                      <p className="text-xl font-bold text-primary">
                        {item.price ? `${item.price} ₴` : 'Цена недоступна'}
                      </p>
                      {item.is_on_sale && item.original_price && (
                        <p className="text-sm text-gray-500 line-through">
                          {item.original_price} ₴
                        </p>
                      )}
                    </div>
                    {item.rating && (
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-yellow-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.799-2.034c-.784-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="text-sm text-gray-600 ml-1">{item.rating}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => removeFromWishlist(item.id)}
                      className="flex items-center text-gray-500 text-sm hover:text-red-500 transition-colors"
                    >
                      <Trash2 size={14} className="mr-1" />
                      {t('remove', 'Удалить')}
                    </button>

                    <button
                      onClick={() => handleAddToCart(item)}
                      className="flex items-center gap-1.5 bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <ShoppingCart size={16} />
                      <span className="font-medium">{t('add_to_cart_short', 'В корзину')}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WishlistPage;
