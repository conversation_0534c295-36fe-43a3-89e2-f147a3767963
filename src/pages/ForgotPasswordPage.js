import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { supabase } from '../supabaseClient';

const ForgotPasswordPage = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) throw error;

      setSuccess(true);
      toast.success(
        t(
          'password_reset_email_sent',
          'Инструкции по сбросу пароля отправлены на вашу электронную почту'
        )
      );
    } catch (error) {
      console.error('Error resetting password:', error);
      let errorMessage = t('password_reset_failed', 'Ошибка запроса сброса пароля');

      if (error.message) {
        if (error.message.includes('User not found')) {
          errorMessage = t('user_not_found', 'Пользователь с таким email не найден');
        } else {
          errorMessage += ': ' + error.message;
        }
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>{t('forgot_password', 'Восстановление пароля')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-semibold mb-6 text-center">
            {t('forgot_password', 'Восстановление пароля')}
          </h1>

          {success ? (
            <div className="text-center">
              <div className="mb-4 text-green-600">
                {t(
                  'check_email_instructions',
                  'Проверьте вашу электронную почту для получения инструкций по сбросу пароля.'
                )}
              </div>
              <Link to="/login" className="text-blue-600 hover:underline">
                {t('back_to_login', 'Вернуться на страницу входа')}
              </Link>
            </div>
          ) : (
            <>
              <p className="mb-6 text-gray-600">
                {t(
                  'forgot_password_description',
                  'Введите адрес электронной почты, связанный с вашей учетной записью, и мы отправим вам ссылку для сброса пароля.'
                )}
              </p>

              <form onSubmit={handleSubmit}>
                <div className="mb-6">
                  <label htmlFor="email" className="block text-gray-700 mb-2">
                    {t('email', 'Email')}
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className={`w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors ${
                    loading ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {loading
                    ? t('sending', 'Отправка...')
                    : t('send_reset_instructions', 'Отправить инструкции')}
                </button>
              </form>

              <div className="mt-6 text-center">
                <Link to="/login" className="text-blue-600 hover:underline">
                  {t('back_to_login', 'Вернуться на страницу входа')}
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default ForgotPasswordPage;
