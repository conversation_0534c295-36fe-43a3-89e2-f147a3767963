import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Helmet } from 'react-helmet-async';
import { FaS<PERSON>ner, FaArrowLeft } from 'react-icons/fa';
import { toast } from 'react-toastify';

const UserOrderDetailPage = () => {
  const { orderId } = useParams();
  const { user } = useAuth();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!orderId || !user) {
      setLoading(false);
      return;
    }

    const fetchOrderDetails = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data, error: orderError } = await supabase
          .from('orders')
          .select(
            `
            *,
            order_items (*, products (*))
          `
          )
          .eq('id', orderId)
          .eq('user_id', user.id)
          .single();

        if (orderError) throw orderError;
        if (!data) throw new Error('Заказ не найден или у вас нет доступа к нему.');

        setOrder(data);
      } catch (err) {
        console.error('Error fetching order details:', err);
        setError(err.message);
        toast.error(err.message || 'Ошибка при загрузке деталей заказа.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId, user]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen-75">
        <FaSpinner className="animate-spin text-primary text-4xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 text-center">
        <Helmet>
          <title>Ошибка заказа</title>
        </Helmet>
        <p className="text-red-500 text-xl">{error}</p>
        <Link to="/profile?tab=orders" className="btn-primary mt-4 inline-block">
          К моим заказам
        </Link>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto p-4 text-center">
        <Helmet>
          <title>Заказ не найден</title>
        </Helmet>
        <p className="text-xl">Заказ не найден.</p>
        <Link to="/profile?tab=orders" className="btn-primary mt-4 inline-block">
          К моим заказам
        </Link>
      </div>
    );
  }

  const getStatusBadge = status => {
    const badges = {
      delivered: { bg: 'bg-green-100', text: 'text-green-800', label: 'Доставлен' },
      shipped: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Отправлен' },
      processing: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'В обработке' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Отменен' },
      pending: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Ожидает обработки' }
    };
    const badge = badges[status] || { bg: 'bg-gray-100', text: 'text-gray-800', label: status };
    return (
      <span className={`px-3 py-1.5 text-sm font-semibold rounded-full ${badge.bg} ${badge.text}`}>
        {badge.label}
      </span>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6 md:p-8 bg-gray-50 min-h-screen">
      <Helmet>
        <title>{`Заказ #${order.id.slice(0, 8)}`}</title>
      </Helmet>

      <div className="mb-6">
        <Link
          to="/profile?tab=orders"
          className="text-primary hover:text-primary-dark font-medium flex items-center"
        >
          <FaArrowLeft className="mr-2" />
          Назад к моим заказам
        </Link>
      </div>

      <div className="bg-white shadow-xl rounded-lg p-4 md:p-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 pb-4 border-b">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
              Заказ #{order.id.slice(0, 8)}
            </h1>
            <p className="text-gray-500 mt-1">
              Дата:{' '}
              {new Date(order.created_at).toLocaleDateString('uk-UA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
          <div className="mt-3 md:mt-0">{getStatusBadge(order.status)}</div>
        </div>

        <div className="grid md:grid-cols-3 gap-4 md:gap-8 mb-8">
          <div className="md:col-span-2">
            <h2 className="text-xl font-semibold text-gray-700 mb-3">Товары в заказе</h2>
            <div className="space-y-3">
              {order.order_items &&
                order.order_items.map(item => (
                  <div
                    key={item.id}
                    className="flex flex-col md:flex-row md:items-center bg-gray-50 p-3 md:p-4 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center">
                      <img
                        src={item.products?.image || '/placeholder.png'}
                        alt={item.products?.name}
                        className="w-16 h-16 md:w-20 md:h-20 object-contain rounded-md mr-4 bg-white p-1"
                      />
                      <div className="flex-grow min-w-0">
                        <Link
                          to={`/product/${item.products?.id}`}
                          className="font-semibold text-primary hover:underline block text-sm md:text-base break-words line-clamp-2 md:line-clamp-none"
                        >
                          {item.products?.name || 'Название товара отсутствует'}
                        </Link>
                        <p className="text-xs md:text-sm text-gray-600 mt-1">
                          Количество: {item.quantity}
                        </p>
                        <p className="text-xs md:text-sm text-gray-600">
                          Цена за единицу: {parseFloat(item.price).toFixed(2)} грн
                        </p>
                      </div>
                    </div>
                    <p className="text-base md:text-lg font-semibold text-gray-800 mt-2 md:mt-0 md:ml-auto">
                      {(parseFloat(item.price) * item.quantity).toFixed(2)} грн
                    </p>
                  </div>
                ))}
              {(!order.order_items || order.order_items.length === 0) && (
                <p>В этом заказе нет товаров.</p>
              )}
            </div>
          </div>

          <div className="mt-6 md:mt-0">
            <h2 className="text-xl font-semibold text-gray-700 mb-3">Сумма заказа</h2>
            <div className="bg-gray-100 p-4 md:p-6 rounded-lg space-y-2 md:space-y-3">
              <div className="flex justify-between">
                <span className="text-sm md:text-base text-gray-600">Подытог:</span>
                <span className="font-medium text-sm md:text-base">
                  {parseFloat(
                    order.total_amount - (order.shipping_fee || 0) + (order.discount_amount || 0)
                  ).toFixed(2)}{' '}
                  грн
                </span>
              </div>
              {order.discount_amount > 0 && (
                <div className="flex justify-between text-green-600 text-sm md:text-base">
                  <span>Скидка:</span>
                  <span className="font-medium">
                    -{parseFloat(order.discount_amount).toFixed(2)} грн
                  </span>
                </div>
              )}
              <div className="flex justify-between text-sm md:text-base">
                <span className="text-gray-600">Доставка:</span>
                <span className="font-medium">
                  {parseFloat(order.shipping_fee || 0).toFixed(2)} грн
                </span>
              </div>
              <div className="flex justify-between text-base md:text-xl font-bold text-gray-800 pt-2 md:pt-3 border-t mt-2 md:mt-3">
                <span>Итого:</span>
                <span>{parseFloat(order.total_amount).toFixed(2)} грн</span>
              </div>
            </div>

            <h2 className="text-xl font-semibold text-gray-700 mt-6 md:mt-8 mb-3">
              Адрес доставки
            </h2>
            <div className="bg-gray-100 p-4 md:p-6 rounded-lg text-sm md:text-base">
              {order.shipping_address ? (
                <>
                  <p>{order.shipping_address.full_name}</p>
                  <p>
                    {order.shipping_address.street}, {order.shipping_address.house}
                  </p>
                  <p>
                    {order.shipping_address.city}, {order.shipping_address.zip_code}
                  </p>
                  {order.shipping_address.nova_poshta_branch && (
                    <p className="break-words">
                      Отделение НП: {order.shipping_address.nova_poshta_branch}
                    </p>
                  )}
                  <p>{order.shipping_address.phone}</p>
                </>
              ) : (
                <p>Адрес доставки не указан.</p>
              )}
            </div>

            <h2 className="text-xl font-semibold text-gray-700 mt-6 md:mt-8 mb-3">Способ оплаты</h2>
            <div className="bg-gray-100 p-4 md:p-6 rounded-lg text-sm md:text-base">
              <p>{order.payment_method || 'Не указан'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserOrderDetailPage;
