import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useWishlist } from '../context/WishlistContext';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { Helmet } from 'react-helmet-async';

const ProfilePage = () => {
  const { t, i18n } = useTranslation();
  const { user, updatePassword } = useAuth();
  const { wishlist } = useWishlist();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('info');
  const [orders, setOrders] = useState([]);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchOrders = async () => {
      setLoading(true);
      try {
        // Если в Firestore нет данных, проверяем localStorage
        const savedOrders = localStorage.getItem(`orders_${user.uid}`);
        const parsedOrders = savedOrders ? JSON.parse(savedOrders) : [];
        setOrders(parsedOrders);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError(t('error_fetching_data', 'Failed to load orders.'));
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user, navigate, t]);

  const handlePasswordChange = async e => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      setError(t('password_mismatch', 'Passwords do not match.'));
      return;
    }
    try {
      await updatePassword(user, newPassword);
      setSuccess(t('password_updated', 'Password updated successfully.'));
      setError('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (err) {
      setError(t('password_update_error', 'Failed to update password.'));
      setSuccess('');
    }
  };

  const changeLanguage = lng => {
    i18n.changeLanguage(lng);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">
        <p>{t('loading', 'Loading...')}</p>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{t('profile_title', 'My Profile - Kitchen Shop')}</title>
        <meta
          name="description"
          content={t(
            'profile_description',
            'Manage your Kitchen Shop account, orders, and preferences.'
          )}
        />
      </Helmet>
      <div className="container mx-auto px-6 py-12">
        <h1 className="text-3xl font-semibold mb-8 text-center">{t('profile', 'Profile')}</h1>
        <div className="card">
          <div className="flex border-b mb-6">
            <button
              onClick={() => setActiveTab('info')}
              className={`px-4 py-2 ${activeTab === 'info' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600'}`}
            >
              {t('user_info', 'User Info')}
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`px-4 py-2 ${activeTab === 'orders' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600'}`}
            >
              {t('orders', 'Orders')}
            </button>
            <button
              onClick={() => setActiveTab('wishlist')}
              className={`px-4 py-2 ${activeTab === 'wishlist' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600'}`}
            >
              {t('wishlist', 'Wishlist')}
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`px-4 py-2 ${activeTab === 'settings' ? 'border-b-2 border-blue-600 text-blue-600' : 'text-gray-600'}`}
            >
              {t('settings', 'Settings')}
            </button>
          </div>
          {activeTab === 'info' && (
            <div>
              <p className="text-gray-600 mb-2">
                {t('email', 'Email')}: {user?.email}
              </p>
            </div>
          )}
          {activeTab === 'orders' && (
            <div>
              {orders.length === 0 ? (
                <p className="text-gray-600">{t('no_orders', 'You have no orders.')}</p>
              ) : (
                <div className="grid gap-4">
                  {orders.map((order, index) => (
                    <div key={`${order.id}-${index}`} className="border-b py-4">
                      <p className="text-gray-600">
                        {t('order_id', 'Order ID')}: {order.id}
                      </p>
                      <p className="text-gray-600">
                        {t('order_date', 'Order Date')}: {order.date}
                      </p>
                      <p className="text-gray-600">
                        {t('order_total', 'Total')}: {order.total} {t('price')}
                      </p>
                      <h3 className="text-lg font-semibold mt-2">{t('items', 'Items')}</h3>
                      <ul className="list-disc pl-5">
                        {order.items.map((item, itemIndex) => (
                          <li key={`${item.id}-${itemIndex}`} className="text-gray-600">
                            {item.name} - {item.quantity} x {item.price} {t('price')}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
          {activeTab === 'wishlist' && (
            <div>
              {wishlist.length === 0 ? (
                <p className="text-gray-600">{t('wishlist_empty', 'Your wishlist is empty.')}</p>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {wishlist.map((product, index) => (
                    <ProductCard key={`${product.id}-${index}`} {...product} />
                  ))}
                </div>
              )}
            </div>
          )}
          {activeTab === 'settings' && (
            <div>
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">
                  {t('change_password', 'Change Password')}
                </h2>
                <form onSubmit={handlePasswordChange} className="space-y-4">
                  <input
                    type="password"
                    value={newPassword}
                    onChange={e => setNewPassword(e.target.value)}
                    placeholder={t('new_password', 'New Password')}
                    className="w-full p-2 border rounded"
                    required
                  />
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={e => setConfirmPassword(e.target.value)}
                    placeholder={t('confirm_password', 'Confirm Password')}
                    className="w-full p-2 border rounded"
                    required
                  />
                  {error && <p className="text-red-500">{error}</p>}
                  {success && <p className="text-green-600">{success}</p>}
                  <button type="submit" className="btn-primary">
                    {t('update_password', 'Update Password')}
                  </button>
                </form>
              </div>
              <div>
                <h2 className="text-xl font-semibold mb-4">{t('language', 'Language')}</h2>
                <div className="flex gap-4">
                  <button
                    onClick={() => changeLanguage('uk')}
                    className={`px-4 py-2 ${i18n.language === 'uk' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'} rounded`}
                  >
                    Українська
                  </button>
                  <button
                    onClick={() => changeLanguage('en')}
                    className={`px-4 py-2 ${i18n.language === 'en' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'} rounded`}
                  >
                    English
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ProfilePage;
