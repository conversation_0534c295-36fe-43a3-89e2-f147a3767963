import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { Link } from 'react-router-dom';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const AnalyticsPage = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('month'); // month, week, year
  const [salesData, setSalesData] = useState([]);
  const [topProducts, setTopProducts] = useState([]);
  const [categorySales, setCategorySales] = useState([]);
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  // Wrap fetchAnalyticsData in useCallback
  const fetchAnalyticsData = useCallback(async () => {
    setLoading(true);
    try {
      // В реальном приложении здесь будут запросы к вашей БД
      // Для примера используем моковые данные

      // Данные по продажам
      const mockSalesData = generateMockSalesData(period);
      setSalesData(mockSalesData);

      // Данные по товарам
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('id, name, price')
        .order('price', { ascending: false })
        .limit(10);

      if (productsError) throw productsError;

      const topProductsWithSales = productsData
        .map(product => ({
          ...product,
          sales: Math.floor(Math.random() * 500) + 100 // Моковые данные продаж
        }))
        .sort((a, b) => b.sales - a.sales);

      setTopProducts(topProductsWithSales);

      // Данные по категориям
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('categories')
        .select('id, name')
        .limit(5);

      if (categoriesError) throw categoriesError;

      const categorySalesData = categoriesData.map(category => ({
        name: category.name,
        value: Math.floor(Math.random() * 1000) + 200 // Моковые данные продаж
      }));

      setCategorySales(categorySalesData);
    } catch (error) {
      console.error('Ошибка при загрузке аналитики:', error);
      toast.error('Не удалось загрузить данные аналитики');
    } finally {
      setLoading(false);
    }
  }, [period]); // Add any dependencies this function relies on

  // Fix useEffect by adding the missing dependency
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]); // Added missing dependency

  const generateMockSalesData = periodType => {
    let days = 30;
    // Remove unused variable
    // let format = 'DD.MM';

    if (periodType === 'week') {
      days = 7;
    } else if (periodType === 'year') {
      days = 12;
      // format = 'MMM';
    }

    return Array(days)
      .fill()
      .map((_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (days - i - 1));

        let label = `${date.getDate()}.${date.getMonth() + 1}`;
        if (periodType === 'year') {
          const months = [
            'Янв',
            'Фев',
            'Мар',
            'Апр',
            'Май',
            'Июн',
            'Июл',
            'Авг',
            'Сен',
            'Окт',
            'Ноя',
            'Дек'
          ];
          label = months[date.getMonth()];
        }

        return {
          name: label,
          продажи: Math.floor(Math.random() * 10000) + 5000,
          посетители: Math.floor(Math.random() * 500) + 100
        };
      });
  };

  return (
    <>
      <Helmet>
        <title>{t('analytics', 'Аналитика - Админ панель')}</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="container mx-auto px-6 py-12">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold">{t('sales_analytics', 'Аналитика продаж')}</h1>
          <div className="flex gap-4">
            <Link
              to="/admin"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              {t('back_to_dashboard', 'Вернуться к панели')}
            </Link>
          </div>
        </div>

        <div className="mb-6 bg-white rounded-lg shadow p-4">
          <div className="flex gap-4 mb-6">
            <button
              onClick={() => setPeriod('week')}
              className={`px-4 py-2 rounded ${period === 'week' ? 'bg-primary text-white' : 'bg-gray-200'}`}
            >
              {t('last_week', 'Неделя')}
            </button>
            <button
              onClick={() => setPeriod('month')}
              className={`px-4 py-2 rounded ${period === 'month' ? 'bg-primary text-white' : 'bg-gray-200'}`}
            >
              {t('last_month', 'Месяц')}
            </button>
            <button
              onClick={() => setPeriod('year')}
              className={`px-4 py-2 rounded ${period === 'year' ? 'bg-primary text-white' : 'bg-gray-200'}`}
            >
              {t('last_year', 'Год')}
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div>
              <h2 className="text-xl font-medium mb-4">
                {t('sales_over_time', 'Динамика продаж')}
              </h2>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={salesData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="продажи" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="посетители" stroke="#82ca9d" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-4">
            <h2 className="text-xl font-medium mb-4">{t('top_products', 'Лучшие товары')}</h2>
            {loading ? (
              <div className="flex justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={topProducts}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis type="category" dataKey="name" width={150} tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="sales" name="Продажи" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h2 className="text-xl font-medium mb-4">
              {t('category_distribution', 'Распределение по категориям')}
            </h2>
            {loading ? (
              <div className="flex justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={categorySales}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={entry => entry.name}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categorySales.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AnalyticsPage;
