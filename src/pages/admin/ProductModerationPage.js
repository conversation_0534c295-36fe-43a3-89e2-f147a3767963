import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient'; // Исправленный путь
import { FaCheckCircle, FaTimesCircle, FaEdit, FaSpinner } from 'react-icons/fa';
import { Link } from 'react-router-dom';

const ProductModerationPage = () => {
  const { t } = useTranslation();
  const [pendingProducts, setPendingProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState({}); // Для отслеживания загрузки действий по ID товара
  const [notification, setNotification] = useState(null); // Для уведомлений

  // Пагинация
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const itemsPerPage = 10;

  // Функция для показа уведомлений
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000); // Убираем через 3 секунды
  };

  const fetchPendingProducts = useCallback(
    async (page = 1) => {
      setLoading(true);
      setError(null);
      try {
        // Получаем общее количество для пагинации
        const { count, error: countError } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true })
          .eq('moderation_status', 'pending_approval');

        if (countError) throw countError;
        setTotalCount(count || 0);

        // Получаем продукты для текущей страницы
        const from = (page - 1) * itemsPerPage;
        const to = from + itemsPerPage - 1;

        const { data, error: fetchError } = await supabase
          .from('products')
          .select('*')
          .eq('moderation_status', 'pending_approval')
          .order('created_at', { ascending: true })
          .range(from, to);

        if (fetchError) throw fetchError;
        setPendingProducts(data || []);
        setCurrentPage(page);
      } catch (err) {
        console.error('Error fetching pending products:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    },
    [itemsPerPage]
  );

  useEffect(() => {
    fetchPendingProducts(currentPage);
  }, [fetchPendingProducts, currentPage]);

  const handleApprove = async productId => {
    console.log('Approving product:', productId);
    setActionLoading(prev => ({ ...prev, [productId]: true }));
    try {
      const { data, error: updateError } = await supabase
        .from('products')
        .update({
          moderation_status: 'approved',
          is_active: true
          // Убираем status из-за проблем с этой колонкой
        })
        .eq('id', productId)
        .select();

      if (updateError) throw updateError;

      console.log('Product approved successfully:', data);

      // Обновить список после одобрения
      setPendingProducts(prev => prev.filter(p => p.id !== productId));
      setTotalCount(prev => prev - 1);

      // Показать сообщение об успехе
      showNotification(`Продукт успешно одобрен!`, 'success');
    } catch (err) {
      console.error('Error approving product:', err);
      setError(`Failed to approve product ${productId}: ${err.message}`);
      showNotification(`Ошибка при одобрении: ${err.message}`, 'error');
    } finally {
      setActionLoading(prev => ({ ...prev, [productId]: false }));
    }
  };

  const handleDecline = async productId => {
    console.log('Declining product:', productId);
    // Решите, что делать при отклонении: просто обновить статус или удалить
    // Вариант 1: Обновить статус
    setActionLoading(prev => ({ ...prev, [productId]: true }));
    try {
      const { data, error: updateError } = await supabase
        .from('products')
        .update({ moderation_status: 'rejected', is_active: false }) // is_active: false тоже важно
        .eq('id', productId)
        .select();

      if (updateError) throw updateError;

      console.log('Product rejected successfully:', data);

      setPendingProducts(prev => prev.filter(p => p.id !== productId));
      setTotalCount(prev => prev - 1);

      // Показать сообщение об успехе
      showNotification(`Продукт отклонен!`, 'warning');
    } catch (err) {
      console.error('Error declining product:', err);
      setError(`Failed to decline product ${productId}: ${err.message}`);
      showNotification(`Ошибка при отклонении: ${err.message}`, 'error');
    } finally {
      setActionLoading(prev => ({ ...prev, [productId]: false }));
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <FaSpinner className="animate-spin text-4xl text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-500 p-4 bg-red-100 border border-red-400 rounded">
        {t('error_loading_pending_products', 'Error loading products for moderation:')} {error}
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Компонент уведомлений */}
      {notification && (
        <div
          className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            notification.type === 'success'
              ? 'bg-green-500 text-white'
              : notification.type === 'error'
                ? 'bg-red-500 text-white'
                : notification.type === 'warning'
                  ? 'bg-yellow-500 text-white'
                  : 'bg-blue-500 text-white'
          }`}
        >
          <div className="flex items-center justify-between">
            <span>{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-4 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('product_moderation', 'Product Moderation')}</h1>
        <div className="flex items-center gap-4">
          <span className="text-lg font-semibold text-gray-600">
            {totalCount} товаров в очереди
          </span>
          <button
            onClick={() => fetchPendingProducts(currentPage)}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {loading ? 'Загрузка...' : 'Обновить'}
          </button>
        </div>
      </div>

      {pendingProducts.length === 0 ? (
        <div className="text-center text-gray-500 py-10">
          {t('no_products_pending_moderation', 'No products are currently pending moderation.')}
        </div>
      ) : (
        <>
          <div className="overflow-x-auto bg-white shadow-md rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('image', 'Image')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('name', 'Name')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('price', 'Price')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('category', 'Category')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('created_at', 'Created At')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('actions', 'Actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pendingProducts.map(product => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.image && (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-16 h-16 object-cover rounded"
                        />
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      {product.external_id && (
                        <div className="text-xs text-gray-500">Ext. ID: {product.external_id}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {product.price} {/* Добавьте форматирование цены, если нужно */}
                      {product.original_price && product.original_price > product.price && (
                        <span className="ml-2 text-xs text-gray-500 line-through">
                          {product.original_price}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.category_id} {/* Позже можно заменить на имя категории */}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(product.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleApprove(product.id)}
                        disabled={actionLoading[product.id]}
                        className="text-green-600 hover:text-green-900 mr-3 p-1 disabled:opacity-50"
                        title={t('approve', 'Approve')}
                      >
                        {actionLoading[product.id] ? (
                          <FaSpinner className="animate-spin" />
                        ) : (
                          <FaCheckCircle size={18} />
                        )}
                      </button>
                      <button
                        onClick={() => handleDecline(product.id)}
                        disabled={actionLoading[product.id]}
                        className="text-red-600 hover:text-red-900 mr-3 p-1 disabled:opacity-50"
                        title={t('decline', 'Decline')}
                      >
                        {actionLoading[product.id] ? (
                          <FaSpinner className="animate-spin" />
                        ) : (
                          <FaTimesCircle size={18} />
                        )}
                      </button>
                      {/* Ссылка на редактирование товара, исправлен путь */}
                      <Link
                        to={`/admin/products/edit/${product.id}`}
                        className="text-indigo-600 hover:text-indigo-900 p-1"
                        title={t('edit', 'Edit')}
                      >
                        <FaEdit size={16} />
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Компонент пагинации */}
          {totalCount > itemsPerPage && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                Показано {(currentPage - 1) * itemsPerPage + 1} до{' '}
                {Math.min(currentPage * itemsPerPage, totalCount)} из {totalCount} товаров
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => fetchPendingProducts(currentPage - 1)}
                  disabled={currentPage === 1 || loading}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Предыдущая
                </button>

                {/* Номера страниц */}
                <div className="flex space-x-1">
                  {Array.from(
                    { length: Math.ceil(totalCount / itemsPerPage) },
                    (_, i) => i + 1
                  ).map(page => (
                    <button
                      key={page}
                      onClick={() => fetchPendingProducts(page)}
                      disabled={loading}
                      className={`px-3 py-2 text-sm font-medium rounded-md disabled:opacity-50 ${
                        page === currentPage
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => fetchPendingProducts(currentPage + 1)}
                  disabled={currentPage === Math.ceil(totalCount / itemsPerPage) || loading}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Следующая
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProductModerationPage;
