import React from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '../../components/admin/AdminLayout';
import { toast } from 'react-toastify';
import { supabase } from '../../supabaseClient';
import ProductForm from './components/ProductForm';

const NewProduct = () => {
  const { t } = useTranslation();

  return (
    <AdminLayout>
      <Helmet>
        <title>{t('new_product', 'Новый товар')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-6">
        <ProductForm isNew={true} />
      </div>
    </AdminLayout>
  );
};

export default NewProduct;
