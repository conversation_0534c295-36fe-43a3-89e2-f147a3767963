import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { supabase } from '../../supabaseClient';

const AdminDashboardContent = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState({
    products: 0,
    categories: 0,
    orders: 0,
    pendingOrders: 0,
    banners: 0,
    users: 0
  });

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);

        // Получаем количество товаров
        const { count: productsCount, error: productsError } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true });

        if (productsError) throw productsError;

        // Получаем количество категорий
        const { count: categoriesCount, error: categoriesError } = await supabase
          .from('categories')
          .select('*', { count: 'exact', head: true });

        if (categoriesError) throw categoriesError;

        // Получаем количество заказов
        let ordersCount = 0;
        let pendingOrdersCount = 0;

        try {
          const { count: allOrdersCount, error: ordersError } = await supabase
            .from('orders')
            .select('*', { count: 'exact', head: true });

          if (ordersError) throw ordersError;
          ordersCount = allOrdersCount || 0;

          // Получаем количество ожидающих заказов
          const { count: pendingCount, error: pendingError } = await supabase
            .from('orders')
            .select('*', { count: 'exact', head: true })
            .eq('status', 'pending');

          if (pendingError) throw pendingError;
          pendingOrdersCount = pendingCount || 0;
        } catch (e) {}

        // Получаем количество баннеров
        let bannersCount = 0;
        try {
          const { count: bannerCount, error: bannersError } = await supabase
            .from('banners')
            .select('*', { count: 'exact', head: true });

          if (bannersError) throw bannersError;
          bannersCount = bannerCount || 0;
        } catch (e) {}

        // Получаем количество пользователей
        let usersCount = 0;
        try {
          const { count: userCount, error: usersError } = await supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true });

          if (usersError) throw usersError;
          usersCount = userCount || 0;
        } catch (e) {}

        setStatistics({
          products: productsCount || 0,
          categories: categoriesCount || 0,
          orders: ordersCount,
          pendingOrders: pendingOrdersCount,
          banners: bannersCount,
          users: usersCount
        });
      } catch (error) {
        console.error('Error fetching dashboard statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  return (
    <>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Статистические карточки */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Товары */}
            <Link
              to="/admin/products"
              className="bg-white rounded-lg shadow p-4 transition hover:shadow-md"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{t('products', 'Товары')}</p>
                  <p className="text-2xl font-semibold">{statistics.products}</p>
                </div>
                <div className="bg-blue-100 p-2 rounded-full">
                  <svg
                    className="w-5 h-5 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                    />
                  </svg>
                </div>
              </div>
              <div className="mt-3">
                <span className="text-primary text-sm font-medium">
                  {t('manage_products', 'Управление товарами')} →
                </span>
              </div>
            </Link>

            {/* Категории */}
            <Link
              to="/admin/categories"
              className="bg-white rounded-lg shadow p-4 transition hover:shadow-md"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{t('categories', 'Категории')}</p>
                  <p className="text-2xl font-semibold">{statistics.categories}</p>
                </div>
                <div className="bg-green-100 p-2 rounded-full">
                  <svg
                    className="w-5 h-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                    />
                  </svg>
                </div>
              </div>
              <div className="mt-3">
                <span className="text-primary text-sm font-medium">
                  {t('manage_categories', 'Управление категориями')} →
                </span>
              </div>
            </Link>

            {/* Заказы */}
            <Link
              to="/admin/orders"
              className="bg-white rounded-lg shadow p-4 transition hover:shadow-md"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{t('orders', 'Заказы')}</p>
                  <p className="text-2xl font-semibold">{statistics.orders}</p>
                </div>
                <div className="bg-orange-100 p-2 rounded-full">
                  <svg
                    className="w-5 h-5 text-orange-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
              </div>
              <div className="mt-3">
                <span className="text-primary text-sm font-medium">
                  {t('manage_orders', 'Управление заказами')} →
                </span>
              </div>
            </Link>

            {/* Ожидающие заказы */}
            <Link
              to="/admin/orders?status=pending"
              className="bg-white rounded-lg shadow p-4 transition hover:shadow-md"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{t('pending_orders', 'Ожидающие заказы')}</p>
                  <p className="text-2xl font-semibold">{statistics.pendingOrders}</p>
                </div>
                <div className="bg-yellow-100 p-2 rounded-full">
                  <svg
                    className="w-5 h-5 text-yellow-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
              </div>
              <div className="mt-3">
                <span className="text-primary text-sm font-medium">
                  {t('view_pending_orders', 'Просмотр ожидающих')} →
                </span>
              </div>
            </Link>

            {/* Баннеры */}
            <Link
              to="/admin/banners"
              className="bg-white rounded-lg shadow p-4 transition hover:shadow-md"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{t('banners', 'Баннеры')}</p>
                  <p className="text-2xl font-semibold">{statistics.banners}</p>
                </div>
                <div className="bg-purple-100 p-2 rounded-full">
                  <svg
                    className="w-5 h-5 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>
              <div className="mt-3">
                <span className="text-primary text-sm font-medium">
                  {t('manage_banners', 'Управление баннерами')} →
                </span>
              </div>
            </Link>

            {/* Пользователи */}
            <Link
              to="/admin/users"
              className="bg-white rounded-lg shadow p-4 transition hover:shadow-md"
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{t('users', 'Пользователи')}</p>
                  <p className="text-2xl font-semibold">{statistics.users}</p>
                </div>
                <div className="bg-indigo-100 p-2 rounded-full">
                  <svg
                    className="w-5 h-5 text-indigo-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                </div>
              </div>
              <div className="mt-3">
                <span className="text-primary text-sm font-medium">
                  {t('manage_users', 'Управление пользователями')} →
                </span>
              </div>
            </Link>
          </div>

          {/* Быстрые действия */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b">
              <h2 className="font-semibold">{t('quick_actions', 'Быстрые действия')}</h2>
            </div>

            <div className="p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <Link
                to="/admin/products/new"
                className="w-full py-2 text-center bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {t('add_product', 'Добавить товар')}
              </Link>
              <Link
                to="/admin/categories/new"
                className="w-full py-2 text-center bg-green-600 text-white rounded hover:bg-green-700"
              >
                {t('add_category', 'Добавить категорию')}
              </Link>
              <Link
                to="/admin/banners/new"
                className="w-full py-2 text-center bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                {t('add_banner', 'Добавить баннер')}
              </Link>
              <Link
                to="/admin/settings"
                className="w-full py-2 text-center bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                {t('store_settings', 'Настройки магазина')}
              </Link>
              <Link
                to="/admin/inventory"
                className="w-full py-2 text-center bg-teal-600 text-white rounded hover:bg-teal-700"
              >
                {t('manage_inventory', 'Управление запасами')}
              </Link>
            </div>
          </div>

          {/* Последние заказы */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b flex justify-between items-center">
              <h2 className="font-semibold">{t('recent_orders', 'Недавние заказы')}</h2>
              <Link to="/admin/orders" className="text-primary text-sm font-medium hover:underline">
                {t('view_all', 'Посмотреть все')} →
              </Link>
            </div>

            <div className="p-4">
              {statistics.orders > 0 ? (
                <p className="text-gray-500 text-sm">Загрузка заказов...</p>
              ) : (
                <p className="text-gray-500 text-sm">{t('no_orders_yet', 'Пока нет заказов')}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AdminDashboardContent;
