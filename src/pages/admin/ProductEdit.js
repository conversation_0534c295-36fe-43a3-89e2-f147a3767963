import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { TABLES } from '../../config/constants';
import { toast } from 'react-toastify';
import { AdminLayout } from '../../components/admin/Layout';
import ProductForm from '../../components/admin/ProductForm';

const ProductEdit = () => {
  const { t } = useTranslation();
  const { id: productId } = useParams(); // Изменено: получаем 'id' из параметров и переименовываем в productId
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState(null);

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) {
        setLoading(false);
        return;
      }

      try {
        // Получаем данные товара вместе со связанными данными
        const { data, error } = await supabase
          .from(TABLES.PRODUCTS)
          .select(
            `
            *,
            category:categories(id, name),
            brand:brands!brand_id(id, name),
            params:product_params(*)
          `
          )
          .eq('id', productId)
          .single();

        if (error) {
          console.error('Ошибка при загрузке товара:', error);
          throw error;
        }
        setProduct(data);
      } catch (error) {
        console.error('Ошибка при загрузке товара:', error);
        toast.error(t('error_loading_product', 'Ошибка при загрузке товара'));
        navigate('/admin/products');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId, navigate, t]);

  // Обработчик успешного сохранения
  const handleSuccess = () => {
    toast.success(
      productId
        ? t('product_updated', 'Товар успешно обновлен')
        : t('product_created', 'Товар успешно создан')
    );
    navigate('/admin/products');
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">
                {productId
                  ? t('edit_product', 'Редактировать товар')
                  : t('new_product', 'Новый товар')}
              </h1>
              <button
                onClick={() => navigate('/admin/products')}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              >
                {t('back_to_list', 'Вернуться к списку')}
              </button>
            </div>

            {!productId && !product ? (
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded mb-6">
                {t('creating_new_product', 'Создание нового товара')}
              </div>
            ) : !product ? (
              <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded mb-6">
                {t('product_not_found', 'Товар не найден')}
              </div>
            ) : (
              <ProductForm product={product} isNew={!productId} onSuccess={handleSuccess} />
            )}
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default ProductEdit;
