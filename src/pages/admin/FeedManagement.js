import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FaPlus,
  FaSync,
  FaCheck,
  FaTimes,
  FaEdit,
  FaTrash,
  FaSpinner,
  FaChartLine,
  FaClock,
  FaExclamationTriangle,
  FaInfoCircle,
  FaEye,
  FaHistory
} from 'react-icons/fa';
import { supabase } from '../../supabaseClient';

const FeedManagement = () => {
  const { t } = useTranslation();
  const [feeds, setFeeds] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [processing, setProcessing] = useState({});
  const [selectedFeed, setSelectedFeed] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('feeds'); // 'feeds', 'monitoring', 'history'
  const [recentJobs, setRecentJobs] = useState([]);
  const [systemStats, setSystemStats] = useState(null);
  const [selectedJobDetails, setSelectedJobDetails] = useState(null);
  const [isJobModalOpen, setIsJobModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    language: 'ru',
    active: true
  });
  const [formErrors, setFormErrors] = useState({});
  const [notification, setNotification] = useState(null); // Для уведомлений

  // Функция для показа уведомлений
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000); // Убираем через 5 секунд
  };

  useEffect(() => {
    fetchFeeds();
    if (activeTab === 'monitoring' || activeTab === 'history') {
      fetchSystemStats();
      fetchRecentJobs();
    }
  }, [activeTab]);

  // Auto-refresh data every 30 seconds for monitoring tab
  useEffect(() => {
    let interval;
    if (activeTab === 'monitoring') {
      interval = setInterval(() => {
        fetchSystemStats();
        fetchRecentJobs();
        fetchFeeds(); // Also refresh feeds to get latest status
      }, 30000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [activeTab]);

  const fetchFeeds = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.from('feeds').select('*').order('name');

      if (error) throw error;

      // Get recent jobs for each feed
      const feedsWithJobs = await Promise.all(
        data.map(async feed => {
          const { data: jobs } = await supabase
            .from('feed_jobs')
            .select('*')
            .eq('feed_id', feed.id)
            .order('created_at', { ascending: false })
            .limit(1);

          return {
            ...feed,
            lastJob: jobs && jobs.length > 0 ? jobs[0] : null
          };
        })
      );

      setFeeds(feedsWithJobs);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching feeds:', err);
      setError(err.message);
      setLoading(false);
    }
  };

  const fetchSystemStats = async () => {
    try {
      // Get overall system statistics
      const { data: stats, error: statsError } = await supabase.from('feed_stats').select('*');

      if (statsError) throw statsError;

      // Get recent job statistics (last 24 hours)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const { data: recentStats, error: recentError } = await supabase
        .from('feed_jobs')
        .select('status, created_at, items_created, items_updated, items_failed')
        .gte('created_at', yesterday.toISOString());

      if (recentError) throw recentError;

      // Calculate system health metrics
      const totalFeeds = stats ? stats.length : 0;
      const activeFeeds = stats ? stats.filter(s => s.is_active).length : 0;
      const recentJobsCount = recentStats ? recentStats.length : 0;
      const successfulRecentJobs = recentStats
        ? recentStats.filter(j => j.status === 'completed').length
        : 0;
      const failedRecentJobs = recentStats
        ? recentStats.filter(j => j.status === 'failed').length
        : 0;

      // Check for feeds that haven't been processed recently (over 8 hours)
      const eightHoursAgo = new Date();
      eightHoursAgo.setHours(eightHoursAgo.getHours() - 8);

      const staleFeeds = stats
        ? stats.filter(
            s => s.is_active && (!s.last_job_date || new Date(s.last_job_date) < eightHoursAgo)
          ).length
        : 0;

      setSystemStats({
        totalFeeds,
        activeFeeds,
        recentJobsCount,
        successfulRecentJobs,
        failedRecentJobs,
        staleFeeds,
        successRate:
          recentJobsCount > 0 ? ((successfulRecentJobs / recentJobsCount) * 100).toFixed(1) : 100,
        totalItemsCreated: recentStats
          ? recentStats.reduce((sum, job) => sum + (job.items_created || 0), 0)
          : 0,
        totalItemsUpdated: recentStats
          ? recentStats.reduce((sum, job) => sum + (job.items_updated || 0), 0)
          : 0,
        totalItemsFailed: recentStats
          ? recentStats.reduce((sum, job) => sum + (job.items_failed || 0), 0)
          : 0
      });
    } catch (err) {
      console.error('Error fetching system stats:', err);
    }
  };

  const fetchRecentJobs = async () => {
    try {
      const { data: jobs, error } = await supabase
        .from('feed_jobs')
        .select(
          `
          *,
          feeds (name, url)
        `
        )
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      setRecentJobs(jobs || []);
    } catch (err) {
      console.error('Error fetching recent jobs:', err);
    }
  };

  const viewJobDetails = async job => {
    setSelectedJobDetails(job);
    setIsJobModalOpen(true);
  };

  const handleInputChange = e => {
    const { name, value, type, checked } = e.target;
    const inputValue = type === 'checkbox' ? checked : value;

    setFormData({
      ...formData,
      [name]: inputValue
    });

    // Clear validation error when field is edited
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = t('feed_name_required', 'Feed name is required');
    }

    if (!formData.url.trim()) {
      errors.url = t('feed_url_required', 'Feed URL is required');
    } else if (!/^https?:\/\/.+\.xml$/i.test(formData.url)) {
      errors.url = t('feed_url_invalid', 'URL must be valid and end with .xml');
    }

    if (!formData.language) {
      errors.language = t('language_required', 'Language is required');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async e => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const feedData = { ...formData };

      if (selectedFeed) {
        // Update existing feed
        const { error } = await supabase.from('feeds').update(feedData).eq('id', selectedFeed.id);

        if (error) throw error;
      } else {
        // Create new feed
        const { error } = await supabase.from('feeds').insert([feedData]);

        if (error) throw error;
      }

      // Refresh feeds list and close modal
      fetchFeeds();
      handleCloseModal();
    } catch (err) {
      console.error('Error saving feed:', err);
      setError(err.message);
    }
  };

  const handleProcessFeed = async feedId => {
    try {
      setProcessing(prev => ({ ...prev, [feedId]: true }));

      // Импортируем функцию обработки фидов напрямую
      const { processFeed } = await import('../../utils/feedUtils');

      // Вызываем обработку фида напрямую из React приложения
      const result = await processFeed(feedId);

      console.log('Feed processing completed:', result);

      // Показываем уведомление об успехе
      if (result.stats) {
        const { created, updated, failed } = result.stats;
        showNotification(
          `Фид обработан успешно!\nСоздано: ${created}\nОбновлено: ${updated}\nОшибок: ${failed}`,
          'success'
        );
      }

      // Обновляем список фидов
      fetchFeeds();
    } catch (err) {
      console.error('Error processing feed:', err);
      setError(`Ошибка обработки фида: ${err.message}`);
      showNotification(`Ошибка обработки фида: ${err.message}`, 'error');
    } finally {
      setProcessing(prev => ({ ...prev, [feedId]: false }));
    }
  };

  const handleDeleteFeed = async feedId => {
    if (window.confirm(t('confirm_delete_feed', 'Are you sure you want to delete this feed?'))) {
      try {
        const { error } = await supabase.from('feeds').delete().eq('id', feedId);

        if (error) throw error;

        fetchFeeds();
      } catch (err) {
        console.error('Error deleting feed:', err);
        setError(err.message);
      }
    }
  };

  const handleOpenModal = (feed = null) => {
    if (feed) {
      setSelectedFeed(feed);
      setFormData({
        name: feed.name,
        url: feed.url,
        language: feed.language || 'ru',
        active: feed.active
      });
    } else {
      setSelectedFeed(null);
      setFormData({
        name: '',
        url: '',
        language: 'ru',
        active: true
      });
    }
    setFormErrors({});
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedFeed(null);
    setFormData({
      name: '',
      url: '',
      language: 'ru',
      active: true
    });
  };

  const formatDate = dateString => {
    if (!dateString) return t('never', 'Never');
    return new Date(dateString).toLocaleString();
  };

  const renderJobStatus = job => {
    if (!job) return null;

    let statusClass = 'text-gray-500';
    let icon = null;

    switch (job.status) {
      case 'completed':
        statusClass = 'text-green-600';
        icon = <FaCheck className="inline mr-1" />;
        break;
      case 'failed':
        statusClass = 'text-red-600';
        icon = <FaTimes className="inline mr-1" />;
        break;
      case 'processing':
        statusClass = 'text-blue-600';
        icon = <FaSpinner className="inline mr-1 animate-spin" />;
        break;
      default:
        statusClass = 'text-gray-600';
        break;
    }

    return (
      <span className={`font-medium ${statusClass}`}>
        {icon} {job.status}
      </span>
    );
  };

  const renderTabNavigation = () => (
    <div className="border-b border-gray-200 mb-6">
      <nav className="-mb-px flex space-x-8">
        {[
          { key: 'feeds', label: t('feeds', 'Feeds'), icon: FaSync },
          { key: 'monitoring', label: t('monitoring', 'Monitoring'), icon: FaChartLine },
          { key: 'history', label: t('history', 'History'), icon: FaHistory }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === tab.key
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <tab.icon
              className={`mr-2 h-5 w-5 ${
                activeTab === tab.key ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
              }`}
            />
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  );

  const renderSystemStats = () => {
    if (!systemStats) return null;

    const getHealthStatus = () => {
      if (systemStats.failedRecentJobs > 0 || systemStats.staleFeeds > 0) {
        return {
          status: 'warning',
          color: 'bg-yellow-100 text-yellow-800',
          icon: FaExclamationTriangle
        };
      }
      if (systemStats.successRate < 95) {
        return { status: 'error', color: 'bg-red-100 text-red-800', icon: FaTimes };
      }
      return { status: 'healthy', color: 'bg-green-100 text-green-800', icon: FaCheck };
    };

    const health = getHealthStatus();

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <health.icon
                  className={`h-6 w-6 ${health.color.includes('green') ? 'text-green-600' : health.color.includes('yellow') ? 'text-yellow-600' : 'text-red-600'}`}
                />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">System Health</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${health.color}`}
                    >
                      {health.status === 'healthy'
                        ? 'Healthy'
                        : health.status === 'warning'
                          ? 'Warning'
                          : 'Error'}
                    </span>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FaSync className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Feeds</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {systemStats.activeFeeds} / {systemStats.totalFeeds}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FaChartLine className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Success Rate (24h)</dt>
                  <dd className="text-lg font-medium text-gray-900">{systemStats.successRate}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FaClock className="h-6 w-6 text-gray-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Recent Jobs (24h)</dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {systemStats.recentJobsCount}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {systemStats.staleFeeds > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 md:col-span-2">
            <div className="flex">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Stale Feeds Detected</h3>
                <p className="mt-1 text-sm text-yellow-700">
                  {systemStats.staleFeeds} feed(s) haven't been processed in over 8 hours. Check the
                  automatic processing system.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 md:col-span-2">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-blue-800">Processing Statistics (24h)</h3>
              <div className="mt-2 flex space-x-4 text-sm text-blue-700">
                <span>Created: {systemStats.totalItemsCreated}</span>
                <span>Updated: {systemStats.totalItemsUpdated}</span>
                <span>Failed: {systemStats.totalItemsFailed}</span>
              </div>
            </div>
            <FaInfoCircle className="h-8 w-8 text-blue-600" />
          </div>
        </div>
      </div>
    );
  };

  const renderJobHistory = () => (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Processing Jobs</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Feed
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Started
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Results
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentJobs.map(job => (
                <tr key={job.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {job.feeds?.name || 'Unknown Feed'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">{renderJobStatus(job)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(job.started_at || job.created_at)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {job.started_at && job.finished_at
                      ? `${Math.round((new Date(job.finished_at) - new Date(job.started_at)) / 1000)}s`
                      : job.status === 'processing'
                        ? 'Running...'
                        : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {job.status === 'completed' ? (
                      <div className="flex space-x-2">
                        <span className="text-green-600">+{job.items_created || 0}</span>
                        <span className="text-blue-600">↺{job.items_updated || 0}</span>
                        <span className="text-red-600">✗{job.items_failed || 0}</span>
                      </div>
                    ) : job.status === 'failed' ? (
                      <span className="text-red-600">Failed</span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button
                      onClick={() => viewJobDetails(job)}
                      className="text-blue-600 hover:text-blue-800"
                      title="View Details"
                    >
                      <FaEye />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {recentJobs.length === 0 && (
            <div className="text-center py-8 text-gray-500">No recent jobs found</div>
          )}
        </div>
      </div>
    </div>
  );

  const renderJobDetailsModal = () => {
    if (!selectedJobDetails) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-full overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Job Details</h2>
            <button
              onClick={() => setIsJobModalOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <FaTimes size={20} />
            </button>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Feed</label>
                <p className="mt-1 text-sm text-gray-900">
                  {selectedJobDetails.feeds?.name || 'Unknown'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <div className="mt-1">{renderJobStatus(selectedJobDetails)}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Created</label>
                <p className="mt-1 text-sm text-gray-900">
                  {formatDate(selectedJobDetails.created_at)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Started</label>
                <p className="mt-1 text-sm text-gray-900">
                  {formatDate(selectedJobDetails.started_at)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Finished</label>
                <p className="mt-1 text-sm text-gray-900">
                  {formatDate(selectedJobDetails.finished_at)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Duration</label>
                <p className="mt-1 text-sm text-gray-900">
                  {selectedJobDetails.started_at && selectedJobDetails.finished_at
                    ? `${Math.round((new Date(selectedJobDetails.finished_at) - new Date(selectedJobDetails.started_at)) / 1000)} seconds`
                    : 'N/A'}
                </p>
              </div>
            </div>

            {selectedJobDetails.status === 'completed' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-green-800 mb-2">Processing Results</h3>
                <div className="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-green-700">Processed:</span>
                    <span className="ml-1 text-green-900">
                      {selectedJobDetails.items_processed || 0}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-green-700">Created:</span>
                    <span className="ml-1 text-green-900">
                      {selectedJobDetails.items_created || 0}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-green-700">Updated:</span>
                    <span className="ml-1 text-green-900">
                      {selectedJobDetails.items_updated || 0}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-red-700">Failed:</span>
                    <span className="ml-1 text-red-900">
                      {selectedJobDetails.items_failed || 0}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {selectedJobDetails.error_message && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-red-800 mb-2">Error Details</h3>
                <pre className="text-sm text-red-700 whitespace-pre-wrap">
                  {selectedJobDetails.error_message}
                </pre>
              </div>
            )}

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-800 mb-2">Feed URL</h3>
              <a
                href={selectedJobDetails.feeds?.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:underline break-all"
              >
                {selectedJobDetails.feeds?.url}
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Компонент уведомлений */}
      {notification && (
        <div
          className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 max-w-md ${
            notification.type === 'success'
              ? 'bg-green-500 text-white'
              : notification.type === 'error'
                ? 'bg-red-500 text-white'
                : notification.type === 'warning'
                  ? 'bg-yellow-500 text-white'
                  : 'bg-blue-500 text-white'
          }`}
        >
          <div className="flex items-start justify-between">
            <span className="whitespace-pre-line">{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-4 text-white hover:text-gray-200 flex-shrink-0"
            >
              ×
            </button>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('feed_management', 'Feed Management')}</h1>
        {activeTab === 'feeds' && (
          <button
            onClick={() => handleOpenModal()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
          >
            <FaPlus className="mr-2" />
            {t('add_new_feed', 'Add New Feed')}
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
          <button className="float-right font-bold" onClick={() => setError(null)}>
            &times;
          </button>
        </div>
      )}

      {/* Tab Navigation */}
      {renderTabNavigation()}

      {/* Tab Content */}
      {activeTab === 'feeds' && (
        <>
          {loading ? (
            <div className="text-center py-8">
              <FaSpinner className="text-4xl animate-spin inline-block text-blue-600" />
              <p className="mt-2 text-gray-600">{t('loading', 'Loading...')}</p>
            </div>
          ) : feeds.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-600 mb-4">{t('no_feeds', 'No feeds have been added yet.')}</p>
              <button
                onClick={() => handleOpenModal()}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                {t('add_your_first_feed', 'Add your first feed')}
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="py-2 px-4 text-left">{t('name', 'Name')}</th>
                    <th className="py-2 px-4 text-left">{t('url', 'URL')}</th>
                    <th className="py-2 px-4 text-left">{t('language', 'Language')}</th>
                    <th className="py-2 px-4 text-left">{t('status', 'Status')}</th>
                    <th className="py-2 px-4 text-left">{t('last_processed', 'Last Processed')}</th>
                    <th className="py-2 px-4 text-left">{t('last_job', 'Last Job')}</th>
                    <th className="py-2 px-4 text-left">{t('actions', 'Actions')}</th>
                  </tr>
                </thead>
                <tbody>
                  {feeds.map(feed => (
                    <tr key={feed.id} className="border-t border-gray-200 hover:bg-gray-50">
                      <td className="py-3 px-4">{feed.name}</td>
                      <td className="py-3 px-4">
                        <a
                          href={feed.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline truncate block max-w-xs"
                        >
                          {feed.url}
                        </a>
                      </td>
                      <td className="py-3 px-4">
                        {feed.language === 'uk' ? 'Ukrainian' : 'Russian'}
                      </td>
                      <td className="py-3 px-4">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            feed.active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {feed.active ? t('active', 'Active') : t('inactive', 'Inactive')}
                        </span>
                      </td>
                      <td className="py-3 px-4">{formatDate(feed.last_fetched)}</td>
                      <td className="py-3 px-4">
                        {feed.lastJob ? (
                          <div>
                            {renderJobStatus(feed.lastJob)}
                            {feed.lastJob.status === 'completed' && (
                              <div className="text-xs text-gray-600 mt-1">
                                +{feed.lastJob.items_created} / ↺{feed.lastJob.items_updated} / ✗
                                {feed.lastJob.items_failed}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-500">{t('none', 'None')}</span>
                        )}
                      </td>
                      <td className="py-3 px-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleProcessFeed(feed.id)}
                            disabled={processing[feed.id]}
                            className="text-blue-600 hover:text-blue-800 p-1"
                            title={t('process_feed', 'Process Feed')}
                          >
                            {processing[feed.id] ? (
                              <FaSpinner className="animate-spin" />
                            ) : (
                              <FaSync />
                            )}
                          </button>
                          <button
                            onClick={() => handleOpenModal(feed)}
                            className="text-yellow-600 hover:text-yellow-800 p-1"
                            title={t('edit_feed', 'Edit Feed')}
                          >
                            <FaEdit />
                          </button>
                          <button
                            onClick={() => handleDeleteFeed(feed.id)}
                            className="text-red-600 hover:text-red-800 p-1"
                            title={t('delete_feed', 'Delete Feed')}
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}

      {activeTab === 'monitoring' && (
        <div>
          {systemStats && renderSystemStats()}
          {loading ? (
            <div className="text-center py-8">
              <FaSpinner className="text-4xl animate-spin inline-block text-blue-600" />
              <p className="mt-2 text-gray-600">Loading monitoring data...</p>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Automatic Processing Status
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <FaInfoCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-blue-800">
                        GitHub Actions Automation
                      </h4>
                      <p className="mt-1 text-sm text-blue-700">
                        Feeds are automatically processed every 6 hours (00:00, 06:00, 12:00, 18:00
                        UTC) via GitHub Actions. Check the{' '}
                        <a
                          href="https://github.com/your-repo/actions"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline font-medium"
                        >
                          Actions tab
                        </a>{' '}
                        in GitHub for detailed workflow logs.
                      </p>
                      <div className="mt-2 text-xs text-blue-600">
                        Next scheduled run:{' '}
                        {(() => {
                          const now = new Date();
                          const hours = [0, 6, 12, 18];
                          const currentHour = now.getUTCHours();
                          const nextHour = hours.find(h => h > currentHour) || hours[0];
                          const nextRun = new Date(now);
                          nextRun.setUTCHours(nextHour, 0, 0, 0);
                          if (nextHour <= currentHour) nextRun.setUTCDate(nextRun.getUTCDate() + 1);
                          return nextRun.toLocaleString();
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {recentJobs.length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Recent Activity (Last 10 Jobs)
                    </h3>
                    <div className="space-y-3">
                      {recentJobs.slice(0, 10).map(job => (
                        <div
                          key={job.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            {renderJobStatus(job)}
                            <span className="text-sm text-gray-600">
                              {job.feeds?.name || 'Unknown Feed'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-3 text-sm text-gray-500">
                            <span>{formatDate(job.created_at)}</span>
                            <button
                              onClick={() => viewJobDetails(job)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <FaEye />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {activeTab === 'history' && (
        <div>
          {loading ? (
            <div className="text-center py-8">
              <FaSpinner className="text-4xl animate-spin inline-block text-blue-600" />
              <p className="mt-2 text-gray-600">Loading job history...</p>
            </div>
          ) : (
            renderJobHistory()
          )}
        </div>
      )}

      {/* Modal for adding/editing feeds */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">
              {selectedFeed ? t('edit_feed', 'Edit Feed') : t('add_feed', 'Add New Feed')}
            </h2>

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="name" className="block text-gray-700 font-medium mb-1">
                  {t('feed_name', 'Feed Name')}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md ${formErrors.name ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder={t('feed_name_placeholder', 'Enter feed name')}
                />
                {formErrors.name && <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>}
              </div>

              <div className="mb-4">
                <label htmlFor="url" className="block text-gray-700 font-medium mb-1">
                  {t('feed_url', 'Feed URL')}
                </label>
                <input
                  type="text"
                  id="url"
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md ${formErrors.url ? 'border-red-500' : 'border-gray-300'}`}
                  placeholder={t('feed_url_placeholder', 'https://example.com/feed.xml')}
                />
                {formErrors.url && <p className="text-red-500 text-sm mt-1">{formErrors.url}</p>}
              </div>

              <div className="mb-4">
                <label htmlFor="language" className="block text-gray-700 font-medium mb-1">
                  {t('language', 'Language')}
                </label>
                <select
                  id="language"
                  name="language"
                  value={formData.language}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md ${formErrors.language ? 'border-red-500' : 'border-gray-300'}`}
                >
                  <option value="ru">Russian</option>
                  <option value="uk">Ukrainian</option>
                </select>
                {formErrors.language && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.language}</p>
                )}
              </div>

              <div className="mb-4 flex items-center">
                <input
                  type="checkbox"
                  id="active"
                  name="active"
                  checked={formData.active}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <label htmlFor="active" className="text-gray-700">
                  {t('feed_active', 'Active')}
                </label>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
                >
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {selectedFeed ? t('update', 'Update') : t('save', 'Save')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Job Details Modal */}
      {isJobModalOpen && renderJobDetailsModal()}
    </div>
  );
};

export default FeedManagement;
