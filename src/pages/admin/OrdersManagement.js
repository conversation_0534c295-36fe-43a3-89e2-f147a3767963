/* eslint-disable prettier/prettier */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { useSearchParams } from 'react-router-dom';
import {
  FaSearch,
  FaFilter,
  FaFileExport,
  FaEnvelope,
  FaArrowDown,
  FaArrowUp,
  FaInbox,
  FaShippingFast,
  FaCheckCircle
} from 'react-icons/fa';
// AdminMenuSidebar is already included in the parent layout component
// import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar';
// Add this import for toast ID tracking
import { v4 as uuidv4 } from 'uuid';

const OrdersManagement = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const initialStatus = searchParams.get('status') || '';

  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [statusFilter, setStatusFilter] = useState(initialStatus);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [totalOrders, setTotalOrders] = useState(0);
  const [page, setPage] = useState(1);
  const [tableExists, setTableExists] = useState(true);
  const [selectAll, setSelectAll] = useState(false);
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState('desc');
  const [batchProcessing, setBatchProcessing] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [paymentFilter, setPaymentFilter] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const itemsPerPage = 10;

  // Категории заказов
  const orderCategories = {
    all: { label: t('all_orders', 'Все заказы'), icon: <FaInbox className="mr-2" />, filter: '' },
    new: {
      label: t('new_orders', 'Новые заказы'),
      icon: <FaInbox className="mr-2 text-yellow-500" />,
      filter: 'pending'
    },
    inProgress: {
      label: t('in_progress_orders', 'В работе'),
      icon: <FaShippingFast className="mr-2 text-blue-500" />,
      filter: ['processing', 'packed', 'shipped']
    },
    completed: {
      label: t('completed_orders', 'Выполненные'),
      icon: <FaCheckCircle className="mr-2 text-green-500" />,
      filter: 'delivered'
    }
  };

  // Функция для определения счетчиков заказов для каждой категории
  const [orderCounts, setOrderCounts] = useState({
    all: 0,
    new: 0,
    inProgress: 0,
    completed: 0
  });

  useEffect(() => {
    const fetchOrderCounts = async () => {
      if (!tableExists) return;

      try {
        // Подсчитываем заказы для каждой категории
        const { data: all, error: errorAll } = await supabase
          .from('orders')
          .select('id', { count: 'exact' });

        const { data: newOrders, error: errorNew } = await supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .eq('status', 'pending');

        const { data: inProgress, error: errorInProgress } = await supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .in('status', ['processing', 'packed', 'shipped']);

        const { data: completed, error: errorCompleted } = await supabase
          .from('orders')
          .select('id', { count: 'exact' })
          .eq('status', 'delivered');

        if (!errorAll && !errorNew && !errorInProgress && !errorCompleted) {
          setOrderCounts({
            all: all ? all.length : 0,
            new: newOrders ? newOrders.length : 0,
            inProgress: inProgress ? inProgress.length : 0,
            completed: completed ? completed.length : 0
          });
        }
      } catch (error) {
        console.error('Error fetching order counts:', error);
      }
    };

    fetchOrderCounts();
  }, [tableExists, orders]);

  useEffect(() => {
    // Устанавливаем фильтр статуса в зависимости от активной вкладки
    const tabFilter = orderCategories[activeTab]?.filter || '';
    if (Array.isArray(tabFilter)) {
      // Если массив статусов - это уже будет учтено в fetchOrders
      setStatusFilter(''); // Очищаем для предотвращения конфликта
    } else {
      setStatusFilter(tabFilter);
    }
    setPage(1);
  }, [activeTab]);

  // Check if orders table exists first
  useEffect(() => {
    const checkOrdersTable = async () => {
      try {
        const { error } = await supabase.from('orders').select('id').limit(1);

        if (error && error.code === '42P01') {
          // Table doesn't exist code
          setTableExists(false);
          setLoading(false);
          return false;
        }

        return true;
      } catch (err) {
        console.error('Error checking orders table:', err);
        setTableExists(false);
        setLoading(false);
        return false;
      }
    };

    checkOrdersTable();
  }, []);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        if (!tableExists) return;

        setLoading(true);

        // Create query
        let query = supabase.from('orders').select('*', { count: 'exact' });

        // Apply filters based on active tab and other filters
        if (activeTab === 'inProgress') {
          query = query.in('status', orderCategories.inProgress.filter);
        } else if (statusFilter) {
          query = query.eq('status', statusFilter);
        }

        if (paymentFilter) {
          query = query.eq('payment_status', paymentFilter);
        }

        if (searchQuery) {
          query = query.or(
            `customer_name.ilike.%${searchQuery}%,customer_email.ilike.%${searchQuery}%,customer_phone.ilike.%${searchQuery}%,id.ilike.${searchQuery}%`
          );
        }

        if (dateFilter) {
          const today = new Date();
          let startDate;

          switch (dateFilter) {
            case 'today':
              startDate = new Date();
              startDate.setHours(0, 0, 0, 0);
              break;
            case 'week':
              startDate = new Date();
              startDate.setDate(today.getDate() - 7);
              break;
            case 'month':
              startDate = new Date();
              startDate.setMonth(today.getMonth() - 1);
              break;
            default:
              startDate = null;
          }

          if (startDate) {
            query = query.gte('created_at', startDate.toISOString());
          }
        }

        // Apply sorting
        query = query.order(sortField, { ascending: sortDirection === 'asc' });

        // Apply pagination
        const from = (page - 1) * itemsPerPage;
        const to = from + itemsPerPage - 1;
        query = query.range(from, to);

        const { data, count, error } = await query;

        if (error) throw error;

        // Теперь загрузим order_items отдельным запросом, если у нас есть заказы
        if (data && data.length > 0) {
          const orderIds = data.map(order => order.id);
          const { data: itemsData, error: itemsError } = await supabase
            .from('order_items')
            .select('*')
            .in('order_id', orderIds);

          if (itemsError) throw itemsError;

          // Добавляем order_items к соответствующим заказам
          const ordersWithItems = data.map(order => {
            const items = itemsData.filter(item => item.order_id === order.id);
            return { ...order, order_items: items };
          });

          setOrders(ordersWithItems || []);
        } else {
          setOrders(data || []);
        }

        setTotalOrders(count || 0);

        // Reset selected orders when fetching new data
        setSelectedOrders([]);
        setSelectAll(false);
      } catch (error) {
        console.error('Error fetching orders:', error);
        toast.error(t('error_fetching_orders', 'Ошибка при загрузке заказов'));
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [
    statusFilter,
    dateFilter,
    paymentFilter,
    searchQuery,
    page,
    sortField,
    sortDirection,
    t,
    activeTab,
    tableExists
  ]);

  const handleStatusChange = e => {
    setStatusFilter(e.target.value);
    setPage(1); // Reset to first page when changing filter
  };

  const handlePaymentStatusChange = e => {
    setPaymentFilter(e.target.value);
    setPage(1); // Reset to first page when changing filter
  };

  const handleDateFilterChange = e => {
    setDateFilter(e.target.value);
    setPage(1); // Reset to first page when changing filter
  };

  const handleSearch = e => {
    setSearchQuery(e.target.value);
    setPage(1);
  };

  // Handle toggling sort direction
  const handleSort = field => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setPage(1);
  };

  // Get formatted total for an order
  const getOrderTotal = order => {
    if (!order.order_items || !Array.isArray(order.order_items)) {
      return '0.00';
    }

    const total = order.order_items.reduce((sum, item) => {
      return sum + item.price * item.quantity;
    }, 0);

    return total.toFixed(2);
  };

  // Get status badge color
  const getStatusBadgeClass = status => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-l-4 border-yellow-500';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-l-4 border-blue-500';
      case 'packed':
        return 'bg-indigo-100 text-indigo-800 border-l-4 border-indigo-500';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-l-4 border-purple-500';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-l-4 border-green-500';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-l-4 border-red-500';
      case 'refunded':
        return 'bg-pink-100 text-pink-800 border-l-4 border-pink-500';
      default:
        return 'bg-gray-100 text-gray-800 border-l-4 border-gray-500';
    }
  };

  // Get payment status badge color
  const getPaymentStatusBadgeClass = status => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-l-4 border-green-500';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-l-4 border-yellow-500';
      case 'failed':
        return 'bg-red-100 text-red-800 border-l-4 border-red-500';
      case 'refunded':
        return 'bg-pink-100 text-pink-800 border-l-4 border-pink-500';
      default:
        return 'bg-gray-100 text-gray-800 border-l-4 border-gray-500';
    }
  };

  // Handle bulk status change
  const handleBulkStatusChange = async newStatus => {
    if (selectedOrders.length === 0) {
      toast.warning(t('no_orders_selected', 'Не выбрано ни одного заказа'));
      return;
    }

    // Create a unique toast ID
    const toastId = uuidv4();

    try {
      setBatchProcessing(true);

      // Show loading toast
      toast.loading(t('updating_statuses', 'Обновление статусов...'), {
        toastId,
        autoClose: false
      });

      // Update all selected orders
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus, updated_at: new Date() })
        .in('id', selectedOrders);

      if (error) throw error;

      // Add status history entries in bulk
      try {
        // First check if status history table exists
        await supabase.rpc('exec_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS order_status_history (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              order_id UUID REFERENCES orders(id),
              status TEXT NOT NULL,
              note TEXT,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              created_by TEXT
            );
          `
        });

        // Prepare history entries
        const historyEntries = selectedOrders.map(orderId => ({
          order_id: orderId,
          status: newStatus,
          note: t('bulk_update', 'Массовое обновление')
        }));

        // Insert into history
        await supabase.from('order_status_history').insert(historyEntries);
      } catch (historyError) {
        console.error('Error recording bulk status history:', historyError);
        // Continue even if history recording fails
      }

      // Update orders in state
      setOrders(prev =>
        prev.map(order =>
          selectedOrders.includes(order.id) ? { ...order, status: newStatus } : order
        )
      );

      // Update toast to success
      toast.update(toastId, {
        render: t('bulk_status_updated', 'Статусы заказов обновлены'),
        type: 'success',
        autoClose: 3000,
        isLoading: false
      });

      setSelectedOrders([]);
      setSelectAll(false);
    } catch (error) {
      console.error('Error updating order statuses in bulk:', error);

      // Update toast to error
      toast.update(toastId, {
        render: t('error_bulk_updating', 'Ошибка при массовом обновлении заказов'),
        type: 'error',
        autoClose: 3000,
        isLoading: false
      });
    } finally {
      setBatchProcessing(false);
    }
  };

  // Handle batch export
  const handleExportOrders = () => {
    if (selectedOrders.length === 0 && orders.length === 0) {
      toast.warning(t('no_orders_to_export', 'Нет заказов для экспорта'));
      return;
    }

    try {
      const ordersToExport =
        selectedOrders.length > 0
          ? orders.filter(order => selectedOrders.includes(order.id))
          : orders;

      const exportData = ordersToExport.map(order => ({
        id: order.id,
        customer_name: order.customer_name,
        customer_email: order.customer_email,
        customer_phone: order.customer_phone,
        total_amount: order.total_amount || getOrderTotal(order),
        status: order.status,
        payment_status: order.payment_status,
        created_at: new Date(order.created_at).toLocaleString(),
        items_count: order.order_items?.length || 0
      }));

      // Create CSV
      const headers = Object.keys(exportData[0]).join(',');
      const rows = exportData
        .map(item =>
          Object.values(item).map(value =>
            typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
          )
        )
        .join(',');

      const csv = [headers, ...rows].join('\n');

      // Create a blob and download
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `orders_export_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(t('orders_exported', 'Заказы успешно экспортированы'));
    } catch (error) {
      console.error('Error exporting orders:', error);
      toast.error(t('export_error', 'Ошибка при экспорте заказов'));
    }
  };

  // Handle bulk email notification
  const handleBulkEmailNotification = async () => {
    if (selectedOrders.length === 0) {
      toast.warning(t('no_orders_selected', 'Не выбрано ни одного заказа'));
      return;
    }

    try {
      setBatchProcessing(true);

      // In a real application, this would send emails to customers
      // Simulating delay for demonstration purposes
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success(t('notifications_sent', 'Уведомления успешно отправлены'));
    } catch (error) {
      console.error('Error sending notifications:', error);
      toast.error(t('notification_error', 'Ошибка при отправке уведомлений'));
    } finally {
      setBatchProcessing(false);
    }
  };

  // Handle checking/unchecking all orders
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(orders.map(order => order.id));
    }
    setSelectAll(!selectAll);
  };

  // Handle selecting/deselecting individual order
  const handleSelectOrder = orderId => {
    if (selectedOrders.includes(orderId)) {
      setSelectedOrders(prev => prev.filter(id => id !== orderId));
      setSelectAll(false);
    } else {
      setSelectedOrders(prev => [...prev, orderId]);
      // If all orders are now selected, update selectAll state
      if (orders.length === selectedOrders.length + 1) {
        setSelectAll(true);
      }
    }
  };

  // Create database tables
  const createOrderTables = async () => {
    try {
      setLoading(true);

      // Create orders table
      await supabase.rpc('exec_sql', {
        query: `
          CREATE TABLE IF NOT EXISTS orders (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            customer_name TEXT NOT NULL,
            customer_email TEXT,
            customer_phone TEXT NOT NULL,
            shipping_address JSONB,
            total_amount DECIMAL(10, 2) NOT NULL,
            status TEXT DEFAULT 'pending',
            payment_method TEXT,
            payment_status TEXT DEFAULT 'pending',
            notes TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
          );

          CREATE TABLE IF NOT EXISTS order_items (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            order_id UUID REFERENCES orders(id),
            product_id UUID REFERENCES products(id),
            quantity INTEGER NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            product_name TEXT NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
          );
          
          CREATE TABLE IF NOT EXISTS order_status_history (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            order_id UUID REFERENCES orders(id),
            status TEXT NOT NULL,
            note TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            created_by TEXT
          );
        `
      });

      setTableExists(true);
      toast.success(t('orders_tables_created', 'Таблицы заказов созданы'));
    } catch (error) {
      console.error('Error creating orders table:', error);
      toast.error(t('error_creating_tables', 'Ошибка при создании таблиц'));
    } finally {
      setLoading(false);
    }
  };

  // Initialize orders system if needed
  const renderInitializeButton = () => (
    <div className="text-center py-12">
      <div className="mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
          />
        </svg>
        <h3 className="mt-2 text-lg font-medium text-gray-900">
          {t('orders_module_not_initialized', 'Модуль заказов не инициализирован')}
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          {t('initialize_orders_desc', 'Создайте необходимые таблицы для учета заказов')}
        </p>
      </div>

      <button
        onClick={createOrderTables}
        disabled={loading}
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
      >
        {loading
          ? t('initializing', 'Инициализация...')
          : t('initialize_orders', 'Инициализировать модуль заказов')}
      </button>
    </div>
  );

  // Render sort indicator
  const renderSortIndicator = field => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? (
      <FaArrowUp className="inline-block ml-1 text-xs" />
    ) : (
      <FaArrowDown className="inline-block ml-1 text-xs" />
    );
  };

  const handlePageChange = newPage => {
    setPage(newPage);
  };

  // Render orders table with visual status grouping
  const renderOrdersTable = () => {
    // Группировка заказов по статусу для визуального разделения
    let lastOrderStatus = null;

    return (
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 w-5">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('id')}
                >
                  {t('order_id', 'Номер заказа')}
                  {renderSortIndicator('id')}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('customer_name')}
                >
                  {t('customer', 'Клиент')}
                  {renderSortIndicator('customer_name')}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('created_at')}
                >
                  {t('date', 'Дата')}
                  {renderSortIndicator('created_at')}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('total_amount')}
                >
                  {t('total', 'Сумма')}
                  {renderSortIndicator('total_amount')}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('status')}
                >
                  {t('status', 'Статус')}
                  {renderSortIndicator('status')}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort('payment_status')}
                >
                  {t('payment', 'Оплата')}
                  {renderSortIndicator('payment_status')}
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {t('actions', 'Действия')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders.map((order, index) => {
                // Добавляем разделительный заголовок при смене статуса, если это не первый заказ
                const statusChanged = lastOrderStatus !== order.status;
                lastOrderStatus = order.status;

                // Определяем индикаторы для разных статусов
                const statusIndicators = {
                  pending: { color: 'yellow', icon: 'inbox', label: t('pending', 'Новый') },
                  processing: {
                    color: 'blue',
                    icon: 'cog',
                    label: t('processing', 'Обрабатывается')
                  },
                  packed: { color: 'indigo', icon: 'box', label: t('packed', 'Упакован') },
                  shipped: { color: 'purple', icon: 'truck', label: t('shipped', 'Отправлен') },
                  delivered: {
                    color: 'green',
                    icon: 'check-circle',
                    label: t('delivered', 'Доставлен')
                  },
                  cancelled: {
                    color: 'red',
                    icon: 'times-circle',
                    label: t('cancelled', 'Отменен')
                  }
                };

                const statusInfo = statusIndicators[order.status] || {
                  color: 'gray',
                  icon: 'question',
                  label: order.status
                };

                return (
                  <React.Fragment key={order.id}>
                    {statusChanged && activeTab === 'all' && index > 0 && (
                      <tr className={`bg-${statusInfo.color}-50 group-divider`}>
                        <td colSpan="8" className="px-6 py-3">
                          <div className="flex items-center">
                            <div
                              className={`w-3 h-3 rounded-full bg-${statusInfo.color}-500 mr-2`}
                            ></div>
                            <span className="font-medium text-gray-700">{statusInfo.label}</span>
                          </div>
                        </td>
                      </tr>
                    )}
                    <tr
                      className={`hover:bg-gray-50 ${
                        order.status === 'pending'
                          ? 'bg-yellow-50'
                          : order.status === 'delivered'
                            ? 'bg-green-50'
                            : order.status === 'cancelled'
                              ? 'bg-red-50'
                              : ''
                      }`}
                    >
                      <td className="px-3 py-4">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.id)}
                          onChange={() => handleSelectOrder(order.id)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          #{order.id.substring(0, 8)}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{order.customer_name}</div>
                        <div className="text-sm text-gray-500">
                          {order.customer_email || order.customer_phone}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(order.created_at).toLocaleDateString()}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(order.created_at).toLocaleTimeString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {order.total_amount || getOrderTotal(order)} грн
                        </div>
                        <div className="text-xs text-gray-500">
                          {order.order_items?.length || 0} {t('items', 'товаров')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-3 py-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(order.status)}`}
                        >
                          {t(order.status, order.status)}
                        </span>
                        <div className="text-xs text-gray-500 mt-1">
                          {order.updated_at
                            ? new Date(order.updated_at).toLocaleDateString()
                            : new Date(order.created_at).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-3 py-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getPaymentStatusBadgeClass(order.payment_status)}`}
                        >
                          {t(
                            order.payment_status || 'pending',
                            order.payment_status || 'Ожидает оплаты'
                          )}
                        </span>
                        <div className="text-xs text-gray-500 mt-1">
                          {order.payment_method || t('not_specified', 'Не указан')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex flex-col space-y-2 items-end">
                          <Link
                            to={`/admin/orders/${order.id}`}
                            className="text-primary hover:text-primary-dark"
                          >
                            {t('view_details', 'Подробнее')}
                          </Link>
                        </div>
                      </td>
                    </tr>
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalOrders > itemsPerPage && (
          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between items-center">
              <div className="text-sm text-gray-700">
                {t('showing', 'Показано')}{' '}
                <span className="font-medium">{(page - 1) * itemsPerPage + 1}</span> -{' '}
                <span className="font-medium">{Math.min(page * itemsPerPage, totalOrders)}</span>{' '}
                {t('of', 'из')} <span className="font-medium">{totalOrders}</span>{' '}
                {t('results', 'результатов')}
              </div>
              <div>
                <button
                  onClick={() => handlePageChange(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md mr-2 ${
                    page === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('previous', 'Предыдущая')}
                </button>
                <button
                  onClick={() => handlePageChange(page + 1)}
                  disabled={page * itemsPerPage >= totalOrders}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    page * itemsPerPage >= totalOrders
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('next', 'Следующая')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>{t('orders_management', 'Управление заказами')} | Admin</title>
      </Helmet>

      <div className="min-h-screen bg-gray-100">
        <div className="py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-6">
              {t('orders_management', 'Управление заказами')}
            </h1>

            {!tableExists ? (
              renderInitializeButton()
            ) : (
              <>
                {/* Табы для разных категорий заказов */}
                <div className="flex flex-col mb-6 items-start">
                  <div className="flex overflow-x-auto gap-2 bg-white p-1 rounded-lg shadow">
                    {Object.entries(orderCategories).map(([key, category]) => (
                      <button
                        key={key}
                        onClick={() => setActiveTab(key)}
                        className={`flex items-center px-4 py-3 whitespace-nowrap rounded-md font-medium transition-all ${
                          activeTab === key
                            ? 'bg-blue-50 text-blue-700 shadow-sm'
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}
                      >
                        {category.icon}
                        {category.label}
                        <span
                          className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                            activeTab === key
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-600'
                          }`}
                        >
                          {orderCounts[key]}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Строка поиска и фильтры */}
                <div className="bg-white rounded-lg shadow p-4 mb-6">
                  <div className="flex flex-wrap gap-4">
                    <div className="flex-grow min-w-[200px]">
                      <div className="relative rounded-md">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <FaSearch className="text-gray-400" />
                        </div>
                        <input
                          type="text"
                          value={searchQuery}
                          onChange={handleSearch}
                          placeholder={t('search_orders', 'Поиск заказов...')}
                          className="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-gray-300 rounded-md p-2 border"
                        />
                      </div>
                    </div>

                    <button
                      onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                      className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <FaFilter className="mr-2" />
                      {showAdvancedFilters
                        ? t('hide_filters', 'Скрыть фильтры')
                        : t('show_filters', 'Показать фильтры')}
                    </button>

                    <button
                      onClick={handleExportOrders}
                      className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      <FaFileExport className="mr-2" /> {t('export', 'Экспорт')}
                    </button>
                  </div>
                </div>

                {/* Basic filters */}
                <div
                  className={`bg-white rounded-lg shadow p-4 mb-6 ${showAdvancedFilters ? 'block' : 'hidden'}`}
                >
                  <div className="flex flex-wrap gap-4">
                    <div className="flex-1 min-w-[200px]">
                      <label className="block mb-1 text-sm font-medium">
                        {t('status', 'Статус')}:
                      </label>
                      <select
                        value={statusFilter}
                        onChange={handleStatusChange}
                        className="border p-2 w-full rounded text-sm"
                      >
                        <option value="">{t('all_statuses', 'Все статусы')}</option>
                        <option value="pending">{t('pending', 'Ожидает обработки')}</option>
                        <option value="processing">{t('processing', 'В обработке')}</option>
                        <option value="packed">{t('packed', 'Упакован')}</option>
                        <option value="shipped">{t('shipped', 'Отправлен')}</option>
                        <option value="delivered">{t('delivered', 'Доставлен')}</option>
                        <option value="cancelled">{t('cancelled', 'Отменен')}</option>
                        <option value="refunded">{t('refunded', 'Возвращен')}</option>
                      </select>
                    </div>

                    <div className="flex-1 min-w-[200px]">
                      <label className="block mb-1 text-sm font-medium">
                        {t('payment_status', 'Статус оплаты')}:
                      </label>
                      <select
                        value={paymentFilter}
                        onChange={handlePaymentStatusChange}
                        className="border p-2 w-full rounded text-sm"
                      >
                        <option value="">{t('all_payment_statuses', 'Все статусы оплаты')}</option>
                        <option value="pending">{t('payment_pending', 'Ожидает оплаты')}</option>
                        <option value="paid">{t('paid', 'Оплачен')}</option>
                        <option value="failed">{t('payment_failed', 'Ошибка оплаты')}</option>
                        <option value="payment_refunded">
                          {t('payment_refunded', 'Возвращены средства')}
                        </option>
                      </select>
                    </div>

                    <div className="flex-1 min-w-[200px]">
                      <label className="block mb-1 text-sm font-medium">
                        {t('time_period', 'Период')}:
                      </label>
                      <select
                        value={dateFilter}
                        onChange={handleDateFilterChange}
                        className="border p-2 w-full rounded text-sm"
                      >
                        <option value="">{t('all_time', 'Все время')}</option>
                        <option value="today">{t('today', 'Сегодня')}</option>
                        <option value="week">{t('last_7_days', 'Последние 7 дней')}</option>
                        <option value="month">{t('last_30_days', 'Последние 30 дней')}</option>
                      </select>
                    </div>

                    <div className="flex-1 min-w-[200px] flex items-end">
                      <button
                        onClick={() => {
                          setStatusFilter('');
                          setPaymentFilter('');
                          setDateFilter('');
                          setSearchQuery('');
                          setPage(1);
                        }}
                        className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded text-sm w-full"
                      >
                        {t('reset_filters', 'Сбросить фильтры')}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Batch actions bar (visible when orders are selected) */}
                {selectedOrders.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg shadow p-4 mb-6">
                    <div className="flex flex-wrap gap-3 items-center">
                      <div className="text-blue-800 font-medium">
                        {t('selected_orders', 'Выбрано заказов')}: {selectedOrders.length}
                      </div>

                      <div className="flex-1"></div>

                      <div className="flex gap-2 flex-wrap">
                        <div className="relative inline-block text-left">
                          <select
                            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                            onChange={e => {
                              if (e.target.value) {
                                handleBulkStatusChange(e.target.value);
                                e.target.value = ''; // Reset after action
                              }
                            }}
                            disabled={batchProcessing}
                          >
                            <option value="">{t('change_status', 'Изменить статус')}</option>
                            <option value="processing">{t('processing', 'В обработке')}</option>
                            <option value="packed">{t('packed', 'Упакован')}</option>
                            <option value="shipped">{t('shipped', 'Отправлен')}</option>
                            <option value="delivered">{t('delivered', 'Доставлен')}</option>
                            <option value="cancelled">{t('cancelled', 'Отменен')}</option>
                          </select>
                        </div>

                        <button
                          onClick={handleExportOrders}
                          disabled={batchProcessing}
                          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 flex items-center disabled:opacity-50"
                        >
                          <FaFileExport className="mr-2" /> {t('export', 'Экспорт')}
                        </button>

                        <button
                          onClick={handleBulkEmailNotification}
                          disabled={batchProcessing}
                          className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 flex items-center disabled:opacity-50"
                        >
                          {batchProcessing ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                              {t('processing', 'Обработка...')}
                            </>
                          ) : (
                            <>
                              <FaEnvelope className="mr-2" /> {t('notify_customers', 'Уведомить')}
                            </>
                          )}
                        </button>

                        <button
                          onClick={() => setSelectedOrders([])}
                          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                        >
                          {t('clear_selection', 'Очистить')}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Orders list */}
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <>
                    {orders.length > 0 ? (
                      renderOrdersTable()
                    ) : (
                      <div className="bg-white rounded-lg shadow p-6 text-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="mx-auto h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                          />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                          {t('no_orders_found', 'Заказы не найдены')}
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          {statusFilter || dateFilter || searchQuery || paymentFilter
                            ? t(
                                'no_orders_with_filter',
                                'Заказы с указанными параметрами не найдены'
                              )
                            : t('no_orders_yet', 'Заказы еще не созданы')}
                        </p>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default OrdersManagement;
