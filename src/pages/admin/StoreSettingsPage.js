import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import EmailSystemManager from '../../components/admin/EmailSystemManager';

const StoreSettingsPage = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('email');

  const tabs = [
    { id: 'email', label: 'Email Уведомления', icon: '📧' },
    { id: 'general', label: 'Общие настройки', icon: '⚙️' },
    { id: 'payment', label: 'Оплата', icon: '💳' },
    { id: 'shipping', label: 'Доставка', icon: '🚚' }
  ];

  return (
    <>
      <Helmet>
        <title>{t('store_settings', 'Настройки магазина')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold">{t('store_settings', 'Настройки магазина')}</h1>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'email' && <EmailSystemManager />}

          {activeTab === 'general' && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <div className="mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="mx-auto h-16 w-16 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <h2 className="mt-4 text-xl font-medium text-gray-900">
                  {t('feature_in_development', 'Функционал в разработке')}
                </h2>
                <p className="mt-2 text-base text-gray-500">
                  {t('settings_coming_soon', 'Общие настройки будут доступны в ближайшее время.')}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'payment' && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <div className="mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="mx-auto h-16 w-16 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
                <h2 className="mt-4 text-xl font-medium text-gray-900">
                  {t('feature_in_development', 'Функционал в разработке')}
                </h2>
                <p className="mt-2 text-base text-gray-500">
                  {t(
                    'payment_settings_coming_soon',
                    'Настройки оплаты будут доступны в ближайшее время.'
                  )}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'shipping' && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <div className="mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="mx-auto h-16 w-16 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M8 17l4 4 4-4m-4-5v9m-4-13a4 4 0 118 0v3H8V4z"
                  />
                </svg>
                <h2 className="mt-4 text-xl font-medium text-gray-900">
                  {t('feature_in_development', 'Функционал в разработке')}
                </h2>
                <p className="mt-2 text-base text-gray-500">
                  {t(
                    'shipping_settings_coming_soon',
                    'Настройки доставки будут доступны в ближайшее время.'
                  )}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default StoreSettingsPage;
