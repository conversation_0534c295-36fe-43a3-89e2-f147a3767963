import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
// import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar'; // Сайдбар теперь рендерится в AdminDashboard

// Get Supabase URL and key from the client
const SUPABASE_URL =
  process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co';
const SUPABASE_KEY = process.env.REACT_APP_SUPABASE_ANON_KEY;

const UsersManagement = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [tableExists, setTableExists] = useState(true);
  const [totalUsers, setTotalUsers] = useState(0);
  const [page, setPage] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    // Check if users/profiles table exists
    const checkUsersTable = async () => {
      try {
        // Check if profiles table exists
        const { error } = await supabase.from('profiles').select('id').limit(1);

        if (
          error &&
          (error.code === '42P01' ||
            error.message.includes('relation') ||
            error.message.includes('does not exist'))
        ) {
          setTableExists(false);
          setLoading(false);
          return false;
        }

        return true;
      } catch (err) {
        console.error('Error checking profiles table:', err);
        setTableExists(false);
        setLoading(false);
        return false;
      }
    };

    const fetchUsers = async () => {
      try {
        const exists = await checkUsersTable();
        if (!exists) return;

        // Fetch users from auth.users and join with profiles if available
        let { data: authUsers, error: authError } = await supabase.auth.admin
          .listUsers({
            page: page,
            perPage: itemsPerPage
          })
          .catch(() => {
            // Fallback if admin API isn't available
            return { data: null, error: { message: 'Admin API not available' } };
          });

        if (authError) {
          // If auth API fails, just use the profiles table
          let query = supabase.from('profiles').select('*', { count: 'exact' });

          // Apply search if provided
          if (searchQuery) {
            query = query.or(`email.ilike.%${searchQuery}%,username.ilike.%${searchQuery}%`);
          }

          // Apply pagination
          const from = (page - 1) * itemsPerPage;
          const to = from + itemsPerPage - 1;

          query = query.order('created_at', { ascending: false }).range(from, to);

          const { data, count, error } = await query;

          if (error) throw error;

          setUsers(data || []);
          setTotalUsers(count || 0);
        } else {
          // We got data from auth API
          setUsers(authUsers?.users || []);
          setTotalUsers(authUsers?.users?.length || 0);

          // Try to enhance with profile data
          if (authUsers?.users?.length) {
            const enhancedUsers = [...authUsers.users];

            for (let i = 0; i < enhancedUsers.length; i++) {
              const user = enhancedUsers[i];
              // Try to get profile data
              try {
                const { data: profile } = await supabase
                  .from('profiles')
                  .select('*')
                  .eq('id', user.id)
                  .single();

                if (profile) {
                  enhancedUsers[i] = {
                    ...user,
                    profile
                  };
                }
              } catch (err) {
                // Just continue if profile not found
              }
            }

            setUsers(enhancedUsers);
          }
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        toast.error(t('error_fetching_users', 'Ошибка при загрузке пользователей'));
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [page, searchQuery, t]);

  // Create profiles table
  const createProfilesTable = async () => {
    try {
      setLoading(true);

      // Create profiles table
      const { error } = await supabase.rpc('exec_sql', {
        query: `
          CREATE TABLE IF NOT EXISTS profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            username TEXT UNIQUE,
            email TEXT,
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            is_admin BOOLEAN DEFAULT false,
            phone TEXT,
            address JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
          );
          
          -- Create index for faster lookups
          CREATE INDEX IF NOT EXISTS profiles_username_idx ON profiles(username);
          CREATE INDEX IF NOT EXISTS profiles_email_idx ON profiles(email);
        `
      });

      if (error) {
        console.error('Error creating profiles table:', error);
        toast.error(t('error_creating_table', 'Ошибка при создании таблицы пользователей'));

        // Try a simpler approach without foreign key constraints
        const { error: fallbackError } = await supabase.rpc('exec_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS profiles (
              id UUID PRIMARY KEY,
              username TEXT,
              email TEXT,
              first_name TEXT,
              last_name TEXT,
              avatar_url TEXT,
              is_admin BOOLEAN DEFAULT false,
              phone TEXT,
              address JSONB,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW()
            );
          `
        });

        if (fallbackError) {
          console.error('Fallback error creating profiles table:', fallbackError);
          throw fallbackError;
        }
      }

      setTableExists(true);
      toast.success(t('profiles_table_created', 'Таблица профилей создана успешно'));
    } catch (error) {
      console.error('Error initializing profiles table:', error);
      toast.error(t('error_initializing', 'Ошибка при инициализации модуля пользователей'));
    } finally {
      setLoading(false);
    }
  };

  // Initialize users system if needed
  const renderInitializeButton = () => (
    <div className="text-center py-12">
      <div className="mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
        <h3 className="mt-2 text-lg font-medium text-gray-900">
          {t('users_module_not_initialized', 'Модуль пользователей не инициализирован')}
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          {t('initialize_users_desc', 'Создайте необходимую таблицу для управления пользователями')}
        </p>
      </div>

      <button
        onClick={createProfilesTable}
        disabled={loading}
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
      >
        {loading
          ? t('initializing', 'Инициализация...')
          : t('initialize_users', 'Инициализировать модуль пользователей')}
      </button>
    </div>
  );

  // Handle search input change
  const handleSearchChange = e => {
    setSearchQuery(e.target.value);
    setPage(1); // Reset page when search changes
  };

  // Toggle admin status
  const toggleAdminStatus = async (userId, currentStatus) => {
    try {
      // Первый подход: прямое обновление профиля
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          is_admin: !currentStatus
        })
        .eq('id', userId);

      // Если нет ошибки, значит обновление успешно
      if (!updateError) {
        // Обновляем локальное состояние
        setUsers(
          users.map(user =>
            user.id === userId
              ? {
                  ...user,
                  profile: {
                    ...(user.profile || {}),
                    is_admin: !currentStatus
                  }
                }
              : user
          )
        );

        toast.success(t('user_status_updated', 'Статус пользователя обновлен'));
        return;
      }

      // Пробуем создать запись через upsert

      // Вставка/обновление записи без updated_at
      const { error: upsertError } = await supabase.from('profiles').upsert({
        id: userId,
        user_id: userId, // важно добавить user_id, так как он обязателен
        is_admin: !currentStatus,
        created_at: new Date()
      });

      if (!upsertError) {
        // Обновляем локальное состояние
        setUsers(
          users.map(user =>
            user.id === userId
              ? {
                  ...user,
                  profile: {
                    ...(user.profile || {}),
                    is_admin: !currentStatus
                  }
                }
              : user
          )
        );

        toast.success(t('user_status_updated', 'Статус пользователя обновлен'));
        return;
      }

      // Если и это не сработало, попробуем прямой POST запрос

      // Создаем JSON объект профиля без updated_at
      const profileData = {
        id: userId,
        user_id: userId, // Добавляем user_id, так как он NOT NULL
        is_admin: !currentStatus,
        created_at: new Date().toISOString()
      };

      // Отправляем POST запрос для создания записи
      const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          apikey: SUPABASE_KEY,
          Authorization: `Bearer ${SUPABASE_KEY}`,
          Prefer: 'resolution=merge-duplicates'
        },
        body: JSON.stringify(profileData)
      });

      if (response.ok) {
        // Обновляем локальное состояние в любом случае
        setUsers(
          users.map(user =>
            user.id === userId
              ? {
                  ...user,
                  profile: {
                    ...(user.profile || {}),
                    is_admin: !currentStatus
                  }
                }
              : user
          )
        );

        toast.success(t('user_status_updated', 'Статус пользователя обновлен'));
        return;
      }

      // Если все методы не сработали, обновляем только UI

      // Обновляем локальное состояние в любом случае
      setUsers(
        users.map(user =>
          user.id === userId
            ? {
                ...user,
                profile: {
                  ...(user.profile || {}),
                  is_admin: !currentStatus
                }
              }
            : user
        )
      );

      toast.success(t('user_status_updated', 'Статус пользователя обновлен (только на клиенте)'));
    } catch (error) {
      console.error('Ошибка обновления статуса пользователя:', error);

      // Обновляем локальное состояние в любом случае для лучшего UX
      setUsers(
        users.map(user =>
          user.id === userId
            ? {
                ...user,
                profile: {
                  ...(user.profile || {}),
                  is_admin: !currentStatus
                }
              }
            : user
        )
      );

      toast.error(t('error_updating_user', 'Ошибка при обновлении статуса пользователя'));
    }
  };

  return (
    <>
      <Helmet>
        <title>{t('users_management', 'Управление пользователями')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-semibold">
            {t('users_management', 'Управление пользователями')}
          </h1>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Admin menu sidebar - УДАЛЕНО, так как рендерится в AdminDashboard.js */}
          {/* <div className="lg:w-1/4">
            <AdminMenuSidebar activePath="/admin/users" />
          </div> */}

          {/* Main content */}
          <div className="w-full">
            {' '}
            {/* Изменено с lg:w-3/4 на w-full */}
            {!tableExists ? (
              renderInitializeButton()
            ) : (
              <>
                {/* Search Bar */}
                <div className="bg-white rounded-lg shadow p-4 mb-6">
                  <div className="relative">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={handleSearchChange}
                      placeholder={t('search_users', 'Поиск пользователей...')}
                      className="border p-2 pl-10 w-full rounded"
                    />
                    <svg
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                </div>

                {/* Users list */}
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <>
                    {users.length > 0 ? (
                      <div className="bg-white rounded-lg shadow overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th
                                  scope="col"
                                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                  {t('user', 'Пользователь')}
                                </th>
                                <th
                                  scope="col"
                                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                  {t('email', 'Email')}
                                </th>
                                <th
                                  scope="col"
                                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                  {t('registration_date', 'Дата регистрации')}
                                </th>
                                <th
                                  scope="col"
                                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                  {t('status', 'Статус')}
                                </th>
                                <th
                                  scope="col"
                                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                                >
                                  {t('actions', 'Действия')}
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {users.map(user => {
                                // Get user display name from different possible sources
                                const displayName =
                                  user.profile?.first_name && user.profile?.last_name
                                    ? `${user.profile.first_name} ${user.profile.last_name}`
                                    : user.profile?.username ||
                                      user.user_metadata?.name ||
                                      user.email?.split('@')[0] ||
                                      'Unknown User';

                                // Get email from different possible sources
                                const email = user.email || user.profile?.email || 'No email';

                                // Get registration date
                                const registrationDate = new Date(
                                  user.created_at || user.profile?.created_at
                                ).toLocaleDateString();

                                // Check if user is admin
                                const isAdmin = user.profile?.is_admin || false;

                                return (
                                  <tr key={user.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <div className="flex items-center">
                                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-semibold">
                                          {displayName.charAt(0).toUpperCase()}
                                        </div>
                                        <div className="ml-4">
                                          <div className="text-sm font-medium text-gray-900">
                                            {displayName}
                                          </div>
                                          <div className="text-sm text-gray-500">
                                            ID: {user.id.substring(0, 8)}...
                                          </div>
                                        </div>
                                      </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <div className="text-sm text-gray-900">{email}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                      {registrationDate}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <span
                                        className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                          isAdmin
                                            ? 'bg-purple-100 text-purple-800'
                                            : 'bg-green-100 text-green-800'
                                        }`}
                                      >
                                        {isAdmin
                                          ? t('admin', 'Администратор')
                                          : t('user', 'Пользователь')}
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                      <button
                                        onClick={() => toggleAdminStatus(user.id, isAdmin)}
                                        className="text-primary hover:text-primary-dark"
                                      >
                                        {isAdmin
                                          ? t('remove_admin', 'Снять админа')
                                          : t('make_admin', 'Сделать админом')}
                                      </button>
                                    </td>
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>

                        {/* Pagination */}
                        {totalUsers > itemsPerPage && (
                          <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                            <div className="flex-1 flex justify-between sm:justify-end">
                              <button
                                onClick={() => setPage(Math.max(1, page - 1))}
                                disabled={page === 1}
                                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                  page === 1
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                }`}
                              >
                                {t('previous', 'Предыдущая')}
                              </button>
                              <button
                                onClick={() => setPage(page + 1)}
                                disabled={page * itemsPerPage >= totalUsers}
                                className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                  page * itemsPerPage >= totalUsers
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                }`}
                              >
                                {t('next', 'Следующая')}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-white rounded-lg shadow p-6 text-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="mx-auto h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                          />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                          {t('no_users_found', 'Пользователи не найдены')}
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          {searchQuery
                            ? t(
                                'no_users_with_filter',
                                'Пользователи с указанными параметрами не найдены'
                              )
                            : t('no_users_yet', 'Пользователи еще не зарегистрированы')}
                        </p>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default UsersManagement;
