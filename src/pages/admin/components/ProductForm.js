import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

const ProductForm = ({ product, categories, brands, onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || '',
    stock_quantity: product?.stock || '',
    sku: product?.sku || '',
    category_id: product?.category_id || '',
    brand_id: product?.brand_id || '',
    image: product?.image || ''
  });
  const [imageFile, setImageFile] = useState(null);

  const handleChange = e => {
    const { name, value, type, files } = e.target;
    if (type === 'file' && files[0]) {
      setImageFile(files[0]);
      return;
    }
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async e => {
    e.preventDefault();
    // ... existing validation logic ...
    await onSubmit(formData);
  };

  return (
    <div className="space-y-6">
      {/* Добавляем верхнюю панель */}
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow">
        <div>
          <h1 className="text-xl font-semibold">{product?.name || 'Новый товар'}</h1>
          {product && (
            <p className="text-sm text-gray-500">
              ID: {product.id} • Создан: {new Date(product.created_at).toLocaleDateString()}
            </p>
          )}
        </div>
        <div className="flex gap-3">
          <button
            type="button"
            onClick={() => navigate('/admin/products')}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            {t('cancel', 'Отмена')}
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 text-white bg-primary rounded-md hover:bg-primary-dark disabled:bg-gray-400"
          >
            {isLoading ? t('saving', 'Сохранение...') : t('save', 'Сохранить')}
          </button>
        </div>
      </div>

      {/* Остальные секции формы */}
      <form className="bg-white rounded-lg shadow p-6" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            {/* Basic Info */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">
                {t('product_name', 'Название товара')}
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
                {t('description', 'Описание')}
              </label>
              <textarea
                id="description"
                name="description"
                rows="5"
                value={formData.description}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              />
            </div>

            {/* Price and Stock */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="price">
                  {t('price', 'Цена')}
                </label>
                <input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  value={formData.price}
                  onChange={handleChange}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                />
              </div>
              <div>
                <label
                  className="block text-sm font-medium text-gray-700 mb-1"
                  htmlFor="stock_quantity"
                >
                  {t('stock_quantity', 'Количество на складе')}
                </label>
                <input
                  id="stock_quantity"
                  name="stock_quantity"
                  type="number"
                  min="0"
                  value={formData.stock_quantity}
                  onChange={handleChange}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                />
              </div>
            </div>

            {/* SKU and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="sku">
                  {t('sku', 'Артикул (SKU)')}
                </label>
                <input
                  id="sku"
                  name="sku"
                  type="text"
                  value={formData.sku}
                  onChange={handleChange}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                />
              </div>
              <div>
                <label
                  className="block text-sm font-medium text-gray-700 mb-1"
                  htmlFor="category_id"
                >
                  {t('category', 'Категория')}
                </label>
                <select
                  id="category_id"
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleChange}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                >
                  <option value="">{t('select_category', '-- Выберите категорию --')}</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Brand */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="brand_id">
                {t('brand', 'Бренд')}
              </label>
              <select
                id="brand_id"
                name="brand_id"
                value={formData.brand_id}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              >
                <option value="">{t('select_brand', '-- Выберите бренд --')}</option>
                {brands.map(brand => (
                  <option key={brand.id} value={brand.id}>
                    {brand.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Image Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('product_image', 'Изображение товара')}
            </label>
            <div className="mt-2 mb-4">
              <div className="flex items-center">
                <label className="block">
                  <span className="sr-only">Choose image</span>
                  <input
                    type="file"
                    className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    accept="image/*"
                    onChange={handleChange}
                  />
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="mt-6 flex justify-end">
          <button
            type="button"
            onClick={() => window.history.back()}
            className="mr-2 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md transition-colors"
          >
            {t('cancel', 'Отмена')}
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors disabled:bg-blue-300"
          >
            {isLoading ? t('saving', 'Сохранение...') : t('create', 'Создать товар')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
