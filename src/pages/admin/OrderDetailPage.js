import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import {
  FaArrowLeft,
  FaTruck,
  FaPrint,
  FaFileInvoice,
  FaEnvelope,
  FaTimes,
  FaCheck
} from 'react-icons/fa';
import { supabase } from '../../supabaseClient';
import { EmailService } from '../../services/emailService';
import { MockEmailService } from '../../services/mockEmailService';
import { toast } from 'react-toastify';
import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar';
import LoadingSpinner from '../../components/LoadingSpinner';
import { v4 as uuidv4 } from 'uuid';
import AdminLayout from '../../components/admin/AdminLayout';

const OrderDetailPage = () => {
  const { id } = useParams();
  const { t } = useTranslation();

  const [order, setOrder] = useState(null);
  const [orderItems, setOrderItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processingAction, setProcessingAction] = useState('');
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [showStatusHistory, setShowStatusHistory] = useState(false);
  const [showChangeStatus, setShowChangeStatus] = useState(false);
  const [note, setNote] = useState('');
  const [statusHistory, setStatusHistory] = useState([]);
  const [newStatus, setNewStatus] = useState('');

  const statusOptions = [
    { value: 'pending', label: t('pending', 'Ожидает обработки') },
    { value: 'processing', label: t('processing', 'В обработке') },
    { value: 'packed', label: t('packed', 'Упакован') },
    { value: 'shipped', label: t('shipped', 'Отправлен') },
    { value: 'delivered', label: t('delivered', 'Доставлен') },
    { value: 'cancelled', label: t('cancelled', 'Отменен') },
    { value: 'refunded', label: t('refunded', 'Возвращен') }
  ];

  // Fetch order data on component mount
  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);

        // Fetch order details
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('*')
          .eq('id', id)
          .single();

        if (orderError) throw orderError;

        // Fetch order items separately from products (fixing the join issue)
        const { data: itemsData, error: itemsError } = await supabase
          .from('order_items')
          .select('*')
          .eq('order_id', id);

        if (itemsError) {
          console.error('Error fetching order items:', itemsError);
          throw itemsError;
        }

        // Process item data if needed
        let processedItems = itemsData || [];

        // Try to fetch product details separately if product_id is available
        if (processedItems.length > 0) {
          const productIds = processedItems
            .filter(item => item.product_id)
            .map(item => item.product_id);

          if (productIds.length > 0) {
            const { data: productsData, error: productsError } = await supabase
              .from('products')
              .select('*')
              .in('id', productIds);

            if (!productsError && productsData) {
              // Join products with order items manually
              processedItems = processedItems.map(item => {
                const product = productsData.find(p => p.id === item.product_id);
                return {
                  ...item,
                  products: product ? product : null
                };
              });
            } else {
            }
          }
        }

        // Fetch status history
        const { data: historyData, error: historyError } = await supabase
          .from('order_status_history')
          .select('*')
          .eq('order_id', id)
          .order('created_at', { ascending: false });

        if (historyError && historyError.code !== '42P01') {
          // Ignore error if table doesn't exist yet
          console.error('Error fetching status history:', historyError);
        }

        setOrder(orderData);
        setOrderItems(processedItems);
        setStatusHistory(historyData || []);
      } catch (error) {
        console.error('Error fetching order details:', error);
        toast.error(t('error_fetching_order', 'Ошибка при загрузке данных заказа'));
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id, t]);

  // Update order status
  const updateOrderStatus = async newStatus => {
    // Create a unique toast ID
    const toastId = uuidv4();

    try {
      setUpdatingStatus(true);

      // Show loading toast
      toast.loading(t('updating_status', 'Обновление статуса...'), {
        toastId,
        autoClose: false
      });

      const timestamp = new Date();

      // First check if status history table exists, if not, create it silently
      try {
        const { data: tableCheck, error: tableCheckError } = await supabase
          .from('order_status_history')
          .select('id')
          .limit(1);

        if (tableCheckError && tableCheckError.code === '42P01') {
          // Table doesn't exist, create it
          await supabase.rpc('exec_sql', {
            query: `
              CREATE TABLE IF NOT EXISTS order_status_history (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
                status TEXT NOT NULL,
                note TEXT,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                created_by TEXT
              );
              
              -- Create policies
              ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;
              
              CREATE POLICY order_status_history_insert_policy ON order_status_history
                  FOR INSERT
                  TO authenticated, anon
                  WITH CHECK (true);
                  
              CREATE POLICY order_status_history_select_policy ON order_status_history
                  FOR SELECT
                  TO authenticated, anon
                  USING (true);
            `
          });
        }
      } catch (error) {
        console.error('Error checking or creating status history table:', error);
        // Continue anyway
      }

      // Update order status
      const { data: updateData, error: updateError } = await supabase
        .from('orders')
        .update({
          status: newStatus,
          updated_at: timestamp
        })
        .eq('id', id);

      if (updateError) {
        console.error('Error updating order status:', updateError);
        throw updateError;
      }

      // Try to add status history record, but don't fail if it doesn't work
      try {
        const { data: historyData, error: historyError } = await supabase
          .from('order_status_history')
          .insert([
            {
              order_id: id,
              status: newStatus,
              note: note || null,
              created_at: timestamp
            }
          ]);

        if (historyError) {
          console.error('Error adding status history:', historyError);
          // If it's a foreign key error, log it but continue
          if (historyError.code === '23503') {
            // Don't throw, continue with the process
          } else {
            // Something else is wrong, but continue anyway
          }
        } else {
        }
      } catch (historyException) {
        console.error('Exception adding status history:', historyException);
        // Continue anyway
      }

      // Update order in state
      setOrder(prev => ({ ...prev, status: newStatus }));

      // Try to refetch status history, but don't fail if it doesn't work
      try {
        const { data: historyData, error: historyRefetchError } = await supabase
          .from('order_status_history')
          .select('*')
          .eq('order_id', id)
          .order('created_at', { ascending: false });

        if (!historyRefetchError) {
          setStatusHistory(historyData || []);
        } else {
          console.error('Error refetching status history:', historyRefetchError);
        }
      } catch (refetchError) {
        console.error('Error refetching status history:', refetchError);
        // Continue anyway
      }

      // Clear note
      setNote('');

      // Update toast to success
      toast.update(toastId, {
        render: t('status_updated', 'Статус заказа обновлен'),
        type: 'success',
        autoClose: 3000,
        isLoading: false
      });

      // Send notification if appropriate
      if (['processing', 'shipped', 'delivered'].includes(newStatus)) {
        sendStatusNotification(newStatus);
      }
    } catch (error) {
      console.error('Error updating order status:', error);

      // Update toast to error
      toast.update(toastId, {
        render: t('error_updating_status', 'Ошибка при обновлении статуса'),
        type: 'error',
        autoClose: 3000,
        isLoading: false
      });
    } finally {
      setUpdatingStatus(false);
    }
  };

  // Send notification about status change
  const sendStatusNotification = async (statusToNotify = null) => {
    // Create a unique toast ID
    const toastId = uuidv4();

    try {
      setProcessingAction('email');

      // Show loading toast
      toast.loading(t('sending_notification', 'Отправка уведомления...'), {
        toastId,
        autoClose: false
      });

      if (!order.customer_email) {
        throw new Error('Customer email not found');
      }

      // Send status update email through our email service (with fallback)
      const oldStatus = 'pending'; // This could be tracked more precisely
      const currentStatus = statusToNotify || order.status;

      try {
        // Попытка отправки через основной EmailService
        await EmailService.sendStatusUpdate(order.id, oldStatus, {
          ...order,
          status: currentStatus,
          order_items: orderItems
        });

        console.log('✅ Status update email sent via EmailService');
      } catch (emailServiceError) {
        console.warn('⚠️ EmailService failed, trying MockEmailService:', emailServiceError.message);

        // Fallback к MockEmailService
        await MockEmailService.sendStatusUpdate(order.id, oldStatus, {
          ...order,
          status: currentStatus,
          order_items: orderItems
        });

        console.log('✅ Status update email sent via MockEmailService');
      }

      // Update toast to success
      toast.update(toastId, {
        render: t('notification_sent', 'Уведомление отправлено клиенту'),
        type: 'success',
        autoClose: 3000,
        isLoading: false
      });
    } catch (error) {
      console.error('Error sending notification:', error);

      // Update toast to error
      toast.update(toastId, {
        render:
          t('error_sending_notification', 'Ошибка при отправке уведомления: ') + error.message,
        type: 'error',
        autoClose: 5000,
        isLoading: false
      });
    } finally {
      setProcessingAction('');
    }
  };

  // Generate and "print" invoice (simulated)
  const handlePrintInvoice = async () => {
    // Create a unique toast ID
    const toastId = uuidv4();

    try {
      setProcessingAction('invoice');

      // Show loading toast
      toast.loading(t('generating_invoice', 'Создание счета...'), {
        toastId,
        autoClose: false
      });

      // Simulate invoice generation delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update toast to success
      toast.update(toastId, {
        render: t('invoice_generated', 'Счет успешно сформирован'),
        type: 'success',
        autoClose: 3000,
        isLoading: false
      });

      // In a real app, this would open a printable invoice
    } catch (error) {
      // Update toast to error
      toast.update(toastId, {
        render: t('error_generating_invoice', 'Ошибка при создании счета'),
        type: 'error',
        autoClose: 3000,
        isLoading: false
      });
    } finally {
      setProcessingAction('');
    }
  };

  // Generate shipping label (simulated)
  const handlePrintShippingLabel = async () => {
    // Create a unique toast ID
    const toastId = uuidv4();

    try {
      setProcessingAction('shipping');

      // Show loading toast
      toast.loading(t('generating_label', 'Создание накладной...'), {
        toastId,
        autoClose: false
      });

      // Simulate label generation delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update toast to success
      toast.update(toastId, {
        render: t('shipping_label_generated', 'Транспортная накладная сформирована'),
        type: 'success',
        autoClose: 3000,
        isLoading: false
      });

      // In a real app, this would open a printable shipping label
    } catch (error) {
      // Update toast to error
      toast.update(toastId, {
        render: t('error_generating_label', 'Ошибка при создании накладной'),
        type: 'error',
        autoClose: 3000,
        isLoading: false
      });
    } finally {
      setProcessingAction('');
    }
  };

  // Format date for display
  const formatDate = dateString => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get styled status badge
  const getStatusBadge = status => {
    let bgColor = 'bg-gray-100';
    let textColor = 'text-gray-800';

    switch (status) {
      case 'pending':
        bgColor = 'bg-yellow-100';
        textColor = 'text-yellow-800';
        break;
      case 'processing':
        bgColor = 'bg-blue-100';
        textColor = 'text-blue-800';
        break;
      case 'packed':
        bgColor = 'bg-indigo-100';
        textColor = 'text-indigo-800';
        break;
      case 'shipped':
        bgColor = 'bg-purple-100';
        textColor = 'text-purple-800';
        break;
      case 'delivered':
        bgColor = 'bg-green-100';
        textColor = 'text-green-800';
        break;
      case 'cancelled':
        bgColor = 'bg-red-100';
        textColor = 'text-red-800';
        break;
      case 'refunded':
        bgColor = 'bg-pink-100';
        textColor = 'text-pink-800';
        break;
      default:
        break;
    }

    return (
      <span className={`px-3 py-1 rounded-full ${bgColor} ${textColor} text-sm font-medium`}>
        {t(status, status)}
      </span>
    );
  };

  // Calculate order total
  const calculateTotal = () => {
    return orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0).toFixed(2);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <h3 className="text-lg font-medium text-gray-900">
            {t('order_not_found', 'Заказ не найден')}
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {t('order_not_found_desc', 'Заказ с указанным идентификатором не существует')}
          </p>
          <div className="mt-6">
            <Link
              to="/admin/orders"
              className="inline-flex items-center px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
            >
              <FaArrowLeft className="mr-2" /> {t('back_to_orders', 'Вернуться к заказам')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>
          {t('order_details', 'Детали заказа')} #{id.substring(0, 8)}
        </title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link
            to="/admin/orders"
            className="inline-flex items-center text-primary hover:underline"
          >
            <FaArrowLeft className="mr-1" /> {t('back_to_orders', 'Вернуться к заказам')}
          </Link>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Remove the duplicate AdminMenuSidebar */}
          {/* <div className="lg:w-1/4">
            <AdminMenuSidebar activePath="/admin/orders" />
          </div> */}

          {/* Main content - now full width */}
          <div className="w-full">
            {/* Order header */}
            <div className="bg-white rounded-lg shadow mb-6">
              <div className="p-6 border-b border-gray-200">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                      {t('order', 'Заказ')} #{id.substring(0, 8)}
                    </h1>
                    <p className="text-sm text-gray-500 mt-1">
                      {t('placed_on', 'Оформлен')}: {formatDate(order.created_at)}
                    </p>
                  </div>
                  <div className="mt-4 sm:mt-0">
                    <div className="relative">
                      <div className="flex items-center gap-2">
                        {getStatusBadge(order.status)}
                        <button
                          type="button"
                          onClick={() => setShowStatusHistory(!showStatusHistory)}
                          className="ml-2 px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm flex items-center"
                        >
                          {showStatusHistory ? (
                            <>
                              <FaTimes className="mr-1" size={12} /> {t('hide', 'Скрыть')}
                            </>
                          ) : (
                            <>
                              <FaCheck className="mr-1" size={12} /> {t('history', 'История')}
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order actions */}
              <div className="p-6 bg-gray-50 flex flex-wrap gap-3">
                <div className="relative inline-block">
                  <button
                    type="button"
                    onClick={() => setShowChangeStatus(prev => !prev)}
                    className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark flex items-center"
                  >
                    {t('change_status', 'Изменить статус')}
                    <svg
                      className="w-4 h-4 ml-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                  </button>

                  {/* Status change dropdown */}
                  {showChangeStatus && (
                    <div className="absolute z-50 w-64 mt-2 bg-white rounded-md shadow-lg py-1 left-0 border border-gray-200">
                      <div className="p-2 border-b border-gray-100">
                        <h3 className="text-sm font-medium text-gray-700">
                          {t('select_new_status', 'Выберите новый статус')}
                        </h3>
                      </div>
                      <div className="p-2 max-h-80 overflow-y-auto">
                        {statusOptions.map(option => (
                          <button
                            key={option.value}
                            onClick={() => {
                              updateOrderStatus(option.value);
                              setShowChangeStatus(false);
                            }}
                            disabled={updatingStatus || order.status === option.value}
                            className={`w-full text-left px-3 py-2 my-1 rounded-md text-sm ${
                              order.status === option.value
                                ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                                : option.value === 'pending'
                                  ? 'text-yellow-800 bg-yellow-50 hover:bg-yellow-100'
                                  : option.value === 'processing'
                                    ? 'text-blue-800 bg-blue-50 hover:bg-blue-100'
                                    : option.value === 'packed'
                                      ? 'text-indigo-800 bg-indigo-50 hover:bg-indigo-100'
                                      : option.value === 'shipped'
                                        ? 'text-purple-800 bg-purple-50 hover:bg-purple-100'
                                        : option.value === 'delivered'
                                          ? 'text-green-800 bg-green-50 hover:bg-green-100'
                                          : option.value === 'cancelled'
                                            ? 'text-red-800 bg-red-50 hover:bg-red-100'
                                            : option.value === 'refunded'
                                              ? 'text-pink-800 bg-pink-50 hover:bg-pink-100'
                                              : 'hover:bg-gray-100'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span>{option.label}</span>
                              {order.status === option.value && (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              )}
                            </div>
                          </button>
                        ))}
                      </div>
                      <div className="p-2 border-t border-gray-100">
                        <textarea
                          id="note"
                          rows="2"
                          value={note}
                          onChange={e => setNote(e.target.value)}
                          className="w-full border rounded p-2 text-sm"
                          placeholder={t(
                            'status_note_placeholder',
                            'Примечание к изменению статуса'
                          )}
                        ></textarea>
                      </div>
                    </div>
                  )}
                </div>

                <button
                  onClick={handlePrintInvoice}
                  disabled={processingAction === 'invoice'}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center disabled:opacity-50"
                >
                  {processingAction === 'invoice' ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                      {t('generating', 'Создание...')}
                    </div>
                  ) : (
                    <>
                      <FaFileInvoice className="mr-2" /> {t('print_invoice', 'Счет')}
                    </>
                  )}
                </button>

                <button
                  onClick={handlePrintShippingLabel}
                  disabled={
                    processingAction === 'shipping' ||
                    order.status === 'pending' ||
                    order.status === 'cancelled'
                  }
                  className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 flex items-center disabled:opacity-50"
                >
                  {processingAction === 'shipping' ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                      {t('generating', 'Создание...')}
                    </div>
                  ) : (
                    <>
                      <FaTruck className="mr-2" /> {t('shipping_label', 'Накладная')}
                    </>
                  )}
                </button>

                <button
                  onClick={() => sendStatusNotification(order.status)}
                  disabled={processingAction === 'email'}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center disabled:opacity-50"
                >
                  {processingAction === 'email' ? (
                    <div className="flex items-center">
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                      {t('sending', 'Отправка...')}
                    </div>
                  ) : (
                    <>
                      <FaEnvelope className="mr-2" /> {t('send_notification', 'Уведомление')}
                    </>
                  )}
                </button>

                <button
                  onClick={() => window.print()}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 flex items-center"
                >
                  <FaPrint className="mr-2" /> {t('print', 'Печать')}
                </button>
              </div>
            </div>

            {/* Status history section (replace the old status change section) */}
            {showStatusHistory && (
              <div className="bg-white rounded-lg shadow mb-6">
                <div className="p-6">
                  <h3 className="font-medium text-gray-700 mb-4">
                    {t('status_history', 'История статусов')}
                  </h3>

                  {statusHistory.length > 0 ? (
                    <ul className="divide-y divide-gray-200">
                      {statusHistory.map(item => (
                        <li key={item.id} className="py-3">
                          <div className="flex items-start">
                            {getStatusBadge(item.status)}
                            <div className="ml-3">
                              <div className="text-sm text-gray-500">
                                {formatDate(item.created_at)}
                              </div>
                              {item.note && (
                                <div className="text-sm text-gray-700 mt-1">{item.note}</div>
                              )}
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-sm">
                      {t('no_status_history', 'История изменений статуса отсутствует')}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Customer info and Order details in a grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {/* Customer info */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">
                    {t('customer_info', 'Информация о клиенте')}
                  </h2>

                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium text-gray-500">{t('name', 'Имя')}:</div>
                      <div className="text-base">{order.customer_name}</div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500">
                        {t('email', 'Email')}:
                      </div>
                      <div className="text-base">
                        {order.customer_email || t('not_provided', 'Не указан')}
                      </div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-gray-500">
                        {t('phone', 'Телефон')}:
                      </div>
                      <div className="text-base">{order.customer_phone}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Shipping address */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">
                    {t('shipping_address', 'Адрес доставки')}
                  </h2>

                  {order.shipping_address ? (
                    <div className="space-y-1">
                      {/* Определим тип доставки */}
                      {order.shipping_address.delivery_type && (
                        <div className="mb-2 font-medium text-gray-700">
                          {order.shipping_address.delivery_type === 'nova_poshta'
                            ? t('nova_poshta', 'Новая Почта')
                            : order.shipping_address.delivery_type === 'courier'
                              ? t('courier', 'Курьерская доставка')
                              : t('pickup', 'Самовывоз')}
                        </div>
                      )}

                      {/* Данные Новой Почты */}
                      {order.shipping_address.delivery_type === 'nova_poshta' && (
                        <>
                          {order.shipping_address.city && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('city', 'Город')}:</span>{' '}
                              {order.shipping_address.city}
                            </p>
                          )}
                          {order.shipping_address.nova_poshta_branch && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('branch', 'Отделение')}:</span>{' '}
                              {order.shipping_address.nova_poshta_branch}
                            </p>
                          )}
                          {order.shipping_address.nova_poshta_address && (
                            <p className="text-gray-700">
                              <span className="font-medium">
                                {t('post_address', 'Адрес отделения')}:
                              </span>{' '}
                              {order.shipping_address.nova_poshta_address}
                            </p>
                          )}
                        </>
                      )}

                      {/* Данные Курьерской доставки */}
                      {order.shipping_address.delivery_type === 'courier' && (
                        <>
                          {order.shipping_address.city && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('city', 'Город')}:</span>{' '}
                              {order.shipping_address.city}
                            </p>
                          )}
                          {order.shipping_address.street && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('street', 'Улица')}:</span>{' '}
                              {order.shipping_address.street}
                            </p>
                          )}
                          {order.shipping_address.house && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('house', 'Дом')}:</span>{' '}
                              {order.shipping_address.house}
                              {order.shipping_address.apartment &&
                                `, ${t('apartment', 'кв.')} ${order.shipping_address.apartment}`}
                            </p>
                          )}
                          {order.shipping_address.entrance && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('entrance', 'Подъезд')}:</span>{' '}
                              {order.shipping_address.entrance}
                              {order.shipping_address.floor &&
                                `, ${t('floor', 'этаж')} ${order.shipping_address.floor}`}
                            </p>
                          )}
                          {order.shipping_address.zip && (
                            <p className="text-gray-700">
                              <span className="font-medium">{t('zip', 'Индекс')}:</span>{' '}
                              {order.shipping_address.zip}
                            </p>
                          )}
                        </>
                      )}

                      {/* Самовывоз */}
                      {order.shipping_address.delivery_type === 'pickup' && (
                        <p className="text-gray-700">{t('pickup_address', 'Адрес самовывоза')}</p>
                      )}

                      {/* Если формат адреса устаревший или нестандартный */}
                      {!order.shipping_address.delivery_type && (
                        <>
                          {order.shipping_address.street && <p>{order.shipping_address.street}</p>}
                          {(order.shipping_address.city ||
                            order.shipping_address.state ||
                            order.shipping_address.zip) && (
                            <p>
                              {order.shipping_address.city}
                              {order.shipping_address.city && order.shipping_address.state && ', '}
                              {order.shipping_address.state} {order.shipping_address.zip || ''}
                            </p>
                          )}
                          {order.shipping_address.country && (
                            <p>{order.shipping_address.country}</p>
                          )}
                        </>
                      )}

                      {order.shipping_address.notes && (
                        <p className="text-sm text-gray-700 mt-2 border-t pt-2">
                          <span className="font-medium">{t('notes', 'Примечания')}:</span>{' '}
                          {order.shipping_address.notes}
                        </p>
                      )}

                      {/* Получатель */}
                      {order.shipping_address.recipient_name && (
                        <div className="mt-4 pt-2 border-t border-gray-200">
                          <p className="text-gray-700">
                            <span className="font-medium">{t('recipient', 'Получатель')}:</span>{' '}
                            {order.shipping_address.recipient_name}
                          </p>
                          {order.shipping_address.recipient_phone && (
                            <p className="text-gray-700">
                              <span className="font-medium">
                                {t('recipient_phone', 'Телефон получателя')}:
                              </span>{' '}
                              {order.shipping_address.recipient_phone}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">
                      {t('no_shipping_address', 'Адрес доставки не указан')}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Order items */}
            <div className="bg-white rounded-lg shadow mb-6">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  {t('order_items', 'Товары в заказе')}
                </h2>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('product', 'Товар')}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('price', 'Цена')}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('quantity', 'Количество')}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('subtotal', 'Сумма')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orderItems.map(item => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {item.products?.image && (
                              <div className="flex-shrink-0 h-10 w-10">
                                <img
                                  className="h-10 w-10 object-cover rounded"
                                  src={item.products.image}
                                  alt={item.product_name}
                                  onError={e => {
                                    e.target.onerror = null;
                                    e.target.src = 'https://placehold.co/40x40/EEE/31343C?text=Img';
                                  }}
                                />
                              </div>
                            )}
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {item.product_name}
                              </div>
                              {item.products?.sku && (
                                <div className="text-sm text-gray-500">
                                  {t('sku', 'Артикул')}: {item.products.sku}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                          {parseFloat(item.price).toFixed(2)} грн
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-500">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          {(item.price * item.quantity).toFixed(2)} грн
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gray-50">
                    <tr>
                      <td
                        colSpan="3"
                        className="px-6 py-4 text-right text-sm font-medium text-gray-500"
                      >
                        {t('order_total', 'Итого')}:
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                        {order.total_amount || calculateTotal()} грн
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Notes */}
            {order.notes && (
              <div className="bg-white rounded-lg shadow mb-6">
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-2">
                    {t('order_notes', 'Примечания к заказу')}
                  </h2>
                  <div className="text-gray-700 whitespace-pre-line">{order.notes}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default OrderDetailPage;
