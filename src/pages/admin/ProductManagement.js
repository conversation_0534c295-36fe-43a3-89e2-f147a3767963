import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { Link } from 'react-router-dom';

// A simple ProductFilters component as a fallback
const ProductFilters = ({
  categories = [],
  selectedCategory = '',
  setSelectedCategory = () => {
    /* no-op */
  },
  searchQuery = '',
  setSearchQuery = () => {
    /* no-op */
  },
  sortOption = 'name_asc',
  setSortOption = () => {
    /* no-op */
  },
  stockFilter = 'all',
  setStockFilter = () => {
    /* no-op */
  }
}) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-4 bg-white p-4 rounded-lg shadow">
      <h3 className="font-medium">{t('filters', 'Фильтры')}</h3>

      <div>
        <label className="block mb-1 text-sm">{t('search', 'Поиск')}</label>
        <input
          type="text"
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          placeholder={t('search_products', 'Поиск товаров...')}
          className="w-full border rounded p-2 text-sm"
        />
      </div>

      <div>
        <label className="block mb-1 text-sm">{t('category', 'Категория')}</label>
        <select
          value={selectedCategory}
          onChange={e => setSelectedCategory(e.target.value)}
          className="w-full border rounded p-2 text-sm"
        >
          <option value="">{t('all_categories', 'Все категории')}</option>
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block mb-1 text-sm">{t('sort_by', 'Сортировать по')}</label>
        <select
          value={sortOption}
          onChange={e => setSortOption(e.target.value)}
          className="w-full border rounded p-2 text-sm"
        >
          <option value="name_asc">{t('name_asc', 'Название (А-Я)')}</option>
          <option value="name_desc">{t('name_desc', 'Название (Я-А)')}</option>
          <option value="price_asc">{t('price_asc', 'Цена (возрастание)')}</option>
          <option value="price_desc">{t('price_desc', 'Цена (убывание)')}</option>
          <option value="created_at_desc">{t('newest', 'Сначала новые')}</option>
          <option value="created_at_asc">{t('oldest', 'Сначала старые')}</option>
        </select>
      </div>

      <div>
        <label className="block mb-1 text-sm">{t('stock', 'Наличие')}</label>
        <select
          value={stockFilter}
          onChange={e => setStockFilter(e.target.value)}
          className="w-full border rounded p-2 text-sm"
        >
          <option value="all">{t('all_products', 'Все товары')}</option>
          <option value="in_stock">{t('in_stock', 'В наличии')}</option>
          <option value="out_of_stock">{t('out_of_stock', 'Нет в наличии')}</option>
          <option value="low_stock">{t('low_stock', 'Малый запас')}</option>
        </select>
      </div>

      <button
        onClick={() => {
          setSelectedCategory('');
          setSearchQuery('');
          setSortOption('name_asc');
          setStockFilter('all');
        }}
        className="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded transition-colors"
      >
        {t('reset_filters', 'Сбросить фильтры')}
      </button>
    </div>
  );
};

const ProductManagement = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState('name_asc');
  const [stockFilter, setStockFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [isUpdatingAllProducts, setIsUpdatingAllProducts] = useState(false);
  const pageSize = 20;

  // Получение категорий для фильтра
  const fetchCategories = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('categories').select('id, name').order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  // Вспомогательная функция для получения поля сортировки
  const getSortField = option => {
    switch (option) {
      case 'name_asc':
        return { column: 'name', ascending: true };
      case 'name_desc':
        return { column: 'name', ascending: false };
      case 'price_asc':
        return { column: 'price', ascending: true };
      case 'price_desc':
        return { column: 'price', ascending: false };
      case 'created_at_desc':
        return { column: 'created_at', ascending: false };
      case 'created_at_asc':
        return { column: 'created_at', ascending: true };
      default:
        return { column: 'name', ascending: true };
    }
  };

  // Функция получения товаров
  const fetchProducts = useCallback(async () => {
    setLoading(true);
    try {
      let query = supabase.from('products').select('*', {
        count: 'exact'
      });

      if (selectedCategory) {
        query = query.eq('category_id', selectedCategory);
      }

      if (searchQuery) {
        query = query.ilike('name', `%${searchQuery}%`);
      }

      // Фильтр по наличию
      if (stockFilter !== 'all') {
        switch (stockFilter) {
          case 'in_stock':
            query = query.eq('is_available', true);
            break;
          case 'out_of_stock':
            query = query.eq('is_available', false);
            break;
          case 'low_stock':
            query = query.eq('is_available', true).lt('stock', 10).gt('stock', 0);
            break;
          default:
            break;
        }
      }

      // Применяем сортировку
      const sort = getSortField(sortOption);
      query = query.order(sort.column, { ascending: sort.ascending });

      // Пагинация
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      // Получаем данные и количество в одном запросе
      const { data, count, error } = await query;

      if (error) throw error;

      setProducts(data || []);
      setTotalPages(Math.ceil((count || 0) / pageSize));
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error(t('error_fetching_products', 'Ошибка при загрузке товаров'));
    } finally {
      setLoading(false);
    }
  }, [selectedCategory, searchQuery, stockFilter, sortOption, page, pageSize, t]);

  // Загрузка данных при монтировании
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Загрузка товаров при изменении фильтров
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Выбор товаров для массовых действий
  const toggleProductSelection = productId => {
    setSelectedProducts(prev =>
      prev.includes(productId) ? prev.filter(id => id !== productId) : [...prev, productId]
    );
  };

  // Выбор всех товаров на странице
  const toggleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p.id));
    }
  };

  // Функция для удаления товара
  const handleDeleteProduct = async productId => {
    if (
      !window.confirm(t('confirm_delete_product', 'Вы уверены, что хотите удалить этот товар?'))
    ) {
      return;
    }

    try {
      const { error } = await supabase.from('products').delete().eq('id', productId);

      if (error) throw error;

      setProducts(products.filter(product => product.id !== productId));
      toast.success(t('product_deleted', 'Товар успешно удален'));
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error(t('error_deleting_product', 'Ошибка при удалении товара'));
    }
  };

  // Функция для массового удаления товаров
  const handleBulkDelete = async () => {
    if (
      !window.confirm(t('confirm_bulk_delete', 'Вы уверены, что хотите удалить выбранные товары?'))
    ) {
      return;
    }

    try {
      const { error } = await supabase.from('products').delete().in('id', selectedProducts);

      if (error) throw error;

      setProducts(products.filter(product => !selectedProducts.includes(product.id)));
      setSelectedProducts([]);
      toast.success(t('products_deleted', 'Товары успешно удалены'));
    } catch (error) {
      console.error('Error deleting products:', error);
      toast.error(t('error_deleting_products', 'Ошибка при удалении товаров'));
    }
  };

  // Функция для изменения статуса товаров
  const handleBulkStatusChange = async status => {
    try {
      // Преобразуем статус в булево значение для поля is_active
      const isActive = status === 'active';

      const { error } = await supabase
        .from('products')
        .update({ is_active: isActive })
        .in('id', selectedProducts);

      if (error) throw error;

      setProducts(
        products.map(product =>
          selectedProducts.includes(product.id) ? { ...product, is_active: isActive } : product
        )
      );

      setSelectedProducts([]);
      toast.success(t('products_updated', 'Товары успешно обновлены'));
    } catch (error) {
      console.error('Error updating products:', error);
      toast.error(t('error_updating_products', 'Ошибка при обновлении товаров'));
    }
  };

  // Функция для активации всех одобренных товаров
  const activateAllApprovedProducts = async () => {
    if (
      !window.confirm(
        t('confirm_activate_all', 'Вы уверены, что хотите активировать все одобренные товары?')
      )
    ) {
      return;
    }

    setIsUpdatingAllProducts(true);
    try {
      const { error } = await supabase
        .from('products')
        .update({ is_active: true })
        .eq('moderation_status', 'approved');

      if (error) throw error;

      // Обновляем локальный список товаров
      fetchProducts();
      toast.success(t('all_products_activated', 'Все одобренные товары активированы'));
    } catch (error) {
      console.error('Error activating all products:', error);
      toast.error(t('error_activating_products', 'Ошибка при активации товаров'));
    } finally {
      setIsUpdatingAllProducts(false);
    }
  };

  // Функция для активации всех товаров
  const activateAllProducts = async () => {
    if (
      !window.confirm(
        t('confirm_activate_all_products', 'Вы уверены, что хотите активировать ВСЕ товары?')
      )
    ) {
      return;
    }

    setIsUpdatingAllProducts(true);
    try {
      const { error } = await supabase.from('products').update({ is_active: true });

      if (error) throw error;

      // Обновляем локальный список товаров
      fetchProducts();
      toast.success(t('all_products_activated', 'Все товары активированы'));
    } catch (error) {
      console.error('Error activating all products:', error);
      toast.error(t('error_activating_products', 'Ошибка при активации товаров'));
    } finally {
      setIsUpdatingAllProducts(false);
    }
  };

  const resetFilters = () => {
    setSelectedCategory('');
    setSearchQuery('');
    setSortOption('name_asc');
    setStockFilter('all');
    setPage(1);
  };

  return (
    <>
      <Helmet>
        <title>{t('product_management', 'Управление товарами')}</title>
      </Helmet>

      <div className="px-4 py-4">
        <div className="mb-6 flex flex-wrap justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4 sm:mb-0">
            {t('product_management', 'Управление товарами')}
          </h1>

          <div className="flex flex-wrap gap-3 items-center">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 shadow-sm hover:bg-gray-50"
            >
              {showFilters
                ? t('hide_filters', 'Скрыть фильтры')
                : t('show_filters', 'Показать фильтры')}
            </button>

            {selectedProducts.length > 0 && (
              <div className="flex gap-2 items-center">
                <span className="text-sm">{selectedProducts.length} выбрано</span>
                <button
                  onClick={() => handleBulkStatusChange('active')}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                >
                  Сделать активными
                </button>
                <button
                  onClick={() => handleBulkStatusChange('inactive')}
                  className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
                >
                  Сделать неактивными
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                >
                  Удалить
                </button>
                <button
                  onClick={() => setSelectedProducts([])}
                  className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300"
                >
                  Отменить
                </button>
              </div>
            )}

            <div className="flex gap-2">
              <button
                onClick={activateAllApprovedProducts}
                disabled={isUpdatingAllProducts}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                {isUpdatingAllProducts ? 'Обработка...' : 'Активировать одобренные'}
              </button>

              <button
                onClick={activateAllProducts}
                disabled={isUpdatingAllProducts}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {isUpdatingAllProducts ? 'Обработка...' : 'Активировать ВСЕ товары'}
              </button>

              <Link
                to="/admin/products/new"
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 ml-auto"
              >
                {t('add_product', 'Добавить товар')}
              </Link>
            </div>
          </div>
        </div>

        {/* Фильтры (показываются/скрываются) */}
        {showFilters && (
          <div className="mb-6 p-4 bg-white rounded-lg shadow">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block mb-1 text-sm font-medium">{t('search', 'Поиск')}</label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  placeholder={t('search_products', 'Поиск товаров...')}
                  className="w-full p-2 border rounded"
                />
              </div>

              <div>
                <label className="block mb-1 text-sm font-medium">
                  {t('category', 'Категория')}
                </label>
                <select
                  value={selectedCategory}
                  onChange={e => setSelectedCategory(e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  <option value="">{t('all_categories', 'Все категории')}</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block mb-1 text-sm font-medium">
                  {t('sort_by', 'Сортировать по')}
                </label>
                <select
                  value={sortOption}
                  onChange={e => setSortOption(e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  <option value="name_asc">{t('name_asc', 'Название (А-Я)')}</option>
                  <option value="name_desc">{t('name_desc', 'Название (Я-А)')}</option>
                  <option value="price_asc">{t('price_asc', 'Цена (возрастание)')}</option>
                  <option value="price_desc">{t('price_desc', 'Цена (убывание)')}</option>
                  <option value="created_at_desc">{t('newest', 'Сначала новые')}</option>
                  <option value="created_at_asc">{t('oldest', 'Сначала старые')}</option>
                </select>
              </div>

              <div>
                <label className="block mb-1 text-sm font-medium">{t('stock', 'Наличие')}</label>
                <select
                  value={stockFilter}
                  onChange={e => setStockFilter(e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  <option value="all">{t('all_products', 'Все товары')}</option>
                  <option value="in_stock">{t('in_stock', 'В наличии')}</option>
                  <option value="out_of_stock">{t('out_of_stock', 'Нет в наличии')}</option>
                  <option value="low_stock">{t('low_stock', 'Малый запас')}</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <button
                onClick={resetFilters}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                {t('reset_filters', 'Сбросить фильтры')}
              </button>
            </div>
          </div>
        )}

        {/* Таблица товаров */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {loading ? (
            <div className="p-8 flex justify-center">
              <div className="w-12 h-12 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-3 py-3 text-left">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={selectedProducts.length === products.length && products.length > 0}
                        onChange={toggleSelectAll}
                      />
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('product', 'Товар')}
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('price', 'Цена')}
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('stock', 'Наличие')}
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('status', 'Статус')}
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('actions', 'Действия')}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {products.map(product => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-3 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={selectedProducts.includes(product.id)}
                          onChange={() => toggleProductSelection(product.id)}
                        />
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {product.image ? (
                              <img
                                className="h-10 w-10 rounded-full object-cover"
                                src={product.image}
                                alt={product.name}
                                onError={e => {
                                  e.target.onerror = null;
                                  e.target.src = '/placeholder.jpg';
                                }}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-500">Нет</span>
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                            <div className="text-sm text-gray-500">
                              {product.vendor || 'Нет данных'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Intl.NumberFormat('ru-RU', {
                            style: 'currency',
                            currency: 'RUB'
                          }).format(product.price || 0)}
                        </div>
                        {product.old_price && (
                          <div className="text-sm text-gray-500 line-through">
                            {new Intl.NumberFormat('ru-RU', {
                              style: 'currency',
                              currency: 'RUB'
                            }).format(product.old_price)}
                          </div>
                        )}
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap">
                        <div
                          className={`text-sm ${product.is_available ? 'text-green-600' : 'text-red-600'}`}
                        >
                          {product.is_available
                            ? product.stock > 0
                              ? `${product.stock} шт.`
                              : t('in_stock', 'В наличии')
                            : t('out_of_stock', 'Нет в наличии')}
                        </div>
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            !product.is_active
                              ? 'bg-red-100 text-red-800'
                              : 'bg-green-100 text-green-800'
                          }`}
                        >
                          {product.is_active
                            ? t('active', 'Активный')
                            : t('inactive', 'Неактивный')}
                        </span>
                      </td>
                      <td className="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Link
                            to={`/admin/products/edit/${product.id}`}
                            className="text-indigo-600 hover:text-indigo-900 px-2 py-1"
                          >
                            {t('edit', 'Редактировать')}
                          </Link>
                          <button
                            onClick={() => handleDeleteProduct(product.id)}
                            className="text-red-600 hover:text-red-900 px-2 py-1"
                          >
                            {t('delete', 'Удалить')}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {products.length === 0 && !loading && (
            <div className="p-8 text-center">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-12 w-12">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                  />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                {t('no_products', 'Товары не найдены')}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {t('try_different_filters', 'Попробуйте изменить параметры фильтрации.')}
              </p>
            </div>
          )}

          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <nav
                    className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                    aria-label="Pagination"
                  >
                    <button
                      onClick={() => setPage(page > 1 ? page - 1 : 1)}
                      disabled={page === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        page === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">{t('previous', 'Предыдущая')}</span>
                      &larr;
                    </button>

                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (page <= 3) {
                        pageNum = i + 1;
                      } else if (page >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = page - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border ${
                            page === pageNum
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          } text-sm font-medium`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    <button
                      onClick={() => setPage(page < totalPages ? page + 1 : totalPages)}
                      disabled={page === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        page === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">{t('next', 'Следующая')}</span>
                      &rarr;
                    </button>
                  </nav>
                </div>
              </div>

              <div className="flex items-center justify-between w-full sm:hidden">
                <button
                  onClick={() => setPage(page > 1 ? page - 1 : 1)}
                  disabled={page === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    page === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('previous', 'Назад')}
                </button>
                <span className="text-sm text-gray-700">
                  {page} / {totalPages}
                </span>
                <button
                  onClick={() => setPage(page < totalPages ? page + 1 : totalPages)}
                  disabled={page === totalPages}
                  className={`relative inline-flex items-center px-4 py-2 ml-3 border border-gray-300 text-sm font-medium rounded-md ${
                    page === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {t('next', 'Вперед')}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ProductManagement;
