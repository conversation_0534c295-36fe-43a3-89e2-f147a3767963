import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { Link } from 'react-router-dom';
import EmailSystemDiagnostic from '../../components/admin/EmailSystemDiagnostic';

const AdminDashboardPage = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState({
    products: 0,
    categories: 0,
    orders: 0,
    users: 0,
    revenue: 0
  });

  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);

        // Get product count
        const { count: productCount } = await supabase
          .from('products')
          .select('*', { count: 'exact', head: true });

        // Get category count
        const { count: categoryCount } = await supabase
          .from('categories')
          .select('*', { count: 'exact', head: true });

        // Try to get order count if table exists
        let orderCount = 0;
        try {
          const { count } = await supabase
            .from('orders')
            .select('*', { count: 'exact', head: true });

          if (count) orderCount = count;
        } catch (e) {}

        // Try to get user count
        let userCount = 0;
        try {
          const { count } = await supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true });

          if (count) userCount = count;
        } catch (e) {}

        setStatistics({
          products: productCount || 0,
          categories: categoryCount || 0,
          orders: orderCount || 0,
          users: userCount || 0,
          revenue: 0
        });
      } catch (error) {
        console.error('Error fetching dashboard statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  return (
    <>
      <Helmet>
        <title>{t('admin_dashboard', 'Панель администратора')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-semibold">
            {t('admin_dashboard', 'Панель администратора')}
          </h1>
        </div>

        <div className="flex flex-col lg:flex-row gap-4">
          {/* Remove admin menu - it's already in the parent layout */}
          {/* <div className="lg:w-auto">
            <AdminMenuSidebar activePath="/admin" />
          </div> */}

          {/* Main content - make it full width */}
          <div className="w-full">
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                {/* Statistics cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  {/* Products card */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">{t('products', 'Товары')}</p>
                        <p className="text-3xl font-semibold">{statistics.products}</p>
                      </div>
                      <div className="bg-blue-100 p-2 rounded-full">
                        <svg
                          className="w-6 h-6 text-blue-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Link
                        to="/admin/products"
                        className="text-primary text-sm font-medium hover:underline"
                      >
                        {t('manage_products', 'Управление товарами')} →
                      </Link>
                    </div>
                  </div>

                  {/* Categories card */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">{t('categories', 'Категории')}</p>
                        <p className="text-3xl font-semibold">{statistics.categories}</p>
                      </div>
                      <div className="bg-green-100 p-2 rounded-full">
                        <svg
                          className="w-6 h-6 text-green-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Link
                        to="/admin/categories"
                        className="text-primary text-sm font-medium hover:underline"
                      >
                        {t('manage_categories', 'Управление категориями')} →
                      </Link>
                    </div>
                  </div>

                  {/* Orders card */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">{t('orders', 'Заказы')}</p>
                        <p className="text-3xl font-semibold">{statistics.orders}</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Link
                        to="/admin/orders"
                        className="text-primary text-sm font-medium hover:underline"
                      >
                        {t('manage_orders', 'Управление заказами')} →
                      </Link>
                    </div>
                  </div>

                  {/* Users card */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">{t('users', 'Пользователи')}</p>
                        <p className="text-3xl font-semibold">{statistics.users}</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Link
                        to="/admin/users"
                        className="text-primary text-sm font-medium hover:underline"
                      >
                        {t('manage_users', 'Управление пользователями')} →
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Email System Diagnostic */}
                <div className="mt-8">
                  <EmailSystemDiagnostic />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminDashboardPage;
