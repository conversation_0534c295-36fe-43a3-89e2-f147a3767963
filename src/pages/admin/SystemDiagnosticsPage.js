import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import DatabaseHealthCheck from '../../components/admin/DatabaseHealthCheck';
import FunctionalityTester from '../../components/admin/FunctionalityTester';
import DatabaseDiagnostic from '../../components/admin/DatabaseDiagnostic';

const SystemDiagnosticsPage = () => {
  const [activeTab, setActiveTab] = useState('health');

  const tabs = [
    {
      id: 'health',
      label: 'Состояние БД',
      icon: '🏥',
      description: 'Проверка подключения, таблиц, RLS, функций'
    },
    {
      id: 'functionality',
      label: 'Функциональные тесты',
      icon: '🧪',
      description: 'Тестирование основных функций приложения'
    },
    {
      id: 'diagnostic',
      label: 'Базовая диагностика',
      icon: '🔍',
      description: 'Простая проверка таблиц и данных'
    },
    {
      id: 'repair',
      label: 'Восстановление',
      icon: '🔧',
      description: 'Инструменты для исправления проблем'
    }
  ];

  const RepairTools = () => {
    const [isRunning, setIsRunning] = useState(false);
    const [logs, setLogs] = useState([]);

    const addLog = (message, type = 'info') => {
      const timestamp = new Date().toLocaleTimeString();
      setLogs(prev => [...prev, { timestamp, message, type }]);
    };

    const runDatabaseRepair = async () => {
      setIsRunning(true);
      setLogs([]);

      addLog('🔧 Начинаем восстановление базы данных...', 'info');

      try {
        // Здесь можно добавить скрипты восстановления
        addLog('ℹ️ Функции восстановления будут добавлены по необходимости', 'info');
        addLog('✅ Проверка завершена', 'success');
      } catch (error) {
        addLog(`❌ Ошибка: ${error.message}`, 'error');
      } finally {
        setIsRunning(false);
      }
    };

    return (
      <div className="p-6">
        <h3 className="text-xl font-semibold mb-4">🔧 Инструменты восстановления</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">Восстановление таблиц</h4>
            <p className="text-gray-600 mb-4">Создание отсутствующих таблиц и индексов</p>
            <button
              onClick={runDatabaseRepair}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
            >
              {isRunning ? 'Выполняется...' : 'Запустить'}
            </button>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">Очистка кэша</h4>
            <p className="text-gray-600 mb-4">Очистка кэша приложения и перезагрузка</p>
            <button
              onClick={() => {
                localStorage.clear();
                sessionStorage.clear();
                window.location.reload();
              }}
              className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
            >
              Очистить кэш
            </button>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">Проверка соединения</h4>
            <p className="text-gray-600 mb-4">Тест подключения к Supabase</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Перезагрузить
            </button>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">Экспорт логов</h4>
            <p className="text-gray-600 mb-4">Скачать логи для анализа</p>
            <button
              onClick={() => {
                const logs = JSON.stringify(console, null, 2);
                const blob = new Blob([logs], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `system-logs-${new Date().toISOString()}.json`;
                a.click();
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              Скачать логи
            </button>
          </div>
        </div>

        {/* Логи восстановления */}
        {logs.length > 0 && (
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-3">Логи восстановления</h4>
            <div className="max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="flex items-start py-1 text-sm">
                  <span className="text-gray-500 mr-2">{log.timestamp}</span>
                  <span
                    className={`${
                      log.type === 'error'
                        ? 'text-red-600'
                        : log.type === 'success'
                          ? 'text-green-600'
                          : log.type === 'warning'
                            ? 'text-yellow-600'
                            : 'text-gray-600'
                    }`}
                  >
                    {log.message}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>Диагностика системы - Панель администратора</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">🔬 Диагностика системы</h1>
          <p className="text-gray-600">
            Комплексная проверка состояния базы данных и функциональности приложения
          </p>
        </div>

        {/* Предупреждение */}
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start">
            <span className="text-yellow-600 text-xl mr-3">⚠️</span>
            <div>
              <h3 className="font-semibold text-yellow-800">Важно</h3>
              <p className="text-yellow-700">
                Эти инструменты предназначены для диагностики и устранения проблем. Некоторые тесты
                могут изменять данные. Используйте с осторожностью в продакшене.
              </p>
            </div>
          </div>
        </div>

        {/* Навигация по вкладкам */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Описание активной вкладки */}
          <div className="mt-4 p-3 bg-gray-50 rounded">
            <p className="text-sm text-gray-600">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Содержимое вкладок */}
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'health' && <DatabaseHealthCheck />}
          {activeTab === 'functionality' && <FunctionalityTester />}
          {activeTab === 'diagnostic' && <DatabaseDiagnostic />}
          {activeTab === 'repair' && <RepairTools />}
        </div>

        {/* Информация о системе */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">🌐 Информация о среде</h4>
            <div className="text-sm space-y-1">
              <div>Node.js: {process.env.NODE_ENV}</div>
              <div>React: {React.version}</div>
              <div>Время: {new Date().toLocaleString()}</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">🔧 Переменные окружения</h4>
            <div className="text-sm space-y-1">
              <div>
                Supabase URL: {process.env.REACT_APP_SUPABASE_URL ? '✅ Настроен' : '❌ Не найден'}
              </div>
              <div>
                Supabase Key:{' '}
                {process.env.REACT_APP_SUPABASE_ANON_KEY ? '✅ Настроен' : '❌ Не найден'}
              </div>
              <div>
                Service Role:{' '}
                {process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY ? '✅ Настроен' : '❌ Не найден'}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-4">
            <h4 className="font-semibold mb-2">📊 Статистика</h4>
            <div className="text-sm space-y-1">
              <div>
                Память: {(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(1) || 'N/A'} MB
              </div>
              <div>User Agent: {navigator.userAgent.split(' ')[0]}</div>
              <div>Онлайн: {navigator.onLine ? '✅' : '❌'}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SystemDiagnosticsPage;
