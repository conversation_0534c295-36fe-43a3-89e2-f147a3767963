import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-toastify';
import { supabase } from '../../supabaseClient';
import ProductForm from '../../components/admin/ProductForm';

const EditProduct = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProductData = async () => {
      try {
        // Validate ID
        if (!id) {
          throw new Error('No product ID provided');
        }

        // Get current auth state
        const {
          data: { session },
          error: authError
        } = await supabase.auth.getSession();

        // Fetch product with all related data
        const { data: productData, error: productError } = await supabase
          .from('products')
          .select(
            `
            *,
            category:categories(id, name),
            brand:brands(id, name),
            params:product_params(*)
          `
          )
          .eq('id', id)
          .single();

        if (productError) {
          throw productError;
        }

        if (!productData) {
          toast.error(t('product_not_found', 'Товар не найден'));
          navigate('/admin/products');
          return;
        }

        // Parse JSON fields
        let processedData = {
          ...productData,
          image_gallery: productData.image_gallery ? JSON.parse(productData.image_gallery) : [],
          tags: productData.tags ? JSON.parse(productData.tags) : [],
          category_id: productData.category?.id || '',
          brand_id: productData.brand?.id || '',
          params: productData.params || []
        };
        setProduct(processedData);
      } catch (error) {
        console.error('Error fetching product:', error);
        toast.error(t('error_loading_product', 'Ошибка при загрузке товара'));
        navigate('/admin/products');
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [id, navigate, t]);

  const handleSuccess = () => {
    toast.success(t('product_updated', 'Товар успешно обновлен'));
    navigate('/admin/products');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl text-gray-600">{t('loading', 'Загрузка...')}</div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>
          {t('edit_product', 'Редактирование товара')} | {product?.name}
        </title>
      </Helmet>

      <div className="container mx-auto px-4 py-6">
        <ProductForm product={product} isNew={false} onSuccess={handleSuccess} />
      </div>
    </>
  );
};

export default EditProduct;
