import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import BrandForm from '../../components/admin/BrandForm';
import LoadingSpinner from '../../components/LoadingSpinner';

const BrandEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [brand, setBrand] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBrand = async () => {
      try {
        if (!id) {
          throw new Error('Brand ID is missing');
        }

        const { data, error } = await supabase.from('brands').select('*').eq('id', id).single();

        if (error) throw error;

        if (!data) {
          throw new Error(`Бренд с ID ${id} не найден`);
        }

        // Ensure all required fields are present
        const processedData = {
          id: data.id,
          name: data.name || '',
          logo_url: data.logo_url || '',
          website_url: data.website_url || '',
          position: data.position || 1,
          active: typeof data.active === 'boolean' ? data.active : true
        };
        setBrand(processedData);
        setError(null);
      } catch (error) {
        console.error('Error fetching brand:', error);
        setError(error.message);
        toast.error(`Ошибка загрузки бренда: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchBrand();
  }, [id]);

  const handleSuccess = () => {
    navigate('/admin/brands');
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6 text-red-600">Ошибка загрузки бренда</h1>
        <p className="mb-4">{error}</p>
        <button
          onClick={() => navigate('/admin/brands')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Вернуться к списку брендов
        </button>
      </div>
    );
  }

  if (!brand) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Бренд не найден</h1>
        <button
          onClick={() => navigate('/admin/brands')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Вернуться к списку брендов
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">{t('edit_brand', 'Редактирование бренда')}</h1>
      <BrandForm brand={brand} onSuccess={handleSuccess} />
    </div>
  );
};

export default BrandEdit;
