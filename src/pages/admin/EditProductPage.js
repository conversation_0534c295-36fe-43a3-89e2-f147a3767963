import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../../supabaseClient';

// Добавим обертку try/catch вокруг основного кода компонента
const EditProductPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [product, setProduct] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setIsLoading(true);

        const { data, error } = await supabase.from('products').select('*').eq('id', id).single();

        if (error) {
          console.error('Error fetching product:', error);
          setError(`Ошибка при получении данных товара: ${error.message}`);
          return;
        }

        if (!data) {
          console.error('Product not found');
          setError(`Товар с ID ${id} не найден`);
          return;
        }
        setProduct(data);
      } catch (err) {
        console.error('Unexpected error:', err);
        setError(`Неожиданная ошибка: ${err.message}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  // Вместо показа общей ошибки, отобразим подробную информацию
  if (error) {
    return (
      <div className="error-container">
        <h2>Произошла ошибка</h2>
        <p>{error}</p>
        <pre>{JSON.stringify(error, null, 2)}</pre>
        <button onClick={() => navigate('/admin/products')}>Вернуться к списку товаров</button>
      </div>
    );
  }

  if (isLoading) {
    return <div>Загрузка...</div>;
  }

  if (!product) {
    return <div>Товар не найден</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Редактирование товара</h1>
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">{product.name}</h2>
        <p className="text-gray-600 mb-4">ID: {product.id}</p>

        {/* Здесь будет форма редактирования */}
        <div className="flex justify-end mt-6">
          <button
            className="px-4 py-2 bg-gray-200 rounded mr-2"
            onClick={() => navigate('/admin/products')}
          >
            Отмена
          </button>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded"
            onClick={() => navigate(`/admin/products/edit-full/${id}`)}
          >
            Перейти к полному редактированию
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditProductPage;
