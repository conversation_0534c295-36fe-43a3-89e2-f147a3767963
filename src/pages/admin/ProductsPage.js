import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { TABLES } from '../../config/constants';
import { AdminLayout } from '../../components/admin/Layout';
import { toast } from 'react-toastify';
import { FaPlus, FaEdit, FaTrash, FaSearch, FaEye } from 'react-icons/fa';

const ProductsPage = () => {
  const { t } = useTranslation();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const productsPerPage = 20;

  const fetchProducts = useCallback(
    async (page = 1, searchQuery = '') => {
      setLoading(true);

      try {
        let query = supabase.from(TABLES.PRODUCTS).select(
          `
          *,
          category:categories(id, name)
        `,
          { count: 'exact' }
        );

        // Добавляем поиск, если есть строка поиска
        if (searchQuery) {
          query = query.ilike('name', `%${searchQuery}%`);
        }

        // Пагинация
        const startRow = (page - 1) * productsPerPage;
        const { data, count, error } = await query
          .order('updated_at', { ascending: false })
          .range(startRow, startRow + productsPerPage - 1);

        if (error) throw error;

        setProducts(data || []);
        setTotalProducts(count || 0);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast.error(t('error_loading_products', 'Ошибка при загрузке товаров'));
      } finally {
        setLoading(false);
      }
    },
    [t]
  );

  useEffect(() => {
    fetchProducts(currentPage, search);
  }, [fetchProducts, currentPage, search]);

  const handleSearch = e => {
    e.preventDefault();
    setCurrentPage(1); // Сбрасываем на первую страницу
    fetchProducts(1, search);
  };

  const handleDelete = async (productId, productName) => {
    if (window.confirm(t('confirm_delete_product', `Удалить товар "${productName}"?`))) {
      try {
        const { error } = await supabase.from(TABLES.PRODUCTS).delete().eq('id', productId);

        if (error) throw error;

        toast.success(t('product_deleted', 'Товар удален'));
        fetchProducts(currentPage, search);
      } catch (error) {
        console.error('Error deleting product:', error);
        toast.error(t('error_deleting_product', 'Ошибка при удалении товара'));
      }
    }
  };

  const totalPages = Math.ceil(totalProducts / productsPerPage);

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">{t('products_management', 'Управление товарами')}</h1>
          <Link
            to="/admin/products/new"
            className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark flex items-center"
          >
            <FaPlus className="mr-2" />
            {t('add_product', 'Добавить товар')}
          </Link>
        </div>

        {/* Поиск */}
        <div className="mb-6 bg-white p-4 rounded-md shadow">
          <form onSubmit={handleSearch} className="flex">
            <input
              type="text"
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="flex-grow px-3 py-2 border rounded-l"
              placeholder={t('search_products', 'Поиск товаров...')}
            />
            <button
              type="submit"
              className="bg-primary text-white px-4 py-2 rounded-r hover:bg-primary-dark"
            >
              <FaSearch />
            </button>
          </form>
        </div>

        {/* Таблица товаров */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : products.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              {search
                ? t('no_products_found', 'Товары не найдены')
                : t('no_products', 'Нет товаров')}
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('product_name', 'Название товара')}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('category', 'Категория')}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('price', 'Цена')}
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {t('stock', 'Остаток')}
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">{t('actions', 'Действия')}</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products.map(product => (
                      <tr key={product.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0">
                              {product.image ? (
                                <img
                                  className="h-10 w-10 rounded object-cover"
                                  src={product.image}
                                  alt={product.name}
                                  onError={e => {
                                    e.target.onerror = null;
                                    e.target.src = 'https://placehold.co/40x40/EEE/31343C?text=Img';
                                  }}
                                />
                              ) : (
                                <div className="h-10 w-10 rounded bg-gray-200 flex items-center justify-center text-gray-500">
                                  <span className="text-xs">Нет фото</span>
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {product.name}
                              </div>
                              <div className="text-sm text-gray-500">{product.sku || '-'}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {product.category?.name || '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{product.price} ₴</div>
                          {product.original_price && (
                            <div className="text-xs text-gray-500 line-through">
                              {product.original_price} ₴
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              product.stock > product.min_stock
                                ? 'bg-green-100 text-green-800'
                                : product.stock > 0
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {product.stock}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <Link
                              to={`/product/${product.id}`}
                              className="text-blue-600 hover:text-blue-900"
                              target="_blank"
                            >
                              <FaEye />
                            </Link>
                            <Link
                              to={`/admin/products/edit/${product.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              <FaEdit />
                            </Link>
                            <button
                              onClick={() => handleDelete(product.id, product.name)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Пагинация */}
              {totalPages > 1 && (
                <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                  <div>
                    <p className="text-sm text-gray-700">
                      {t('showing', 'Показано')}{' '}
                      <span className="font-medium">{(currentPage - 1) * productsPerPage + 1}</span>{' '}
                      {t('to', 'по')}{' '}
                      <span className="font-medium">
                        {Math.min(currentPage * productsPerPage, totalProducts)}
                      </span>{' '}
                      {t('of', 'из')} <span className="font-medium">{totalProducts}</span>{' '}
                      {t('products', 'товаров')}
                    </p>
                  </div>
                  <div>
                    <nav
                      className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                      aria-label="Pagination"
                    >
                      <button
                        onClick={() => setCurrentPage(curr => Math.max(curr - 1, 1))}
                        disabled={currentPage === 1}
                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 ${
                          currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                        }`}
                      >
                        &laquo;
                      </button>

                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        // Логика для отображения страниц вокруг текущей
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                              currentPage === pageNum
                                ? 'bg-primary text-white'
                                : 'text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}

                      <button
                        onClick={() => setCurrentPage(curr => Math.min(curr + 1, totalPages))}
                        disabled={currentPage === totalPages}
                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 ${
                          currentPage === totalPages
                            ? 'opacity-50 cursor-not-allowed'
                            : 'hover:bg-gray-50'
                        }`}
                      >
                        &raquo;
                      </button>
                    </nav>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default ProductsPage;
