import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
// import AdminLayout from '../components/AdminLayout'; // Removed AdminLayout
import { toast } from 'react-toastify';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaEyeSlash,
  FaSyncAlt,
  FaWrench,
  FaDatabase
} from 'react-icons/fa';
import LoadingSpinner from '../../components/LoadingSpinner'; // Corrected path
import DatabaseDiagnostic from '../../components/admin/DatabaseDiagnostic'; // Updated path

const BannerManagement = () => {
  const [banners, setBanners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showDiagnostic, setShowDiagnostic] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    fetchBanners();
  }, [refreshKey]);

  const fetchBanners = async () => {
    try {
      setLoading(true);

      // Direct query to the banners table
      const { data, error } = await supabase.from('banners').select('*').order('position');

      if (error) {
        console.error('Error fetching banners:', error);
        toast.error('Failed to load banners: ' + error.message);
        return;
      }

      // Process the data to normalize image URLs
      const normalizedBanners = (data || []).map(banner => {
        // Handle different image field names (image and image_url)
        const imageUrl = banner.image_url || banner.image || null;

        return {
          ...banner,
          // Ensure both image fields are available for compatibility
          image: imageUrl,
          image_url: imageUrl
        };
      });
      setBanners(normalizedBanners);
    } catch (error) {
      console.error('Error in banner loading:', error);
      toast.error('An error occurred while loading banners');
    } finally {
      setLoading(false);
    }
  };

  // Function to run a database fix
  const fixDatabaseStructure = async () => {
    try {
      setLoading(true);
      toast.info('Running database structure fix...');

      // Import the function dynamically to avoid circular dependencies
      const { checkAndCreateBannersTable } = await import('../../utils/initDatabase');
      const result = await checkAndCreateBannersTable();

      if (result) {
        toast.success('Database structure fixed successfully');
        // Refresh data
        setRefreshKey(prev => prev + 1);
      } else {
        toast.error('Failed to fix database structure');
      }
    } catch (error) {
      console.error('Error fixing database:', error);
      toast.error('Error fixing database: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const createSampleBanners = async () => {
    try {
      setLoading(true);

      // Creating more visually appealing banners with the updated color scheme
      const topBanner = {
        title: 'Quality Furniture Fittings',
        subtitle: 'Discover our range of premium hardware for your furniture projects',
        button_text: 'Shop Now',
        button_link: '/products',
        image:
          'https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80',
        image_url:
          'https://images.unsplash.com/photo-1556761175-5973dc0f32e7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80',
        position: 1,
        active: true
      };

      const bottomBanner = {
        title: 'Special Offers',
        subtitle: 'Limited time discounts on selected products',
        button_text: 'View Offers',
        button_link: '/sale',
        image:
          'https://images.unsplash.com/photo-1579202673506-ca3ce28943ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80',
        image_url:
          'https://images.unsplash.com/photo-1579202673506-ca3ce28943ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80',
        position: 6,
        active: true
      };

      // Add both banners
      const { error } = await supabase.from('banners').insert([topBanner, bottomBanner]).select();

      if (error) {
        toast.error('Error creating sample banners: ' + error.message);
        return;
      }

      toast.success('Sample banners created successfully!');
      fetchBanners(); // Refresh banner list
    } catch (error) {
      console.error('Error creating sample banners:', error);
      toast.error('An error occurred while creating sample banners');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBanner = async (id, title) => {
    if (window.confirm(`Вы действительно хотите удалить баннер "${title || 'без названия'}"?`)) {
      try {
        const { error } = await supabase.from('banners').delete().eq('id', id);

        if (error) throw error;

        toast.success('Баннер успешно удален');
        fetchBanners(); // Повторно загружаем список баннеров
      } catch (error) {
        console.error('Ошибка при удалении баннера:', error);
        toast.error('Ошибка при удалении баннера');
      }
    }
  };

  const toggleBannerActive = async (id, active) => {
    try {
      setLoading(true);
      const { error } = await supabase.from('banners').update({ active: !active }).eq('id', id);

      if (error) {
        console.error('Error toggling banner status:', error);
        toast.error('Не удалось изменить статус баннера');
      } else {
        fetchBanners();
        toast.success('Статус баннера успешно изменен');
      }
    } catch (error) {
      console.error('Error toggling banner active status:', error);
      toast.error('Произошла ошибка при изменении статуса баннера');
    } finally {
      setLoading(false);
    }
  };

  // Group banners by position (top/bottom)
  const topBanners = banners.filter(banner => banner.position <= 5);
  const bottomBanners = banners.filter(banner => banner.position > 5);

  return (
    // Removed AdminLayout wrapper. The content will be rendered within AdminDashboard's Outlet.
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Banner Management</h1>
        <div className="flex gap-2">
          <button
            onClick={fixDatabaseStructure}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md flex items-center"
          >
            <FaDatabase className="mr-2" /> Fix Database
          </button>
          <button
            onClick={() => setShowDiagnostic(!showDiagnostic)}
            className="bg-muted hover:bg-gray-500 text-white px-4 py-2 rounded-md flex items-center"
          >
            <FaWrench className="mr-2" /> {showDiagnostic ? 'Hide Diagnostic' : 'Show Diagnostic'}
          </button>
          <button
            onClick={createSampleBanners}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center"
          >
            <FaSyncAlt className="mr-2" /> Create Sample Banners
          </button>
          <Link
            to="/admin/banners/create"
            className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-md flex items-center"
          >
            <FaPlus className="mr-2" /> Add Banner
          </Link>
        </div>
      </div>

      {showDiagnostic && <DatabaseDiagnostic />}

      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden mb-8">
            <div className="p-4 border-b">
              <h2 className="font-semibold text-lg">Верхние баннеры (позиции 1-5)</h2>
            </div>

            {topBanners.length > 0 ? (
              <div className="overflow-x-auto">
                {/* Таблица с верхними баннерами - без изменений */}
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Превью
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Заголовок
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Позиция
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Статус
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Действия
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {topBanners.map(banner => (
                      <tr key={banner.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {banner.image_url || banner.image ? (
                            <img
                              src={banner.image_url || banner.image}
                              alt={banner.title || 'Banner'}
                              className="h-16 w-32 object-cover rounded"
                              onError={e => {
                                e.target.onerror = null;
                                e.target.src =
                                  'https://placehold.co/320x160/EEE/31343C?text=No+Image';
                              }}
                            />
                          ) : (
                            <div className="h-16 w-32 bg-cardBackground flex items-center justify-center rounded">
                              Нет изображения
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {banner.title || 'Без заголовка'}
                          </div>
                          {banner.subtitle && (
                            <div className="text-xs text-gray-500">{banner.subtitle}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {banner.position}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {banner.active ? (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              Активен
                            </span>
                          ) : (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                              Неактивен
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-3">
                            <button
                              onClick={() => toggleBannerActive(banner.id, banner.active)}
                              className={`${banner.active ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'}`}
                              title={banner.active ? 'Деактивировать' : 'Активировать'}
                            >
                              {banner.active ? <FaEyeSlash /> : <FaEye />}
                            </button>
                            <Link
                              to={`/admin/banners/edit/${banner.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                              title="Редактировать"
                            >
                              <FaEdit />
                            </Link>
                            <button
                              onClick={() => handleDeleteBanner(banner.id, banner.title)}
                              className="text-red-600 hover:text-red-900"
                              title="Удалить"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Верхние баннеры отсутствуют.{' '}
                <button onClick={createSampleBanners} className="text-blue-500 hover:underline">
                  Создать тестовые баннеры
                </button>
              </div>
            )}
          </div>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="p-4 border-b">
              <h2 className="font-semibold text-lg">Нижние баннеры (позиции 6-10)</h2>
            </div>

            {bottomBanners.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Превью
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Заголовок
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Позиция
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Статус
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Действия
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {bottomBanners.map(banner => (
                      <tr key={banner.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {banner.image_url || banner.image ? (
                            <img
                              src={banner.image_url || banner.image}
                              alt={banner.title || 'Banner'}
                              className="h-16 w-32 object-cover rounded"
                              onError={e => {
                                e.target.onerror = null;
                                e.target.src =
                                  'https://placehold.co/320x160/EEE/31343C?text=No+Image';
                              }}
                            />
                          ) : (
                            <div className="h-16 w-32 bg-cardBackground flex items-center justify-center rounded">
                              Нет изображения
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {banner.title || 'Без заголовка'}
                          </div>
                          {banner.subtitle && (
                            <div className="text-xs text-gray-500">{banner.subtitle}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {banner.position}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {banner.active ? (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              Активен
                            </span>
                          ) : (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                              Неактивен
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-3">
                            <button
                              onClick={() => toggleBannerActive(banner.id, banner.active)}
                              className={`${banner.active ? 'text-gray-600 hover:text-gray-900' : 'text-green-600 hover:text-green-900'}`}
                              title={banner.active ? 'Деактивировать' : 'Активировать'}
                            >
                              {banner.active ? <FaEyeSlash /> : <FaEye />}
                            </button>
                            <Link
                              to={`/admin/banners/edit/${banner.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                              title="Редактировать"
                            >
                              <FaEdit />
                            </Link>
                            <button
                              onClick={() => handleDeleteBanner(banner.id, banner.title)}
                              className="text-red-600 hover:text-red-900"
                              title="Удалить"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">Нижние баннеры отсутствуют.</div>
            )}
          </div>

          <div className="mt-8 bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-blue-800 mb-2">Как работают баннеры</h3>
            <ul className="list-disc list-inside text-blue-700 space-y-1 ml-4">
              <li>Верхние баннеры (позиции 1-5) отображаются в верхней части главной страницы</li>
              <li>Нижние баннеры (позиции 6-10) отображаются в середине главной страницы</li>
              <li>Только активные баннеры будут показаны на сайте</li>
              <li>Позиция определяет порядок отображения (меньшая позиция показывается раньше)</li>
            </ul>
          </div>
        </>
      )}
    </> // Removed AdminLayout wrapper
  );
};

export default BannerManagement;
