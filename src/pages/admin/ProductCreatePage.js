/* eslint-disable prettier/prettier */
/* eslint-disable prettier/prettier */
/* eslint-disable prettier/prettier */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { supabase, convertToBigInt } from '../../supabaseClient';
// Import required icons, removing unused FaRegStar
import { FaImage, FaStar, FaTrash, FaArrowUp, FaArrowDown } from 'react-icons/fa';
import LoadingSpinner from '../../components/LoadingSpinner';
import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar';

const ProductCreatePage = () => {
  const navigate = useNavigate();
  const [fields, setFields] = useState({
    name: '',
    description: '',
    price: '',
    stock: '0',
    category_id: '',
    image: '',
    sku: '',
    vendor: '',
    is_active: true,
    is_featured: false,
    is_new: false,
    is_on_sale: false,
    is_bestseller: false,
    image_gallery: []
  });
  const [productParams, setProductParams] = useState([]);
  const [newParam, setNewParam] = useState({ name: '', value: '' });
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [galleryUrls, setGalleryUrls] = useState([]);
  const [newGalleryUrl, setNewGalleryUrl] = useState('');

  // Define the gallery management functions
  const addGalleryImage = () => {
    if (newGalleryUrl && newGalleryUrl.trim()) {
      setGalleryUrls([...galleryUrls, newGalleryUrl.trim()]);
      setNewGalleryUrl('');
    }
  };

  const removeGalleryImage = index => {
    const updatedGallery = [...galleryUrls];
    updatedGallery.splice(index, 1);
    setGalleryUrls(updatedGallery);
  };

  const moveGalleryImage = (index, direction) => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === galleryUrls.length - 1)
    ) {
      return;
    }

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    const updatedGallery = [...galleryUrls];
    const temp = updatedGallery[index];
    updatedGallery[index] = updatedGallery[newIndex];
    updatedGallery[newIndex] = temp;
    setGalleryUrls(updatedGallery);
  };

  const setMainImage = url => {
    // Set the selected image as main and move it from gallery to main image
    setFields({
      ...fields,
      image: url
    });

    // Remove from gallery
    const updatedGallery = galleryUrls.filter(item => item !== url);
    setGalleryUrls(updatedGallery);
  };

  // Load available categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const { data, error } = await supabase.from('categories').select('id, name').order('name');
        if (error) throw error;
        setCategories(data || []);
      } catch (error) {
        console.error('Error loading categories:', error);
        toast.error('Не удалось загрузить категории');
      } finally {
        setLoading(false);
      }
    };

    loadCategories();
  }, []);

  const handleChange = e => {
    const { name, value, type, checked } = e.target;
    setFields(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle product parameters change
  const handleParamChange = (index, field, value) => {
    const updatedParams = [...productParams];
    updatedParams[index][field] = value;
    setProductParams(updatedParams);
  };

  // Add new parameter
  const addNewParam = () => {
    if (!newParam.name || !newParam.value) {
      toast.warning('Необходимо указать название и значение характеристики');
      return;
    }

    setProductParams([
      ...productParams,
      {
        name: newParam.name,
        value: newParam.value
      }
    ]);

    setNewParam({ name: '', value: '' });
  };

  // Remove parameter
  const removeParam = index => {
    const updatedParams = [...productParams];
    updatedParams.splice(index, 1);
    setProductParams(updatedParams);
  };

  // Handle image file change
  const handleImageChange = e => {
    const file = e.target.files[0];
    if (!file) return;

    setImageFile(file);

    // Image preview
    const reader = new FileReader();
    reader.onload = () => {
      setImagePreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  // Upload image to storage
  const uploadImage = async () => {
    if (!imageFile) return fields.image;

    setUploadProgress(0);

    try {
      // Create a data URL as temporary preview
      const reader = new FileReader();
      return new Promise(resolve => {
        reader.onload = () => {
          // Show 100% progress
          setUploadProgress(100);

          // Display toast with fallback suggestion
          toast.info(
            <div>
              <p>Функция загрузки файлов недоступна.</p>
              <p>Используйте внешний хостинг изображений (например, imgur.com или imgbb.com)</p>
              <a
                href="https://imgbb.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:underline"
              >
                imgbb.com
              </a>
            </div>,
            { autoClose: 8000 }
          );

          // Return either the existing image URL or empty string
          resolve(fields.image || '');
        };
        reader.readAsDataURL(imageFile);
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Ошибка загрузки изображения');
      return fields.image || '';
    }
  };

  const handleSave = async e => {
    e.preventDefault();
    setSaving(true);

    try {
      // 1. Upload image if selected
      let imageUrl = fields.image;
      if (imageFile) {
        imageUrl = await uploadImage();
      }

      // Check existing products for schema structure
      const { data: productSample, error: sampleError } = await supabase
        .from('products')
        .select('*')
        .limit(1);

      // Determine available columns
      let availableColumns = [];
      if (!sampleError && productSample && productSample.length > 0) {
        availableColumns = Object.keys(productSample[0]);
      }

      // 2. Prepare the product data for saving
      const productData = {
        name: fields.name,
        description: fields.description,
        price: parseFloat(fields.price) || 0,
        category_id: fields.category_id,
        image: imageUrl,
        sku: fields.sku,
        vendor: fields.vendor,
        is_active: fields.is_active,
        is_featured: fields.is_featured,
        is_new: fields.is_new,
        is_on_sale: fields.is_on_sale,
        is_bestseller: fields.is_bestseller,
        image_gallery: galleryUrls
      };

      // Add fields only if they exist in the schema
      if (availableColumns.length === 0 || availableColumns.includes('description'))
        productData.description = fields.description;

      if (availableColumns.length === 0 || availableColumns.includes('price'))
        productData.price = parseFloat(fields.price) || 0;

      if (availableColumns.length === 0 || availableColumns.includes('image'))
        productData.image = imageUrl;

      if (availableColumns.length === 0 || availableColumns.includes('category_id'))
        productData.category_id = fields.category_id;

      if (availableColumns.length === 0 || availableColumns.includes('vendor'))
        productData.vendor = fields.vendor;

      if (availableColumns.length === 0 || availableColumns.includes('is_on_sale'))
        productData.is_on_sale = fields.is_on_sale;

      if (availableColumns.length === 0 || availableColumns.includes('is_new'))
        productData.is_new = fields.is_new;

      if (availableColumns.length === 0 || availableColumns.includes('is_bestseller'))
        productData.is_bestseller = fields.is_bestseller;

      if (availableColumns.length === 0 || availableColumns.includes('sku'))
        productData.sku = fields.sku || null;

      if (availableColumns.length === 0 || availableColumns.includes('original_price'))
        productData.original_price = parseFloat(fields.original_price) || null;

      // Handle stock/stock_quantity field variations
      if (availableColumns.length === 0 || availableColumns.includes('stock_quantity'))
        productData.stock_quantity = parseInt(fields.stock_quantity, 10) || 0;
      else if (availableColumns.includes('stock'))
        productData.stock = parseInt(fields.stock, 10) || 0;

      if (availableColumns.length === 0 || availableColumns.includes('display_order'))
        productData.display_order = parseInt(fields.display_order, 10) || 0;

      if (availableColumns.length === 0 || availableColumns.includes('min_stock'))
        productData.min_stock = parseInt(fields.min_stock, 10) || 5;

      // 3. Insert new product
      const { data: newProduct, error: insertError } = await supabase
        .from('products')
        .insert([productData])
        .select()
        .single();

      if (insertError) {
        throw new Error(`Error creating product: ${insertError.message}`);
      }

      // 4. Add product parameters if any
      if (productParams.length > 0 && newProduct.id) {
        const paramsToInsert = productParams.map(param => ({
          product_id: convertToBigInt(newProduct.id), // Convert UUID to BigInt string for product_params table
          name: param.name,
          value: param.value
        }));

        const { error: paramsError } = await supabase.from('product_params').insert(paramsToInsert);

        if (paramsError) {
          console.error('Error adding product parameters:', paramsError);
          // Not considering this a critical error
        }
      }

      toast.success('Товар успешно создан');
      navigate('/admin/products');
    } catch (err) {
      console.error('Error saving product:', err);
      toast.error(`Ошибка при создании товара: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/products');
  };

  if (loading) return <LoadingSpinner />;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Создание нового товара</h1>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar */}
        <div className="lg:w-1/4">
          <AdminMenuSidebar activePath="/admin/products" />
        </div>

        {/* Product creation form */}
        <div className="lg:w-3/4">
          <form onSubmit={handleSave} className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left column - basic info */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic information */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <h2 className="text-xl font-semibold mb-4">Основная информация</h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block mb-1 font-medium">Название товара:</label>
                      <input
                        type="text"
                        name="name"
                        value={fields.name}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                        required
                      />
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Категория:</label>
                      <select
                        name="category_id"
                        value={fields.category_id}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                        required
                      >
                        <option value="">-- Выберите категорию --</option>
                        {categories.map(cat => (
                          <option key={cat.id} value={cat.id}>
                            {cat.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Производитель:</label>
                      <input
                        type="text"
                        name="vendor"
                        value={fields.vendor}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                      />
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Описание:</label>
                      <textarea
                        name="description"
                        value={fields.description}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                        rows="4"
                      />
                    </div>
                  </div>
                </div>

                {/* Price information */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <h2 className="text-xl font-semibold mb-4">Цены и наличие</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block mb-1 font-medium">Цена:</label>
                      <input
                        type="number"
                        step="0.01"
                        name="price"
                        value={fields.price}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                        required
                      />
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Старая цена:</label>
                      <input
                        type="number"
                        step="0.01"
                        name="original_price"
                        value={fields.original_price}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                      />
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Артикул (SKU):</label>
                      <input
                        type="text"
                        name="sku"
                        value={fields.sku}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                      />
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Количество на складе:</label>
                      <input
                        type="number"
                        name="stock"
                        value={fields.stock}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                      />
                    </div>

                    <div>
                      <label className="block mb-1 font-medium">Минимальный запас:</label>
                      <input
                        type="number"
                        name="min_stock"
                        value={fields.min_stock}
                        onChange={handleChange}
                        className="border p-2 w-full rounded"
                      />
                    </div>

                    <div className="flex items-center space-x-4 mt-3">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          name="is_on_sale"
                          checked={fields.is_on_sale}
                          onChange={handleChange}
                          className="form-checkbox"
                        />
                        <span className="ml-2">Со скидкой</span>
                      </label>

                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          name="is_new"
                          checked={fields.is_new}
                          onChange={handleChange}
                          className="form-checkbox"
                        />
                        <span className="ml-2">Новинка</span>
                      </label>

                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          name="is_bestseller"
                          checked={fields.is_bestseller}
                          onChange={handleChange}
                          className="form-checkbox"
                        />
                        <span className="ml-2">Хит продаж</span>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Product specifications */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <h2 className="text-xl font-semibold mb-4">Характеристики товара</h2>

                  {/* Existing specifications */}
                  {productParams.length > 0 ? (
                    <div className="mb-6">
                      <div className="grid grid-cols-[1fr,1fr,auto] gap-2 mb-2 font-medium">
                        <div>Характеристика</div>
                        <div>Значение</div>
                        <div></div>
                      </div>

                      {productParams.map((param, index) => (
                        <div key={index} className="grid grid-cols-[1fr,1fr,auto] gap-2 mb-2">
                          <input
                            type="text"
                            value={param.name}
                            onChange={e => handleParamChange(index, 'name', e.target.value)}
                            className="border p-2 w-full rounded"
                          />
                          <input
                            type="text"
                            value={param.value}
                            onChange={e => handleParamChange(index, 'value', e.target.value)}
                            className="border p-2 w-full rounded"
                          />
                          <button
                            type="button"
                            onClick={() => removeParam(index)}
                            className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                          >
                            ✕
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 mb-4">Добавьте характеристики товара</p>
                  )}

                  {/* New specification form */}
                  <div className="grid grid-cols-[1fr,1fr,auto] gap-2">
                    <input
                      type="text"
                      placeholder="Название характеристики"
                      value={newParam.name}
                      onChange={e => setNewParam({ ...newParam, name: e.target.value })}
                      className="border p-2 w-full rounded"
                    />
                    <input
                      type="text"
                      placeholder="Значение характеристики"
                      value={newParam.value}
                      onChange={e => setNewParam({ ...newParam, value: e.target.value })}
                      className="border p-2 w-full rounded"
                    />
                    <button
                      type="button"
                      onClick={addNewParam}
                      className="px-3 py-2 bg-primary text-white rounded hover:bg-primary-dark"
                    >
                      Добавить
                    </button>
                  </div>
                </div>
              </div>

              {/* Right column - image and preview */}
              <div className="space-y-6">
                {/* Image management */}
                <div className="bg-white p-6 rounded-lg shadow">
                  <h2 className="text-xl font-semibold mb-4">Изображение товара</h2>

                  {/* Current image or preview */}
                  <div className="mb-4">
                    <div className="aspect-w-1 aspect-h-1 w-full mb-4">
                      {imagePreview || fields.image ? (
                        <img
                          src={imagePreview || fields.image}
                          alt={fields.name || 'Предпросмотр изображения'}
                          className="w-full h-64 object-contain border rounded bg-gray-50"
                          onError={e => {
                            e.target.src = '/placeholder.png';
                          }}
                        />
                      ) : (
                        <div className="w-full h-64 flex items-center justify-center bg-gray-100 border rounded">
                          <span className="text-gray-400">Нет изображения</span>
                        </div>
                      )}
                    </div>

                    {/* Image upload field */}
                    <div>
                      <label className="block mb-1 font-medium">Загрузить изображение:</label>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="w-full"
                      />

                      {/* Image URL */}
                      <label className="block mt-4 mb-1 font-medium">
                        или вставить URL изображения:
                      </label>
                      <input
                        type="text"
                        name="image"
                        value={fields.image}
                        onChange={handleChange}
                        placeholder="https://example.com/image.jpg"
                        className="border p-2 w-full rounded"
                      />
                    </div>

                    {/* Upload progress */}
                    {uploadProgress > 0 && uploadProgress < 100 && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Загрузка: {uploadProgress}%</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Add new section for image gallery management */}
                <div className="mt-8 border-t pt-6">
                  <h3 className="text-lg font-medium mb-4">Галерея изображений товара</h3>

                  {/* Gallery input field */}
                  <div className="flex mb-2">
                    <input
                      type="text"
                      value={newGalleryUrl}
                      onChange={e => setNewGalleryUrl(e.target.value)}
                      placeholder="Вставьте URL изображения для галереи"
                      className="border p-2 flex-grow rounded-l"
                    />
                    <button
                      type="button"
                      onClick={addGalleryImage}
                      className="bg-blue-500 text-white px-4 rounded-r hover:bg-blue-600"
                    >
                      <FaImage className="inline mr-1" /> Добавить
                    </button>
                  </div>

                  {/* Gallery preview */}
                  {galleryUrls.length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mt-4">
                      {galleryUrls.map((url, index) => (
                        <div key={index} className="relative border rounded p-1 group">
                          <img
                            src={url}
                            alt={`Галерея ${index + 1}`}
                            className="h-24 w-full object-cover"
                            onError={e => {
                              e.target.src = '/placeholder.png';
                            }}
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <div className="flex gap-1">
                              <button
                                type="button"
                                onClick={() => setMainImage(url)}
                                className="p-1 bg-yellow-500 text-white rounded"
                                title="Сделать главным изображением"
                              >
                                <FaStar size={14} />
                              </button>
                              <button
                                type="button"
                                onClick={() => moveGalleryImage(index, 'up')}
                                disabled={index === 0}
                                className={`p-1 ${
                                  index === 0 ? 'bg-gray-300' : 'bg-blue-500'
                                } text-white rounded`}
                                title="Переместить вверх"
                              >
                                <FaArrowUp size={14} />
                              </button>
                              <button
                                type="button"
                                onClick={() => moveGalleryImage(index, 'down')}
                                disabled={index === galleryUrls.length - 1}
                                className={`p-1 ${
                                  index === galleryUrls.length - 1 ? 'bg-gray-300' : 'bg-blue-500'
                                } text-white rounded`}
                                title="Переместить вниз"
                              >
                                <FaArrowDown size={14} />
                              </button>
                              <button
                                type="button"
                                onClick={() => removeGalleryImage(index)}
                                className="p-1 bg-red-500 text-white rounded"
                                title="Удалить"
                              >
                                <FaTrash size={14} />
                              </button>
                            </div>
                          </div>
                          <div className="bg-gray-100 text-xs p-1">
                            {/* Show the first 20 characters of the URL */}
                            {url.substring(0, 20)}...
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm italic mt-2">
                      Нет дополнительных изображений. Добавьте изображения в галерею.
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex justify-end gap-4 mt-8">
              <button
                type="button"
                onClick={handleCancel}
                className="px-6 py-2 border rounded hover:bg-gray-100"
              >
                Отмена
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
              >
                {saving ? 'Создание...' : 'Создать товар'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ProductCreatePage;
