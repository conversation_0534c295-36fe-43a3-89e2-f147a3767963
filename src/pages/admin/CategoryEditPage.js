import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar';

const CategoryEditPage = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState([]);
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  const [category, setCategory] = useState({
    name: '',
    description: '',
    parent_id: null,
    image: '',
    meta_title: '',
    meta_description: '',
    slug: '',
    display_order: 0,
    is_active: true,
    is_featured: false
  });

  // Загружаем данные категории и список всех категорий
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Загружаем данные редактируемой категории
        const { data: categoryData, error: categoryError } = await supabase
          .from('categories')
          .select('*')
          .eq('id', id)
          .single();

        if (categoryError) {
          toast.error(t('category_not_found', 'Категория не найдена'));
          navigate('/admin/categories');
          return;
        }

        // Set the category data with safe fallbacks for all fields
        setCategory({
          name: categoryData.name || '',
          description: categoryData.description || '',
          parent_id: categoryData.parent_id || null,
          image: categoryData.image || '',
          meta_title: categoryData.meta_title || '',
          meta_description: categoryData.meta_description || '',
          slug: categoryData.slug || '',
          display_order: categoryData.display_order !== undefined ? categoryData.display_order : 0,
          is_active: categoryData.is_active !== undefined ? categoryData.is_active : true,
          is_featured: categoryData.is_featured !== undefined ? categoryData.is_featured : false // Add support for is_featured field
        });

        setImagePreview(categoryData.image || '');

        // Загружаем список всех категорий для выбора родительской
        const { data: allCategories, error: categoriesError } = await supabase
          .from('categories')
          .select('*')
          .order('name');

        if (!categoriesError) {
          // Фильтруем текущую категорию и её потомков
          const filteredCategories = allCategories.filter(cat => cat.id !== id);
          setCategories(filteredCategories);
        }
      } catch (error) {
        console.error('Error loading category data:', error);
        toast.error(t('error_loading_data', 'Ошибка при загрузке данных'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, navigate, t]);

  const handleChange = e => {
    const { name, value, type, checked } = e.target;
    setCategory(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Обработчик изменения файла изображения
  const handleImageChange = e => {
    const file = e.target.files[0];
    if (!file) return;

    setImageFile(file);

    // Предпросмотр изображения
    const reader = new FileReader();
    reader.onload = () => {
      setImagePreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  // Загрузка изображения в хранилище
  const uploadImage = async () => {
    if (!imageFile) return category.image;

    setUploadProgress(0);

    try {
      // Create FormData object for the file upload
      const formData = new FormData();
      formData.append('image', imageFile);

      // Send the file to your custom server endpoint
      const response = await fetch('https://your-server.com/api/upload', {
        method: 'POST',
        body: formData,
        onUploadProgress: progress => {
          setUploadProgress(Math.round((progress.loaded / progress.total) * 100));
        }
      });

      if (!response.ok) throw new Error('Upload failed');

      // Get the image URL from response
      const data = await response.json();
      return data.imageUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(t('error_uploading_image', 'Ошибка загрузки изображения'));
      return category.image || '';
    }
  };

  const handleSave = async e => {
    e.preventDefault();
    setSaving(true);

    try {
      // Загружаем изображение, если оно было изменено
      let imageUrl = category.image;
      if (imageFile) {
        imageUrl = await uploadImage();

        // If the image URL is empty or a data URL, warn the user
        if (!imageUrl || imageUrl.startsWith('data:')) {
          toast.warning(
            t(
              'using_external_image',
              'Пожалуйста, используйте URL внешнего изображения в поле "URL изображения"'
            )
          );
        }
      }

      // First fetch the current category to determine available columns
      const { data: currentCategory, error: fetchError } = await supabase
        .from('categories')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('Error fetching category for schema:', fetchError);
        throw new Error('Could not fetch category schema');
      }

      // Determine which columns are available in the database
      const availableColumns = Object.keys(currentCategory);

      // Create an update object with only fields that exist in the database
      const updateData = { updated_at: new Date() };

      if (availableColumns.includes('name')) updateData.name = category.name;
      if (availableColumns.includes('description')) updateData.description = category.description;
      if (availableColumns.includes('parent_id')) updateData.parent_id = category.parent_id;
      if (availableColumns.includes('image')) updateData.image = imageUrl;
      if (availableColumns.includes('meta_title')) updateData.meta_title = category.meta_title;
      if (availableColumns.includes('meta_description'))
        updateData.meta_description = category.meta_description;
      if (availableColumns.includes('slug')) updateData.slug = category.slug;

      // Handle different naming conventions for is_active
      if (availableColumns.includes('is_active')) updateData.is_active = category.is_active;
      else if (availableColumns.includes('active')) updateData.active = category.is_active;

      // Handle different naming conventions for display_order
      if (availableColumns.includes('display_order'))
        updateData.display_order = category.display_order;
      else if (availableColumns.includes('order')) updateData.order = category.display_order;

      // Handle different naming conventions for is_featured
      if (availableColumns.includes('is_featured')) updateData.is_featured = category.is_featured;
      else if (availableColumns.includes('featured')) updateData.featured = category.is_featured;

      // Обновляем данные категории
      const { error: updateError } = await supabase
        .from('categories')
        .update(updateData)
        .eq('id', id);

      if (updateError) {
        console.error('Update error details:', updateError);
        throw updateError;
      }

      toast.success(t('category_updated', 'Категория успешно обновлена'));
      navigate('/admin/categories');
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error(t('error_updating_category', 'Ошибка при обновлении категории'));
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/categories');
  };

  const generateSlug = () => {
    const slug = category.name
      .toLowerCase()
      .replace(/[^\wа-яё\s]/gi, '')
      .replace(/\s+/g, '-');

    setCategory(prev => ({ ...prev, slug }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{t('edit_category', 'Редактирование категории')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-semibold">
            {t('edit_category', 'Редактирование категории')}
          </h1>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Боковое меню админки */}
          <div className="lg:w-1/4">
            <AdminMenuSidebar activePath="/admin/categories" />
          </div>

          {/* Форма редактирования категории */}
          <div className="lg:w-3/4">
            <form onSubmit={handleSave} className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Основная информация */}
                <div className="lg:col-span-2 space-y-6">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">
                      {t('basic_info', 'Основная информация')}
                    </h2>
                    <div className="space-y-4">
                      <div>
                        <label className="block mb-1 font-medium">
                          {t('category_name', 'Название категории')}:
                        </label>
                        <input
                          type="text"
                          name="name"
                          value={category.name || ''}
                          onChange={handleChange}
                          className="border p-2 w-full rounded"
                          required
                        />
                      </div>

                      <div>
                        <label className="block mb-1 font-medium">
                          {t('parent_category', 'Родительская категория')}:
                        </label>
                        <select
                          name="parent_id"
                          value={category.parent_id || ''}
                          onChange={handleChange}
                          className="border p-2 w-full rounded"
                        >
                          <option value="">{t('no_parent', '-- Корневая категория --')}</option>
                          {categories.map(cat => (
                            <option key={cat.id} value={cat.id}>
                              {cat.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block mb-1 font-medium">
                          {t('description', 'Описание')}:
                        </label>
                        <textarea
                          name="description"
                          value={category.description || ''}
                          onChange={handleChange}
                          rows="4"
                          className="border p-2 w-full rounded"
                        />
                      </div>

                      <div>
                        <label className="block mb-1 font-medium">
                          {t('display_order', 'Порядок отображения')}:
                        </label>
                        <input
                          type="number"
                          name="display_order"
                          value={category.display_order || 0}
                          onChange={handleChange}
                          className="border p-2 w-full rounded"
                        />
                      </div>

                      <div className="flex items-center">
                        <input
                          id="is_active"
                          type="checkbox"
                          name="is_active"
                          checked={category.is_active}
                          onChange={handleChange}
                          className="h-4 w-4 mr-2"
                        />
                        <label htmlFor="is_active">{t('is_active', 'Активная категория')}</label>
                      </div>

                      <div className="flex items-center mt-3">
                        <input
                          id="is_featured"
                          type="checkbox"
                          name="is_featured"
                          checked={category.is_featured || false}
                          onChange={handleChange}
                          className="h-4 w-4 mr-2"
                        />
                        <label htmlFor="is_featured">
                          {t('is_featured', 'Избранная категория')}
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* SEO информация */}
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">
                      {t('seo_info', 'SEO информация')}
                    </h2>
                    <div className="space-y-4">
                      <div>
                        <label className="block mb-1 font-medium">{t('slug', 'URL-слаг')}:</label>
                        <div className="flex space-x-2">
                          <input
                            type="text"
                            name="slug"
                            value={category.slug || ''}
                            onChange={handleChange}
                            className="border p-2 flex-grow rounded"
                          />
                          <button
                            type="button"
                            onClick={generateSlug}
                            className="px-3 py-2 bg-gray-200 rounded hover:bg-gray-300"
                          >
                            {t('generate', 'Сгенерировать')}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block mb-1 font-medium">
                          {t('meta_title', 'Meta-заголовок')}:
                        </label>
                        <input
                          type="text"
                          name="meta_title"
                          value={category.meta_title || ''}
                          onChange={handleChange}
                          className="border p-2 w-full rounded"
                        />
                      </div>

                      <div>
                        <label className="block mb-1 font-medium">
                          {t('meta_description', 'Meta-описание')}:
                        </label>
                        <textarea
                          name="meta_description"
                          value={category.meta_description || ''}
                          onChange={handleChange}
                          rows="3"
                          className="border p-2 w-full rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Изображение категории */}
                <div className="space-y-6">
                  <div className="bg-white p-6 rounded-lg shadow">
                    <h2 className="text-xl font-semibold mb-4">
                      {t('category_image', 'Изображение категории')}
                    </h2>

                    <div className="mb-4">
                      <div className="aspect-w-1 aspect-h-1 w-full mb-4">
                        {imagePreview ? (
                          <img
                            src={imagePreview}
                            alt={category.name}
                            className="w-full h-64 object-contain border rounded"
                            onError={e => {
                              e.target.src = '/placeholder.png';
                            }}
                          />
                        ) : (
                          <div className="w-full h-64 flex items-center justify-center bg-gray-100 border rounded">
                            <span className="text-gray-400">
                              {t('no_image', 'Нет изображения')}
                            </span>
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block mb-1 font-medium">
                          {t('upload_image', 'Загрузить изображение')}:
                        </label>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageChange}
                          className="w-full"
                        />

                        <label className="block mt-4 mb-1 font-medium">
                          {t('image_url', 'или URL изображения')}:
                        </label>
                        <input
                          type="text"
                          name="image"
                          value={category.image || ''}
                          onChange={handleChange}
                          placeholder="https://example.com/image.jpg"
                          className="border p-2 w-full rounded"
                        />
                      </div>

                      {uploadProgress > 0 && uploadProgress < 100 && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="bg-blue-600 h-2.5 rounded-full"
                              style={{ width: `${uploadProgress}%` }}
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {t('upload_progress', 'Загрузка')}: {uploadProgress}%
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Кнопки действий */}
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-6 py-2 border rounded hover:bg-gray-100"
                >
                  {t('cancel', 'Отменить')}
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-6 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
                >
                  {saving ? t('saving', 'Сохранение...') : t('save', 'Сохранить')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default CategoryEditPage;
