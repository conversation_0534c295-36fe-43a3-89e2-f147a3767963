import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { Link } from 'react-router-dom';

const InventoryManagement = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [lowStockProducts, setLowStockProducts] = useState([]);
  const [filter, setFilter] = useState('all'); // all, low, zero
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 20;

  // Исправленная функция получения товаров
  const fetchProducts = useCallback(async () => {
    setLoading(true);
    try {
      // Базовый запрос
      let query = supabase
        .from('products')
        .select('id, name, price, stock, min_stock, category_id, image, vendor', { count: 'exact' })
        .order('name');

      // Применяем фильтры
      if (filter === 'low') {
        // ИСПРАВЛЕНО: Используем SQL запрос, т.к. в Supabase нет возможности сравнить два поля напрямую
        const { data: lowStockData, error: lowStockError } = await supabase.rpc('exec_sql', {
          query: `
            SELECT id, name, price, stock, min_stock, category_id, image, vendor
            FROM products
            WHERE stock < min_stock AND stock > 0
            ORDER BY name
          `
        });

        if (lowStockError) throw lowStockError;

        // Имитируем стандартный ответ с данными и подсчетом
        const filteredBySearch = searchQuery
          ? lowStockData.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()))
          : lowStockData;

        const paginatedData = filteredBySearch.slice((page - 1) * pageSize, page * pageSize);

        setProducts(paginatedData);
        setTotalPages(Math.ceil(filteredBySearch.length / pageSize));
        setLoading(false);
        return;
      } else if (filter === 'zero') {
        query = query.eq('stock', 0);
      }

      // Применяем поиск
      if (searchQuery) {
        query = query.ilike('name', `%${searchQuery}%`);
      }

      // Пагинация
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const { data, count, error } = await query;

      if (error) throw error;

      setProducts(data || []);
      setTotalPages(Math.ceil((count || 0) / pageSize));
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error(t('error_fetching_products', 'Ошибка при загрузке товаров'));
    } finally {
      setLoading(false);
    }
  }, [filter, page, searchQuery, pageSize, t]);

  // Исправленная функция получения товаров с малым запасом
  const fetchLowStockProducts = useCallback(async () => {
    try {
      // ИСПРАВЛЕНО: Используем SQL запрос для сравнения полей
      const { data, error } = await supabase.rpc('exec_sql', {
        query: `
          SELECT id, name, stock, min_stock
          FROM products
          WHERE stock < min_stock AND stock > 0
          ORDER BY name
          LIMIT 10
        `
      });

      if (error) throw error;

      setLowStockProducts(data || []);
    } catch (error) {
      console.error('Error fetching low stock products:', error);
    }
  }, []);

  // Инициализация таблицы и загрузка данных
  useEffect(() => {
    const setupInventoryColumns = async () => {
      try {
        // Создаем необходимые колонки, если их нет
        await supabase.rpc('exec_sql', {
          query: `
            DO $$ 
            BEGIN 
              BEGIN
                ALTER TABLE products ADD COLUMN IF NOT EXISTS stock INTEGER DEFAULT 0;
                ALTER TABLE products ADD COLUMN IF NOT EXISTS min_stock INTEGER DEFAULT 5;
              EXCEPTION
                WHEN duplicate_column THEN NULL;
              END;
            END $$;
          `
        });
      } catch (error) {
        console.error('Error setting up inventory columns:', error);
      }

      // Загружаем данные
      fetchProducts();
      fetchLowStockProducts();
    };

    setupInventoryColumns();
  }, [fetchProducts, fetchLowStockProducts]);

  // Use non-memoized version to avoid circular reference
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const updateProductStock = async (productId, newStock) => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ stock: newStock })
        .eq('id', productId);

      if (error) throw error;

      // Обновляем локальное состояние
      setProducts(
        products.map(product =>
          product.id === productId ? { ...product, stock: newStock } : product
        )
      );

      // Обновляем список товаров с малым запасом
      const updatedProduct = products.find(p => p.id === productId);
      if (updatedProduct) {
        const isLowStock = newStock > 0 && newStock < updatedProduct.min_stock;
        const wasInLowStock = lowStockProducts.some(p => p.id === productId);

        if (isLowStock && !wasInLowStock) {
          setLowStockProducts([...lowStockProducts, { ...updatedProduct, stock: newStock }]);
        } else if (!isLowStock && wasInLowStock) {
          setLowStockProducts(lowStockProducts.filter(p => p.id !== productId));
        } else if (isLowStock && wasInLowStock) {
          setLowStockProducts(
            lowStockProducts.map(p => (p.id === productId ? { ...p, stock: newStock } : p))
          );
        }
      }

      toast.success(t('stock_updated', 'Количество товара обновлено'));
    } catch (error) {
      console.error('Error updating product stock:', error);
      toast.error(t('error_updating_stock', 'Ошибка при обновлении количества'));
    }
  };

  const updateMinStock = async (productId, newMinStock) => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ min_stock: newMinStock })
        .eq('id', productId);

      if (error) throw error;

      // Обновляем локальное состояние
      setProducts(
        products.map(product =>
          product.id === productId ? { ...product, min_stock: newMinStock } : product
        )
      );

      toast.success(t('min_stock_updated', 'Минимальный запас обновлён'));

      // Проверяем, изменился ли статус товара (стал ли он товаром с малым запасом)
      const updatedProduct = products.find(p => p.id === productId);
      if (updatedProduct) {
        const isLowStock = updatedProduct.stock > 0 && updatedProduct.stock < newMinStock;
        const wasInLowStock = lowStockProducts.some(p => p.id === productId);

        if (isLowStock && !wasInLowStock) {
          setLowStockProducts([...lowStockProducts, updatedProduct]);
        } else if (!isLowStock && wasInLowStock) {
          setLowStockProducts(lowStockProducts.filter(p => p.id !== productId));
        }
      }
    } catch (error) {
      console.error('Error updating min stock:', error);
      toast.error(t('error_updating_min_stock', 'Ошибка при обновлении минимального запаса'));
    }
  };

  const batchUpdateStock = async () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.csv';

    fileInput.onchange = async e => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        setLoading(true);
        const reader = new FileReader();
        reader.onload = async event => {
          try {
            const csv = event.target.result;
            const lines = csv.split('\n');

            // Validate CSV structure
            const headers = lines[0].toLowerCase().split(',');
            const idColumnIndex = headers.indexOf('id');
            const stockColumnIndex = headers.indexOf('stock');
            const minStockColumnIndex = headers.indexOf('min_stock');

            if (idColumnIndex === -1 || stockColumnIndex === -1) {
              toast.error(
                t('invalid_csv_format', 'Неверный формат CSV. Необходимы столбцы: id, stock')
              );
              return;
            }

            const updates = [];
            const errors = [];

            // Process each line of the CSV
            for (let i = 1; i < lines.length; i++) {
              const line = lines[i].trim();
              if (!line) continue;

              const values = line.split(',');
              const productId = values[idColumnIndex]?.trim();
              const stock = parseInt(values[stockColumnIndex]);

              // Validate the data
              if (!productId) {
                errors.push(`Строка ${i}: Отсутствует ID товара`);
                continue;
              }

              if (isNaN(stock) && stockColumnIndex !== -1) {
                errors.push(`Строка ${i}: Некорректное значение остатка для товара ${productId}`);
                continue;
              }

              const updateData = { id: productId };

              // Only add valid fields to update
              if (!isNaN(stock)) {
                updateData.stock = stock;
              }

              // Add min_stock if it exists in the CSV
              if (minStockColumnIndex !== -1) {
                const minStock = parseInt(values[minStockColumnIndex]);
                if (!isNaN(minStock)) {
                  updateData.min_stock = minStock;
                }
              }

              updates.push(updateData);
            }

            if (errors.length > 0) {
              // Show first 5 errors
              const errorMessage =
                errors.slice(0, 5).join('\n') +
                (errors.length > 5 ? `\n...и еще ${errors.length - 5} ошибок` : '');
              toast.error(errorMessage);

              if (updates.length === 0) {
                return; // No valid updates to process
              }
            }

            if (updates.length > 0) {
              // Use batch operation instead of individual updates for better performance
              // Process in chunks of 100 to avoid payload size limits
              const chunkSize = 100;
              for (let i = 0; i < updates.length; i += chunkSize) {
                const chunk = updates.slice(i, i + chunkSize);

                const { error } = await supabase
                  .from('products')
                  .upsert(chunk, { onConflict: 'id' });

                if (error) {
                  console.error('Error batch updating products:', error);
                  toast.error(t('batch_update_error', 'Ошибка при массовом обновлении запасов'));
                  return;
                }
              }

              toast.success(
                t('batch_update_success_count', `Обновлено успешно: ${updates.length} товаров`)
              );

              // Refresh data
              fetchProducts();
              fetchLowStockProducts();
            } else {
              toast.warning(t('no_valid_updates', 'Нет действительных данных для обновления'));
            }
          } catch (error) {
            console.error('Error processing CSV:', error);
            toast.error(t('error_processing_csv', 'Ошибка при обработке CSV файла'));
          } finally {
            setLoading(false);
          }
        };

        reader.onerror = () => {
          toast.error(t('error_reading_file', 'Ошибка чтения файла'));
          setLoading(false);
        };

        reader.readAsText(file);
      } catch (error) {
        console.error('File processing error:', error);
        toast.error(t('file_processing_error', 'Ошибка обработки файла'));
        setLoading(false);
      }
    };

    fileInput.click();
  };

  return (
    <>
      <Helmet>
        <title>{t('inventory_management', 'Управление запасами - Админ панель')}</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="container mx-auto px-6 py-12">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold">
            {t('inventory_management', 'Управление запасами')}
          </h1>
          <div className="flex gap-4">
            <Link
              to="/admin"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              {t('back_to_dashboard', 'Вернуться к панели')}
            </Link>
          </div>
        </div>

        {/* Уведомления о малом запасе */}
        {lowStockProducts.length > 0 && (
          <div className="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h2 className="text-lg font-medium text-yellow-800 mb-2">
              {t('low_stock_warning', 'Внимание! Товары с малым запасом')}
            </h2>
            <ul className="space-y-1">
              {lowStockProducts.map(product => (
                <li key={product.id} className="flex items-center justify-between">
                  <span>{product.name}</span>
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                    {t('stock_count', 'Остаток')}: {product.stock} / {product.min_stock}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Фильтр и поиск */}
        <div className="mb-6 flex flex-col md:flex-row gap-4">
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">{t('filter', 'Фильтр')}:</span>
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  setFilter('all');
                  setPage(1);
                }}
                className={`px-3 py-1 rounded ${filter === 'all' ? 'bg-primary text-white' : 'bg-gray-200'}`}
              >
                {t('all_products', 'Все товары')}
              </button>
              <button
                onClick={() => {
                  setFilter('low');
                  setPage(1);
                }}
                className={`px-3 py-1 rounded ${filter === 'low' ? 'bg-primary text-white' : 'bg-gray-200'}`}
              >
                {t('low_stock', 'Малый запас')}
              </button>
              <button
                onClick={() => {
                  setFilter('zero');
                  setPage(1);
                }}
                className={`px-3 py-1 rounded ${filter === 'zero' ? 'bg-primary text-white' : 'bg-gray-200'}`}
              >
                {t('out_of_stock', 'Нет в наличии')}
              </button>
            </div>
          </div>

          <div className="flex-grow flex items-center space-x-2">
            <input
              type="text"
              placeholder={t('search_products', 'Поиск товаров...')}
              value={searchQuery}
              onChange={e => {
                setSearchQuery(e.target.value);
                setPage(1);
              }}
              className="border rounded p-2 flex-grow"
            />
            <button
              onClick={() => {
                setSearchQuery('');
                setPage(1);
              }}
              className="px-3 py-1 bg-gray-200 rounded"
            >
              {t('clear', 'Очистить')}
            </button>
          </div>

          <div>
            <button
              onClick={batchUpdateStock}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
              disabled={loading}
            >
              {t('batch_update_stock', 'Массовое обновление')}
            </button>
          </div>
        </div>

        {/* Таблица товаров */}
        <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('product', 'Товар')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('vendor', 'Производитель')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('price', 'Цена')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('current_stock', 'Текущий запас')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('min_stock', 'Мин. запас')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('status', 'Статус')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  </td>
                </tr>
              ) : products.length === 0 ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                    {t('no_products_found', 'Товары не найдены')}
                  </td>
                </tr>
              ) : (
                products.map(product => (
                  <tr key={product.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {product.image && (
                          <img
                            className="h-10 w-10 rounded object-cover mr-3"
                            src={product.image}
                            alt={product.name}
                          />
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.vendor || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.price?.toFixed(2) || '0.00'} ₴
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <input
                          type="number"
                          min="0"
                          value={product.stock || 0}
                          onChange={e => {
                            setProducts(
                              products.map(p =>
                                p.id === product.id
                                  ? { ...p, stock: parseInt(e.target.value) || 0 }
                                  : p
                              )
                            );
                          }}
                          onBlur={e =>
                            updateProductStock(product.id, parseInt(e.target.value) || 0)
                          }
                          className="w-20 border rounded p-1 text-sm"
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <input
                          type="number"
                          min="0"
                          value={product.min_stock || 0}
                          onChange={e => {
                            setProducts(
                              products.map(p =>
                                p.id === product.id
                                  ? { ...p, min_stock: parseInt(e.target.value) || 0 }
                                  : p
                              )
                            );
                          }}
                          onBlur={e => updateMinStock(product.id, parseInt(e.target.value) || 0)}
                          className="w-20 border rounded p-1 text-sm"
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.stock === 0 ? (
                        <span className="px-2 py-1 text-xs rounded bg-red-100 text-red-800">
                          {t('out_of_stock', 'Нет в наличии')}
                        </span>
                      ) : product.stock < product.min_stock ? (
                        <span className="px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-800">
                          {t('low_stock', 'Малый запас')}
                        </span>
                      ) : (
                        <span className="px-2 py-1 text-xs rounded bg-green-100 text-green-800">
                          {t('in_stock', 'В наличии')}
                        </span>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Пагинация */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-4">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                className={`px-3 py-1 rounded ${page === 1 ? 'bg-gray-100 text-gray-400' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                &lt;
              </button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
                <button
                  key={pageNum}
                  onClick={() => setPage(pageNum)}
                  className={`px-3 py-1 rounded ${pageNum === page ? 'bg-primary text-white' : 'bg-gray-200 hover:bg-gray-300'}`}
                >
                  {pageNum}
                </button>
              ))}

              <button
                onClick={() => setPage(Math.min(totalPages, page + 1))}
                disabled={page === totalPages}
                className={`px-3 py-1 rounded ${page === totalPages ? 'bg-gray-100 text-gray-400' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                &gt;
              </button>
            </nav>
          </div>
        )}

        {/* Инструкции для массового обновления */}
        <div className="mt-10 bg-blue-50 p-4 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">
            {t('how_to_batch_update', 'Как сделать массовое обновление запасов')}
          </h3>
          <ol className="list-decimal list-inside text-blue-700 space-y-1">
            <li>{t('prepare_csv', 'Подготовьте CSV файл со столбцами id,stock')}</li>
            <li>{t('click_batch_update', 'Нажмите кнопку "Массовое обновление"')}</li>
            <li>{t('select_file', 'Выберите подготовленный CSV файл')}</li>
            <li>{t('wait_for_results', 'Дождитесь завершения обновления')}</li>
          </ol>
          <p className="mt-2 text-sm text-blue-700">
            {t('csv_example', 'Пример содержимого CSV файла:')}
          </p>
          <pre className="bg-white p-2 rounded text-xs mt-1">
            id,stock,min_stock
            <br />
            123e4567-e89b-12d3-a456-426614174000,10,5
            <br />
            123e4567-e89b-12d3-a456-426614174001,25,10
            <br />
            123e4567-e89b-12d3-a456-426614174002,0,5
          </pre>
        </div>
      </div>
    </>
  );
};

export default InventoryManagement;
