import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
// import AdminLayout from '../components/AdminLayout'; // Removed AdminLayout
import BannerForm from '../../components/admin/BannerForm'; // Updated path
import { toast } from 'react-toastify';

const BannerCreate = () => {
  const navigate = useNavigate();
  const [error, setError] = useState(null);

  const handleError = err => {
    console.error('Error creating banner:', err);
    setError(err.message);
    toast.error(`Ошибка при создании баннера: ${err.message}`);
  };

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6 text-red-600">Ошибка создания баннера</h1>
        <p className="mb-4">{error}</p>
        <button
          onClick={() => navigate('/admin/banners')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Вернуться к списку баннеров
        </button>
      </div>
    );
  }

  return (
    // <AdminLayout> // Removed AdminLayout wrapper
    <>
      <h1 className="text-2xl font-bold mb-6">Создание нового баннера</h1>
      <BannerForm onError={handleError} />
    </>
    // </AdminLayout>
  );
};

export default BannerCreate;
