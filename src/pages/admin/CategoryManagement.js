import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
// import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar'; // Сайдбар теперь рендерится в AdminDashboard

const CategoryManagement = () => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [categoryTree, setCategoryTree] = useState([]);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [productCounts, setProductCounts] = useState({});

  // Function to build category tree
  const buildCategoryTree = useCallback((categories, parentId = null) => {
    return categories
      .filter(category => category.parent_id === parentId)
      .map(category => ({
        ...category,
        children: buildCategoryTree(categories, category.id)
      }));
  }, []);

  // Функция для подсчета всех товаров (включая товары в подкатегориях)
  const calculateTotalProducts = useCallback((categoryId, allCategories, productCountsData) => {
    // Get direct product count for this category
    const directCount = productCountsData[categoryId] || 0;

    // Find all immediate child categories
    const childCategories = allCategories.filter(cat => cat.parent_id === categoryId);

    // Sum up products from all child categories recursively
    const childrenCount = childCategories.reduce((sum, childCategory) => {
      return sum + calculateTotalProducts(childCategory.id, allCategories, productCountsData);
    }, 0);

    // Return total count (direct + from children)
    return directCount + childrenCount;
  }, []);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);

        // Получаем все категории
        const { data, error } = await supabase.from('categories').select('*').order('name');

        if (error) throw error;

        setCategories(data || []);

        // Получаем количество товаров для каждой категории
        const counts = {};
        for (const category of data || []) {
          const { count, error: countError } = await supabase
            .from('products')
            .select('id', { count: 'exact', head: true })
            .eq('category_id', category.id);

          if (!countError) {
            counts[category.id] = count || 0;
          }
        }

        setProductCounts(counts);

        // Создаем древовидную структуру категорий
        const tree = buildCategoryTree(data || []);
        setCategoryTree(tree);
      } catch (error) {
        console.error('Error fetching categories:', error);
        toast.error(t('error_fetching_categories', 'Ошибка при загрузке категорий'));
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [t, buildCategoryTree]);

  // Toggle category expansion
  const toggleCategory = categoryId => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Delete category
  const handleDeleteCategory = async id => {
    if (
      window.confirm(t('confirm_delete_category', 'Вы действительно хотите удалить эту категорию?'))
    ) {
      try {
        // Check if category has products
        const { count, error: countError } = await supabase
          .from('products')
          .select('id', { count: 'exact', head: true })
          .eq('category_id', id);

        if (countError) throw countError;

        if (count > 0) {
          toast.error(t('category_has_products', 'Нельзя удалить категорию с товарами'));
          return;
        }

        // Check if category has subcategories
        const hasChildren = categories.some(cat => cat.parent_id === id);

        if (hasChildren) {
          toast.error(t('category_has_subcategories', 'Нельзя удалить категорию с подкатегориями'));
          return;
        }

        // Delete category if no products and no subcategories
        const { error } = await supabase.from('categories').delete().eq('id', id);

        if (error) throw error;

        // Update category list
        setCategories(categories.filter(cat => cat.id !== id));
        setCategoryTree(buildCategoryTree(categories.filter(cat => cat.id !== id)));
        toast.success(t('category_deleted', 'Категория успешно удалена'));
      } catch (error) {
        console.error('Error deleting category:', error);
        toast.error(t('error_deleting_category', 'Ошибка при удалении категории'));
      }
    }
  };

  // Render category tree
  const renderCategoryTree = (categories, level = 0) => {
    return categories.map(category => {
      const hasChildren = category.children && category.children.length > 0;
      const isExpanded = expandedCategories[category.id];

      // Calculate direct product count for this category
      const directProductCount = productCounts[category.id] || 0;

      // Calculate total including subcategories - pass the FULL categories array, not just children
      const totalProductCount = calculateTotalProducts(category.id, categories, productCounts);

      return (
        <React.Fragment key={category.id}>
          <tr className="hover:bg-gray-50">
            <td className="px-6 py-4">
              <div className="flex items-center" style={{ marginLeft: `${level * 20}px` }}>
                {hasChildren && (
                  <button
                    onClick={() => toggleCategory(category.id)}
                    className="mr-2 h-6 w-6 flex items-center justify-center rounded-full bg-gray-100"
                  >
                    {isExpanded ? '-' : '+'}
                  </button>
                )}
                {category.image ? (
                  <img
                    src={category.image}
                    alt={category.name}
                    className="h-10 w-10 object-cover rounded mr-3"
                    onError={e => {
                      e.target.src = '/placeholder.png';
                    }}
                  />
                ) : (
                  <div className="h-10 w-10 bg-gray-100 rounded mr-3 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                      />
                    </svg>
                  </div>
                )}
                <span>{category.name}</span>
              </div>
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {category.children ? category.children.length : 0}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {totalProductCount > 0 ? (
                <div className="flex flex-col">
                  <span>
                    {totalProductCount} {t('total', 'всего')}
                  </span>
                  {hasChildren && directProductCount > 0 && (
                    <span className="text-xs text-gray-400">
                      ({directProductCount} {t('directly', 'напрямую')})
                    </span>
                  )}
                </div>
              ) : (
                '—'
              )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div className="flex justify-end space-x-3">
                <Link
                  to={`/admin/categories/edit/${category.id}`}
                  className="text-indigo-600 hover:text-indigo-900"
                >
                  {t('edit', 'Редактировать')}
                </Link>
                <button
                  onClick={() => handleDeleteCategory(category.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  {t('delete', 'Удалить')}
                </button>
              </div>
            </td>
          </tr>

          {/* Recursively render children if expanded */}
          {isExpanded && hasChildren && renderCategoryTree(category.children, level + 1)}
        </React.Fragment>
      );
    });
  };

  return (
    <>
      <Helmet>
        <title>{t('category_management', 'Управление категориями')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-semibold">
            {t('category_management', 'Управление категориями')}
          </h1>
          <Link
            to="/admin/categories/new"
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
          >
            {t('add_category', 'Добавить категорию')}
          </Link>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Admin menu sidebar - УДАЛЕНО, так как рендерится в AdminDashboard.js */}
          {/* <div className="lg:w-1/4">
            <AdminMenuSidebar activePath="/admin/categories" />
          </div> */}

          {/* Main content */}
          <div className="w-full">
            {' '}
            {/* Изменено с lg:w-3/4 на w-full, так как сайдбар удален отсюда */}
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow overflow-hidden">
                {categories.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('category_name', 'Название категории')}
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('subcategories', 'Подкатегории')}
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('products', 'Товары')}
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {t('actions', 'Действия')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {renderCategoryTree(categoryTree)}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="mx-auto h-12 w-12 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                      />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      {t('no_categories', 'Нет категорий')}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {t('get_started_by_creating', 'Начните с создания новой категории')}
                    </p>
                    <div className="mt-6">
                      <Link
                        to="/admin/categories/new"
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark"
                      >
                        <svg
                          className="-ml-1 mr-2 h-5 w-5"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        {t('new_category', 'Новая категория')}
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CategoryManagement;
