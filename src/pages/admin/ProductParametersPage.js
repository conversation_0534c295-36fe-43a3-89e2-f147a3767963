import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase, convertFromBigInt } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { Link } from 'react-router-dom';

const ProductParametersPage = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [parameters, setParameters] = useState([]);
  const [selectedParameter, setSelectedParameter] = useState(null);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [paramValues, setParamValues] = useState([]);
  const [selectedValue, setSelectedValue] = useState('');

  const fetchParameters = useCallback(async () => {
    setLoading(true);
    try {
      // Get unique parameter names
      const { data, error } = await supabase.from('product_params').select('name').order('name');

      if (error) throw error;

      // Get unique parameters
      const uniqueParams = [...new Set(data.map(item => item.name))];
      setParameters(uniqueParams);
    } catch (error) {
      console.error('Error fetching parameters:', error);
      toast.error(t('error_fetching_parameters', 'Ошибка при загрузке параметров'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  useEffect(() => {
    fetchParameters();
  }, [fetchParameters]);

  const fetchParameterValues = async paramName => {
    try {
      const { data, error } = await supabase
        .from('product_params')
        .select('value')
        .eq('name', paramName)
        .order('value');

      if (error) throw error;

      // Get unique values
      const uniqueValues = [...new Set(data.map(item => item.value))];
      setParamValues(uniqueValues);
    } catch (error) {
      console.error('Error fetching parameter values:', error);
      toast.error(t('error_fetching_values', 'Ошибка при загрузке значений'));
    }
  };

  const handleParameterSelect = async paramName => {
    setSelectedParameter(paramName);
    setSelectedValue('');
    setFilteredProducts([]);
    await fetchParameterValues(paramName);
  };

  const fetchProductsByParameter = async () => {
    if (!selectedParameter || !selectedValue) return;

    setLoading(true);
    try {
      // First get product IDs that match the parameter and value
      const { data: paramData, error: paramError } = await supabase
        .from('product_params')
        .select('product_id')
        .eq('name', selectedParameter)
        .eq('value', selectedValue);

      if (paramError) throw paramError;

      if (paramData.length === 0) {
        setFilteredProducts([]);
        toast.info(t('no_products_found', 'Товары с выбранным параметром не найдены'));
        setLoading(false);
        return;
      }

      // Convert BigInt strings back to UUID format for the products table query
      const productIds = paramData.map(item => convertFromBigInt(item.product_id));

      // Then get the products
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('id, name, price, image, category_id, vendor')
        .in('id', productIds)
        .order('name');

      if (productError) throw productError;

      setFilteredProducts(productData);
    } catch (error) {
      console.error('Error fetching products by parameter:', error);
      toast.error(t('error_fetching_products', 'Ошибка при загрузке товаров'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>{t('product_parameters', 'Параметры товаров - Админ панель')}</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="container mx-auto px-6 py-12">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-semibold">{t('product_parameters', 'Параметры товаров')}</h1>
          <div className="flex gap-4">
            <Link
              to="/admin"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
            >
              {t('back_to_dashboard', 'Вернуться к панели')}
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="w-full md:w-1/3">
              <h3 className="font-medium mb-4">{t('parameters', 'Параметры')}</h3>
              {loading && parameters.length === 0 ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="h-96 overflow-y-auto border rounded p-2">
                  {parameters.length > 0 ? (
                    parameters.map((param, index) => (
                      <button
                        key={index}
                        onClick={() => handleParameterSelect(param)}
                        className={`w-full text-left p-2 hover:bg-gray-100 ${
                          selectedParameter === param ? 'bg-gray-100 font-medium' : ''
                        }`}
                      >
                        {param}
                      </button>
                    ))
                  ) : (
                    <p className="text-center py-4 text-gray-500">
                      {t('no_parameters', 'Нет доступных параметров')}
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="w-full md:w-1/3">
              <h3 className="font-medium mb-4">{t('parameter_values', 'Значения параметра')}</h3>
              <div className="h-96 overflow-y-auto border rounded p-2">
                {selectedParameter ? (
                  paramValues.length > 0 ? (
                    paramValues.map((value, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedValue(value)}
                        className={`w-full text-left p-2 hover:bg-gray-100 ${
                          selectedValue === value ? 'bg-gray-100 font-medium' : ''
                        }`}
                      >
                        {value}
                      </button>
                    ))
                  ) : (
                    <p className="text-center py-4 text-gray-500">
                      {t('no_values', 'Нет доступных значений')}
                    </p>
                  )
                ) : (
                  <p className="text-center py-4 text-gray-500">
                    {t('select_parameter', 'Выберите параметр слева')}
                  </p>
                )}
              </div>

              {selectedParameter && selectedValue && (
                <div className="mt-4">
                  <button
                    onClick={fetchProductsByParameter}
                    className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
                    disabled={loading}
                  >
                    {loading ? t('loading', 'Загрузка...') : t('find_products', 'Найти товары')}
                  </button>
                </div>
              )}
            </div>

            <div className="w-full md:w-1/3">
              <h3 className="font-medium mb-4">{t('products', 'Товары с параметром')}</h3>
              <div className="h-96 overflow-y-auto border rounded p-2">
                {loading && selectedParameter && selectedValue ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : filteredProducts.length > 0 ? (
                  filteredProducts.map(product => (
                    <div key={product.id} className="p-2 border-b last:border-b-0 hover:bg-gray-50">
                      <div className="flex items-center gap-2">
                        {product.image && (
                          <img
                            src={product.image}
                            alt={product.name}
                            className="w-10 h-10 object-cover"
                          />
                        )}
                        <div>
                          <Link
                            to={`/product/${product.id}`}
                            className="font-medium text-primary hover:underline"
                          >
                            {product.name}
                          </Link>
                          <div className="flex gap-2 text-sm text-gray-500">
                            <span>{product.vendor}</span>
                            <span>{product.price} ₴</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : selectedParameter && selectedValue ? (
                  <p className="text-center py-4 text-gray-500">
                    {t('no_products_with_parameter', 'Нет товаров с выбранным параметром')}
                  </p>
                ) : (
                  <p className="text-center py-4 text-gray-500">
                    {t(
                      'parameter_value_instructions',
                      'Выберите параметр и значение, затем нажмите "Найти товары"'
                    )}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductParametersPage;
