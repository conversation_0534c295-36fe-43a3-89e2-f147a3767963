import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import BrandForm from '../../components/admin/BrandForm';

const BrandCreate = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSuccess = () => {
    navigate('/admin/brands');
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">{t('create_brand', 'Создание бренда')}</h1>
      <BrandForm onSuccess={handleSuccess} />
    </div>
  );
};

export default BrandCreate;
