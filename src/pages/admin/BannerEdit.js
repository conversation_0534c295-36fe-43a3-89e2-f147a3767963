import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import BannerForm from '../../components/admin/BannerForm';
import LoadingSpinner from '../../components/LoadingSpinner';

const BannerEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [banner, setBanner] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Handle errors coming back from the form
  const handleFormError = err => {
    console.error('Error in banner form:', err);
    setError(err.message);
  };

  useEffect(() => {
    const fetchBanner = async () => {
      try {
        if (!id) {
          throw new Error('Banner ID is missing');
        }

        const { data, error } = await supabase.from('banners').select('*').eq('id', id).single();

        if (error) throw error;

        if (!data) {
          throw new Error(`Баннер с ID ${id} не найден`);
        }

        // Ensure all required fields are present
        const processedData = {
          id: data.id,
          title: data.title || '',
          subtitle: data.subtitle || '',
          image_url: data.image_url || '',
          button_text: data.button_text || '',
          button_link: data.button_link || '',
          position: data.position || 1,
          active: typeof data.active === 'boolean' ? data.active : true
        };
        setBanner(processedData);
        setError(null);
      } catch (error) {
        console.error('Error fetching banner:', error);
        setError(error.message);
        toast.error(`Ошибка загрузки баннера: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchBanner();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6 text-red-600">Ошибка загрузки баннера</h1>
        <p className="mb-4">{error}</p>
        <button
          onClick={() => navigate('/admin/banners')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Вернуться к списку баннеров
        </button>
      </div>
    );
  }

  if (!banner) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6">Баннер не найден</h1>
        <button
          onClick={() => navigate('/admin/banners')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Вернуться к списку баннеров
        </button>
      </div>
    );
  }

  return (
    <>
      <h1 className="text-2xl font-bold mb-6">Редактирование баннера</h1>
      <BannerForm banner={banner} isEditing={true} onError={handleFormError} />
    </>
  );
};

export default BannerEdit;
