import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase, supabaseAdmin } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { useSearchParams } from 'react-router-dom';
// import AdminMenuSidebar from '../../components/admin/AdminMenuSidebar'; // Сайдбар теперь рендерится в AdminDashboard

const ReviewsManagementPage = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const initialStatus = searchParams.get('status') || 'pending';

  const [loading, setLoading] = useState(true);
  const [reviews, setReviews] = useState([]);
  const [statusFilter, setStatusFilter] = useState(initialStatus);
  const [totalReviews, setTotalReviews] = useState(0);
  const [page, setPage] = useState(1);
  const [tableExists, setTableExists] = useState(true);
  const itemsPerPage = 10;

  useEffect(() => {
    // Check if reviews table exists
    const checkReviewsTable = async () => {
      try {
        // Check if table exists by trying to select from it
        const { error } = await supabase.from('reviews').select('id').limit(1);

        if (
          error &&
          (error.code === '42P01' ||
            error.message.includes('relation') ||
            error.message.includes('does not exist'))
        ) {
          setTableExists(false);
          setLoading(false);
          return false;
        }

        return true;
      } catch (err) {
        console.error('Error checking reviews table:', err);
        setTableExists(false);
        setLoading(false);
        return false;
      }
    };

    const fetchReviews = async () => {
      try {
        const exists = await checkReviewsTable();
        if (!exists) return;

        // Use admin client for fetching all reviews
        const client = supabaseAdmin || supabase;
        const { data, count, error } = await client
          .from('reviews')
          .select('*', { count: 'exact' })
          .eq('status', statusFilter)
          .order('created_at', { ascending: false })
          .range((page - 1) * itemsPerPage, page * itemsPerPage - 1);

        if (error) {
          console.error('Error fetching reviews:', error);
          setReviews([]);
          setTotalReviews(0);
        } else {
          // Fetch product details separately for each review if needed
          let reviewsWithProducts = [...(data || [])];

          // Try to fetch product info for each review if product_id exists
          for (let i = 0; i < reviewsWithProducts.length; i++) {
            const review = reviewsWithProducts[i];
            if (review.product_id) {
              try {
                const { data: productData } = await supabase
                  .from('products')
                  .select('name, image')
                  .eq('id', review.product_id)
                  .single();

                if (productData) {
                  reviewsWithProducts[i] = {
                    ...review,
                    product: productData
                  };
                }
              } catch (productError) {}
            }
          }

          setReviews(reviewsWithProducts);
          setTotalReviews(count || 0);
        }
      } catch (error) {
        console.error('Error fetching reviews:', error);
        toast.error(t('error_fetching_reviews', 'Ошибка при загрузке отзывов'));
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [statusFilter, page, t]);

  const handleStatusChange = status => {
    setStatusFilter(status);
    setPage(1); // Reset to first page when changing filter
  };

  // Handle updating review status
  const updateReviewStatus = async (reviewId, status) => {
    try {
      // Use admin client for updating review status
      const client = supabaseAdmin || supabase;
      const { error } = await client
        .from('reviews')
        .update({ status, updated_at: new Date() })
        .eq('id', reviewId);

      if (error) throw error;

      // If we're viewing filtered reviews, we should remove this review from the list
      if (status !== statusFilter) {
        setReviews(reviews.filter(r => r.id !== reviewId));
        setTotalReviews(prev => prev - 1);
      } else {
        // Update the review in state
        setReviews(prev =>
          prev.map(review => (review.id === reviewId ? { ...review, status } : review))
        );
      }

      toast.success(t('review_status_updated', 'Статус отзыва обновлен'));
    } catch (error) {
      console.error('Error updating review status:', error);
      toast.error(t('error_updating_status', 'Ошибка при обновлении статуса'));
    }
  };

  // Create reviews table with proper relationships
  const createReviewsTable = async () => {
    try {
      setLoading(true);

      if (!supabaseAdmin) {
        throw new Error(
          'Admin client not available. Please check your service role key configuration.'
        );
      }

      // Create reviews table
      const { error } = await supabaseAdmin.rpc('exec_sql', {
        query: `
          -- First disable RLS temporarily to recreate the table
          ALTER TABLE IF EXISTS reviews DISABLE ROW LEVEL SECURITY;
          
          -- Drop existing table and recreate
          DROP TABLE IF EXISTS reviews CASCADE;

          -- Create reviews table
          CREATE TABLE reviews (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            product_id UUID REFERENCES products(id) ON DELETE CASCADE,
            name TEXT NOT NULL,
            email TEXT,
            rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
            comment TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS reviews_product_id_idx ON reviews(product_id);
          CREATE INDEX IF NOT EXISTS reviews_status_idx ON reviews(status);

          -- Enable UUID extension if not already enabled
          CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

          -- Grant all permissions to service role
          GRANT ALL ON reviews TO service_role;
          GRANT USAGE ON SCHEMA public TO service_role;
          
          -- Grant permissions to authenticated and anonymous users
          GRANT USAGE ON SCHEMA public TO authenticated;
          GRANT USAGE ON SCHEMA public TO anon;
          
          GRANT SELECT ON reviews TO authenticated;
          GRANT SELECT ON reviews TO anon;
          GRANT INSERT ON reviews TO authenticated;
          GRANT INSERT ON reviews TO anon;

          -- Enable RLS
          ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

          -- Drop any existing policies
          DROP POLICY IF EXISTS "service_role_all_access" ON reviews;
          DROP POLICY IF EXISTS "allow_inserts" ON reviews;
          DROP POLICY IF EXISTS "read_approved" ON reviews;
          DROP POLICY IF EXISTS "service_role_select" ON reviews;

          -- Create policies
          CREATE POLICY "allow_public_read_approved_reviews" ON reviews
            FOR SELECT TO public
            USING (status = 'approved');

          CREATE POLICY "allow_public_insert_reviews" ON reviews
            FOR INSERT TO public
            WITH CHECK (true);

          CREATE POLICY "allow_service_role_all" ON reviews
            FOR ALL TO service_role
            USING (true)
            WITH CHECK (true);

          -- Create trigger function to update product rating
          CREATE OR REPLACE FUNCTION update_product_rating()
          RETURNS TRIGGER AS $$
          BEGIN
            -- Update average rating and review count for the product
            UPDATE products
            SET 
              rating = (
                SELECT COALESCE(AVG(rating)::numeric(10,2), 0)
                FROM reviews
                WHERE product_id = NEW.product_id
                AND status = 'approved'
              ),
              reviews_count = (
                SELECT COUNT(*)
                FROM reviews
                WHERE product_id = NEW.product_id
                AND status = 'approved'
              )
            WHERE id = NEW.product_id;
            
            RETURN NEW;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;

          -- Create or replace the trigger
          DROP TRIGGER IF EXISTS update_product_rating_trigger ON reviews;
          CREATE TRIGGER update_product_rating_trigger
          AFTER INSERT OR UPDATE OF status OR DELETE ON reviews
          FOR EACH ROW
          EXECUTE FUNCTION update_product_rating();
        `
      });

      if (error) {
        console.error('Error setting up permissions:', error);
        throw error;
      }

      setTableExists(true);
      toast.success(t('reviews_table_created', 'Таблица отзывов создана успешно'));
    } catch (error) {
      console.error('Error initializing reviews table:', error);
      toast.error(t('error_initializing', 'Ошибка при инициализации модуля отзывов'));
    } finally {
      setLoading(false);
    }
  };

  // Initialize reviews system if needed
  const renderInitializeButton = () => (
    <div className="text-center py-12">
      <div className="mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="mx-auto h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
          />
        </svg>
        <h3 className="mt-2 text-lg font-medium text-gray-900">
          {t('reviews_not_initialized', 'Модуль отзывов не инициализирован')}
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          {t('initialize_reviews_desc', 'Создайте необходимую таблицу для управления отзывами')}
        </p>
      </div>

      <button
        onClick={createReviewsTable}
        disabled={loading}
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
      >
        {loading
          ? t('initializing', 'Инициализация...')
          : t('initialize_reviews', 'Инициализировать модуль отзывов')}
      </button>
    </div>
  );

  // Function to render star rating
  const renderRating = rating => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>{t('reviews_management', 'Управление отзывами')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-semibold">
            {t('reviews_management', 'Управление отзывами')}
          </h1>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Admin menu sidebar - УДАЛЕНО, так как рендерится в AdminDashboard.js */}
          {/* <div className="lg:w-1/4">
            <AdminMenuSidebar activePath="/admin/reviews" />
          </div> */}

          {/* Main content */}
          <div className="w-full">
            {' '}
            {/* Изменено с lg:w-3/4 на w-full */}
            {!tableExists ? (
              renderInitializeButton()
            ) : (
              <>
                {/* Status filter tabs */}
                <div className="mb-6 border-b border-gray-200">
                  <nav className="-mb-px flex space-x-6">
                    <button
                      onClick={() => handleStatusChange('pending')}
                      className={`pb-3 px-1 border-b-2 font-medium text-sm ${
                        statusFilter === 'pending'
                          ? 'border-primary text-primary'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {t('pending', 'Ожидающие')}
                    </button>
                    <button
                      onClick={() => handleStatusChange('approved')}
                      className={`pb-3 px-1 border-b-2 font-medium text-sm ${
                        statusFilter === 'approved'
                          ? 'border-primary text-primary'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {t('approved', 'Одобренные')}
                    </button>
                    <button
                      onClick={() => handleStatusChange('rejected')}
                      className={`pb-3 px-1 border-b-2 font-medium text-sm ${
                        statusFilter === 'rejected'
                          ? 'border-primary text-primary'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {t('rejected', 'Отклоненные')}
                    </button>
                  </nav>
                </div>

                {/* Reviews list */}
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <>
                    {reviews.length > 0 ? (
                      <div className="space-y-6">
                        {reviews.map(review => (
                          <div key={review.id} className="bg-white rounded-lg shadow-sm p-6">
                            <div className="flex items-start justify-between">
                              <div>
                                <div className="flex items-center space-x-2">
                                  <span className="font-semibold">{review.name}</span>
                                  <span className="text-gray-500 text-sm">
                                    {new Date(review.created_at).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="mt-1">{renderRating(review.rating)}</div>
                              </div>

                              <div className="flex items-center space-x-2">
                                {statusFilter === 'pending' && (
                                  <>
                                    <button
                                      onClick={() => updateReviewStatus(review.id, 'approved')}
                                      className="px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                                    >
                                      {t('approve', 'Одобрить')}
                                    </button>
                                    <button
                                      onClick={() => updateReviewStatus(review.id, 'rejected')}
                                      className="px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                                    >
                                      {t('reject', 'Отклонить')}
                                    </button>
                                  </>
                                )}
                                {statusFilter === 'approved' && (
                                  <button
                                    onClick={() => updateReviewStatus(review.id, 'rejected')}
                                    className="px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                                  >
                                    {t('reject', 'Отклонить')}
                                  </button>
                                )}
                                {statusFilter === 'rejected' && (
                                  <button
                                    onClick={() => updateReviewStatus(review.id, 'approved')}
                                    className="px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                                  >
                                    {t('approve', 'Одобрить')}
                                  </button>
                                )}
                              </div>
                            </div>

                            <div className="mt-3 text-gray-800">{review.comment}</div>

                            {review.product && (
                              <div className="mt-4 pt-4 border-t border-gray-100">
                                <div className="flex items-center">
                                  {review.product.image && (
                                    <img
                                      src={review.product.image}
                                      alt={review.product.name}
                                      className="h-12 w-12 object-cover rounded mr-3"
                                      onError={e => {
                                        e.target.src = '/placeholder.png';
                                      }}
                                    />
                                  )}
                                  <div>
                                    <div className="font-medium text-sm">
                                      {t('product', 'Товар')}:
                                    </div>
                                    <div>{review.product.name}</div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}

                        {/* Pagination */}
                        {totalReviews > itemsPerPage && (
                          <div className="flex justify-center mt-8">
                            <div className="flex-1 flex justify-between sm:justify-end">
                              <button
                                onClick={() => setPage(Math.max(1, page - 1))}
                                disabled={page === 1}
                                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                  page === 1
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                }`}
                              >
                                {t('previous', 'Предыдущая')}
                              </button>
                              <button
                                onClick={() => setPage(page + 1)}
                                disabled={page * itemsPerPage >= totalReviews}
                                className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                                  page * itemsPerPage >= totalReviews
                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                }`}
                              >
                                {t('next', 'Следующая')}
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-white rounded-lg shadow p-6 text-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="mx-auto h-12 w-12 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                          />
                        </svg>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">
                          {t('no_reviews_found', 'Отзывы не найдены')}
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          {statusFilter === 'pending'
                            ? t('no_pending_reviews', 'Нет отзывов, ожидающих проверки')
                            : statusFilter === 'approved'
                              ? t('no_approved_reviews', 'Нет одобренных отзывов')
                              : t('no_rejected_reviews', 'Нет отклоненных отзывов')}
                        </p>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ReviewsManagementPage;
