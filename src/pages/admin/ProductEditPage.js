import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { supabase } from '../../supabaseClient';
import ProductForm from '../../components/admin/ProductForm';

const ProductEditPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [relatedProducts, setRelatedProducts] = useState([]);

  const safeParseJSON = (json, fallback) => {
    try {
      return typeof json === 'string' ? JSON.parse(json) : json || fallback;
    } catch {
      return fallback;
    }
  };

  // Converts product attributes to params format
  const attributesToParams = attributes => {
    if (!attributes || typeof attributes !== 'object') return [];

    return Object.entries(attributes).map(([name, value]) => ({
      name,
      value: String(value),
      is_key: false // Default to not key parameters
    }));
  };

  useEffect(() => {
    const fetchProductData = async () => {
      try {
        setLoading(true);

        if (!id) {
          throw new Error('No product ID provided');
        }

        const { data: productData, error: productError } = await supabase
          .from('products')
          .select(
            `
            *,
            category:categories(id, name)
          `
          )
          .eq('id', id)
          .single();

        if (productError) {
          throw productError;
        }

        if (!productData) {
          toast.error(t('product_not_found', 'Товар не найден'));
          navigate('/admin/products');
          return;
        }

        // Get brand data separately to avoid join issues
        let brandData = null;
        if (productData.brand_id) {
          // If product has brand_id field
          try {
            const { data: brand, error: brandError } = await supabase
              .from('brands')
              .select('id, name')
              .eq('id', productData.brand_id)
              .single();

            if (!brandError && brand) {
              brandData = brand;
            }
          } catch (err) {}
        } else if (productData.brand && typeof productData.brand === 'string') {
          // If product has brand field instead of brand_id
          try {
            // First try to get all brands and filter client-side to avoid encoding issues
            const { data: allBrands, error: brandsError } = await supabase
              .from('brands')
              .select('id, name');

            if (!brandsError && allBrands && allBrands.length > 0) {
              // Find the brand with a case-insensitive comparison
              const matchedBrand = allBrands.find(
                b =>
                  b.name.toLowerCase() === productData.brand.toLowerCase() ||
                  productData.brand.toLowerCase().includes(b.name.toLowerCase()) ||
                  b.name.toLowerCase().includes(productData.brand.toLowerCase())
              );

              if (matchedBrand) {
                brandData = matchedBrand;
              }
            }
          } catch (err) {}
        }

        // Fetch related products from the same category
        if (productData.category_id) {
          const { data: related } = await supabase
            .from('products')
            .select('id, name, image, price')
            .eq('category_id', productData.category_id)
            .neq('id', id)
            .limit(4);

          setRelatedProducts(related || []);
        }

        // Parse JSON fields
        let imageGallery = safeParseJSON(productData.image_gallery, []);

        // Ensure image_gallery is an array
        if (!Array.isArray(imageGallery)) {
          imageGallery = [];
        }

        // Convert attributes to params format if they exist
        let params = [];
        if (productData.attributes && typeof productData.attributes === 'object') {
          params = attributesToParams(productData.attributes);
        } else {
          params = productData.params || [];
        }

        let processedData = {
          ...productData,
          image_gallery: imageGallery,
          tags: safeParseJSON(productData.tags, []),
          category_id: productData.category?.id || '',
          // Use brand_id if available, otherwise use brand data we fetched
          brand_id: productData.brand_id || brandData?.id || '',
          brand: brandData || { name: productData.brand || '' },
          params: params
        };

        setProduct(processedData);
      } catch (error) {
        console.error('Error fetching product:', error);
        toast.error(t('error_loading_product', 'Ошибка при загрузке товара'));
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [id, navigate, t]);

  return (
    <div className="container mx-auto px-1 py-4 max-w-full">
      <div className="flex items-center mb-4">
        <Link to="/admin/products" className="mr-2 text-gray-600 hover:text-gray-900">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 17l-5-5m0 0l5-5m-5 5h12"
            />
          </svg>
        </Link>
        <h1 className="text-xl font-bold text-gray-900">
          {loading
            ? t('loading', 'Загрузка...')
            : product
              ? t('edit_product', 'Редактирование товара')
              : t('product_not_found', 'Товар не найден')}
        </h1>
      </div>

      {loading ? (
        <div className="bg-white p-8 rounded-lg shadow flex justify-center items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : !product ? (
        <div className="bg-white p-8 rounded-lg shadow text-center">
          <div className="text-red-500 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 mx-auto"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h2 className="text-lg font-medium">{t('product_not_found', 'Товар не найден')}</h2>
          <p className="mt-2 text-gray-600">
            {t('product_not_found_desc', 'Товар не существует или был удален')}
          </p>
          <div className="mt-4">
            <Link
              to="/admin/products"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              {t('back_to_products', 'Вернуться к списку товаров')}
            </Link>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <ProductForm
            product={product}
            isNew={false}
            onSuccess={() => {
              toast.success(t('product_updated', 'Товар успешно обновлен'));
              navigate('/admin/products');
            }}
          />

          {relatedProducts.length > 0 && (
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="text-lg font-medium mb-3">
                {t('related_products', 'Похожие товары из этой категории')}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                {relatedProducts.map(related => (
                  <Link
                    key={related.id}
                    to={`/admin/products/edit/${related.id}`}
                    className="block bg-gray-50 border rounded-lg p-3 hover:bg-gray-100 transition"
                  >
                    <div className="flex items-center">
                      {related.image ? (
                        <img
                          src={related.image}
                          alt={related.name}
                          className="w-10 h-10 object-cover rounded"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center text-gray-400">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                        </div>
                      )}
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {related.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Intl.NumberFormat('ru-RU', {
                            style: 'currency',
                            currency: 'RUB'
                          }).format(related.price || 0)}
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductEditPage;
