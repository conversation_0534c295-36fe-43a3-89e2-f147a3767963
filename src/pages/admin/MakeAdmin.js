import React, { useState } from 'react';
import { supabase } from '../../supabaseClient';
import { useNavigate } from 'react-router-dom';

const MakeAdmin = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState({ success: false, message: '', userId: null });
  const navigate = useNavigate();

  const makeUserAdmin = async e => {
    e.preventDefault();
    setLoading(true);
    setResult({ success: false, message: '', userId: null });

    try {
      // 1. Get the user ID from auth.users
      const { data: userData, error: userError } = await supabase.auth.admin
        .getUserByEmail(email)
        .catch(() => {
          return { data: null, error: { message: 'Admin API not available' } };
        });

      let userId;

      if (userError) {
        // Try alternative method to get user ID
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .eq('email', email)
          .single();

        if (profileError) {
          throw new Error(`Failed to find user with email ${email}: ${profileError.message}`);
        }

        if (!profileData) {
          throw new Error(`No user found with email ${email}`);
        }

        userId = profileData.id;
      } else {
        userId = userData?.user?.id;
        if (!userId) {
          throw new Error(`No user found with email ${email}`);
        }
      }

      // 2. Check if profiles table exists and has is_admin column
      const { error: checkError } = await supabase.rpc('exec_sql', {
        query: `
          DO $$
          BEGIN
            -- Create profiles table if it doesn't exist
            IF NOT EXISTS (
              SELECT FROM information_schema.tables 
              WHERE table_schema = 'public'
              AND table_name = 'profiles'
            ) THEN
              CREATE TABLE public.profiles (
                id UUID PRIMARY KEY REFERENCES auth.users(id),
                email TEXT,
                first_name TEXT,
                last_name TEXT,
                avatar_url TEXT,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
              );
            END IF;
            
            -- Add is_admin column if it doesn't exist
            IF NOT EXISTS (
              SELECT FROM information_schema.columns 
              WHERE table_schema = 'public'
              AND table_name = 'profiles'
              AND column_name = 'is_admin'
            ) THEN
              ALTER TABLE public.profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
            END IF;
          END $$;
        `
      });

      if (checkError) {
      }

      // 3. Update or insert the profile with is_admin=true
      const { error } = await supabase.from('profiles').upsert({
        id: userId,
        email: email,
        is_admin: true,
        updated_at: new Date().toISOString()
      });

      if (error) {
        // Try direct SQL if standard upsert fails
        const { error: sqlError } = await supabase.rpc('exec_sql', {
          query: `
            INSERT INTO public.profiles (id, email, is_admin, updated_at)
            VALUES ('${userId}', '${email}', TRUE, NOW())
            ON CONFLICT (id) 
            DO UPDATE SET 
              email = EXCLUDED.email,
              is_admin = TRUE,
              updated_at = NOW();
          `
        });

        if (sqlError) {
          throw new Error(`Failed to update profile: ${sqlError.message}`);
        }
      }

      setResult({
        success: true,
        message: `Success! User ${email} is now an admin. Please log out and log back in.`,
        userId
      });
    } catch (error) {
      setResult({
        success: false,
        message: `Error: ${error.message}`,
        userId: null
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md mt-10">
      <h1 className="text-2xl font-bold mb-6">Make User Admin</h1>

      <form onSubmit={makeUserAdmin} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            User Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div>
          <button
            type="submit"
            disabled={loading}
            className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              loading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Processing...' : 'Make Admin'}
          </button>

          <button
            type="button"
            onClick={() => navigate('/admin')}
            className="ml-4 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Back to Dashboard
          </button>
        </div>
      </form>

      {result.message && (
        <div
          className={`mt-6 p-4 rounded-md ${result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}
        >
          <p>{result.message}</p>

          {result.success && result.userId && (
            <div className="mt-3 text-sm">
              <p className="font-semibold">User ID:</p>
              <code className="bg-gray-100 p-1 rounded">{result.userId}</code>
              <p className="mt-2">
                For extra security, consider adding this user ID to the adminUserIds list in
                src/context/AuthContext.js:
              </p>
              <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                {`const adminUserIds = [\n  '${result.userId}',\n  '2aef44bd-57c3-4b0e-a8bb-193cd5ba476e'\n];`}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MakeAdmin;
