import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ProductList from '../components/ProductList'; // Changed ProductGrid to ProductList
import { supabase } from '../supabaseClient'; // Corrected supabaseClient path
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';
import PageHeader from '../components/PageHeader';

const NewArrivalsPage = () => {
  const { t } = useTranslation();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchNewArrivals = async () => {
      try {
        setLoading(true);

        // Retrieve new arrivals (ordered by created_at descending)
        const { data, error } = await supabase
          .from('products')
          .select('*, old_price:original_price') // Select original_price and alias it as old_price
          .eq('is_new', true)
          .order('created_at', { ascending: false })
          .limit(24);

        if (error) throw error;
        setProducts(data || []);
      } catch (err) {
        console.error('Error loading new arrivals:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchNewArrivals();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <Helmet>
        <title>
          {t('newArrivals.pageTitle', 'New Arrivals')} | {t('storeName')}
        </title>
        <meta
          name="description"
          content={t(
            'newArrivals.pageDescription',
            'Check out our newest products and latest additions to our catalog'
          )}
        />
      </Helmet>

      <PageHeader
        title={t('newArrivals.pageTitle', 'New Arrivals')}
        description={t(
          'newArrivals.pageDescription',
          'Check out our newest products and latest additions to our catalog'
        )}
        breadcrumbs={[
          { name: t('home'), href: '/' },
          { name: t('newArrivals.pageTitle', 'New Arrivals'), href: '/new-arrivals' }
        ]}
      />

      {loading ? (
        <div className="flex justify-center py-10">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="text-red-500 text-center py-8">{error}</div>
      ) : products.length === 0 ? (
        <EmptyState
          title={t('newArrivals.noProductsTitle', 'No new arrivals yet')}
          description={t(
            'newArrivals.noProductsDescription',
            'Please check back later for new products'
          )}
          icon="ShoppingBagIcon"
        />
      ) : (
        <div className="pt-6">
          <ProductList products={products} /> {/* Changed ProductGrid to ProductList */}
        </div>
      )}
    </div>
  );
};

export default NewArrivalsPage;
