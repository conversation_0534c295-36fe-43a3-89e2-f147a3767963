import { useTranslation } from 'react-i18next';

const Privacy = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-6 py-12">
      <h1 className="text-3xl font-semibold mb-8 text-center">
        {t('privacy_policy', 'Privacy Policy')}
      </h1>
      <div className="card">
        <p className="text-gray-600 mb-6">
          {t(
            'privacy_description',
            'At KitchenShop, we are committed to protecting your privacy. This Privacy Policy explains how we collect, use, and safeguard your personal information.'
          )}
        </p>
        <h2 className="text-xl font-semibold mb-4">{t('data_collection', 'Data Collection')}</h2>
        <p className="text-gray-600 mb-6">
          {t(
            'data_collection_description',
            'We collect information such as your email address, name, and payment details when you register, place an order, or contact us.'
          )}
        </p>
        <h2 className="text-xl font-semibold mb-4">{t('data_usage', 'Data Usage')}</h2>
        <p className="text-gray-600">
          {t(
            'data_usage_description',
            'Your information is used to process orders, improve our services, and communicate with you about promotions and updates.'
          )}
        </p>
      </div>
    </div>
  );
};

export default Privacy;
