import { useTranslation } from 'react-i18next';

const About = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-6 py-12">
      <h1 className="text-3xl font-semibold mb-8 text-center">{t('about_us', 'About Us')}</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">{t('our_story', 'Our Story')}</h2>
          <p className="text-gray-600">
            {t(
              'about_description',
              'KitchenShop is your one-stop destination for all kitchen needs. We offer a wide range of high-quality products to make your cooking experience enjoyable and efficient.'
            )}
          </p>
        </div>
        <div className="card">
          <h2 className="text-xl font-semibold mb-4">{t('our_mission', 'Our Mission')}</h2>
          <p className="text-gray-600">
            {t(
              'about_mission',
              'Our mission is to provide the best kitchen tools and accessories at affordable prices, with a focus on customer satisfaction and innovation.'
            )}
          </p>
        </div>
      </div>
    </div>
  );
};

export default About;
