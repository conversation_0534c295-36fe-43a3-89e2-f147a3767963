import React from 'react';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { useCart } from '../context/CartContext';
import { Link, useNavigate } from 'react-router-dom';

const CheckoutPage = () => {
  const { t } = useTranslation();
  const { cart, totalPrice } = useCart();
  const navigate = useNavigate();

  const handleCompleteOrder = () => {
    navigate('/order-confirmation');
  };

  return (
    <>
      <Helmet>
        <title>{t('checkout_page_title', 'Оформление заказа - Kitchen Shop')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-semibold mb-6">{t('checkout', 'Оформление заказа')}</h1>

        {cart.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <p className="text-lg text-gray-600 mb-4">{t('cart_empty', 'Ваша корзина пуста')}</p>
            <Link to="/" className="btn-primary">
              {t('continue_shopping', 'Продолжить покупки')}
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Order Summary */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-medium mb-4">
                  {t('contact_info', 'Контактная информация')}
                </h2>
                <p className="text-gray-500 mb-4">
                  {t('checkout_msg', 'Введите ваши данные для оформления заказа')}
                </p>

                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('first_name', 'Имя')}
                      </label>
                      <input type="text" className="w-full border border-gray-300 rounded-md p-2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('last_name', 'Фамилия')}
                      </label>
                      <input type="text" className="w-full border border-gray-300 rounded-md p-2" />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('email', 'Email')}
                    </label>
                    <input type="email" className="w-full border border-gray-300 rounded-md p-2" />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('phone', 'Телефон')}
                    </label>
                    <input type="tel" className="w-full border border-gray-300 rounded-md p-2" />
                  </div>
                </form>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-medium mb-4">
                  {t('shipping_info', 'Информация о доставке')}
                </h2>

                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('address', 'Адрес')}
                    </label>
                    <input type="text" className="w-full border border-gray-300 rounded-md p-2" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('city', 'Город')}
                      </label>
                      <input type="text" className="w-full border border-gray-300 rounded-md p-2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('state', 'Область')}
                      </label>
                      <input type="text" className="w-full border border-gray-300 rounded-md p-2" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('zip_code', 'Индекс')}
                      </label>
                      <input type="text" className="w-full border border-gray-300 rounded-md p-2" />
                    </div>
                  </div>
                </form>
              </div>
            </div>

            {/* Order Summary */}
            <div>
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-medium mb-4">{t('order_summary', 'Сводка заказа')}</h2>

                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="py-2 mb-2 border-b border-gray-200">
                    <span>Вартість доставки: за тарифами перевізника</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t border-gray-200 pt-2 mt-2">
                    <span>{t('total', 'Итого')}:</span>
                    <span>{totalPrice.toFixed(2)} ₴</span>
                  </div>
                </div>

                <button
                  type="button"
                  onClick={handleCompleteOrder}
                  className="w-full mt-6 bg-primary hover:bg-primary-dark text-white py-3 px-4 rounded-md transition-colors"
                >
                  {t('complete_order', 'Оформить заказ')}
                </button>

                <p className="text-center text-sm text-gray-500 mt-4">
                  {t('secure_checkout_message', 'Безопасное оформление заказа')}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CheckoutPage;
