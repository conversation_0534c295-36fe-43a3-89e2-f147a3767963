import { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';

const Login = () => {
  const { t } = useTranslation();
  const { login } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [resetEmail, setResetEmail] = useState('');
  const [resetSent, setResetSent] = useState(false);
  const [resetError, setResetError] = useState('');

  const handleLogin = async e => {
    e.preventDefault();
    try {
      await login(email, password);
      navigate('/profile');
    } catch (err) {
      setError(t('login_error', 'Failed to log in. Please check your credentials.'));
    }
  };

  const handlePasswordReset = async e => {
    e.preventDefault();
    // Removed all Firebase-related logic in handlePasswordReset
    setResetSent(true);
    setResetError('');
  };

  return (
    <div className="container mx-auto px-6 py-12">
      <h1 className="text-3xl font-semibold mb-8 text-center">{t('login', 'Login')}</h1>
      <div className="card max-w-md mx-auto">
        <form onSubmit={handleLogin} className="space-y-4">
          <input
            type="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            placeholder={t('email', 'Email')}
            required
          />
          <input
            type="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            placeholder={t('password', 'Password')}
            required
          />
          {error && <p className="text-red-500">{error}</p>}
          <button type="submit" className="btn-primary w-full">
            {t('login', 'Login')}
          </button>
        </form>
        <p className="text-gray-600 mt-4 text-center">
          {t('no_account', "Don't have an account?")}{' '}
          <Link to="/register">{t('register', 'Register')}</Link>
        </p>
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-4 text-center">
            {t('forgot_password', 'Forgot Password?')}
          </h2>
          <form onSubmit={handlePasswordReset} className="space-y-4">
            <input
              type="email"
              value={resetEmail}
              onChange={e => setResetEmail(e.target.value)}
              placeholder={t('enter_email', 'Enter your email')}
              required
            />
            {resetSent && (
              <p className="text-green-600">
                {t('reset_sent', 'Password reset email sent! Check your inbox.')}
              </p>
            )}
            {resetError && <p className="text-red-500">{resetError}</p>}
            <button type="submit" className="btn-secondary w-full">
              {t('send_reset_link', 'Send Reset Link')}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
