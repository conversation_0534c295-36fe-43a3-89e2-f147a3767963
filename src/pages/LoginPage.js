import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Helmet } from 'react-helmet-async';
import { supabase } from '../supabaseClient';
import { toast } from 'react-toastify';
import { FaEnvelope, FaLock } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

const LoginPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { user } = useAuth();

  // Если пользователь уже залогинен, перенаправляем на главную
  useEffect(() => {
    if (user) {
      navigate('/');
    }
  }, [user, navigate]);

  const handleLogin = async e => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      // Проверяем, что данные и сессия существуют
      if (data && data.session) {
        toast.success(t('login_successful', 'Вход выполнен успешно!'));
        navigate('/');
      } else {
        throw new Error(t('login_error_no_session', 'Не удалось создать сессию'));
      }
    } catch (error) {
      console.error('Error signing in:', error);
      let errorMessage = t('login_failed', 'Ошибка входа');

      if (error.message) {
        // Улучшенные сообщения об ошибках для пользователя
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = t('invalid_credentials', 'Неверный email или пароль');
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = t('email_not_confirmed', 'Email не подтвержден. Проверьте вашу почту');
        } else {
          errorMessage = `${errorMessage}: ${error.message}`;
        }
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>{t('login', 'Вход в аккаунт')}</title>
      </Helmet>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
          <h1 className="text-2xl font-semibold text-center mb-6">
            {t('login', 'Вход в аккаунт')}
          </h1>

          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label htmlFor="email" className="block text-gray-700 text-sm font-medium mb-2">
                {t('email', 'Email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaEnvelope className="text-gray-400" />
                </div>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="password" className="block text-gray-700 text-sm font-medium">
                  {t('password', 'Пароль')}
                </label>
                <Link
                  to="/forgot-password"
                  className="text-sm text-primary hover:text-primary-dark"
                >
                  {t('forgot_password', 'Забыли пароль?')}
                </Link>
              </div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary"
                  placeholder="••••••••"
                />
              </div>
            </div>

            <div className="mb-6">
              <button
                type="submit"
                disabled={loading}
                className="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                {loading ? t('logging_in', 'Вход...') : t('login', 'Войти')}
              </button>
            </div>
          </form>

          <p className="text-center text-gray-600">
            {t('dont_have_account', 'Нет аккаунта?')}{' '}
            <Link to="/register" className="text-primary hover:text-primary-dark">
              {t('register', 'Зарегистрироваться')}
            </Link>
          </p>
        </div>
      </div>
    </>
  );
};

export default LoginPage;
