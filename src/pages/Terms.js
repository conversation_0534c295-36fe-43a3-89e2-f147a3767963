import { useTranslation } from 'react-i18next';

const Terms = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-6 py-12">
      <h1 className="text-3xl font-semibold mb-8 text-center">
        {t('terms_of_use', 'Terms of Use')}
      </h1>
      <div className="card">
        <p className="text-gray-600 mb-6">
          {t(
            'terms_description',
            'By using KitchenShop, you agree to the following terms and conditions. Please read them carefully.'
          )}
        </p>
        <h2 className="text-xl font-semibold mb-4">{t('usage', 'Usage')}</h2>
        <p className="text-gray-600 mb-6">
          {t(
            'usage_description',
            'You agree to use our website for lawful purposes only and in a way that does not infringe on the rights of others.'
          )}
        </p>
        <h2 className="text-xl font-semibold mb-4">{t('returns', 'Returns')}</h2>
        <p className="text-gray-600">
          {t(
            'returns_description',
            'We accept returns within 30 days of purchase, provided the items are in their original condition.'
          )}
        </p>
      </div>
    </div>
  );
};

export default Terms;
