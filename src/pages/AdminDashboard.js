import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../context/AuthContext';
import { Helmet } from 'react-helmet-async';
// import { supabase } from '../supabaseClient'; // supabase не используется в этом файле
import { Link, useNavigate, Outlet } from 'react-router-dom'; // Added Outlet

// Импорт административных компонентов
import AdminMenuSidebar from '../components/admin/AdminMenuSidebar';

const AdminDashboard = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { logout } = useAuth(); // Removed unused variables

  // Statistics and loading state are now managed by AdminDashboardContent or specific child pages
  // This component is now primarily a layout

  const handleLogout = () => {
    logout(() => {
      navigate('/');
    });
  };

  return (
    <>
      <Helmet>
        <title>{t('admin_dashboard_title', 'Admin Dashboard')}</title>
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="container mx-auto px-2 py-4 max-w-full">
        <div className="flex flex-wrap justify-between items-center mb-4">
          <h1 className="text-2xl font-semibold">
            {t('admin_dashboard', 'Панель администратора')}
          </h1>
          <div className="flex gap-2 mt-2 sm:mt-0">
            <Link
              to="/"
              className="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
            >
              {t('view_site', 'Просмотреть сайт')}
            </Link>
            <button
              onClick={handleLogout}
              className="px-3 py-1.5 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
            >
              {t('logout', 'Logout')}
            </button>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-2">
          {/* Сайдбар с меню админки - уменьшенный размер */}
          <div className="lg:w-auto">
            <AdminMenuSidebar activePath="/" />
          </div>

          {/* Основное содержимое - увеличенная ширина */}
          <div className="lg:flex-1 overflow-auto">
            <Outlet />
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminDashboard;
