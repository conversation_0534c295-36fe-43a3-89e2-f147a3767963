import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import BannerSection from '../components/BannerSection';
import FeaturedCategories from '../components/home/<USER>';
import ProductSlider from '../components/home/<USER>';
import BrandsSection from '../components/BrandsSection';
import SEO, { SchemaTemplates } from '../seo';

const Home = () => {
  const { t } = useTranslation();

  // Анимация для секций
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8 }
    }
  };

  // Добавляем класс для body при монтировании компонента
  useEffect(() => {
    document.body.classList.add('has-top-banner');

    // Удаляем класс при размонтировании
    return () => {
      document.body.classList.remove('has-top-banner');
    };
  }, []);

  // Подготавливаем структурированные данные для SEO
  const structuredData = {
    ...SchemaTemplates.organization,
    '@type': ['Organization', 'WebSite'],
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://yoursite.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  };

  return (
    <div className="home-page">
      <SEO
        title={t('home_page_title', 'Главная страница')}
        description={t(
          'home_meta_description',
          'Лучшие товары с доставкой по выгодным ценам в нашем интернет магазине'
        )}
        keywords={t('home_meta_keywords', 'интернет магазин, товары, доставка, акции, скидки')}
        url="/"
        structuredData={structuredData}
      />

      {/* 1. Главный баннер */}
      <BannerSection
        position="top"
        textColor="white"
        darkOverlay={true}
        overlayOpacity="0.4"
        height="large"
        title={t('main_banner_title', 'Откройте для себя новую коллекцию')}
        subtitle={t(
          'main_banner_subtitle',
          'Эксклюзивные товары с непревзойденным качеством и стилем'
        )}
        buttonText={t('shop_now', 'Смотреть коллекцию')}
        buttonLink="/products"
      />

      <div className="container mx-auto px-4">
        {/* 2. Преимущества магазина - улучшенная секция */}
        <motion.section
          className="py-12 md:py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={sectionVariants}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              {t('why_choose_us', 'Почему выбирают нас')}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6"></div>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              {t('why_choose_description', 'Мы предлагаем лучший сервис и качество для наших клиентов')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            <motion.div
              className="flex flex-col items-center text-center p-6 md:p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 md:h-10 md:w-10 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 text-gray-800">Гарантия качества</h3>
              <p className="text-gray-600 leading-relaxed">
                Мы тщательно отбираем каждый товар в нашем магазине и предоставляем официальную гарантию
              </p>
            </motion.div>

            <motion.div
              className="flex flex-col items-center text-center p-6 md:p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 md:h-10 md:w-10 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 text-gray-800">Быстрая доставка</h3>
              <p className="text-gray-600 leading-relaxed">
                Доставим ваш заказ в кратчайшие сроки по всей стране с возможностью отслеживания
              </p>
            </motion.div>

            <motion.div
              className="flex flex-col items-center text-center p-6 md:p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 md:h-10 md:w-10 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
              </div>
              <h3 className="text-xl md:text-2xl font-bold mb-4 text-gray-800">Удобная оплата</h3>
              <p className="text-gray-600 leading-relaxed">
                Множество способов оплаты: карты, электронные кошельки, наличные при получении
              </p>
            </motion.div>
          </div>
        </motion.section>

        {/* 3. Блок с категориями - с анимацией */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={sectionVariants}
        >
          <FeaturedCategories maxCategories={6} />
        </motion.div>
      </div>

      {/* 4. Акции (слайдер) - между двумя контейнерами для полноэкранной секции */}
      <div className="bg-gray-50 py-10 md:py-16 my-8 md:my-12">
        <div className="container mx-auto px-4">
          <motion.section
            className="mb-6 md:mb-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={sectionVariants}
          >
            <div className="flex justify-between items-center mb-6 md:mb-8">
              <h2 className="text-2xl md:text-3xl font-bold">
                {t('special_offers', 'Специальные предложения')}
              </h2>
              <Link
                to="/sale"
                className="text-primary hover:text-primary-dark transition-colors flex items-center text-sm md:text-base"
              >
                {t('view_all', 'Смотреть все')}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 md:h-5 md:w-5 ml-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
            <ProductSlider type="sale" slidesToShow={4} slidesToScroll={1} autoplay={true} />
          </motion.section>
        </div>
      </div>

      <div className="container mx-auto px-4">
        {/* 5. Новые поступления (слайдер) */}
        <motion.section
          className="mb-10 md:mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={sectionVariants}
        >
          <div className="flex justify-between items-center mb-6 md:mb-8">
            <h2 className="text-2xl md:text-3xl font-bold">
              {t('new_arrivals', 'Новые поступления')}
            </h2>
            <Link
              to="/new-arrivals"
              className="text-primary hover:text-primary-dark transition-colors flex items-center text-sm md:text-base"
            >
              {t('view_all', 'Смотреть все')}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 md:h-5 md:w-5 ml-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
          <ProductSlider type="new" slidesToShow={4} slidesToScroll={1} autoplay={true} />
        </motion.section>
      </div>

      {/* Баннер и бестселлеры в общей обертке без отступов */}
      <div className="banner-bestsellers-section" style={{ marginTop: '50px' }}>
        {/* 6. Второй баннер - промо */}
        <div className="bottom-banner-container">
          <BannerSection
            position="bottom"
            textColor="white"
            darkOverlay={true}
            overlayOpacity="0.5"
            height="medium"
            title={t('promo_banner_title', 'Скидки до 50% на летнюю коллекцию')}
            subtitle={t('promo_banner_subtitle', 'Только до конца месяца!')}
            buttonText={t('view_offers', 'Смотреть акции')}
            buttonLink="/sale"
          />
        </div>

        {/* 7. Хиты продаж (слайдер) - сразу после баннера */}
        <div className="container mx-auto px-4 bestsellers-direct">
          <motion.section
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={sectionVariants}
            className="mb-10 md:mb-16"
          >
            <div className="flex justify-between items-center mb-6 md:mb-8">
              <h2 className="text-2xl md:text-3xl font-bold">{t('bestsellers', 'Хиты продаж')}</h2>
              <Link
                to="/bestsellers"
                className="text-primary hover:text-primary-dark transition-colors flex items-center text-sm md:text-base"
              >
                {t('view_all', 'Смотреть все')}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 md:h-5 md:w-5 ml-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
            <ProductSlider type="bestseller" slidesToShow={4} slidesToScroll={1} autoplay={true} />
          </motion.section>
        </div>

        {/* 8. Отзывы клиентов */}
        <div className="bg-gray-50 py-16 my-12">
          <div className="container mx-auto px-4">
            <motion.section
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={sectionVariants}
            >
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                  {t('customer_reviews', 'Отзывы наших клиентов')}
                </h2>
                <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary-dark mx-auto mb-6"></div>
                <p className="text-gray-600 max-w-2xl mx-auto text-lg">
                  {t('reviews_description', 'Узнайте, что говорят о нас наши довольные клиенты')}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Отзыв 1 */}
                <motion.div
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ y: -5 }}
                >
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                        </svg>
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4 italic">
                    "Отличный магазин! Быстрая доставка, качественные товары и приятные цены. Обязательно буду заказывать еще!"
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
                      А
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800">Анна Петрова</p>
                      <p className="text-sm text-gray-500">Постоянный клиент</p>
                    </div>
                  </div>
                </motion.div>

                {/* Отзыв 2 */}
                <motion.div
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ y: -5 }}
                >
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                        </svg>
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4 italic">
                    "Профессиональный подход к каждому клиенту. Помогли с выбором, проконсультировали по всем вопросам."
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
                      М
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800">Михаил Сидоров</p>
                      <p className="text-sm text-gray-500">Клиент с 2022 года</p>
                    </div>
                  </div>
                </motion.div>

                {/* Отзыв 3 */}
                <motion.div
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ y: -5 }}
                >
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                        </svg>
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4 italic">
                    "Удобный сайт, широкий ассортимент и честные цены. Рекомендую всем друзьям и знакомым!"
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
                      Е
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800">Елена Козлова</p>
                      <p className="text-sm text-gray-500">VIP клиент</p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.section>
          </div>
        </div>

        {/* 9. Статистика компании */}
        <div className="bg-gradient-to-r from-primary to-primary-dark py-16 my-12">
          <div className="container mx-auto px-4">
            <motion.section
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={sectionVariants}
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">5000+</div>
                  <div className="text-lg opacity-90">{t('satisfied_customers', 'Довольных клиентов')}</div>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">10000+</div>
                  <div className="text-lg opacity-90">{t('products_sold', 'Товаров продано')}</div>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">50+</div>
                  <div className="text-lg opacity-90">{t('partner_brands', 'Брендов-партнеров')}</div>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">3</div>
                  <div className="text-lg opacity-90">{t('years_experience', 'Года опыта')}</div>
                </motion.div>
              </div>
            </motion.section>
          </div>
        </div>

        {/* 10. Наши бренды (слайдер) */}
        <div className="container mx-auto px-4">
          <motion.section
            className="mb-8 md:mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={sectionVariants}
          >
            <BrandsSection />
          </motion.section>
        </div>
      </div>
    </div>
  );
};

export default Home;
