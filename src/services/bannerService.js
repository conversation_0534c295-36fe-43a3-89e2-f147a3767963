import { supabase } from '../supabaseClient'; // Обновлённый импорт
import { handleError } from '../utils/errors';
import { TABLES, STORAGE_BUCKETS } from '../config/constants';
import { uploadImage } from '../utils/imageHelpers';

export const bannerService = {
  async getBanners() {
    try {
      const { data, error } = await supabase.from(TABLES.BANNERS).select('*').order('position');

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error);
      return [];
    }
  },
  async uploadBannerImage(file, path) {
    try {
      const imageUrl = await uploadImage(file, path, STORAGE_BUCKETS.BANNERS);
      return imageUrl;
    } catch (error) {
      handleError(error);
      return null;
    }
  },
  async getBannerByPosition(position) {
    try {
      const { data, error } = await supabase
        .from(TABLES.BANNERS)
        .select('*')
        .eq('is_active', true)
        .eq('position', position)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error);
      return null;
    }
  },

  async updateBanner(id, bannerData) {
    try {
      const { data, error } = await supabase
        .from(TABLES.BANNERS)
        .update(bannerData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      handleError(error);
      return null;
    }
  },

  async deleteBanner(id) {
    try {
      const { error } = await supabase.from(TABLES.BANNERS).delete().eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      handleError(error);
      return false;
    }
  }
};
