import { supabase } from '../supabaseClient';

// Email service for manual email sending and email logs management
export class EmailService {
  /**
   * Manually send order confirmation email
   * @param {string} orderId - Order ID
   * @param {Object} orderData - Optional order data to avoid extra DB call
   */
  static async sendOrderConfirmation(orderId, orderData = null) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.functions) {
        console.error('❌ Supabase client not available in sendOrderConfirmation');
        throw new Error('Email service not available');
      }

      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          type: 'order_confirmation',
          orderId,
          orderData
        }
      });

      if (error) {
        console.error('Error sending order confirmation email:', error);
        throw error;
      }

      console.log('Order confirmation email sent successfully:', data);
      return data;
    } catch (error) {
      console.error('Failed to send order confirmation email:', error);
      throw error;
    }
  }

  /**
   * Manually send status update email
   * @param {string} orderId - Order ID
   * @param {string} oldStatus - Previous order status
   * @param {Object} orderData - Optional order data to avoid extra DB call
   */
  static async sendStatusUpdate(orderId, oldStatus, orderData = null) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.functions) {
        console.error('❌ Supabase client not available in sendStatusUpdate');
        throw new Error('Email service not available');
      }

      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          type: 'status_update',
          orderId,
          oldStatus,
          orderData
        }
      });

      if (error) {
        console.error('Error sending status update email:', error);
        throw error;
      }

      console.log('Status update email sent successfully:', data);
      return data;
    } catch (error) {
      console.error('Failed to send status update email:', error);
      throw error;
    }
  }

  /**
   * Get email logs for an order
   * @param {string} orderId - Order ID
   */
  static async getOrderEmailLogs(orderId) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in getOrderEmailLogs');
        return [];
      }

      const { data, error } = await supabase
        .from('email_logs')
        .select('*')
        .eq('order_id', orderId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching email logs:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch email logs:', error);
      return [];
    }
  }

  /**
   * Get all email logs with optional filtering
   * @param {Object} filters - Optional filters
   */
  static async getEmailLogs(filters = {}) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in getEmailLogs');
        return [];
      }

      let query = supabase.from('email_logs').select('*').order('created_at', { ascending: false });

      // Apply filters
      if (filters.email_type) {
        query = query.eq('email_type', filters.email_type);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.recipient) {
        query = query.ilike('recipient', `%${filters.recipient}%`);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching email logs:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch email logs:', error);
      return [];
    }
  }

  /**
   * Test email functionality by sending a test email
   * @param {string} recipient - Test email recipient
   * @param {string} orderId - Test order ID (optional)
   */
  static async sendTestEmail(recipient, orderId = null) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.functions) {
        console.error('❌ Supabase client not available in sendTestEmail');
        throw new Error('Email service not available');
      }

      // Create a mock order data for testing
      const testOrderData = {
        id: orderId || 'test-' + Date.now(),
        customer_name: 'Тестовый Клиент',
        customer_email: recipient,
        customer_phone: '+380123456789',
        total_amount: 999.99,
        status: 'pending',
        payment_method: 'cash_on_delivery',
        payment_status: 'pending',
        shipping_address: {
          city: 'Киев',
          nova_poshta_office: 'Отделение №1'
        },
        notes: 'Тестовый заказ для проверки email системы',
        created_at: new Date().toISOString(),
        order_items: [
          {
            product_name: 'Тестовый товар 1',
            quantity: 2,
            price: 299.99
          },
          {
            product_name: 'Тестовый товар 2',
            quantity: 1,
            price: 399.99
          }
        ]
      };

      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          type: 'order_confirmation',
          orderData: testOrderData
        }
      });

      if (error) {
        console.error('Error sending test email:', error);
        throw error;
      }

      console.log('Test email sent successfully:', data);
      return data;
    } catch (error) {
      console.error('Failed to send test email:', error);
      throw error;
    }
  }

  /**
   * Resend email for an order
   * @param {string} orderId - Order ID
   * @param {string} emailType - Email type ('order_confirmation' or 'status_update')
   */
  static async resendEmail(orderId, emailType) {
    try {
      if (emailType === 'order_confirmation') {
        return await this.sendOrderConfirmation(orderId);
      } else if (emailType === 'status_update') {
        // Guard against undefined supabase client
        if (!supabase || !supabase.from) {
          console.error('❌ Supabase client not available in resendEmail');
          throw new Error('Database connection not available');
        }

        // For status update, we need to get current status
        const { data: order, error } = await supabase
          .from('orders')
          .select('status')
          .eq('id', orderId)
          .single();

        if (error) throw error;

        return await this.sendStatusUpdate(orderId, 'pending', { ...order, id: orderId });
      } else {
        throw new Error('Invalid email type');
      }
    } catch (error) {
      console.error('Failed to resend email:', error);
      throw error;
    }
  }

  /**
   * Check if email triggers are working by creating email logs table if needed
   */
  static async initializeEmailSystem() {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in initializeEmailSystem');
        return { success: false, message: 'Database connection not available.' };
      }

      // Check if email_logs table exists by trying to query it
      const { data, error } = await supabase.from('email_logs').select('id').limit(1);

      if (error && error.code === '42P01') {
        // Table doesn't exist, need to create it
        console.warn('Email logs table does not exist. Please run the migration.');
        return { success: false, message: 'Email system not initialized. Please run migrations.' };
      }

      return { success: true, message: 'Email system is ready.' };
    } catch (error) {
      console.error('Error initializing email system:', error);
      return { success: false, message: 'Failed to initialize email system.' };
    }
  }
}

export default EmailService;
