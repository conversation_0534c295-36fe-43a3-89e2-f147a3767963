import { supabase } from '../supabaseClient';

// Mock Email service for development and testing
export class MockEmailService {
  /**
   * Mock send order confirmation email
   * @param {string} orderId - Order ID
   * @param {Object} orderData - Order data
   */
  static async sendOrderConfirmation(orderId, orderData = null) {
    try {
      console.log('🔄 [MOCK] Sending order confirmation email...');
      console.log('📧 To:', orderData?.customer_email);
      console.log('📋 Order ID:', orderId);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Log to database
      try {
        await supabase.from('email_logs').insert({
          order_id: orderId,
          recipient: orderData?.customer_email || '<EMAIL>',
          email_type: 'order_confirmation',
          subject: `Подтверждение заказа №${orderId.substring(0, 8)}`,
          status: 'sent_mock',
          sent_at: new Date().toISOString(),
          external_id: 'mock_' + Date.now()
        });
      } catch (logError) {
        console.warn('Could not log mock email:', logError.message);
      }

      console.log('✅ [MOCK] Order confirmation email "sent" successfully');

      return {
        success: true,
        id: 'mock_' + Date.now(),
        message: 'Mock email sent - no real email was delivered'
      };
    } catch (error) {
      console.error('❌ [MOCK] Failed to send order confirmation email:', error);
      throw error;
    }
  }

  /**
   * Mock send status update email
   * @param {string} orderId - Order ID
   * @param {string} oldStatus - Previous order status
   * @param {Object} orderData - Order data
   */
  static async sendStatusUpdate(orderId, oldStatus, orderData = null) {
    try {
      console.log('🔄 [MOCK] Sending status update email...');
      console.log('📧 To:', orderData?.customer_email);
      console.log('📋 Order ID:', orderId);
      console.log('🔄 Status change:', oldStatus, '→', orderData?.status);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Log to database
      try {
        await supabase.from('email_logs').insert({
          order_id: orderId,
          recipient: orderData?.customer_email || '<EMAIL>',
          email_type: 'status_update',
          subject: `Обновление статуса заказа №${orderId.substring(0, 8)}`,
          status: 'sent_mock',
          sent_at: new Date().toISOString(),
          external_id: 'mock_' + Date.now()
        });
      } catch (logError) {
        console.warn('Could not log mock email:', logError.message);
      }

      console.log('✅ [MOCK] Status update email "sent" successfully');

      return {
        success: true,
        id: 'mock_' + Date.now(),
        message: 'Mock email sent - no real email was delivered'
      };
    } catch (error) {
      console.error('❌ [MOCK] Failed to send status update email:', error);
      throw error;
    }
  }

  /**
   * Get email logs for an order
   * @param {string} orderId - Order ID
   */
  static async getOrderEmailLogs(orderId) {
    try {
      const { data, error } = await supabase
        .from('email_logs')
        .select('*')
        .eq('order_id', orderId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching email logs:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch email logs:', error);
      return [];
    }
  }

  /**
   * Send test email
   * @param {string} email - Email address
   * @param {string} subject - Email subject
   */
  static async sendTestEmail(email, subject = 'Test Email') {
    try {
      console.log('🧪 [MOCK] Sending test email...');
      console.log('📧 To:', email);
      console.log('📝 Subject:', subject);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Log to database
      try {
        await supabase.from('email_logs').insert([
          {
            email_type: 'test_email',
            recipient: email,
            subject: subject,
            status: 'sent',
            sent_at: new Date().toISOString(),
            provider: 'mock_service'
          }
        ]);
      } catch (logError) {
        console.log('Could not log to database:', logError);
      }

      console.log('✅ [MOCK] Test email sent successfully');
      return {
        success: true,
        message: 'Mock test email sent successfully'
      };
    } catch (error) {
      console.error('❌ [MOCK] Error sending test email:', error);
      throw error;
    }
  }

  /**
   * Test mock email functionality
   */
  static async testMockEmail() {
    try {
      const testOrder = {
        id: 'test-mock-' + Date.now(),
        customer_name: 'Test Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+**********',
        total_amount: 100,
        created_at: new Date().toISOString(),
        status: 'pending'
      };

      console.log('🧪 Testing mock email service...');

      const result = await this.sendOrderConfirmation(testOrder.id, testOrder);

      return {
        success: true,
        result,
        message: 'Mock email service is working correctly'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Mock email service test failed'
      };
    }
  }
}

// Re-export EmailService for convenience
export { EmailService } from './emailService';
