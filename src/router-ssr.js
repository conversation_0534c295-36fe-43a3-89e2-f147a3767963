// Router специально для SSR (для использования с StaticRouter)
import React from 'react';
import { Route, Routes } from 'react-router-dom';
import App from './App';
import Home from './pages/Home';
import ProductPage from './pages/ProductPage';
import CategoryPage from './pages/CategoryPage';
import SearchPage from './pages/Search';
import AboutPage from './pages/AboutPage';
import ContactPage from './pages/ContactPage';
import SalePage from './pages/SalePage';
import NewArrivalsPage from './pages/NewArrivalsPage';
import BestsellersPage from './pages/BestsellersPage';
import ErrorPage from './pages/ErrorPage';

// Определяем только основные маршруты для SSR (наиболее важные для SEO)
const RouterSSR = () => (
  <Routes>
    <Route path="/" element={<App />}>
      <Route index element={<Home />} />
      <Route path="product/:id" element={<ProductPage />} />
      <Route path="category/:slug" element={<CategoryPage />} />
      <Route path="search" element={<SearchPage />} />
      <Route path="about" element={<AboutPage />} />
      <Route path="contact" element={<ContactPage />} />
      <Route path="sale" element={<SalePage />} />
      <Route path="new-arrivals" element={<NewArrivalsPage />} />
      <Route path="bestsellers" element={<BestsellersPage />} />
      <Route path="*" element={<ErrorPage />} />
    </Route>
  </Routes>
);

export default RouterSSR;
