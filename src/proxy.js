require('dotenv').config({ path: '.env.local' });
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;
const API_PORT = process.env.REACT_APP_API_PORT || 5001;

// Проксирование API запросов
app.use(
  '/api',
  createProxyMiddleware({
    target: `http://localhost:${API_PORT}`,
    changeOrigin: true,
    onProxyReq: (proxyReq, req) => {},
    onError: (err, req, res) => {
      console.error('Proxy error:', err);
      res.status(500).send('Proxy error: ' + err.message);
    }
  })
);

// Статический контент
app.use(express.static(path.join(__dirname, '../build')));

// Все остальные запросы направляем на React
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../build', 'index.html'));
});

app.listen(PORT, () => {});
