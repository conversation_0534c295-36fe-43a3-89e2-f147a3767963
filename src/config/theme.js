/**
 * Central theme configuration for the application
 * This allows consistent access to theme values throughout the application
 */

export const theme = {
  colors: {
    background: '#F5F5F5', // светлый кремовый фон всей страницы
    cardBackground: '#EDE4D8', // более насыщенный кремовый для карточек товаров
    sectionBackground: '#EDE4D8', // фон секций (баннеры, блоки)

    primary: '#D4B27D', // золотисто-коричневый для кнопок (CTA)
    primaryDark: '#C5A36E', // темнее для hover состояний
    primaryText: '#FFFFFF', // белый текст на кнопках

    heading: '#000000', // черный для заголовков
    body: '#333333', // темно-серый для основного текста
    muted: '#666666', // светло-серый для вторичного текста

    star: '#D4B27D', // золотисто-коричневый для звезд рейтинга

    footerBg: '#F5F5F5', // светлый кремовый фон футера
    footerText: '#333333', // темно-серый текст футера

    border: '#CCCCCC', // цвет рамок и разделителей
    shadow: 'rgba(0, 0, 0, 0.05)' // лёгкие тени
  },

  // Font configurations
  fonts: {
    body: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
    heading: 'Poppins, -apple-system, BlinkMacSystemFont, sans-serif'
  },

  // Font sizes
  fontSizes: {
    h1: '3rem', // Заголовки первого уровня
    h2: '2.25rem', // Заголовки второго уровня
    h3: '1.875rem', // Заголовки третьего уровня
    base: '1rem', // Основной текст
    lg: '1.125rem', // Крупный текст
    sm: '0.875rem' // Мелкий текст
  },

  // Helper function to get a CSS variable-compatible version of the theme
  toCssVars: () => {
    const cssVars = {};

    // Colors to CSS variables
    Object.entries(theme.colors).forEach(([key, value]) => {
      cssVars[`--color-${key}`] = value;
    });

    // Font sizes to CSS variables
    Object.entries(theme.fontSizes).forEach(([key, value]) => {
      cssVars[`--font-size-${key}`] = value;
    });

    return cssVars;
  }
};

export default theme;
