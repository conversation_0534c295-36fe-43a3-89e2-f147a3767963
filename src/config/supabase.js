// Supabase configuration

// Use environment variables with fallbacks for development
export const SUPABASE_URL = process.env.REACT_APP_SUPABASE_URL;
export const SUPABASE_ANON_KEY = process.env.REACT_APP_SUPABASE_ANON_KEY;

export const STORAGE_CONFIG = {
  BANNERS_PATH: 'banners',
  PRODUCTS_PATH: 'products',
  CATEGORIES_PATH: 'categories'
};

// These are the tables we'll use in the application
export const TABLES = {
  PRODUCTS: 'products',
  CATEGORIES: 'categories',
  BANNERS: 'banners',
  PRODUCT_PARAMS: 'product_params',
  ORDERS: 'orders',
  ORDER_ITEMS: 'order_items',
  USERS: 'users',
  PROFILES: 'profiles'
};
