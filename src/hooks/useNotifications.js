import { useEffect, useState } from 'react';

const useNotifications = user => {
  const [token, setToken] = useState(null);

  useEffect(() => {
    const requestPermission = async () => {
      try {
        const permission = await Notification.requestPermission();
        if (permission === 'granted') {
          setToken('placeholder-token');
        } else {
        }
      } catch (error) {
        console.error('Error requesting notification permission:', error);
      }
    };

    requestPermission();
  }, []); // Removed `user` from dependencies to avoid unnecessary re-renders

  return token;
};

export default useNotifications;
