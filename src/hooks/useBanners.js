import { useState, useEffect } from 'react';
import { bannerService } from '../services/bannerService'; // Исправлен импорт
import { handleError } from '../utils/errors'; // Исправлен импорт

export const useBanners = (position = 'top') => {
  const [banner, setBanner] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBanner = async () => {
      try {
        setLoading(true);
        const data = await bannerService.getBannerByPosition(position);
        setBanner(data);
      } catch (err) {
        handleError(err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchBanner();
  }, [position]);

  return { banner, loading, error };
};
