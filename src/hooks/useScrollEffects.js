import { useEffect, useRef } from 'react';

/**
 * Хук для создания эффекта "прилипания" (sticky) элементов при скролле
 * @param {Object} options - Настройки эффекта
 * @param {number} options.threshold - Пороговое значение скролла для активации эффекта
 * @param {boolean} options.enabled - Включен ли эффект
 * @param {string} options.stickyClass - CSS класс для sticky состояния
 * @param {number} options.zIndex - Z-index для sticky элемента
 */
export const useStickyEffect = (options = {}) => {
  const {
    threshold = 100,
    enabled = true,
    stickyClass = 'sticky-active',
    zIndex = 50
  } = options;

  const elementRef = useRef(null);
  const isSticky = useRef(false);

  useEffect(() => {
    if (!enabled || !elementRef.current) return;

    const element = elementRef.current;
    const originalPosition = element.style.position;
    const originalTop = element.style.top;
    const originalZIndex = element.style.zIndex;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      if (scrollTop > threshold && !isSticky.current) {
        // Активируем sticky эффект
        element.style.position = 'sticky';
        element.style.top = '0';
        element.style.zIndex = zIndex;
        element.classList.add(stickyClass);
        isSticky.current = true;
      } else if (scrollTop <= threshold && isSticky.current) {
        // Деактивируем sticky эффект
        element.style.position = originalPosition;
        element.style.top = originalTop;
        element.style.zIndex = originalZIndex;
        element.classList.remove(stickyClass);
        isSticky.current = false;
      }
    };

    // Добавляем слушатель события с throttling для производительности
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });

    // Проверяем состояние при монтировании
    handleScroll();

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      // Восстанавливаем оригинальные стили
      if (element) {
        element.style.position = originalPosition;
        element.style.top = originalTop;
        element.style.zIndex = originalZIndex;
        element.classList.remove(stickyClass);
      }
    };
  }, [threshold, enabled, stickyClass, zIndex]);

  return elementRef;
};

/**
 * Хук для создания параллакс эффекта
 * @param {Object} options - Настройки параллакса
 * @param {number} options.speed - Скорость параллакса (0.1 - 1.0)
 * @param {boolean} options.enabled - Включен ли эффект
 */
export const useParallaxEffect = (options = {}) => {
  const { speed = 0.5, enabled = true } = options;
  const elementRef = useRef(null);

  useEffect(() => {
    if (!enabled || !elementRef.current) return;

    const element = elementRef.current;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + scrollTop;
      
      // Вычисляем смещение для параллакса
      const offset = (scrollTop - elementTop) * speed;
      
      // Применяем трансформацию только если элемент виден
      if (rect.bottom >= 0 && rect.top <= window.innerHeight) {
        element.style.transform = `translateY(${offset}px)`;
      }
    };

    // Throttling для производительности
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      if (element) {
        element.style.transform = '';
      }
    };
  }, [speed, enabled]);

  return elementRef;
};

/**
 * Хук для создания эффекта появления элементов при скролле
 * @param {Object} options - Настройки эффекта
 * @param {number} options.threshold - Порог пересечения (0.0 - 1.0)
 * @param {boolean} options.triggerOnce - Срабатывать только один раз
 */
export const useScrollReveal = (options = {}) => {
  const { threshold = 0.1, triggerOnce = true } = options;
  const elementRef = useRef(null);

  useEffect(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in-up');
            if (triggerOnce) {
              observer.unobserve(entry.target);
            }
          } else if (!triggerOnce) {
            entry.target.classList.remove('animate-fade-in-up');
          }
        });
      },
      { threshold }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [threshold, triggerOnce]);

  return elementRef;
};
