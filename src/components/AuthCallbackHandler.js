import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';

const AuthCallbackHandler = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        if (data?.session) {
          toast.success(t('auth_successful', 'Вход выполнен успешно!'));
        } else {
          toast.error(t('auth_callback_no_session', 'Не удалось получить сессию.'));
        }
      } catch (err) {
        console.error('Authentication callback error:', err);
        toast.error(t('auth_callback_error', 'Ошибка при аутентификации'));
      }

      navigate('/');
    };

    handleAuthCallback();
  }, [navigate, t]);

  return (
    <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <p>{t('authenticating', 'Аутентификация...')}</p>
      </div>
    </div>
  );
};

export default AuthCallbackHandler;
