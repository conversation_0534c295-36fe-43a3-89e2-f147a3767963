import { useState } from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { banners } from '../assets/images/data';

const Carousel = () => {
  const [isLoading, setIsLoading] = useState(true);

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="mb-8 relative">
      {isLoading && (
        <div className="w-full h-96 flex items-center justify-center bg-gray-200">
          <p className="text-gray-600">Loading...</p>
        </div>
      )}
      <Slider {...settings}>
        {banners.map(banner => (
          <div key={banner.id} className="relative">
            <img
              src={banner.image}
              alt={banner.alt}
              className="w-full h-96 object-cover"
              onLoad={handleImageLoad}
              onError={e => {
                e.target.onerror = null;
                e.target.src =
                  banner.fallback ||
                  'https://placehold.co/1200x400/EEE/31343C?text=Banner+Not+Found';
                handleImageLoad();
              }}
            />
            <div className="absolute bottom-4 left-4 text-white">
              <h2 className="text-2xl font-bold">{banner.alt}</h2>
              <p className="text-lg">Shop Now</p>
            </div>
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default Carousel;
