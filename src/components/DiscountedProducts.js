import { useTranslation } from 'react-i18next';
import ProductCard from './ProductCard';
import { products } from '../assets/images/data';

const DiscountedProducts = () => {
  const { t } = useTranslation();
  const discountedProducts = products.filter(product => product.discountPrice);

  if (discountedProducts.length === 0) return null;

  return (
    <div className="container mx-auto px-6 py-12">
      <h2 className="text-3xl font-semibold mb-8 text-center">{t('discounts', 'Акции')}</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {discountedProducts.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
};

export default DiscountedProducts;
