import React, { useEffect } from 'react';
// import { Link } from 'react-router-dom'; // No longer directly used for rendering cards
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { fetchProducts } from '../store/slices/productsSlice';
import ProductList from './ProductList'; // Import the reusable ProductList component

const ProductsList = () => {
  const { t } = useTranslation(); // t might still be needed for loading/error messages if not handled by ProductList
  const dispatch = useDispatch();
  const { items: products, loading, error } = useSelector(state => state.products);

  useEffect(() => {
    dispatch(fetchProducts());
  }, [dispatch]);

  // ProductList component now handles loading, error, and empty states.
  // We just pass the data to it.
  return <ProductList products={products} loading={loading} error={error} />;
};

export default ProductsList;
