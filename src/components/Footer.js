import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Heading, Text } from './ui/Typography';

const Footer = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  // Стиль для ссылок в футере
  const footerLinkClass = 'text-body hover:text-primary transition-colors';

  return (
    <footer className="bg-footerBg py-8">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <Heading as="h3" variant="h5" className="mb-4">
              {t('navigation', 'Навигация')}
            </Heading>
            <ul className="space-y-2">
              <li>
                <Link to="/" className={footerLinkClass}>
                  {t('home', 'Главная')}
                </Link>
              </li>
              <li>
                <Link to="/categories" className={footerLinkClass}>
                  {t('categories', 'Категории')}
                </Link>
              </li>
              <li>
                <Link to="/about" className={footerLinkClass}>
                  {t('about', 'О нас')}
                </Link>
              </li>
              <li>
                <Link to="/contact" className={footerLinkClass}>
                  {t('contact', 'Контакты')}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <Heading as="h3" variant="h5" className="mb-4">
              {t('contacts', 'Контакты')}
            </Heading>
            <Text className="mb-2">{t('email', 'Email')}: <EMAIL></Text>
            <Text>{t('phone', 'Телефон')}: +************</Text>
          </div>
          <div>
            <Heading as="h3" variant="h5" className="mb-4">
              KitchenShop
            </Heading>
            <Text>
              {t(
                'footer_description',
                'KitchenShop - Ваш надежный магазин для кухонных принадлежностей.'
              )}
            </Text>
          </div>
        </div>
        <Text className="text-center mt-8">
          {t('footer_rights', '© {year} KitchenShop. Все права защищены.', { year: currentYear })}
        </Text>
      </div>
    </footer>
  );
};

export default Footer;
