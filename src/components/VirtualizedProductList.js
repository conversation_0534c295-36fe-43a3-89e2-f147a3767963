import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Virtuoso } from 'react-virtuoso'; // Требуется установить: npm install react-virtuoso

const VirtualizedProductList = ({ products }) => {
  const { t } = useTranslation();

  if (!products || products.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {t('no_products_match_filters', 'Нет товаров, соответствующих выбранным фильтрам')}
      </div>
    );
  }

  const ItemRenderer = index => {
    const product = products[index];
    return (
      <Link to={`/product/${product.id}`} className="block">
        <div className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
          <img
            src={product.image || '/placeholder.png'}
            alt={product.name}
            className="w-full h-48 object-cover"
            loading="lazy"
            onError={e => {
              e.target.src = '/placeholder.png';
            }}
          />
          <div className="p-4">
            <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
            <p className="text-lg font-bold">
              {new Intl.NumberFormat('uk-UA', {
                style: 'currency',
                currency: 'UAH'
              }).format(product.price)}
            </p>
          </div>
        </div>
      </Link>
    );
  };

  return (
    <div className="h-[800px]">
      <Virtuoso
        totalCount={products.length}
        itemContent={ItemRenderer}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        overscan={200}
      />
    </div>
  );
};

export default VirtualizedProductList;
