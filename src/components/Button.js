import { motion } from 'framer-motion';

const Button = ({
  children,
  icon: Icon,
  variant = 'primary',
  size = 'md',
  className = '',
  ...props
}) => {
  // Базовые классы для всех кнопок
  const baseClasses =
    'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:ring-2 focus:ring-offset-2';

  // Варианты стилей кнопок
  const variants = {
    primary: 'bg-primary hover:bg-primary-dark text-white focus:ring-primary/50',
    secondary: 'bg-gray-100 hover:bg-gray-200 text-body focus:ring-gray-200',
    outline:
      'border border-primary bg-transparent hover:bg-primary/10 text-primary focus:ring-primary/30',
    ghost: 'bg-transparent hover:bg-gray-100 text-body',
    link: 'bg-transparent underline-offset-2 hover:underline text-primary p-0'
  };

  // Размеры кнопок
  const sizes = {
    sm: 'text-sm px-3 py-1.5',
    md: 'text-base px-4 py-2',
    lg: 'text-lg px-6 py-2.5',
    xl: 'text-xl px-8 py-3'
  };

  return (
    <motion.button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      {...props}
    >
      {Icon && (
        <Icon className={`${size === 'sm' ? 'w-4 h-4' : 'w-5 h-5'} ${children ? 'mr-2' : ''}`} />
      )}
      {children}
    </motion.button>
  );
};

export default Button;
