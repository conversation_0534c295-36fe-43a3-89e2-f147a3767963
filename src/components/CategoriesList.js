import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../supabaseClient';

const CategoriesList = () => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Get all root categories
        const { data: allCategories, error } = await supabase
          .from('categories')
          .select('*')
          .is('parent_id', null)
          .order('name');

        if (error) throw error;

        // Filter to only include categories with products
        const categoriesWithProducts = [];

        for (const category of allCategories || []) {
          // Check if category has any products
          const { count, error: countError } = await supabase
            .from('products')
            .select('id', { count: 'exact', head: true })
            .eq('category_id', category.id);

          if (!countError && count > 0) {
            categoriesWithProducts.push(category);
          }
        }

        setCategories(categoriesWithProducts);
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return <div className="text-center py-4">{t('loading', 'Загрузка...')}</div>;
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
      {categories.map(category => (
        <Link
          key={category.id}
          to={`/category/${category.id}`}
          className="group block p-4 border rounded-lg hover:shadow-md transition-shadow"
        >
          {category.image ? (
            <img
              src={category.image}
              alt={category.name}
              className="w-full h-32 object-cover rounded mb-2"
            />
          ) : (
            <div className="w-full h-32 bg-gray-100 rounded flex items-center justify-center mb-2">
              <span className="text-gray-400">{t('no_image', 'Нет изображения')}</span>
            </div>
          )}
          <h3 className="text-lg font-medium group-hover:text-primary transition-colors">
            {category.name}
          </h3>
        </Link>
      ))}
    </div>
  );
};

export default CategoriesList;
