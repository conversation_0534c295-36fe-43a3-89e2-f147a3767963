import React from 'react';

const EmptyState = ({ title, description, icon: Icon, actionButton = null }) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      {Icon && (
        <div className="rounded-full bg-gray-100 p-4 mb-4">
          <Icon className="w-10 h-10 text-gray-500" />
        </div>
      )}
      <h2 className="text-xl font-medium text-gray-900 mb-1">{title}</h2>
      {description && <p className="text-gray-500 max-w-md mb-6">{description}</p>}
      {actionButton}
    </div>
  );
};

export default EmptyState;
