import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

/**
 * Компонент полноэкранного баннера с улучшенным дизайном и позиционированием
 */
const FullWidthBanner = ({ banner, position = 'top', height = 'medium' }) => {
  if (!banner) {
    return null;
  }

  // Определение высоты баннера
  const heightClass =
    {
      small: 'h-48 sm:h-56 md:h-64',
      medium: 'h-56 sm:h-64 md:h-72 lg:h-80',
      large: 'h-64 sm:h-72 md:h-80 lg:h-96'
    }[height] || 'h-64 md:h-80';

  // Данные баннера
  const { title, subtitle, image_url, link_url } = banner;

  // Анимация для контента
  const contentAnimation = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } }
  };

  // Базовый контент баннера
  const bannerContent = (
    <div
      className={`banner-${position} ${heightClass} relative overflow-hidden w-screen mx-auto`}
      style={{
        marginLeft: 'calc(-50vw + 50%)',
        marginRight: 'calc(-50vw + 50%)',
        width: '100vw'
      }}
    >
      {/* Фоновое изображение */}
      <div
        className="absolute inset-0 bg-cover bg-center w-full h-full transition-transform duration-700 hover:scale-105"
        style={{
          backgroundImage: `url(${image_url || ''})`
        }}
      />

      {/* Затемняющий оверлей */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-black/30 z-10" />

      {/* Контент баннера */}
      <div className="absolute inset-0 flex items-center justify-center z-20">
        <div className="container mx-auto px-6 md:px-12">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={contentAnimation}
            className="max-w-3xl mx-auto text-center"
          >
            <h2 className="text-white text-2xl md:text-4xl lg:text-5xl font-bold mb-4 drop-shadow-lg">
              {title}
            </h2>

            {subtitle && (
              <p className="text-white text-base md:text-xl mb-6 drop-shadow-md">{subtitle}</p>
            )}

            {link_url && link_url !== '#' && (
              <button className="inline-block bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-8 rounded-lg transition duration-300 transform hover:-translate-y-1">
                Подробнее
              </button>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );

  // Если есть ссылка, оборачиваем контент в Link
  if (link_url && link_url !== '#') {
    return (
      <Link to={link_url} className="block">
        {bannerContent}
      </Link>
    );
  }

  return bannerContent;
};

export default FullWidthBanner;
