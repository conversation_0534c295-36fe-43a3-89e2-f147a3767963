import React from 'react';

const AnimatedLoading = ({ height = 'h-24', width = 'w-full', count = 1 }) => {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <div
          key={i}
          className={`rounded-md ${height} ${width} bg-gray-200 relative overflow-hidden`}
        >
          <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"></div>
        </div>
      ))}
    </div>
  );
};

export default AnimatedLoading;
