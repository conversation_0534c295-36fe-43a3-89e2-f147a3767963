import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { getSafeImageUrl } from '../utils/imageHelpers';
import { motion, AnimatePresence } from 'framer-motion';
import { FaChevronLeft, FaChevronRight, FaPlay, FaPause } from 'react-icons/fa';
import { useStickyEffect, useParallaxEffect } from '../hooks/useScrollEffects';

// Импортируем стили для банера
import '../styles/hero-banner.css';

const HeroBannerSlider = ({ position = 'top', autoPlay = true, autoPlayInterval = 5000 }) => {
  const [banners, setBanners] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [direction, setDirection] = useState(0);
  const intervalRef = useRef(null);
  const location = useLocation();

  // Используем кастомные хуки для эффектов
  const stickyRef = useStickyEffect({
    threshold: 100,
    enabled: position === 'top',
    stickyClass: 'hero-banner-sticky',
    zIndex: 50
  });

  const parallaxRef = useParallaxEffect({
    speed: 0.3,
    enabled: true
  });

  const isHomePage = location.pathname === '/';

  // Эффект sticky/прилипания при скролле - теперь обрабатывается хуком
  // useEffect удален, логика перенесена в useStickyEffect

  // Загрузка баннеров
  useEffect(() => {
    const fetchBanners = async () => {
      if (!isHomePage) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        let query = supabase.from('banners').select('*');

        if (position === 'top') {
          query = query.lte('position', 5);
        } else {
          query = query.gt('position', 5);
        }

        const { data, error } = await query.order('position');

        if (error) {
          setError(error.message);
          return;
        }

        const activeBanners = data.filter(b => b.is_active === true || b.is_active === undefined);
        setBanners(activeBanners);
      } catch (err) {
        setError('Failed to load banners');
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, [position, isHomePage]);

  // Автопроигрывание слайдера
  useEffect(() => {
    if (isPlaying && banners.length > 1) {
      intervalRef.current = setInterval(() => {
        nextSlide();
      }, autoPlayInterval);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isPlaying, banners.length, currentIndex, autoPlayInterval]);

  const nextSlide = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % banners.length);
  };

  const prevSlide = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) => (prevIndex - 1 + banners.length) % banners.length);
  };

  const goToSlide = (index) => {
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const getImageUrl = (banner) => {
    return getSafeImageUrl(
      banner?.image_url || banner?.image || '/images/banners/default-banner.jpg',
      'banner'
    );
  };

  const getButtonText = (banner) => {
    return banner?.button_text || banner?.buttonText || 'Подробнее';
  };

  const getButtonLink = (banner) => {
    return banner?.button_link || banner?.link_url || '/products';
  };

  // Анимационные варианты для слайдов
  const slideVariants = {
    enter: (direction) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
    }),
  };

  const swipeConfidenceThreshold = 10000;
  const swipePower = (offset, velocity) => {
    return Math.abs(offset) * velocity;
  };

  if (loading) {
    return (
      <div className={`hero-banner-loading ${position}-banner-loading`}>
        <div className="loading-spinner">Загрузка...</div>
      </div>
    );
  }

  if (error || !banners.length) {
    return null;
  }

  const currentBanner = banners[currentIndex];

  return (
    <div 
      ref={stickyRef} 
      className={`hero-banner-slider ${position}-banner-slider`}
    >
      <div className="hero-banner-container">
        {/* Фоновое изображение с параллаксом */}
        <div ref={parallaxRef} className="hero-banner-background">
          <AnimatePresence initial={false} custom={direction}>
            <motion.img
              key={currentIndex}
              src={getImageUrl(currentBanner)}
              alt={currentBanner.title || ''}
              className="hero-banner-image"
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 }
              }}
              drag="x"
              dragConstraints={{ left: 0, right: 0 }}
              dragElastic={1}
              onDragEnd={(e, { offset, velocity }) => {
                const swipe = swipePower(offset.x, velocity.x);

                if (swipe < -swipeConfidenceThreshold) {
                  nextSlide();
                } else if (swipe > swipeConfidenceThreshold) {
                  prevSlide();
                }
              }}
            />
          </AnimatePresence>
        </div>

        {/* Затемняющий оверлей */}
        <div className="hero-banner-overlay"></div>

        {/* Контент баннера */}
        <div className="hero-banner-content">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              className="hero-banner-text"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="hero-banner-title">
                {currentBanner.title}
              </h1>
              {currentBanner.subtitle && (
                <p className="hero-banner-subtitle">
                  {currentBanner.subtitle}
                </p>
              )}
              <Link
                to={getButtonLink(currentBanner)}
                className="hero-banner-button"
              >
                {getButtonText(currentBanner)}
              </Link>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Навигационные стрелки */}
        {banners.length > 1 && (
          <>
            <button
              className="hero-banner-nav hero-banner-nav-prev"
              onClick={prevSlide}
              aria-label="Предыдущий слайд"
            >
              <FaChevronLeft />
            </button>
            <button
              className="hero-banner-nav hero-banner-nav-next"
              onClick={nextSlide}
              aria-label="Следующий слайд"
            >
              <FaChevronRight />
            </button>
          </>
        )}

        {/* Индикаторы слайдов */}
        {banners.length > 1 && (
          <div className="hero-banner-indicators">
            {banners.map((_, index) => (
              <button
                key={index}
                className={`hero-banner-indicator ${
                  index === currentIndex ? 'active' : ''
                }`}
                onClick={() => goToSlide(index)}
                aria-label={`Перейти к слайду ${index + 1}`}
              />
            ))}
          </div>
        )}

        {/* Кнопка воспроизведения/паузы */}
        {banners.length > 1 && (
          <button
            className="hero-banner-play-pause"
            onClick={togglePlayPause}
            aria-label={isPlaying ? 'Пауза' : 'Воспроизведение'}
          >
            {isPlaying ? <FaPause /> : <FaPlay />}
          </button>
        )}
      </div>
    </div>
  );
};

export default HeroBannerSlider;
