import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../supabaseClient';
import { Link } from 'react-router-dom';
import { FaSearch, FaFilter, FaTruck, FaBoxOpen, FaBox, FaCheck, FaTimes } from 'react-icons/fa';

const OrderHistory = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedOrder, setExpandedOrder] = useState(null);
  const [orderStatusHistory, setOrderStatusHistory] = useState({});
  const [trackingData, setTrackingData] = useState({});

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        // See if orders table exists first - this will fail quietly if not
        let query = supabase
          .from('orders')
          .select('*, order_items(*)')
          .eq('customer_email', user.email)
          .order('created_at', { ascending: false });

        // Apply filter by status if selected
        if (filterStatus) {
          query = query.eq('status', filterStatus);
        }

        // Apply search query if provided
        if (searchQuery) {
          query = query.or(
            `id.ilike.%${searchQuery}%,shipping_address->city.ilike.%${searchQuery}%`
          );
        }

        const { data, error } = await query;

        if (!error) {
          setOrders(data || []);

          // Fetch status history for each order
          if (data && data.length > 0) {
            await fetchOrdersStatusHistory(data.map(order => order.id));
          }
        }
      } catch (err) {
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [user, filterStatus, searchQuery]);

  // Fetch order status history for tracking
  const fetchOrdersStatusHistory = async orderIds => {
    try {
      const { data, error } = await supabase
        .from('order_status_history')
        .select('*')
        .in('order_id', orderIds)
        .order('created_at', { ascending: true });

      if (!error && data) {
        // Organize status history by order ID
        const historyByOrder = {};
        data.forEach(historyItem => {
          if (!historyByOrder[historyItem.order_id]) {
            historyByOrder[historyItem.order_id] = [];
          }
          historyByOrder[historyItem.order_id].push(historyItem);
        });

        setOrderStatusHistory(historyByOrder);
      }
    } catch (err) {}
  };

  // Mock function to fetch tracking information
  // In a real application, this would connect to a shipping API
  const fetchTrackingInfo = async orderId => {
    try {
      setTrackingData(prevData => ({
        ...prevData,
        [orderId]: {
          loading: true
        }
      }));

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const order = orders.find(o => o.id === orderId);
      if (!order) return;

      // Mock tracking data based on order status
      const mockTrackingData = {
        loading: false,
        trackingId: `TR-${orderId.substring(0, 8)}`,
        carrier: 'Express Delivery',
        events: []
      };

      // Add tracking events based on order status
      const now = new Date();

      if (['processing', 'packed', 'shipped', 'delivered'].includes(order.status)) {
        mockTrackingData.events.push({
          date: new Date(new Date(order.created_at).getTime() + 1 * 60 * 60 * 1000).toISOString(),
          status: 'processing',
          location: 'Логистический центр',
          description: t('order_processing_started', 'Заказ в обработке')
        });
      }

      if (['packed', 'shipped', 'delivered'].includes(order.status)) {
        mockTrackingData.events.push({
          date: new Date(new Date(order.created_at).getTime() + 24 * 60 * 60 * 1000).toISOString(),
          status: 'packed',
          location: 'Логистический центр',
          description: t('order_packed', 'Заказ упакован и готов к отправке')
        });
      }

      if (['shipped', 'delivered'].includes(order.status)) {
        mockTrackingData.events.push({
          date: new Date(new Date(order.created_at).getTime() + 48 * 60 * 60 * 1000).toISOString(),
          status: 'shipped',
          location: 'В пути',
          description: t('order_shipped', 'Заказ передан в службу доставки')
        });
      }

      if (order.status === 'delivered') {
        mockTrackingData.events.push({
          date: new Date(new Date(order.created_at).getTime() + 72 * 60 * 60 * 1000).toISOString(),
          status: 'delivered',
          location: order.shipping_address?.city || 'Пункт выдачи',
          description: t('order_delivered', 'Заказ доставлен')
        });
      }

      if (order.status === 'cancelled') {
        mockTrackingData.events.push({
          date: new Date(new Date(order.created_at).getTime() + 12 * 60 * 60 * 1000).toISOString(),
          status: 'cancelled',
          location: 'Логистический центр',
          description: t('order_cancelled', 'Заказ отменен')
        });
      }

      setTrackingData(prevData => ({
        ...prevData,
        [orderId]: mockTrackingData
      }));
    } catch (err) {
      setTrackingData(prevData => ({
        ...prevData,
        [orderId]: {
          loading: false,
          error: true
        }
      }));
    }
  };

  // Format price to currency
  const formatPrice = price => {
    return new Intl.NumberFormat('uk-UA', {
      style: 'currency',
      currency: 'UAH',
      minimumFractionDigits: 0
    }).format(price);
  };

  // Format date with time
  const formatDate = dateString => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Get badge color based on order status
  const getStatusBadgeClass = status => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'packed':
        return 'bg-indigo-100 text-indigo-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status icon
  const getStatusIcon = status => {
    switch (status) {
      case 'processing':
        return <FaBoxOpen className="mr-2" />;
      case 'packed':
        return <FaBox className="mr-2" />;
      case 'shipped':
        return <FaTruck className="mr-2" />;
      case 'delivered':
        return <FaCheck className="mr-2" />;
      case 'cancelled':
        return <FaTimes className="mr-2" />;
      default:
        return null;
    }
  };

  const handleSearch = e => {
    e.preventDefault();
    // Search is already applied via the useEffect
  };

  const toggleOrderExpansion = orderId => {
    if (expandedOrder === orderId) {
      setExpandedOrder(null);
    } else {
      setExpandedOrder(orderId);
      // Fetch tracking info when expanding an order
      if (!trackingData[orderId]) {
        fetchTrackingInfo(orderId);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-10">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">{t('order_history', 'История заказов')}</h2>

      {/* Search and Filter Bar */}
      <div className="mb-6 p-4 bg-white rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="relative flex-grow max-w-md">
            <input
              type="text"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              placeholder={t('search_orders', 'Поиск заказов...')}
              className="w-full p-2 pl-10 border rounded-lg"
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </div>

          <div className="w-full sm:w-auto">
            <div className="relative">
              <select
                value={filterStatus}
                onChange={e => setFilterStatus(e.target.value)}
                className="appearance-none w-full bg-white border p-2 pl-10 pr-8 rounded-lg"
              >
                <option value="">{t('all_statuses', 'Все статусы')}</option>
                <option value="pending">{t('pending', 'Ожидает обработки')}</option>
                <option value="processing">{t('processing', 'В обработке')}</option>
                <option value="packed">{t('packed', 'Упакован')}</option>
                <option value="shipped">{t('shipped', 'Отправлен')}</option>
                <option value="delivered">{t('delivered', 'Доставлен')}</option>
                <option value="cancelled">{t('cancelled', 'Отменен')}</option>
              </select>
              <FaFilter className="absolute left-3 top-3 text-gray-400" />
            </div>
          </div>
        </form>
      </div>

      {orders.length > 0 ? (
        <div className="space-y-6">
          {orders.map(order => (
            <div key={order.id} className="border rounded-lg overflow-hidden bg-white shadow">
              {/* Order Header */}
              <div
                className="bg-gray-50 p-4 flex flex-wrap justify-between items-center cursor-pointer hover:bg-gray-100"
                onClick={() => toggleOrderExpansion(order.id)}
              >
                <div>
                  <p className="text-sm text-gray-500">
                    {t('order_number', 'Заказ №')}:{' '}
                    <span className="font-medium text-black">{order.id.substring(0, 8)}</span>
                  </p>
                  <p className="text-sm text-gray-500">
                    {new Date(order.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full flex items-center ${getStatusBadgeClass(order.status)}`}
                  >
                    {getStatusIcon(order.status)}
                    {t(order.status || 'pending', order.status || 'Ожидает обработки')}
                  </span>
                  <span className="font-medium">{formatPrice(order.total_amount)}</span>
                </div>
              </div>

              {/* Order Items */}
              <div className="p-4 border-b">
                {order.order_items && order.order_items.length > 0 ? (
                  <div className="space-y-3">
                    {order.order_items
                      .slice(0, expandedOrder === order.id ? order.order_items.length : 2)
                      .map(item => (
                        <div key={item.id} className="flex items-center">
                          <div className="h-16 w-16 bg-gray-100 rounded flex-shrink-0 mr-4">
                            {item.product_image && (
                              <img
                                src={item.product_image}
                                alt={item.product_name}
                                className="h-full w-full object-contain"
                                onError={e => {
                                  e.target.src = '/placeholder.png';
                                }}
                              />
                            )}
                          </div>
                          <div className="flex-grow">
                            <p className="font-medium">{item.product_name}</p>
                            <p className="text-sm text-gray-500">
                              {formatPrice(item.price)} × {item.quantity}
                            </p>
                          </div>
                        </div>
                      ))}

                    {/* Show "and X more items" if order is not expanded and has more than 2 items */}
                    {order.order_items.length > 2 && expandedOrder !== order.id && (
                      <p className="text-sm text-gray-500 italic">
                        {t('and_more_items', 'и еще {{count}} товаров', {
                          count: order.order_items.length - 2
                        })}
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">
                    {t('no_items_found', 'Товары не найдены в заказе')}
                  </p>
                )}
              </div>

              {/* Expanded Order Details */}
              {expandedOrder === order.id && (
                <>
                  {/* Shipping Details */}
                  <div className="p-4 border-b">
                    <h3 className="font-medium mb-2">
                      {t('shipping_details', 'Информация о доставке')}
                    </h3>
                    {order.shipping_address ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('delivery_address', 'Адрес доставки')}:
                          </p>
                          <p>
                            {order.shipping_address.street}, {order.shipping_address.city},{' '}
                            {order.shipping_address.state} {order.shipping_address.zip}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('delivery_method', 'Способ доставки')}:
                          </p>
                          <p>
                            {order.delivery_method ||
                              t('standard_shipping', 'Стандартная доставка')}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <p>{t('no_shipping_info', 'Информация о доставке не указана')}</p>
                    )}
                  </div>

                  {/* Tracking Information */}
                  <div className="p-4 border-b">
                    <h3 className="font-medium mb-2">
                      {t('tracking_info', 'Отслеживание заказа')}
                    </h3>

                    {trackingData[order.id] ? (
                      trackingData[order.id].loading ? (
                        <div className="flex justify-center py-4">
                          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
                        </div>
                      ) : trackingData[order.id].error ? (
                        <p className="text-red-600">
                          {t('tracking_error', 'Ошибка при загрузке информации об отслеживании')}
                        </p>
                      ) : (
                        <>
                          <div className="mb-4">
                            <p className="text-sm">
                              <span className="text-gray-500">
                                {t('tracking_number', 'Трек-номер')}:
                              </span>{' '}
                              <span className="font-medium">
                                {trackingData[order.id].trackingId}
                              </span>
                            </p>
                            <p className="text-sm">
                              <span className="text-gray-500">
                                {t('carrier', 'Служба доставки')}:
                              </span>{' '}
                              <span>{trackingData[order.id].carrier}</span>
                            </p>
                          </div>

                          {/* Timeline */}
                          <div className="relative">
                            <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-gray-200"></div>
                            {trackingData[order.id].events.map((event, index) => (
                              <div key={index} className="relative pl-12 pb-6">
                                <div
                                  className={`absolute left-0 rounded-full h-8 w-8 flex items-center justify-center ${getStatusBadgeClass(event.status)}`}
                                >
                                  {getStatusIcon(event.status)}
                                </div>
                                <div className="text-sm mb-1">{formatDate(event.date)}</div>
                                <div className="font-medium">{event.description}</div>
                                <div className="text-sm text-gray-500">{event.location}</div>
                              </div>
                            ))}
                          </div>
                        </>
                      )
                    ) : (
                      <p>{t('no_tracking_info', 'Информация об отслеживании недоступна')}</p>
                    )}
                  </div>

                  {/* Order Status History */}
                  {orderStatusHistory[order.id] && orderStatusHistory[order.id].length > 0 && (
                    <div className="p-4 border-b">
                      <h3 className="font-medium mb-2">
                        {t('status_history', 'История статусов')}
                      </h3>
                      <div className="space-y-3">
                        {orderStatusHistory[order.id].map((historyItem, idx) => (
                          <div key={idx} className="flex items-start">
                            <div
                              className={`min-w-[100px] px-2 py-1 text-xs font-medium rounded-full text-center mr-3 ${getStatusBadgeClass(historyItem.status)}`}
                            >
                              {t(
                                historyItem.status || 'pending',
                                historyItem.status || 'Ожидает обработки'
                              )}
                            </div>
                            <div>
                              <p className="text-sm">{formatDate(historyItem.created_at)}</p>
                              {historyItem.note && (
                                <p className="text-sm text-gray-600 mt-1">{historyItem.note}</p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Footer with Actions */}
              <div className="p-4 bg-gray-50 flex justify-end">
                <Link
                  to={`/orders/${order.id}`}
                  className="inline-flex items-center px-4 py-2 border border-primary text-primary rounded-md hover:bg-primary hover:text-white transition-colors"
                >
                  {t('view_details', 'Подробнее')}
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-10 border rounded bg-white shadow">
          <p className="text-gray-500 mb-4">{t('no_orders', 'У вас пока нет заказов')}</p>
          <Link
            to="/categories"
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
          >
            {t('start_shopping', 'Начать покупки')}
          </Link>
        </div>
      )}
    </div>
  );
};

export default OrderHistory;
