import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { FaUser, FaBell, FaShieldAlt, FaCog, FaDownload, FaTrash, FaSave } from 'react-icons/fa';

const ProfileSettings = ({ profile }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [confirmDelete, setConfirmDelete] = useState('');
  const [notificationSettings, setNotificationSettings] = useState({
    order_updates: true,
    promotions: false,
    newsletter: false,
    security_alerts: true
  });
  const [privacySettings, setPrivacySettings] = useState({
    save_order_history: true,
    save_browsing_history: false,
    allow_personalized_ads: true
  });
  const [formData, setFormData] = useState({
    first_name: profile?.first_name || '',
    last_name: profile?.last_name || '',
    phone: profile?.phone || '',
    email: profile?.email || user?.email || '',
    date_of_birth: profile?.date_of_birth || '',
    gender: profile?.gender || '',
    language: profile?.language || 'ru'
  });

  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Load notification and privacy settings
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('user_settings')
          .select('notification_preferences, privacy_settings')
          .eq('user_id', user.id)
          .single();

        if (data) {
          if (data.notification_preferences) {
            setNotificationSettings(prev => ({
              ...prev,
              ...data.notification_preferences
            }));
          }

          if (data.privacy_settings) {
            setPrivacySettings(prev => ({
              ...prev,
              ...data.privacy_settings
            }));
          }
        }
      } catch (err) {
        console.error('Error loading user settings:', err);
      }
    };

    loadSettings();
  }, [user.id]);

  const handleChange = e => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = e => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handlePrivacyChange = e => {
    const { name, checked } = e.target;
    setPrivacySettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: formData.first_name,
          last_name: formData.last_name,
          phone: formData.phone,
          date_of_birth: formData.date_of_birth,
          gender: formData.gender,
          language: formData.language,
          updated_at: new Date()
        })
        .eq('id', user.id);

      if (error) throw error;

      toast.success(t('profile_updated', 'Профиль успешно обновлен'));
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t('profile_update_error', 'Ошибка при обновлении профиля'));
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async e => {
    e.preventDefault();
    setPasswordError('');

    if (newPassword.length < 8) {
      setPasswordError(t('password_too_short', 'Пароль должен содержать минимум 8 символов'));
      return;
    }

    if (newPassword !== confirmPassword) {
      setPasswordError(t('passwords_dont_match', 'Пароли не совпадают'));
      return;
    }

    setLoading(true);

    try {
      // For actual password change - this would use your auth provider's method
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      // Clear the form
      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');

      toast.success(t('password_updated', 'Пароль успешно обновлен'));
    } catch (error) {
      console.error('Error updating password:', error);
      setPasswordError(t('password_update_error', 'Ошибка при обновлении пароля'));
    } finally {
      setLoading(false);
    }
  };

  const saveNotificationSettings = async () => {
    setLoading(true);
    try {
      const { data: existingSettings, error: fetchError } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (fetchError && fetchError.code !== 'PGRST116') throw fetchError;

      if (existingSettings) {
        // Update existing settings
        const { error } = await supabase
          .from('user_settings')
          .update({
            notification_preferences: notificationSettings,
            updated_at: new Date()
          })
          .eq('user_id', user.id);

        if (error) throw error;
      } else {
        // Insert new settings
        const { error } = await supabase.from('user_settings').insert({
          user_id: user.id,
          notification_preferences: notificationSettings,
          privacy_settings: privacySettings,
          created_at: new Date()
        });

        if (error) throw error;
      }

      toast.success(t('notifications_updated', 'Настройки уведомлений успешно сохранены'));
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast.error(t('save_settings_error', 'Ошибка при сохранении настроек'));
    } finally {
      setLoading(false);
    }
  };

  const savePrivacySettings = async () => {
    setLoading(true);
    try {
      const { data: existingSettings, error: fetchError } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (fetchError && fetchError.code !== 'PGRST116') throw fetchError;

      if (existingSettings) {
        // Update existing settings
        const { error } = await supabase
          .from('user_settings')
          .update({
            privacy_settings: privacySettings,
            updated_at: new Date()
          })
          .eq('user_id', user.id);

        if (error) throw error;
      } else {
        // Insert new settings
        const { error } = await supabase.from('user_settings').insert({
          user_id: user.id,
          notification_preferences: notificationSettings,
          privacy_settings: privacySettings,
          created_at: new Date()
        });

        if (error) throw error;
      }

      toast.success(t('privacy_updated', 'Настройки конфиденциальности успешно сохранены'));
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      toast.error(t('save_settings_error', 'Ошибка при сохранении настроек'));
    } finally {
      setLoading(false);
    }
  };

  const downloadPersonalData = async () => {
    setLoading(true);
    try {
      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      // Get user orders
      const { data: ordersData, error: ordersError } = await supabase
        .from('orders')
        .select('*')
        .eq('customer_email', user.email);

      if (ordersError) throw ordersError;

      // Get user addresses
      const { data: addressesData } = profileData.addresses || [];

      // Compile all data
      const userData = {
        profile: {
          ...profileData,
          addresses: null // Remove it to avoid duplication
        },
        addresses: addressesData,
        orders: ordersData || []
      };

      // Create and download a JSON file
      const blob = new Blob([JSON.stringify(userData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `personal-data-${new Date().toISOString()}.json`;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(t('data_downloaded', 'Данные успешно скачаны'));
    } catch (error) {
      console.error('Error downloading data:', error);
      toast.error(t('download_error', 'Ошибка при скачивании данных'));
    } finally {
      setLoading(false);
    }
  };

  const deleteAccount = async () => {
    if (confirmDelete !== user.email) {
      toast.error(t('email_mismatch', 'Email не совпадает для подтверждения'));
      return;
    }

    setLoading(true);
    try {
      // Delete user data in this order:
      // 1. User settings
      await supabase.from('user_settings').delete().eq('user_id', user.id);

      // 2. User profile
      await supabase.from('profiles').delete().eq('id', user.id);

      // 3. User authentication (this should trigger a sign out)
      const { error } = await supabase.auth.admin.deleteUser(user.id);

      if (error) throw error;

      toast.success(t('account_deleted', 'Аккаунт успешно удален'));

      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Error deleting account:', error);
      toast.error(t('delete_account_error', 'Ошибка при удалении аккаунта'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">{t('profile_settings', 'Настройки профиля')}</h2>

      <div className="mb-6 border-b">
        <nav className="flex flex-wrap -mb-px">
          <button
            onClick={() => setActiveTab('profile')}
            className={`mr-8 py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
              activeTab === 'profile'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FaUser className="mr-2" />
            {t('personal_info', 'Личная информация')}
          </button>
          <button
            onClick={() => setActiveTab('security')}
            className={`mr-8 py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
              activeTab === 'security'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FaShieldAlt className="mr-2" />
            {t('security', 'Безопасность')}
          </button>
          <button
            onClick={() => setActiveTab('notifications')}
            className={`mr-8 py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
              activeTab === 'notifications'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FaBell className="mr-2" />
            {t('notifications', 'Уведомления')}
          </button>
          <button
            onClick={() => setActiveTab('privacy')}
            className={`mr-8 py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
              activeTab === 'privacy'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FaCog className="mr-2" />
            {t('privacy', 'Конфиденциальность')}
          </button>
        </nav>
      </div>

      {/* Personal Information Tab */}
      {activeTab === 'profile' && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('first_name', 'Имя')}
                </label>
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('last_name', 'Фамилия')}
                </label>
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('email', 'Email')}
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  disabled
                  className="w-full p-2 border border-gray-300 rounded bg-gray-50"
                />
                <p className="mt-1 text-sm text-gray-500">
                  {t('email_change_note', 'Email нельзя изменить')}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('phone', 'Телефон')}
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('date_of_birth', 'Дата рождения')}
                </label>
                <input
                  type="date"
                  name="date_of_birth"
                  value={formData.date_of_birth}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('gender', 'Пол')}
                </label>
                <select
                  name="gender"
                  value={formData.gender}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                >
                  <option value="">{t('select_gender', 'Выберите пол')}</option>
                  <option value="male">{t('male', 'Мужской')}</option>
                  <option value="female">{t('female', 'Женский')}</option>
                  <option value="other">{t('other', 'Другой')}</option>
                  <option value="prefer_not_to_say">
                    {t('prefer_not_to_say', 'Предпочитаю не указывать')}
                  </option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('language', 'Предпочитаемый язык')}
                </label>
                <select
                  name="language"
                  value={formData.language}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                >
                  <option value="ru">{t('russian', 'Русский')}</option>
                  <option value="en">{t('english', 'Английский')}</option>
                  <option value="uk">{t('ukrainian', 'Украинский')}</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition flex items-center"
              >
                <FaSave className="mr-2" />
                {loading ? t('saving', 'Сохранение...') : t('save_changes', 'Сохранить изменения')}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-xl font-semibold mb-4">{t('change_password', 'Изменение пароля')}</h3>

          <form onSubmit={handlePasswordChange} className="space-y-4 mb-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('current_password', 'Текущий пароль')}
              </label>
              <input
                type="password"
                value={oldPassword}
                onChange={e => setOldPassword(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('new_password', 'Новый пароль')}
              </label>
              <input
                type="password"
                value={newPassword}
                onChange={e => setNewPassword(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('confirm_password', 'Подтвердите новый пароль')}
              </label>
              <input
                type="password"
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded focus:ring-primary focus:border-primary"
                required
              />
            </div>

            {passwordError && <p className="text-red-600 text-sm">{passwordError}</p>}

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
              >
                {loading ? t('updating', 'Обновление...') : t('update_password', 'Обновить пароль')}
              </button>
            </div>
          </form>

          <div className="mt-8 border-t pt-6">
            <h3 className="text-xl font-semibold mb-4">{t('password_reset', 'Сброс пароля')}</h3>
            <p className="text-gray-600 mb-4">
              {t(
                'password_reset_description',
                'Если вы забыли пароль, мы можем отправить вам ссылку для сброса на ваш email.'
              )}
            </p>
            <button
              onClick={() => supabase.auth.resetPasswordForEmail(user.email)}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition"
            >
              {t('send_password_reset', 'Отправить ссылку для сброса пароля')}
            </button>
          </div>

          <div className="mt-8 border-t pt-6">
            <h3 className="text-xl font-semibold mb-4 text-red-600">
              {t('delete_account', 'Удаление аккаунта')}
            </h3>
            <p className="text-gray-600 mb-4">
              {t(
                'delete_account_warning',
                'Это действие нельзя отменить. После удаления все ваши данные будут безвозвратно удалены.'
              )}
            </p>

            <div className="bg-red-50 p-4 rounded-lg mb-4">
              <p className="text-sm text-red-800 mb-2">
                {t('delete_confirmation', 'Для подтверждения введите ваш email:')}
              </p>
              <input
                type="email"
                value={confirmDelete}
                onChange={e => setConfirmDelete(e.target.value)}
                placeholder={user.email}
                className="w-full p-2 border border-red-300 rounded"
              />
            </div>

            <button
              onClick={deleteAccount}
              disabled={loading || confirmDelete !== user.email}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition flex items-center"
            >
              <FaTrash className="mr-2" />
              {loading
                ? t('deleting', 'Удаление...')
                : t('delete_account_confirm', 'Подтвердить удаление аккаунта')}
            </button>
          </div>
        </div>
      )}

      {/* Notifications Tab */}
      {activeTab === 'notifications' && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-xl font-semibold mb-6">
            {t('notification_preferences', 'Настройки уведомлений')}
          </h3>

          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="order_updates"
                  name="order_updates"
                  type="checkbox"
                  checked={notificationSettings.order_updates}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="order_updates" className="font-medium text-gray-700">
                  {t('order_updates', 'Обновления заказов')}
                </label>
                <p className="text-gray-500">
                  {t('order_updates_desc', 'Получать уведомления о статусе ваших заказов')}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="promotions"
                  name="promotions"
                  type="checkbox"
                  checked={notificationSettings.promotions}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="promotions" className="font-medium text-gray-700">
                  {t('promotions', 'Акции и специальные предложения')}
                </label>
                <p className="text-gray-500">
                  {t(
                    'promotions_desc',
                    'Получать информацию о скидках, акциях и специальных предложениях'
                  )}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="newsletter"
                  name="newsletter"
                  type="checkbox"
                  checked={notificationSettings.newsletter}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="newsletter" className="font-medium text-gray-700">
                  {t('newsletter', 'Новостная рассылка')}
                </label>
                <p className="text-gray-500">
                  {t('newsletter_desc', 'Получать информацию о новинках и полезных статьях')}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="security_alerts"
                  name="security_alerts"
                  type="checkbox"
                  checked={notificationSettings.security_alerts}
                  onChange={handleNotificationChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="security_alerts" className="font-medium text-gray-700">
                  {t('security_alerts', 'Оповещения безопасности')}
                </label>
                <p className="text-gray-500">
                  {t(
                    'security_alerts_desc',
                    'Получать уведомления о входах в аккаунт и изменениях безопасности'
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <button
              onClick={saveNotificationSettings}
              disabled={loading}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
            >
              {loading ? t('saving', 'Сохранение...') : t('save_settings', 'Сохранить настройки')}
            </button>
          </div>
        </div>
      )}

      {/* Privacy Tab */}
      {activeTab === 'privacy' && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-xl font-semibold mb-6">
            {t('privacy_settings', 'Настройки конфиденциальности')}
          </h3>

          <div className="space-y-4 mb-6">
            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="save_order_history"
                  name="save_order_history"
                  type="checkbox"
                  checked={privacySettings.save_order_history}
                  onChange={handlePrivacyChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="save_order_history" className="font-medium text-gray-700">
                  {t('save_order_history', 'Сохранять историю заказов')}
                </label>
                <p className="text-gray-500">
                  {t(
                    'save_order_history_desc',
                    'Хранить историю ваших заказов для быстрого повторного заказа'
                  )}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="save_browsing_history"
                  name="save_browsing_history"
                  type="checkbox"
                  checked={privacySettings.save_browsing_history}
                  onChange={handlePrivacyChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="save_browsing_history" className="font-medium text-gray-700">
                  {t('save_browsing_history', 'Сохранять историю просмотров')}
                </label>
                <p className="text-gray-500">
                  {t(
                    'save_browsing_history_desc',
                    'Сохранять историю просмотренных товаров для персонализированных рекомендаций'
                  )}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex h-5 items-center">
                <input
                  id="allow_personalized_ads"
                  name="allow_personalized_ads"
                  type="checkbox"
                  checked={privacySettings.allow_personalized_ads}
                  onChange={handlePrivacyChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="allow_personalized_ads" className="font-medium text-gray-700">
                  {t('allow_personalized_ads', 'Разрешить персонализированную рекламу')}
                </label>
                <p className="text-gray-500">
                  {t(
                    'allow_personalized_ads_desc',
                    'Разрешить использовать ваши предпочтения для показа релевантной рекламы'
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              onClick={savePrivacySettings}
              disabled={loading}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
            >
              {loading ? t('saving', 'Сохранение...') : t('save_settings', 'Сохранить настройки')}
            </button>
          </div>

          <div className="mt-8 pt-6 border-t">
            <h3 className="text-xl font-semibold mb-4">{t('your_data', 'Ваши данные')}</h3>
            <p className="text-gray-600 mb-4">
              {t(
                'data_access_desc',
                'Вы можете скачать все ваши данные, включая профиль, адреса и историю заказов.'
              )}
            </p>

            <button
              onClick={downloadPersonalData}
              disabled={loading}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition flex items-center"
            >
              <FaDownload className="mr-2" />
              {loading
                ? t('preparing_download', 'Подготовка файла...')
                : t('download_data', 'Скачать ваши данные')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileSettings;
