import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useWishlist } from '../../context/WishlistContext';
import { useCart } from '../../context/CartContext';

const Wishlist = () => {
  const { t } = useTranslation();
  const { wishlist, removeFromWishlist } = useWishlist();
  const { addToCart } = useCart();

  const handleAddToCart = product => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    });
  };

  // Format price to currency
  const formatPrice = price => {
    return new Intl.NumberFormat('uk-UA', {
      style: 'currency',
      currency: 'UAH',
      minimumFractionDigits: 0
    }).format(price);
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">{t('my_wishlist', 'My Wishlist')}</h2>

      {wishlist.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {wishlist.map(product => (
            <div key={product.id} className="border rounded-lg p-4 flex">
              <div className="h-24 w-24 bg-gray-100 rounded flex-shrink-0 mr-4">
                {product.image && (
                  <img
                    src={product.image}
                    alt={product.name}
                    className="h-full w-full object-contain"
                    onError={e => {
                      e.target.src = '/placeholder.png';
                    }}
                  />
                )}
              </div>
              <div className="flex-grow flex flex-col">
                <Link to={`/products/${product.id}`} className="font-medium hover:text-primary">
                  {product.name}
                </Link>
                <p className="text-primary font-semibold mt-1">{formatPrice(product.price)}</p>
                <div className="mt-auto flex flex-wrap gap-2">
                  <button
                    onClick={() => handleAddToCart(product)}
                    className="px-3 py-1 text-xs bg-primary text-white rounded hover:bg-primary-dark transition"
                  >
                    {t('add_to_cart', 'Add to Cart')}
                  </button>
                  <button
                    onClick={() => removeFromWishlist(product.id)}
                    className="px-3 py-1 text-xs bg-white border border-gray-300 text-gray-700 rounded hover:bg-gray-100 transition"
                  >
                    {t('remove', 'Remove')}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-10 border rounded bg-gray-50">
          <p className="text-gray-500 mb-4">{t('wishlist_empty', 'Your wishlist is empty')}</p>
          <Link
            to="/categories"
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition"
          >
            {t('explore_products', 'Explore Products')}
          </Link>
        </div>
      )}
    </div>
  );
};

export default Wishlist;
