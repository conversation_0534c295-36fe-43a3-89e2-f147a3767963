import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import {
  FaHome,
  FaBriefcase,
  FaMapMarkerAlt,
  FaPen,
  FaTrash,
  FaStar,
  FaRegStar,
  FaSearch,
  FaPlus,
  FaTimes
} from 'react-icons/fa';

const AddressList = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [newAddress, setNewAddress] = useState({
    title: '',
    full_name: '',
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    phone: '',
    notes: '',
    is_default: false,
    address_type: 'home'
  });
  const [addressToEdit, setAddressToEdit] = useState(null);

  const fetchAddresses = useCallback(async () => {
    if (!user) {
      setAddresses([]);
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('address, addresses')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error fetching profile addresses:', profileError);
        toast.error(t('error_fetching_addresses', 'Ошибка загрузки адресов'));
        setAddresses([]);
        return;
      }

      if (profile && profile.addresses && Array.isArray(profile.addresses)) {
        setAddresses(profile.addresses);
      } else if (profile && profile.address && typeof profile.address === 'object') {
        setAddresses([profile.address]);
      } else {
        try {
          const { data, error } = await supabase
            .from('addresses')
            .select('*')
            .eq('user_id', user.id);

          if (error) {
            console.error('Error fetching from addresses table:', error);
            toast.error(t('error_fetching_addresses', 'Ошибка загрузки адресов'));
            setAddresses([]);
          } else {
            setAddresses(data || []);
          }
        } catch (err) {
          setAddresses([]);
        }
      }
    } catch (error) {
      console.error('Error in address fetch:', error);
      toast.error(t('error_fetching_addresses', 'Ошибка загрузки адресов'));
      setAddresses([]);
    } finally {
      setLoading(false);
    }
  }, [user, t]); // t используется в toast.error

  useEffect(() => {
    fetchAddresses();
  }, [fetchAddresses]);

  const handleAddressChange = e => {
    const { name, value, type, checked } = e.target;
    const updateObject = isEditingAddress ? setAddressToEdit : setNewAddress;

    updateObject(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const saveAddress = async e => {
    e.preventDefault();
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('addresses')
        .eq('id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        toast.error(t('error_fetching_profile', 'Ошибка при загрузке профиля'));
        return;
      }
      let updatedAddresses = [];
      if (profile?.addresses && Array.isArray(profile.addresses)) {
        updatedAddresses = [...profile.addresses];
        if (newAddress.is_default) {
          updatedAddresses = updatedAddresses.map(addr => ({
            ...addr,
            is_default: false
          }));
        }
      } else if (newAddress.is_default) {
        // No existing addresses, this new one is default
      }
      updatedAddresses.push({
        ...newAddress,
        id: Date.now().toString(),
        created_at: new Date().toISOString()
      });
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ addresses: updatedAddresses })
        .eq('id', user.id);
      if (updateError) throw updateError;
      setAddresses(updatedAddresses);
      setIsAddingAddress(false);
      setNewAddress({
        title: '',
        full_name: '',
        address_line1: '',
        address_line2: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
        phone: '',
        notes: '',
        is_default: false,
        address_type: 'home'
      });
      toast.success(t('address_added', 'Адрес успешно добавлен'));
    } catch (error) {
      console.error('Error saving address:', error);
      toast.error(t('error_saving_address', 'Ошибка при сохранении адреса'));
    }
  };

  const updateAddress = async e => {
    e.preventDefault();
    if (!addressToEdit || !addressToEdit.id) {
      toast.error(t('error_no_address_to_edit', 'Нет адреса для редактирования'));
      return;
    }
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('addresses')
        .eq('id', user.id)
        .single();
      if (profileError) throw profileError;
      let currentAddresses =
        profile?.addresses && Array.isArray(profile.addresses) ? [...profile.addresses] : [];
      if (addressToEdit.is_default) {
        currentAddresses = currentAddresses.map(addr => ({
          ...addr,
          is_default: addr.id === addressToEdit.id
        }));
      }
      const addressIndex = currentAddresses.findIndex(addr => addr.id === addressToEdit.id);
      if (addressIndex !== -1) {
        currentAddresses[addressIndex] = {
          ...addressToEdit,
          updated_at: new Date().toISOString()
        };
      } else {
        currentAddresses.push({ ...addressToEdit, created_at: new Date().toISOString() });
      }
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ addresses: currentAddresses })
        .eq('id', user.id);
      if (updateError) throw updateError;
      setAddresses(currentAddresses);
      setIsEditingAddress(false);
      setAddressToEdit(null);
      toast.success(t('address_updated', 'Адрес успешно обновлен'));
    } catch (error) {
      console.error('Error updating address:', error);
      toast.error(t('error_updating_address', 'Ошибка при обновлении адреса'));
    }
  };

  const deleteAddress = async addressId => {
    if (
      !window.confirm(t('confirm_delete_address', 'Вы уверены, что хотите удалить этот адрес?'))
    ) {
      return;
    }
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('addresses')
        .eq('id', user.id)
        .single();
      if (profileError) throw profileError;
      const updatedAddresses = (profile.addresses || []).filter(addr => addr.id !== addressId);
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ addresses: updatedAddresses })
        .eq('id', user.id);
      if (updateError) throw updateError;
      setAddresses(updatedAddresses);
      toast.success(t('address_deleted', 'Адрес успешно удален'));
    } catch (error) {
      console.error('Error deleting address:', error);
      toast.error(t('error_deleting_address', 'Ошибка при удалении адреса'));
    }
  };

  const setDefaultAddress = async addressId => {
    try {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('addresses')
        .eq('id', user.id)
        .single();
      if (profileError) throw profileError;
      const updatedAddresses = (profile.addresses || []).map(addr => ({
        ...addr,
        is_default: addr.id === addressId
      }));
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ addresses: updatedAddresses })
        .eq('id', user.id);
      if (updateError) throw updateError;
      setAddresses(updatedAddresses);
      toast.success(t('default_address_set', 'Адрес по умолчанию обновлен'));
    } catch (error) {
      console.error('Error setting default address:', error);
      toast.error(t('error_updating_address', 'Ошибка при обновлении адреса'));
    }
  };

  const startEditing = address => {
    setAddressToEdit({ ...address });
    setIsEditingAddress(true);
    setIsAddingAddress(false);
  };

  const cancelEditing = () => {
    setIsEditingAddress(false);
    setAddressToEdit(null);
  };

  const getAddressTypeIcon = type => {
    switch (type) {
      case 'work':
        return <FaBriefcase className="text-blue-600" />;
      case 'other':
        return <FaMapMarkerAlt className="text-purple-600" />;
      case 'home':
      default:
        return <FaHome className="text-green-600" />;
    }
  };

  const filteredAddresses = addresses.filter(address => {
    if (!searchQuery) return true;
    const searchLower = searchQuery.toLowerCase();
    return (
      address.title?.toLowerCase().includes(searchLower) ||
      address.full_name?.toLowerCase().includes(searchLower) ||
      address.address_line1?.toLowerCase().includes(searchLower) ||
      address.city?.toLowerCase().includes(searchLower) ||
      address.country?.toLowerCase().includes(searchLower)
    );
  });

  if (loading) {
    return (
      <div className="flex justify-center py-10">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  const renderForm = (handleSubmitFn, addressData, setAddressDataFn) => (
    <div className="mb-8 p-6 border rounded shadow-sm bg-white">
      <h3 className="text-lg font-medium mb-4">
        {isEditingAddress
          ? t('edit_address', 'Редактировать адрес')
          : t('new_address', 'Новый адрес')}
      </h3>
      <form onSubmit={handleSubmitFn} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('address_title', 'Название (например, Дом, Работа)')}
            </label>
            <input
              type="text"
              name="title"
              value={addressData.title}
              onChange={e => setAddressDataFn(prev => ({ ...prev, title: e.target.value }))}
              className="w-full p-2 border rounded"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('address_type', 'Тип адреса')}
            </label>
            <select
              name="address_type"
              value={addressData.address_type}
              onChange={e => setAddressDataFn(prev => ({ ...prev, address_type: e.target.value }))}
              className="w-full p-2 border rounded"
            >
              <option value="home">{t('home', 'Дом')}</option>
              <option value="work">{t('work', 'Работа')}</option>
              <option value="other">{t('other', 'Другое')}</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('full_name', 'ФИО получателя')}
            </label>
            <input
              type="text"
              name="full_name"
              value={addressData.full_name}
              onChange={e => setAddressDataFn(prev => ({ ...prev, full_name: e.target.value }))}
              className="w-full p-2 border rounded"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">{t('phone', 'Телефон')}</label>
            <input
              type="tel"
              name="phone"
              value={addressData.phone}
              onChange={e => setAddressDataFn(prev => ({ ...prev, phone: e.target.value }))}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-1">
              {t('address_line1', 'Адрес, строка 1')}
            </label>
            <input
              type="text"
              name="address_line1"
              value={addressData.address_line1}
              onChange={e => setAddressDataFn(prev => ({ ...prev, address_line1: e.target.value }))}
              className="w-full p-2 border rounded"
              required
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-1">
              {t('address_line2', 'Адрес, строка 2')}
            </label>
            <input
              type="text"
              name="address_line2"
              value={addressData.address_line2}
              onChange={e => setAddressDataFn(prev => ({ ...prev, address_line2: e.target.value }))}
              className="w-full p-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">{t('city', 'Город')}</label>
            <input
              type="text"
              name="city"
              value={addressData.city}
              onChange={e => setAddressDataFn(prev => ({ ...prev, city: e.target.value }))}
              className="w-full p-2 border rounded"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">{t('state', 'Область/Регион')}</label>
            <input
              type="text"
              name="state"
              value={addressData.state}
              onChange={e => setAddressDataFn(prev => ({ ...prev, state: e.target.value }))}
              className="w-full p-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('postal_code', 'Почтовый индекс')}
            </label>
            <input
              type="text"
              name="postal_code"
              value={addressData.postal_code}
              onChange={e => setAddressDataFn(prev => ({ ...prev, postal_code: e.target.value }))}
              className="w-full p-2 border rounded"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">{t('country', 'Страна')}</label>
            <input
              type="text"
              name="country"
              value={addressData.country}
              onChange={e => setAddressDataFn(prev => ({ ...prev, country: e.target.value }))}
              className="w-full p-2 border rounded"
              required
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-1">
              {t('delivery_notes', 'Примечания к доставке')}
            </label>
            <textarea
              name="notes"
              value={addressData.notes}
              onChange={e => setAddressDataFn(prev => ({ ...prev, notes: e.target.value }))}
              rows="3"
              className="w-full p-2 border rounded"
            ></textarea>
          </div>
          <div className="md:col-span-2 flex items-center">
            <input
              type="checkbox"
              name="is_default"
              id={isEditingAddress ? `edit_is_default_${addressData.id}` : 'new_is_default'}
              checked={addressData.is_default}
              onChange={e => setAddressDataFn(prev => ({ ...prev, is_default: e.target.checked }))}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label
              htmlFor={isEditingAddress ? `edit_is_default_${addressData.id}` : 'new_is_default'}
              className="ml-2 text-sm"
            >
              {t('set_as_default', 'Сделать адресом по умолчанию')}
            </label>
          </div>
        </div>
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={isEditingAddress ? cancelEditing : () => setIsAddingAddress(false)}
            className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50"
          >
            {t('cancel', 'Отмена')}
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
          >
            {isEditingAddress
              ? t('save_changes', 'Сохранить изменения')
              : t('save_address', 'Сохранить адрес')}
          </button>
        </div>
      </form>
    </div>
  );

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">{t('addresses', 'Адреса')}</h2>
        <button
          onClick={() => {
            setIsAddingAddress(!isAddingAddress);
            setIsEditingAddress(false);
            setAddressToEdit(null);
          }}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition flex items-center"
        >
          {isAddingAddress || isEditingAddress ? (
            <>
              <FaTimes className="mr-2" /> {t('cancel', 'Отмена')}
            </>
          ) : (
            <>
              <FaPlus className="mr-2" /> {t('add_address', 'Добавить адрес')}
            </>
          )}
        </button>
      </div>

      {addresses.length > 0 && !isAddingAddress && !isEditingAddress && (
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              placeholder={t('search_addresses', 'Поиск адресов...')}
              className="w-full p-2 pl-10 border rounded-lg"
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            )}
          </div>
        </div>
      )}

      {isAddingAddress && renderForm(saveAddress, newAddress, setNewAddress)}
      {isEditingAddress &&
        addressToEdit &&
        renderForm(updateAddress, addressToEdit, setAddressToEdit)}

      {!isAddingAddress &&
        !isEditingAddress &&
        (filteredAddresses.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredAddresses.map(address => (
              <div
                key={address.id}
                className={`p-6 border rounded-lg shadow-sm relative ${
                  address.is_default ? 'border-primary ring-2 ring-primary' : 'bg-white'
                }`}
              >
                <div className="flex items-center mb-2">
                  {getAddressTypeIcon(address.address_type)}
                  <h3 className="text-lg font-semibold ml-2">{address.title}</h3>
                  {address.is_default && (
                    <span className="ml-auto text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                      {t('default', 'По умолчанию')}
                    </span>
                  )}
                </div>
                <p className="text-gray-700">{address.full_name}</p>
                <p className="text-gray-700">{address.address_line1}</p>
                {address.address_line2 && <p className="text-gray-700">{address.address_line2}</p>}
                <p className="text-gray-700">
                  {address.city}, {address.state} {address.postal_code}
                </p>
                <p className="text-gray-700">{address.country}</p>
                {address.phone && (
                  <p className="text-gray-700">
                    {t('phone_short', 'Тел:')} {address.phone}
                  </p>
                )}
                {address.notes && (
                  <p className="text-sm text-gray-500 mt-2">
                    <strong>{t('notes', 'Примечания')}:</strong> {address.notes}
                  </p>
                )}

                <div className="mt-4 pt-4 border-t flex justify-between items-center">
                  <div className="flex space-x-3">
                    <button
                      onClick={() => startEditing(address)}
                      className="text-blue-600 hover:text-blue-800 flex items-center"
                    >
                      <FaPen className="mr-1" /> {t('edit', 'Редактировать')}
                    </button>
                    <button
                      onClick={() => deleteAddress(address.id)}
                      className="text-red-600 hover:text-red-800 flex items-center"
                    >
                      <FaTrash className="mr-1" /> {t('delete', 'Удалить')}
                    </button>
                  </div>
                  {!address.is_default && (
                    <button
                      onClick={() => setDefaultAddress(address.id)}
                      className="text-green-600 hover:text-green-800 flex items-center"
                    >
                      <FaStar className="mr-1" /> {t('set_as_default', 'По умолчанию')}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-10 text-gray-500">
            {searchQuery
              ? t('no_addresses_found_search', 'Адреса по вашему запросу не найдены.')
              : t('no_addresses', 'У вас пока нет сохраненных адресов.')}
          </div>
        ))}
    </div>
  );
};

export default AddressList;
