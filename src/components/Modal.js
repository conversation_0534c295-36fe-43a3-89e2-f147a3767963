import { useTranslation } from 'react-i18next';
import { useEffect, useRef } from 'react';
import { Transition } from 'react-transition-group';

const Modal = ({
  isOpen,
  onClose,
  onConfirm,
  message,
  title = null,
  cancelText = null,
  confirmText = null
}) => {
  const { t } = useTranslation();
  const modalRef = useRef(null);
  const firstFocusableElementRef = useRef(null);

  useEffect(() => {
    const handleKeyDown = event => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      if (modalRef.current) {
        modalRef.current.focus();
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen && firstFocusableElementRef.current) {
      firstFocusableElementRef.current.focus();
    }
  }, [isOpen]);

  const handleBackdropClick = event => {
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  return (
    <Transition in={isOpen} timeout={300} unmountOnExit>
      {state => (
        <div
          className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300 ${
            state === 'entering' || state === 'entered' ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={handleBackdropClick}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-title"
          tabIndex={-1}
          ref={modalRef}
        >
          <div
            className={`bg-white rounded-lg p-6 max-w-sm w-full transform transition-all duration-300 ${
              state === 'entering' || state === 'entered' ? 'scale-100' : 'scale-90'
            }`}
            onClick={e => e.stopPropagation()}
          >
            <h2 id="modal-title" className="text-xl font-semibold mb-4">
              {title || t('confirmation', 'Confirmation')}
            </h2>
            <div className="text-gray-600 mb-6">{message}</div>
            <div className="flex justify-end gap-4">
              <button
                onClick={onClose}
                className="btn-secondary px-4 py-2 rounded transition-colors hover:bg-gray-300"
                ref={firstFocusableElementRef}
                aria-label={cancelText || t('cancel', 'Cancel')}
              >
                {cancelText || t('cancel', 'Cancel')}
              </button>
              <button
                onClick={onConfirm}
                className="btn-primary px-4 py-2 rounded transition-colors hover:bg-blue-700"
                aria-label={confirmText || t('confirm', 'Confirm')}
              >
                {confirmText || t('confirm', 'Confirm')}
              </button>
            </div>
          </div>
        </div>
      )}
    </Transition>
  );
};

export default Modal;
