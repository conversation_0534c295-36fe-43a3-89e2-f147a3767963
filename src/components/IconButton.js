import { motion } from 'framer-motion';

const IconButton = ({ icon: Icon, tooltip, ...props }) => {
  return (
    <motion.button
      className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative group"
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      {...props}
    >
      <Icon className="w-5 h-5" />
      {tooltip && (
        <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity">
          {tooltip}
        </span>
      )}
    </motion.button>
  );
};

export default IconButton;
