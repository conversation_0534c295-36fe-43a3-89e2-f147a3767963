import React from 'react';
import { useParams } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import ProductCard from './ProductCard';
import { ProductListSkeleton } from './SkeletonLoader';
import DataError from './DataError';
import { useTranslation } from 'react-i18next';

const ProductList = ({ products = [], loading = false, error = null }) => {
  const { t } = useTranslation();
  const { categoryId } = useParams();

  if (error) {
    return (
      <DataError
        message={error}
        resetError={() => {
          /* no-op */
        }}
      />
    );
  }

  if (loading) {
    return <ProductListSkeleton />;
  }

  if (!products || products.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600 text-lg">
          {categoryId
            ? t('no_products_in_category', 'Товаров в данной категории не найдено.')
            : t('no_products', 'Товаров не найдено.')}
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {products.map(product => (
        <ProductCard key={product.id} product={product} />
      ))}
    </div>
  );
};

export default function ProductListWithError(props) {
  return (
    <ErrorBoundary FallbackComponent={DataError}>
      <ProductList {...props} />
    </ErrorBoundary>
  );
}
