import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { supabase } from '../supabaseClient';

const ProductForm = ({ initialData /*, ...other props */ }) => {
  // State for saving status
  const [saving, setSaving] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    stock_quantity: '',
    sku: '',
    category_id: '',
    image: '',
    url: '',
    vendor: '',
    is_on_sale: false,
    is_new: false,
    is_bestseller: false,
    original_price: '',
    display_order: '',
    old_category_id: '', // <- удалить это поле из payload при сохранении
    stock: '',
    min_stock: ''
  });

  // Handle form field changes
  const handleChange = e => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === 'checkbox' ? checked : value;

    setFormData(prevData => ({
      ...prevData,
      [name]: fieldValue
    }));
  };

  const handleSave = async e => {
    e.preventDefault();
    setSaving(true);
    // Формируем payload без old_category_id
    const {
      old_category_id, // удаляем его
      ...payload
    } = formData;
    // Приводим типы для числовых значений
    payload.price = parseFloat(payload.price) || 0;
    payload.original_price = parseFloat(payload.original_price) || 0;
    payload.stock_quantity = parseInt(payload.stock_quantity, 10) || 0;
    payload.display_order = parseInt(payload.display_order, 10) || 0;
    payload.stock = parseInt(payload.stock, 10) || 0;
    payload.min_stock = parseInt(payload.min_stock, 10) || 0;

    const { error } = await supabase.from('products').update(payload).eq('id', formData.id);
    setSaving(false);
    if (error) {
      console.error('Error saving product:', error);
      toast.error('Error saving product');
    } else {
      toast.success('Product saved successfully');
    }
  };

  return (
    <form onSubmit={handleSave} className="space-y-4">
      {/* Group Basic Information */}
      <fieldset className="border p-4 rounded">
        <legend className="px-2">Basic Information</legend>
        {/* Name Field */}
        <div>
          <label className="block mb-1">Name:</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="border p-2 w-full"
            required
          />
        </div>
        {/* Description Field */}
        <div>
          <label className="block mb-1">Description:</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            className="border p-2 w-full"
            rows="3"
          />
        </div>
      </fieldset>

      {/* Group Pricing & Stock */}
      <fieldset className="border p-4 rounded">
        <legend className="px-2">Pricing & Stock</legend>
        <div>
          <label className="block mb-1">Price:</label>
          <input
            type="number"
            step="0.01"
            name="price"
            value={formData.price}
            onChange={handleChange}
            className="border p-2 w-full"
            required
          />
        </div>
        <div>
          <label className="block mb-1">Stock Quantity:</label>
          <input
            type="number"
            name="stock_quantity"
            value={formData.stock_quantity}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block mb-1">Original Price:</label>
            <input
              type="number"
              step="0.01"
              name="original_price"
              value={formData.original_price}
              onChange={handleChange}
              className="border p-2 w-full"
            />
          </div>
          <div>
            <label className="block mb-1">Display Order:</label>
            <input
              type="number"
              name="display_order"
              value={formData.display_order}
              onChange={handleChange}
              className="border p-2 w-full"
            />
          </div>
        </div>
      </fieldset>

      {/* Group Media & Category */}
      <fieldset className="border p-4 rounded">
        <legend className="px-2">Media & Category</legend>
        <div>
          <label className="block mb-1">Image URL:</label>
          <input
            type="text"
            name="image"
            value={formData.image}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="block mb-1">URL:</label>
          <input
            type="text"
            name="url"
            value={formData.url}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="block mb-1">Category ID:</label>
          <input
            type="text"
            name="category_id"
            value={formData.category_id}
            onChange={handleChange}
            className="border p-2 w-full"
            required
          />
        </div>
      </fieldset>

      {/* Group Vendor & Flags */}
      <fieldset className="border p-4 rounded">
        <legend className="px-2">Vendor & Flags</legend>
        <div>
          <label className="block mb-1">Vendor:</label>
          <input
            type="text"
            name="vendor"
            value={formData.vendor}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div className="flex items-center space-x-4">
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              name="is_on_sale"
              checked={formData.is_on_sale}
              onChange={handleChange}
              className="form-checkbox"
            />
            <span className="ml-2">On Sale</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              name="is_new"
              checked={formData.is_new}
              onChange={handleChange}
              className="form-checkbox"
            />
            <span className="ml-2">New</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              name="is_bestseller"
              checked={formData.is_bestseller}
              onChange={handleChange}
              className="form-checkbox"
            />
            <span className="ml-2">Bestseller</span>
          </label>
        </div>
      </fieldset>

      <button
        type="submit"
        disabled={saving}
        className="bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark transition"
      >
        {saving ? 'Saving...' : 'Save Changes'}
      </button>
    </form>
  );
};

export default ProductForm;
