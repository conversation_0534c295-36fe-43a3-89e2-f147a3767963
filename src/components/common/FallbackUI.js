import React from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const FallbackUI = ({ error, retry, type = 'default' }) => {
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg">
        <div className="text-red-500 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Произошла ошибка</h3>
        <p className="text-gray-600 text-center mb-4">
          Не удалось загрузить контент. Пожалуйста, попробуйте снова.
        </p>
        {retry && (
          <button
            onClick={retry}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors"
          >
            Повторить
          </button>
        )}
      </div>
    );
  }

  switch (type) {
    case 'product':
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map(item => (
            <div key={item} className="p-4 border rounded-lg">
              <Skeleton height={200} className="mb-4" />
              <Skeleton count={2} className="mb-2" />
              <Skeleton width={100} className="mb-4" />
              <Skeleton height={36} />
            </div>
          ))}
        </div>
      );

    case 'detail':
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Skeleton height={400} className="rounded-lg" />
          <div>
            <Skeleton height={40} className="mb-4" />
            <Skeleton count={3} className="mb-4" />
            <Skeleton height={50} width={200} />
          </div>
        </div>
      );

    default:
      return (
        <div className="w-full">
          <Skeleton height={200} className="mb-4" />
          <Skeleton count={3} className="mb-2" />
        </div>
      );
  }
};

export default FallbackUI;
