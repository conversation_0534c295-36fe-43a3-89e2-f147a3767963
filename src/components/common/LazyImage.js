import React, { useState, useEffect, useRef } from 'react';
import { createImageErrorHandler, getSafeImageUrl } from '../../utils/imageHelpers';
import {
  optimizeImageUrl,
  generateSrcSet,
  generateSizes,
  createOptimizedImageUrl
} from '../../utils/imageOptimizer';

/**
 * Компонент для ленивой загрузки изображений с оптимизацией
 * Изображение загружается только когда оно попадает в область видимости
 * @param {Object} props - Свойства компонента
 * @param {string} props.src - URL изображения
 * @param {string} props.alt - Альтернативный текст
 * @param {string} props.type - Тип изображения (product, category, brand)
 * @param {string} props.className - CSS классы
 * @param {Function} props.onLoad - Callback для события загрузки
 * @param {Function} props.onError - Callback для события ошибки
 * @param {string} props.priority - Приоритет изображения (low, high)
 * @param {Object} props.imgProps - Дополнительные свойства для тега img
 */
const LazyImage = ({
  src,
  alt = '',
  type = 'product',
  className = '',
  onLoad,
  onError,
  priority = 'low',
  imgProps = {}
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [imageSrc, setImageSrc] = useState('');
  const [srcSet, setSrcSet] = useState('');
  const [sizes, setSizes] = useState('');
  const imageRef = useRef(null); // Добавляем ref для прямого доступа к элементу
  const uniqueId = useRef(`lazy-image-${Math.random().toString(36).substring(2, 15)}`); // Уникальный ID для каждого экземпляра

  // Обработчик ошибки по умолчанию
  const defaultErrorHandler = createImageErrorHandler(type);

  useEffect(() => {
    const loadImage = async () => {
      const safeUrl = getSafeImageUrl(src, type);
      const optimizedUrl = await createOptimizedImageUrl(safeUrl, type);
      setImageSrc(optimizedUrl);

      if (safeUrl.includes('supabase')) {
        setSrcSet(generateSrcSet(safeUrl));
        setSizes(generateSizes(type));
      }
    };

    loadImage();

    // Создаем новый IntersectionObserver
    const observer = new IntersectionObserver(
      entries => {
        // Если элемент виден, загружаем изображение
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '300px' // Увеличиваем область предзагрузки для лучшей производительности слайдера
      }
    );

    // Используем ref для доступа к DOM элементу
    if (imageRef.current) {
      observer.observe(imageRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [src, type]);

  const handleLoad = e => {
    setIsLoaded(true);
    if (onLoad) onLoad(e);
  };

  const handleError = e => {
    defaultErrorHandler(e);
    if (onError) onError(e);
  };

  // Принудительно показываем изображение для приоритетных изображений
  const forceLoadImage = priority === 'high' || window.location.pathname === '/' || imageSrc;

  return (
    <div
      id={uniqueId.current}
      ref={imageRef}
      className={`relative ${className}`}
      style={{ minHeight: '50px' }}
    >
      {!isLoaded && <div className="absolute inset-0 bg-gray-200 animate-pulse rounded"></div>}

      {(isInView || forceLoadImage) && (
        <img
          src={imageSrc || getSafeImageUrl(src, type)}
          srcSet={srcSet}
          sizes={sizes}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          loading={priority === 'high' ? 'eager' : 'lazy'}
          fetchpriority={priority}
          decoding="async"
          {...imgProps}
        />
      )}
    </div>
  );
};

export default LazyImage;
