import { partners } from '../assets/images/data';
import { useTranslation } from 'react-i18next';

const Partners = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-6 py-12">
      <h2 className="text-3xl font-semibold mb-8 text-center">{t('partners', 'Our Partners')}</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
        {partners.map(partner => (
          <a
            href={partner.link}
            key={partner.id}
            className="flex items-center justify-center p-4 bg-white border border-gray-200 rounded-lg"
          >
            <img
              src={partner.image}
              alt={partner.name}
              className="h-12 object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
            />
          </a>
        ))}
      </div>
    </div>
  );
};

export default Partners;
