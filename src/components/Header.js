import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useCompare } from '../context/CompareContext';
import { useAuth } from '../context/AuthContext';
import SearchBar from './SearchBar';
import ProfileDropdown from './ProfileDropdown';
import { FaUser, FaSignInAlt, FaUserPlus } from 'react-icons/fa';
// import { Text } from './ui/Typography'; // Text не используется
import Button from './Button';
import { _AdminMenuSidebar } from './admin/AdminMenuSidebar';

const Header = () => {
  const { t, i18n } = useTranslation();
  const { cart } = useCart();
  const { wishlist } = useWishlist();
  const { compareItems } = useCompare();
  const { user, signOut, isLoggedIn, loading } = useAuth();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [authMenuOpen, setAuthMenuOpen] = useState(false);
  const authDropdownRef = useRef(null);

  // Проверяем, находимся ли мы в админ-разделе
  const _isAdminPage = location.pathname.startsWith('/admin');

  useEffect(() => {
    const handleClickOutside = event => {
      if (authDropdownRef.current && !authDropdownRef.current.contains(event.target)) {
        setAuthMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const changeLanguage = lng => {
    i18n.changeLanguage(lng);
  };

  const handleLogout = async () => {
    await signOut();
    navigate('/login');
    setIsMobileMenuOpen(false);
  };

  const totalCartItems = cart.reduce((sum, item) => sum + item.quantity, 0);

  // Обновляем стили для счётчиков, чтобы они были идеально круглыми
  const navLinkClass = 'text-body hover:text-primary transition-colors';
  const navLinkWithBadgeClass = 'text-body hover:text-primary transition-colors relative';
  const badgeClass =
    'absolute -top-2 -right-2 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full';
  const mobileBadgeClass =
    'absolute -top-2 left-20 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full';

  return (
    <header className="fixed-header bg-white shadow-md fixed top-0 left-0 right-0 z-50">
      <div className="container mx-auto flex justify-between items-center py-3 md:py-4 px-4 md:px-6">
        <Link
          to="/"
          className="font-heading text-xl md:text-2xl font-semibold text-heading flex items-center"
        >
          KitchenShop
        </Link>

        {/* Навигация для десктопа */}
        <nav className="hidden md:flex space-x-6 items-center">
          <Link to="/" className={navLinkClass}>
            {t('home', 'Главная')}
          </Link>
          <Link to="/categories" className={navLinkClass}>
            {t('categories', 'Категории')}
          </Link>
          <Link to="/about" className={navLinkClass}>
            {t('about', 'О нас')}
          </Link>
          <Link to="/contact" className={navLinkClass}>
            {t('contact', 'Контакты')}
          </Link>
          {user && user.app_metadata?.claims_admin && (
            <Link to="/admin" className={navLinkClass}>
              {t('admin', 'Админка')}
            </Link>
          )}
          <Link to="/cart" className={navLinkWithBadgeClass}>
            {t('cart', 'Корзина')}
            {totalCartItems > 0 && <span className={badgeClass}>{totalCartItems}</span>}
          </Link>
          <Link to="/wishlist" className={navLinkWithBadgeClass}>
            {t('wishlist', 'Избранное')}
            {wishlist.length > 0 && <span className={badgeClass}>{wishlist.length}</span>}
          </Link>
          <Link to="/compare" className={navLinkWithBadgeClass}>
            {t('compare', 'Сравнить')}
            {compareItems.length > 0 && <span className={badgeClass}>{compareItems.length}</span>}
          </Link>
          <div className="w-64">
            <SearchBar />
          </div>
        </nav>

        {/* Бургер-меню для мобильных устройств */}
        <div className="md:hidden flex items-center">
          <Button
            variant="ghost"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-1 flex items-center justify-center"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d={isMobileMenuOpen ? 'M6 18L18 6M6 6l12 12' : 'M4 6h16M4 12h16M4 18h16'}
              />
            </svg>
          </Button>
        </div>

        {/* Правая часть хедера (язык и профиль) */}
        <div className="hidden md:flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => changeLanguage(i18n.language === 'uk' ? 'en' : 'uk')}
            className="p-2"
          >
            {i18n.language === 'uk' ? 'EN' : 'UK'}
          </Button>
          <div className="relative">
            {!loading && (
              <>
                {isLoggedIn ? (
                  <ProfileDropdown />
                ) : (
                  <div className="relative" ref={authDropdownRef}>
                    <Button
                      variant="ghost"
                      onClick={() => setAuthMenuOpen(!authMenuOpen)}
                      className="flex items-center justify-center w-12 h-12 rounded-full text-primary hover:bg-primary hover:text-white transition-colors duration-200"
                      aria-label={t('account', 'Аккаунт')}
                    >
                      <FaUser className="text-3xl" />
                    </Button>

                    {authMenuOpen && (
                      <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <Link
                          to="/login"
                          className="flex items-center px-4 py-2 text-sm text-body hover:bg-gray-100 hover:text-primary"
                          onClick={() => setAuthMenuOpen(false)}
                        >
                          <FaSignInAlt className="mr-2" />
                          {t('login', 'Войти')}
                        </Link>
                        <Link
                          to="/register"
                          className="flex items-center px-4 py-2 text-sm text-body hover:bg-gray-100 hover:text-primary"
                          onClick={() => setAuthMenuOpen(false)}
                        >
                          <FaUserPlus className="mr-2" />
                          {t('register', 'Регистрация')}
                        </Link>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Мобильное меню */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white shadow-md">
          {/* Добавляем поиск в верхней части мобильного меню */}
          <div className="p-4 border-b">
            <SearchBar />
          </div>

          <nav className="flex flex-col space-y-4 p-4">
            <Link to="/" className={navLinkClass} onClick={() => setIsMobileMenuOpen(false)}>
              {t('home', 'Главная')}
            </Link>
            <Link
              to="/categories"
              className={navLinkClass}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('categories', 'Категории')}
            </Link>
            <Link to="/about" className={navLinkClass} onClick={() => setIsMobileMenuOpen(false)}>
              {t('about', 'О нас')}
            </Link>
            <Link to="/contact" className={navLinkClass} onClick={() => setIsMobileMenuOpen(false)}>
              {t('contact', 'Контакты')}
            </Link>
            {user && user.app_metadata?.claims_admin && (
              <Link to="/admin" className={navLinkClass} onClick={() => setIsMobileMenuOpen(false)}>
                {t('admin', 'Админка')}
              </Link>
            )}
            <Link
              to="/cart"
              className={navLinkWithBadgeClass}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('cart', 'Корзина')}
              {totalCartItems > 0 && <span className={mobileBadgeClass}>{totalCartItems}</span>}
            </Link>
            <Link
              to="/wishlist"
              className={navLinkWithBadgeClass}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('wishlist', 'Избранное')}
              {wishlist.length > 0 && <span className={mobileBadgeClass}>{wishlist.length}</span>}
            </Link>
            <Link
              to="/compare"
              className={navLinkWithBadgeClass}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('compare', 'Сравнить')}
              {compareItems.length > 0 && (
                <span className={mobileBadgeClass}>{compareItems.length}</span>
              )}
            </Link>
            <Button
              variant="ghost"
              onClick={() => changeLanguage(i18n.language === 'uk' ? 'en' : 'uk')}
              className="text-left p-0"
            >
              {i18n.language === 'uk' ? 'EN' : 'UK'}
            </Button>
            {user ? (
              <>
                <Link
                  to="/account"
                  className={navLinkClass}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {t('profile', 'Профиль')}
                </Link>
                <Link
                  to="/privacy"
                  className={navLinkClass}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {t('privacy', 'Конфиденциальность')}
                </Link>
                <Link
                  to="/terms"
                  className={navLinkClass}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {t('terms', 'Условия')}
                </Link>
                <Button
                  variant="ghost"
                  onClick={handleLogout}
                  className="text-left p-0 text-body hover:text-primary"
                >
                  {t('logout', 'Выйти')}
                </Button>
              </>
            ) : (
              <div className="flex flex-col space-y-2">
                <Link
                  to="/login"
                  className={`flex items-center ${navLinkClass}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <FaSignInAlt className="mr-2" />
                  {t('login', 'Войти')}
                </Link>
                <Link
                  to="/register"
                  className={`flex items-center ${navLinkClass}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <FaUserPlus className="mr-2" />
                  {t('register', 'Регистрация')}
                </Link>
              </div>
            )}
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
