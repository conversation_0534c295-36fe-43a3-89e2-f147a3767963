import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { getSafeImageUrl, createImageErrorHandler } from '../../utils/imageHelpers';
import { motion } from 'framer-motion';

const FeaturedCategories = ({ maxCategories = 6 }) => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  // Use ref to prevent re-fetching on re-renders
  const initialLoadDone = useRef(false);

  // Generate stable keys for categories that won't change on re-renders
  const getStableKey = category => {
    return `cat-${category.id}`;
  };

  useEffect(() => {
    // Only fetch categories once when component mounts
    if (initialLoadDone.current) return;

    const fetchCategories = async () => {
      setLoading(true);
      try {
        // First, try to get categories from localStorage for immediate display
        const cachedCategories = localStorage.getItem('featuredCategories');
        if (cachedCategories) {
          setCategories(JSON.parse(cachedCategories));
          setLoading(false);
        }

        // Get featured categories first
        const { data: featuredCategories, error } = await supabase
          .from('categories')
          .select('*')
          .eq('is_featured', true)
          .order('display_order', { ascending: true });

        if (error) throw error;

        // If we need more categories, get non-featured ones
        let allCategories = [...featuredCategories];

        if (featuredCategories.length < maxCategories) {
          const { data: regularCategories, error: regularError } = await supabase
            .from('categories')
            .select('*')
            .eq('is_featured', false)
            .order('display_order', { ascending: true })
            .limit(maxCategories - featuredCategories.length);

          if (regularError) throw regularError;
          allCategories = [...featuredCategories, ...(regularCategories || [])];
        }

        if (allCategories.length > 0) {
          // Cache the results for future use
          localStorage.setItem(
            'featuredCategories',
            JSON.stringify(allCategories.slice(0, maxCategories))
          );
          setCategories(allCategories.slice(0, maxCategories));
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
        initialLoadDone.current = true;
      }
    };

    fetchCategories();
  }, [maxCategories]);

  if (loading && categories.length === 0) {
    return (
      <section className="mb-12">
        <h2 className="text-2xl font-bold mb-6">{t('featured_categories', 'Категории')}</h2>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </section>
    );
  }

  const handleImageError = createImageErrorHandler('category');

  return (
    <section className="mb-16">
      <h2 className="text-3xl font-bold mb-8 text-center">
        {t('featured_categories', 'Категории')}
      </h2>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
        {categories.map((category, index) => (
          <motion.div
            key={getStableKey(category)}
            className="relative overflow-hidden"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: index * 0.1,
              ease: 'easeOut'
            }}
          >
            <Link to={`/category/${category.id}`} className="block category-card">
              <div className="aspect-square rounded-lg overflow-hidden relative group">
                <img
                  src={getSafeImageUrl(category.image, 'category')}
                  alt={category.name}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  onError={handleImageError}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent opacity-80 group-hover:opacity-70 transition-opacity duration-300"></div>
                <div className="absolute inset-0 flex items-end p-4">
                  <h3 className="text-white text-lg md:text-xl font-bold mb-2 group-hover:mb-3 transition-all duration-300">
                    {category.name}
                  </h3>
                </div>
              </div>
            </Link>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default FeaturedCategories;
