import React from 'react';
import { useTranslation } from 'react-i18next';

const partners = [
  { id: 1, name: '<PERSON><PERSON>', logo: '/images/partners/bosch.png' },
  { id: 2, name: 'Siemens', logo: '/images/partners/siemens.png' },
  { id: 3, name: 'Electrolux', logo: '/images/partners/electrolux.png' },
  { id: 4, name: 'AEG', logo: '/images/partners/aeg.png' },
  { id: 5, name: '<PERSON><PERSON><PERSON>', logo: '/images/partners/gorenje.png' },
  { id: 6, name: '<PERSON><PERSON>', logo: '/images/partners/miele.png' }
];

const PartnersList = ({ className = '' }) => {
  const { t } = useTranslation();

  return (
    <section className={className}>
      <h2 className="text-2xl font-bold mb-8">{t('our_partners', 'Наши партнеры')}</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
        {partners.map(partner => (
          <div
            key={partner.id}
            className="flex items-center justify-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
          >
            <img
              src={partner.logo}
              alt={partner.name}
              className="max-h-12 w-auto grayscale hover:grayscale-0 transition-all"
            />
          </div>
        ))}
      </div>
    </section>
  );
};

export default PartnersList;
