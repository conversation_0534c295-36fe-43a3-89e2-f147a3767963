import React, { useState, useEffect, useRef } from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { supabase } from '../../supabaseClient';
import { Link } from 'react-router-dom';
import { getSafeImageUrl, createImageErrorHandler } from '../../utils/imageHelpers';

const BrandSlider = ({ slidesToShow = 6, autoplay = true }) => {
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);

  // Use ref to track if we've loaded data
  const dataLoaded = useRef(false);

  useEffect(() => {
    // Only fetch once when component mounts
    if (dataLoaded.current) return;

    const fetchBrands = async () => {
      setLoading(true);
      try {
        // Check cache first for immediate display
        const cachedBrands = localStorage.getItem('brands');
        if (cachedBrands) {
          setBrands(JSON.parse(cachedBrands));
          setLoading(false);
        }

        // Fetch from database
        const { data, error } = await supabase.from('brands').select('*').order('name');

        if (error) throw error;

        if (data && data.length > 0) {
          // Filter out any brands that use placeholder services
          const filteredBrands = data.filter(brand => {
            if (!brand.logo) return true;
            return !(
              brand.logo.includes('via.placeholder.com') ||
              brand.logo.includes('placeholder.com') ||
              brand.logo.includes('placehold.it')
            );
          });

          localStorage.setItem('brands', JSON.stringify(filteredBrands));
          setBrands(filteredBrands);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
      } finally {
        setLoading(false);
        dataLoaded.current = true;
      }
    };

    fetchBrands();
  }, []);

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: slidesToShow,
    slidesToScroll: 1,
    autoplay: autoplay,
    autoplaySpeed: 3000,
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 5
        }
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 4
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3
        }
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 2
        }
      }
    ]
  };

  if (loading) {
    return (
      <div className="flex justify-center py-4">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (brands.length === 0) {
    return null;
  }

  const handleImageError = createImageErrorHandler('brand');

  return (
    <div className="brand-slider py-4">
      <Slider {...settings}>
        {brands.map(brand => (
          <div key={brand.id} className="px-4">
            <Link
              to={`/brands/${brand.id}`}
              className="block bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 h-[100px] flex items-center justify-center"
            >
              <img
                src={getSafeImageUrl(brand.logo, 'brand')}
                alt={brand.name}
                className="max-h-[80px] max-w-full object-contain"
                onError={handleImageError}
              />
            </Link>
          </div>
        ))}
      </Slider>
    </div>
  );
};

export default BrandSlider;
