import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { supabase } from '../../supabaseClient';
import { FaRegHeart, FaHeart, FaShoppingCart, FaBalanceScale, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { addToWishlist, removeFromWishlist } from '../../store/slices/wishlistSlice';
import { formatPrice } from '../../utils/formatters';
import { getSafeImageUrl, createImageErrorHandler } from '../../utils/imageHelpers';
import { useCart } from '../../context/CartContext';
import { useCompare } from '../../context/CompareContext';
import { getCache, setCache, clearCache } from '../../utils/cacheService';
import LazyImage from '../common/LazyImage';

// Кастомные стрелки навигации
const CustomPrevArrow = ({ onClick }) => (
  <button
    className="absolute left-[-50px] top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-all duration-200 hover:shadow-xl"
    onClick={onClick}
    aria-label="Previous slide"
  >
    <FaChevronLeft className="text-gray-600 text-lg" />
  </button>
);

const CustomNextArrow = ({ onClick }) => (
  <button
    className="absolute right-[-50px] top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-all duration-200 hover:shadow-xl"
    onClick={onClick}
    aria-label="Next slide"
  >
    <FaChevronRight className="text-gray-600 text-lg" />
  </button>
);

const ProductSlider = ({
  type = 'new', // 'new', 'sale', 'bestseller'
  slidesToShow = 4,
  slidesToScroll = 1,
  autoplay = true
}) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const { addToCart } = useCart();
  const { addToCompare, isInCompare } = useCompare();
  const dispatch = useDispatch();
  const wishlistItems = useSelector(state => state.wishlist.items);

  // Use ref to track if we've loaded data
  const dataLoaded = useRef(false);

  // Cache key for this specific slider
  const cacheKey = `products-${type}`;

  // Кеш действителен 15 минут (в миллисекундах)
  const CACHE_TTL = 15 * 60 * 1000;

  useEffect(() => {
    // Only fetch once when component mounts
    if (dataLoaded.current) return;

    // Clear cache for sale products to force refresh
    if (type === 'sale') {
      clearCache(cacheKey);
    }

    const fetchProducts = async () => {
      setLoading(true);

      try {
        // For sale products, always get fresh data to ensure we have original_price
        let cachedProducts = null;
        if (type !== 'sale') {
          // Only use cache for non-sale products
          cachedProducts = getCache(cacheKey, CACHE_TTL);
        }

        if (cachedProducts) {
          setProducts(cachedProducts);
          setLoading(false);
          dataLoaded.current = true;
          return; // Используем только кешированные данные, если они актуальны
        }

        let query = supabase.from('products').select('*');

        // Apply filters based on type
        if (type === 'new') {
          query = query.eq('is_new', true);
        } else if (type === 'sale') {
          // For sale items, explicitly select original_price to ensure it's included
          query = supabase.from('products').select('*, original_price').eq('is_on_sale', true);
        } else if (type === 'bestseller') {
          query = query.eq('is_bestseller', true);
        }

        const { data, error } = await query.order('created_at', { ascending: false }).limit(12);

        if (error) throw error;

        if (data && data.length > 0) {
          // Log the data to check if original_price exists
          if (type === 'sale') {
            data.forEach(product => {});

            // If no products have original_price, create a test product
            const hasOriginalPrices = data.some(product => product.original_price);
            if (!hasOriginalPrices && data.length > 0) {
              const firstProduct = data[0];
              // Set original_price to a higher value than current price
              const updatedPrice = firstProduct.price * 1.5;
              const { error } = await supabase
                .from('products')
                .update({ original_price: updatedPrice })
                .eq('id', firstProduct.id);

              if (error) {
                console.error('Error updating product with original_price:', error);
              } else {
                // Update the local data
                data[0].original_price = updatedPrice;
              }
            }
          }

          // Кешируем данные с помощью нового сервиса кеширования
          setCache(cacheKey, data);
          setProducts(data);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
        dataLoaded.current = true;
      }
    };

    fetchProducts();
  }, [type, cacheKey]);

  const handleAddToCart = product => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    });
  };

  const handleToggleWishlist = product => {
    const isInWishlist = wishlistItems.some(item => item.id === product.id);

    if (isInWishlist) {
      dispatch(removeFromWishlist(product.id));
    } else {
      dispatch(
        addToWishlist({
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image
        })
      );
    }
  };

  // Функция для добавления товара в сравнение
  const handleAddToCompare = (e, product) => {
    e.preventDefault();
    e.stopPropagation();
    addToCompare({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image
    });
  };

  const handleImageError = createImageErrorHandler('product');

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: slidesToShow,
    slidesToScroll: slidesToScroll,
    autoplay: false, // Отключаем автопроигрывание согласно предпочтениям
    autoplaySpeed: 3000,
    lazyLoad: 'ondemand',
    arrows: true,
    prevArrow: <CustomPrevArrow />,
    nextArrow: <CustomNextArrow />,
    swipeToSlide: true,
    className: 'product-slider-with-external-arrows',
    centerMode: false,
    centerPadding: '0px',
    adaptiveHeight: false,
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1
        }
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (products.length === 0) {
    return <div className="text-center py-8 text-gray-500">Нет товаров в данной категории</div>;
  }

  return (
    <div className="product-slider relative px-16">
      <Slider {...settings}>
        {products.map(product => {
          const isInWishlist = wishlistItems.some(item => item.id === product.id);
          const productInCompare = isInCompare(product.id);

          // Добавим проверку изображения здесь
          const productImage = product.image || '';

          return (
            <div key={product.id} className="px-1 pb-1">
              <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg hover:-translate-y-1 duration-300">
                <div className="relative" style={{ paddingTop: '100%' }}>
                  <Link to={`/product/${product.id}`} className="block absolute inset-0">
                    <LazyImage
                      src={productImage}
                      alt={product.name}
                      type="product"
                      className="absolute inset-0 w-full h-full object-cover"
                      onError={handleImageError}
                    />
                  </Link>

                  {/* Badges */}
                  <div className="absolute top-2 left-2 flex flex-col gap-1">
                    {product.is_new && (
                      <span className="bg-green-500 text-white px-2 py-1 text-xs rounded">
                        Новинка
                      </span>
                    )}
                    {product.is_on_sale && (
                      <span className="bg-red-500 text-white px-2 py-1 text-xs rounded">
                        Скидка
                      </span>
                    )}
                    {product.is_bestseller && (
                      <span className="bg-amber-500 text-white px-2 py-1 text-xs rounded">Хит</span>
                    )}
                  </div>

                  {/* Buttons in top right */}
                  <div className="absolute top-2 right-2 flex flex-col gap-2">
                    {/* Wishlist button */}
                    <button
                      onClick={() => handleToggleWishlist(product)}
                      className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 transition-colors"
                    >
                      {isInWishlist ? (
                        <FaHeart className="text-red-500" />
                      ) : (
                        <FaRegHeart className="text-gray-400" />
                      )}
                    </button>

                    {/* Compare button */}
                    <button
                      onClick={e => handleAddToCompare(e, product)}
                      className={`bg-white p-2 rounded-full shadow-md transition-colors ${
                        productInCompare ? 'bg-blue-100' : 'hover:bg-gray-100'
                      }`}
                      title={productInCompare ? 'В сравнении' : 'Добавить в сравнение'}
                    >
                      <FaBalanceScale
                        className={productInCompare ? 'text-blue-500' : 'text-gray-400'}
                      />
                    </button>
                  </div>
                </div>

                <div className="p-3">
                  <Link to={`/product/${product.id}`}>
                    <h3
                      className="font-medium text-gray-900 mb-1 hover:text-primary text-sm line-clamp-2"
                      title={product.name}
                    >
                      {product.name}
                    </h3>
                  </Link>

                  <div className="flex justify-between items-center mt-2">
                    <div>
                      {product.is_on_sale && (product.original_price || product.old_price) ? (
                        <div className="flex flex-col">
                          <span className="text-md font-bold text-primary">
                            {formatPrice(product.price)}
                          </span>
                          <span className="text-xs text-gray-500 line-through">
                            {formatPrice(product.original_price || product.old_price)}
                          </span>
                        </div>
                      ) : (
                        <span className="text-md font-bold text-gray-900">
                          {formatPrice(product.price)}
                        </span>
                      )}
                    </div>

                    <button
                      onClick={() => handleAddToCart(product)}
                      className="bg-primary hover:bg-primary-dark text-white p-1.5 rounded-full transition-colors"
                      aria-label="Add to cart"
                    >
                      <FaShoppingCart className="text-sm" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </Slider>
    </div>
  );
};

export default ProductSlider;
