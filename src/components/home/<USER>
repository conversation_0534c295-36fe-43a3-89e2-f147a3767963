import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import ProductCard from '../ProductCard';

const ProductsSection = ({ title, filter, className = '', limit = 8 }) => {
  const { t } = useTranslation();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        let query = supabase
          .from('products')
          .select(
            `
            *,
            categories (
              id,
              name
            )
          `
          )
          .limit(limit);

        // Показываем только активные товары
        query = query.eq('is_active', true);

        // Fix: Match filter property names with how they're passed from Home.js
        if (filter?.isOnSale) {
          query = query.eq('is_on_sale', true);
        }
        if (filter?.isNew) {
          query = query.eq('is_new', true);
        }
        if (filter?.isBestseller) {
          query = query.eq('is_bestseller', true);
        }

        const { data, error } = await query.order('created_at', { ascending: false });

        if (error) {
          throw error;
        }
        setProducts(data || []);
      } catch (error) {
        console.error(`Error fetching products for "${title}":`, error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [filter, limit, title]);

  if (loading) {
    return <div className="animate-pulse">{/* Skeleton loading */}</div>;
  }

  return (
    <section className={className}>
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold">{title}</h2>
        <Link
          to={`/products?${new URLSearchParams(filter).toString()}`}
          className="text-primary hover:text-primary-dark transition-colors"
        >
          {t('view_all', 'Смотреть все')} →
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.length > 0 ? (
          products.map(product => <ProductCard key={product.id} product={product} />)
        ) : (
          <div className="col-span-full text-center py-8 text-gray-500">
            {t('no_products_found', 'Товары не найдены')}
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductsSection;
