import React from 'react';
import { Link } from 'react-router-dom';

const MainBanner = () => {
  return (
    <div className="relative w-full h-[500px] mb-12">
      <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent z-10" />
      <img
        src="/images/banner.jpg"
        alt="Kitchen Equipment"
        className="w-full h-full object-cover"
      />
      <div className="absolute inset-0 z-20 flex items-center justify-start px-20">
        <div className="text-white max-w-2xl">
          <h1 className="text-5xl font-bold mb-4">Профессиональная кухонная техника</h1>
          <p className="text-xl mb-8">Лучшие бренды по доступным ценам</p>
          <Link
            to="/categories"
            className="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg 
            transition duration-300 inline-block"
          >
            Смотреть каталог
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MainBanner;
