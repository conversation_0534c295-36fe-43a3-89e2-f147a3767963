import React from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { supabase } from '../supabaseClient';

const SeedData = () => {
  const { t } = useTranslation();

  // Добавление тестовых категорий
  const seedCategories = async () => {
    try {
      // Сначала проверяем структуру таблицы
      const { data: tableInfo, error: tableError } = await supabase
        .from('categories')
        .select('*')
        .limit(1);

      if (tableError) {
        throw new Error(`Ошибка при проверке таблицы: ${tableError.message}`);
      }

      // Определяем, есть ли колонка image
      const hasImageColumn = tableInfo.length > 0 ? 'image' in tableInfo[0] : false;

      // Создаем категории в зависимости от структуры таблицы
      const categories = [
        { name: 'Гостиная' },
        { name: 'Спальня' },
        { name: 'Кухня' },
        { name: 'Ванная' }
      ];

      // Если есть колонка image, добавляем URL изображений
      if (hasImageColumn) {
        categories.forEach(cat => {
          cat.image = `https://example.com/${cat.name.toLowerCase()}.jpg`;
        });
      }

      const { data, error } = await supabase.from('categories').insert(categories);

      if (error) throw error;
      toast.success(`Добавлено ${categories.length} категорий!`);
    } catch (error) {
      console.error('Error seeding categories:', error);
      toast.error(`Ошибка при добавлении категорий: ${error.message}`);
    }
  };

  // Добавление тестовых товаров
  const seedProducts = async () => {
    try {
      // Проверяем структуру таблицы
      const { data: tableInfo, error: tableError } = await supabase
        .from('products')
        .select('*')
        .limit(1);

      if (tableError) {
        throw new Error(`Ошибка при проверке таблицы: ${tableError.message}`);
      }

      // Определяем, какие колонки есть
      const hasColumns = {
        description: tableInfo.length > 0 ? 'description' in tableInfo[0] : false,
        image_url: tableInfo.length > 0 ? 'image_url' in tableInfo[0] : false
      };

      // Получаем категории для связи
      const { data: categories } = await supabase.from('categories').select('id');

      if (!categories || categories.length === 0) {
        toast.warning('Сначала добавьте категории!');
        return;
      }

      const categoryId = categories[0].id;

      // Создаем базовые продукты
      const products = [
        {
          name: 'Диван "Комфорт"',
          price: 25000,
          category_id: categoryId
        },
        {
          name: 'Кровать "Сон"',
          price: 18000,
          category_id: categoryId
        },
        {
          name: 'Кухонный стол "Обед"',
          price: 12000,
          category_id: categoryId
        }
      ];

      // Добавляем дополнительные поля, если они есть в таблице
      if (hasColumns.description) {
        products[0].description = 'Удобный угловой диван для гостиной';
        products[1].description = 'Двуспальная кровать с ортопедическим матрасом';
        products[2].description = 'Раскладной кухонный стол';
      }

      if (hasColumns.image_url) {
        products[0].image_url = 'https://example.com/sofa.jpg';
        products[1].image_url = 'https://example.com/bed.jpg';
        products[2].image_url = 'https://example.com/table.jpg';
      }

      const { data, error } = await supabase.from('products').insert(products);

      if (error) throw error;
      toast.success(`Добавлено ${products.length} товаров!`);
    } catch (error) {
      console.error('Error seeding products:', error);
      toast.error(`Ошибка при добавлении товаров: ${error.message}`);
    }
  };

  // Добавление тестовых баннеров
  const seedBanners = async () => {
    try {
      // Проверяем существование таблицы banners
      const { error: tableError } = await supabase.from('banners').select('*').limit(1);

      if (tableError && tableError.code === '42P01') {
        // код "relation does not exist"
        // Создаем таблицу, если ее нет
        await supabase.rpc('create_banners_table');
        toast.info('Создана таблица "banners"');
      } else if (tableError) {
        throw tableError;
      }

      const banners = [
        { title: 'Скидки до 50%', image_url: 'https://example.com/banner1.jpg', is_active: true },
        {
          title: 'Новые поступления',
          image_url: 'https://example.com/banner2.jpg',
          is_active: true
        }
      ];

      const { error } = await supabase.from('banners').insert(banners);

      if (error) throw error;
      toast.success(`Добавлено ${banners.length} баннеров!`);
    } catch (error) {
      console.error('Error seeding banners:', error);
      toast.error(`Ошибка при добавлении баннеров: ${error.message}`);
    }
  };

  // Добавление тестовых партнеров
  const seedPartners = async () => {
    try {
      // Проверяем существование таблицы partners
      const { error: tableError } = await supabase.from('partners').select('*').limit(1);

      if (tableError && tableError.code === '42P01') {
        // код "relation does not exist"
        // Создаем таблицу, если ее нет
        await supabase.rpc('create_partners_table');
        toast.info('Создана таблица "partners"');
      } else if (tableError) {
        throw tableError;
      }

      const partners = [
        { name: 'IKEA', image: 'https://example.com/ikea.jpg', is_active: true },
        { name: 'Ashley Furniture', image: 'https://example.com/ashley.jpg', is_active: true }
      ];

      const { error } = await supabase.from('partners').insert(partners);

      if (error) throw error;
      toast.success(`Добавлено ${partners.length} партнеров!`);
    } catch (error) {
      console.error('Error seeding partners:', error);
      toast.error(`Ошибка при добавлении партнеров: ${error.message}`);
    }
  };

  // Добавление всех тестовых данных
  const seedAllData = async () => {
    try {
      await seedCategories();
      await seedProducts();
      await seedBanners();
      await seedPartners();
      toast.success('Все тестовые данные добавлены!');
    } catch (error) {
      console.error('Error seeding all data:', error);
      toast.error(`Ошибка при добавлении данных: ${error.message}`);
    }
  };

  return (
    <div className="container mx-auto px-6 py-12 text-center">
      <h1 className="text-3xl font-semibold mb-6">{t('seed_data', 'Создание тестовых данных')}</h1>
      <div className="flex flex-col gap-4 max-w-md mx-auto">
        <button
          onClick={seedCategories}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          {t('seed_categories', 'Добавить тестовые категории')}
        </button>
        <button
          onClick={seedProducts}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          {t('seed_products', 'Добавить тестовые товары')}
        </button>
        <button
          onClick={seedBanners}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          {t('seed_banners', 'Добавить тестовые баннеры')}
        </button>
        <button
          onClick={seedPartners}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          {t('seed_partners', 'Добавить тестовых партнеров')}
        </button>
        <button
          onClick={seedAllData}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          {t('seed_all', 'Добавить все тестовые данные')}
        </button>
      </div>
    </div>
  );
};

export default SeedData;
