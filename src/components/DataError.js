import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const DataError = ({ message, resetError }) => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-6 py-12">
      <div className="max-w-md mx-auto text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          {t('error_occurred', 'Произошла ошибка')}
        </h2>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="space-x-4">
          {resetError && (
            <button
              onClick={resetError}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              {t('try_again', 'Попробовать снова')}
            </button>
          )}
          <Link
            to="/"
            className="inline-block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            {t('back_to_home', 'Вернуться на главную')}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DataError;
