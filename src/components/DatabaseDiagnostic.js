import React, { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';

const DatabaseDiagnostic = () => {
  const [schema, setSchema] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDatabaseSchema();
  }, []);

  const fetchDatabaseSchema = async () => {
    setLoading(true);
    setError(null);

    try {
      // Получаем список таблиц и их структуру
      const tableResults = {};
      const tables = ['categories', 'products', 'banners', 'partners'];

      for (const tableName of tables) {
        try {
          // Проверяем существование таблицы
          const { data, error } = await supabase.from(tableName).select('*').limit(1);

          if (error) {
            tableResults[tableName] = {
              exists: false,
              error: error.message
            };
            continue;
          }

          // Если таблица существует и есть данные, получаем структуру
          if (data && data.length > 0) {
            tableResults[tableName] = {
              exists: true,
              columns: Object.keys(data[0]),
              sampleData: data[0]
            };
          } else {
            // Если таблица существует, но данных нет, получаем только структуру
            // Пытаемся получить структуру из метаданных
            tableResults[tableName] = {
              exists: true,
              empty: true,
              columns: [] // Пустой список колонок, так как данных нет
            };
          }
        } catch (err) {
          tableResults[tableName] = {
            exists: false,
            error: err.message
          };
        }
      }

      setSchema(tableResults);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching database schema:', err);
    } finally {
      setLoading(false);
    }
  };

  // Функция для создания SQL-команд для добавления недостающих колонок
  const generateAlterTableSQL = () => {
    const sql = [];

    // Проверяем наличие нужных колонок в таблицах
    if (schema?.categories?.exists) {
      if (!schema.categories.columns?.includes('image')) {
        sql.push('ALTER TABLE categories ADD COLUMN image TEXT;');
      }
    }

    if (schema?.products?.exists) {
      if (!schema.products.columns?.includes('image_url')) {
        sql.push('ALTER TABLE products ADD COLUMN image_url TEXT;');
      }
      if (!schema.products.columns?.includes('description')) {
        sql.push('ALTER TABLE products ADD COLUMN description TEXT;');
      }
    }

    return sql.join('\n');
  };

  return (
    <div className="container mx-auto p-4">
      <h2 className="text-2xl font-bold mb-4">Диагностика базы данных</h2>

      {loading && <p className="text-gray-500">Загрузка структуры базы данных...</p>}

      {error && (
        <div className="bg-red-100 border border-red-300 p-4 rounded mb-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {schema && (
        <div>
          <h3 className="text-xl font-semibold mb-2">Структура таблиц:</h3>

          {Object.entries(schema).map(([tableName, tableInfo]) => (
            <div key={tableName} className="mb-4 p-4 border rounded">
              <h4 className="text-lg font-medium">{tableName}</h4>

              {tableInfo.exists ? (
                <>
                  <p className="text-green-600">✓ Таблица существует</p>

                  {tableInfo.columns && tableInfo.columns.length > 0 ? (
                    <div>
                      <p className="font-medium mt-2">Колонки:</p>
                      <ul className="list-disc list-inside">
                        {tableInfo.columns.map(column => (
                          <li key={column}>{column}</li>
                        ))}
                      </ul>
                    </div>
                  ) : (
                    <p className="text-yellow-600 mt-2">
                      Таблица пустая, невозможно определить колонки
                    </p>
                  )}

                  {tableInfo.sampleData && (
                    <div className="mt-2">
                      <p className="font-medium">Пример данных:</p>
                      <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                        {JSON.stringify(tableInfo.sampleData, null, 2)}
                      </pre>
                    </div>
                  )}
                </>
              ) : (
                <>
                  <p className="text-red-600">✗ Таблица не существует или недоступна</p>
                  {tableInfo.error && <p className="text-red-500">Ошибка: {tableInfo.error}</p>}
                </>
              )}
            </div>
          ))}

          <h3 className="text-xl font-semibold mt-4 mb-2">SQL для исправления структуры:</h3>
          <pre className="bg-gray-800 text-white p-4 rounded overflow-auto">
            {generateAlterTableSQL() || '-- Все необходимые колонки уже существуют'}
          </pre>

          <button
            onClick={fetchDatabaseSchema}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Обновить информацию
          </button>
        </div>
      )}
    </div>
  );
};

export default DatabaseDiagnostic;
