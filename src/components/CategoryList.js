import { supabase } from '../supabaseClient';

import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const CategoryList = ({ onCategorySelect }) => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        // Загружаем все категории
        const { data: categories, error: categoriesError } = await supabase
          .from('categories')
          .select('*');

        if (categoriesError) {
          throw categoriesError;
        }

        // Загружаем все товары, чтобы определить, какие категории имеют товары
        const { data: products, error: productsError } = await supabase
          .from('products')
          .select('*');

        if (productsError) {
          throw productsError;
        }

        const categoriesList = categories || [];
        const productsList = products || [];

        // Фильтруем категории, для которых есть товары
        const categoriesWithProducts = categoriesList.filter(category =>
          productsList.some(product => product.category_id === category.id)
        );

        setCategories(categoriesWithProducts);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(
          t('error_loading_data', 'Не удалось загрузить данные. Проверьте подключение к интернету.')
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [t]);

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-12 text-center">{t('loading', 'Загрузка...')}</div>
    );
  }

  if (error) {
    return <div className="container mx-auto px-6 py-12 text-center text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <h2 className="text-3xl font-bold mb-8 text-center">{t('categories', 'Категории')}</h2>
      {categories.length === 0 ? (
        <p className="text-gray-600 text-center">{t('no_categories', 'Категорий пока нет.')}</p>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {categories.map(category => (
            <div
              key={category.id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300 animate-fadeInUp"
            >
              <Link to={`/category/${category.id}`}>
                <div className="relative overflow-hidden">
                  <img
                    src={category.image || 'https://placehold.co/235x235/EEE/31343C?text=Category'}
                    alt={category.name || t('category_image', 'Category image')}
                    className="w-full h-48 object-cover transition-transform duration-300 hover:scale-110"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-opacity duration-300 flex items-center justify-center">
                    <h4 className="text-white text-lg font-semibold opacity-0 hover:opacity-100 transition-opacity duration-300">
                      {category.name}
                    </h4>
                  </div>
                </div>
                <h4 className="text-center p-4 text-lg font-semibold text-gray-800">
                  {category.name}
                </h4>
              </Link>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoryList;
