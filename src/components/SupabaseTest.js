import { useState } from 'react';
import { supabase } from '../supabaseClient';

const SupabaseTest = () => {
  const [status, setStatus] = useState('');
  const [details, setDetails] = useState(null);

  const testConnection = async () => {
    try {
      setStatus('Testing connection...');
      setDetails(null);

      // Проверяем таблицу categories
      const { data: categories, error: catError } = await supabase
        .from('categories')
        .select('*')
        .limit(1);

      if (catError) {
        console.error('Categories test failed:', catError);
        setDetails({ categoryError: catError.message });
        throw new Error('Cannot access categories table');
      }

      // Проверяем таблицу products
      const { data: products, error: prodError } = await supabase
        .from('products')
        .select('*')
        .limit(1);

      if (prodError) {
        console.error('Products test failed:', prodError);
        setDetails({ productsError: prodError.message });
        throw new Error('Cannot access products table');
      }

      setDetails({
        categoriesCount: categories?.length || 0,
        productsCount: products?.length || 0,
        categoriesExample: categories?.[0] || null,
        productsExample: products?.[0] || null
      });

      setStatus('Connection successful! Database tables are accessible.');
    } catch (err) {
      console.error('Connection test failed:', err);
      setStatus(`Connection error: ${err.message}`);
    }
  };

  return (
    <div className="p-4 border rounded mt-4">
      <h3 className="font-semibold mb-2">Supabase Connection Test</h3>
      <button
        onClick={testConnection}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Test Connection
      </button>
      {status && <div className="mt-2 font-medium">{status}</div>}
      {details && (
        <pre className="mt-2 p-2 bg-gray-100 text-sm overflow-auto max-h-40 rounded">
          {JSON.stringify(details, null, 2)}
        </pre>
      )}
    </div>
  );
};

export default SupabaseTest;
