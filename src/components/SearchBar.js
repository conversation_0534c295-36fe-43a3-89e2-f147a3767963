import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { FaSearch } from 'react-icons/fa';

const SearchBar = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [query, setQuery] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Синхронизация с URL при монтировании компонента
  useEffect(() => {
    // Получаем запрос из URL, если находимся на странице поиска
    const urlQuery = searchParams.get('query');
    if (urlQuery) {
      setQuery(urlQuery);
    }
  }, [searchParams]);

  // Обработчик отправки формы поиска
  const handleSearch = async e => {
    e.preventDefault();

    if (!query.trim()) return;

    try {
      setIsSubmitting(true);

      // Используем navigate вместо прямой манипуляции с URL
      const encodedQuery = encodeURIComponent(query.trim());
      navigate(`/search?query=${encodedQuery}`);

      // Добавляем небольшую задержку для предотвращения множественных быстрых запросов
      await new Promise(resolve => setTimeout(resolve, 300));
    } catch (error) {
      console.error('Ошибка при перенаправлении на страницу поиска:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Обработка нажатия Enter в поле ввода
  const handleKeyPress = e => {
    if (e.key === 'Enter' && !isSubmitting) {
      handleSearch(e);
    }
  };

  return (
    <form onSubmit={handleSearch} className="flex w-full relative">
      <input
        type="text"
        value={query}
        onChange={e => setQuery(e.target.value)}
        onKeyDown={handleKeyPress}
        placeholder={t('search_placeholder', 'Поиск товаров...')}
        className="p-2 pr-9 border rounded-md w-full focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600"
        aria-label={t('search_products', 'Поиск товаров')}
        disabled={isSubmitting}
      />
      <button
        type="submit"
        disabled={isSubmitting}
        className={`absolute right-2 top-1/2 transform -translate-y-1/2 ${
          isSubmitting ? 'text-gray-300' : 'text-gray-400 hover:text-blue-600'
        }`}
        aria-label={t('search', 'Поиск')}
      >
        <FaSearch />
      </button>
    </form>
  );
};

export default SearchBar;
