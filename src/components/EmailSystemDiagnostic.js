import React, { useState } from 'react';
import { EmailService } from '../services/emailService';
import { toast } from 'react-toastify';

const EmailSystemDiagnostic = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState(null);

  const testEmailSystem = async () => {
    setTesting(true);
    setResults(null);

    const testResults = {
      emailServiceAvailable: false,
      functionResponse: null,
      error: null
    };

    try {
      // Test order data
      const testOrder = {
        id: 'test-' + Date.now(),
        customer_name: 'Test Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+1234567890',
        total_amount: 100,
        created_at: new Date().toISOString(),
        shipping_address: {
          city: 'Test City',
          nova_poshta_office: '1'
        }
      };

      // Test EmailService
      console.log('Testing EmailService with order:', testOrder);

      const response = await EmailService.sendOrderConfirmation(testOrder.id, testOrder);

      testResults.emailServiceAvailable = true;
      testResults.functionResponse = response;

      toast.success('📧 Email system test successful!');
    } catch (error) {
      console.error('Email system test failed:', error);
      testResults.error = error.message;

      if (error.message.includes('Email service not available')) {
        toast.error('❌ Email service not available');
      } else if (error.message.includes('FunctionsHttpError')) {
        toast.error('❌ Supabase function error (400 Bad Request)');
      } else {
        toast.error('❌ Email test failed: ' + error.message);
      }
    }

    setResults(testResults);
    setTesting(false);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">📧 Email System Diagnostic</h2>

      <div className="mb-6">
        <button
          onClick={testEmailSystem}
          disabled={testing}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {testing ? (
            <>
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              Testing Email System...
            </>
          ) : (
            '🧪 Test Email System'
          )}
        </button>
      </div>

      {results && (
        <div className="space-y-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-2">Test Results:</h3>

            <div className="space-y-2">
              <div className="flex items-center">
                <span
                  className={`w-3 h-3 rounded-full mr-3 ${results.emailServiceAvailable ? 'bg-green-500' : 'bg-red-500'}`}
                ></span>
                <span>
                  EmailService Available: {results.emailServiceAvailable ? '✅ Yes' : '❌ No'}
                </span>
              </div>

              {results.functionResponse && (
                <div className="ml-6">
                  <span className="text-green-600">
                    ✅ Function Response: {JSON.stringify(results.functionResponse)}
                  </span>
                </div>
              )}

              {results.error && (
                <div className="ml-6">
                  <span className="text-red-600">❌ Error: {results.error}</span>
                </div>
              )}
            </div>
          </div>

          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">📋 Recommendations:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              {!results.emailServiceAvailable ? (
                <>
                  <li>• Check Supabase Edge Function deployment</li>
                  <li>• Verify function permissions and authentication</li>
                  <li>• Check Supabase function logs in dashboard</li>
                  <li>• Ensure SMTP configuration is correct</li>
                </>
              ) : (
                <>
                  <li>✅ Email system is working correctly</li>
                  <li>• Orders should receive email confirmations</li>
                  <li>• Check spam folder if emails don't arrive</li>
                </>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailSystemDiagnostic;
