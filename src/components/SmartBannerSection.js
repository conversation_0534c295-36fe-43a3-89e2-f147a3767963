import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { getSafeImageUrl } from '../utils/imageHelpers';
import { useAuth } from '../context/AuthContext';
import HeroBannerSlider from './HeroBannerSlider';

// Импортируем оригинальные стили для обратной совместимости
import '../styles/banner.css';

/**
 * Умный компонент баннера, который автоматически выбирает между
 * обычным баннером и слайдером в зависимости от настроек
 */
const SmartBannerSection = ({ position = 'top', ...props }) => {
  const [banners, setBanners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageError, setImageError] = useState(false);
  const [useSlider, setUseSlider] = useState(false);
  const bannerRef = useRef(null);
  const imageRef = useRef(null);
  const location = useLocation();
  const { user } = useAuth();

  const isHomePage = location.pathname === '/';

  const defaultBannerImage = {
    top: '/images/banners/default-top-banner.jpg',
    bottom: '/images/banners/default-bottom-banner.jpg'
  };

  // Загрузка баннеров и определение режима отображения
  const fetchBanners = useCallback(async () => {
    if (!isHomePage) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('banners').select('*');

      if (position === 'top') {
        query = query.lte('position', 5);
      } else {
        query = query.gt('position', 5);
      }

      const { data, error } = await query.order('position');

      if (error) {
        setError(error.message);
        return;
      }

      const activeBanners = data.filter(b => {
        return b.is_active === true || b.is_active === undefined;
      });

      setBanners(activeBanners);

      // Определяем, нужно ли использовать слайдер
      // Если есть более одного активного баннера ИЛИ хотя бы один баннер помечен как slider_enabled
      const shouldUseSlider = activeBanners.length > 1 || 
        activeBanners.some(banner => banner.slider_enabled);

      setUseSlider(shouldUseSlider);

    } catch (err) {
      setError('Failed to load banners');
    } finally {
      setLoading(false);
    }
  }, [position, isHomePage]);

  useEffect(() => {
    fetchBanners();
  }, [fetchBanners]);

  // Эффект параллакса для одиночного баннера
  useEffect(() => {
    if (useSlider || !banners.length) return;

    const handleScroll = () => {
      if (!imageRef.current || !bannerRef.current) return;

      const bannerRect = bannerRef.current.getBoundingClientRect();
      const isBannerNearView =
        bannerRect.bottom > -500 && bannerRect.top < window.innerHeight + 500;

      if (isBannerNearView) {
        if (position === 'top') {
          const offset = bannerRect.top * 0.5;
          imageRef.current.style.transform = `translateY(${offset}px)`;
        } else {
          const windowHeight = window.innerHeight;
          const docHeight = document.documentElement.scrollHeight;
          const scrollTop = window.pageYOffset;
          const scrollProgress = scrollTop / (docHeight - windowHeight);
          const distanceFromTop = bannerRect.top;

          if (distanceFromTop < windowHeight) {
            const parallaxIntensity = Math.min(scrollProgress * 2, 1);
            const maxOffset = 200;
            const currentOffset = parallaxIntensity * maxOffset;
            imageRef.current.style.transform = `translateY(-${currentOffset}px)`;
          }
        }
      }
    };

    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    return () => window.removeEventListener('scroll', throttledScroll);
  }, [position, useSlider, banners.length]);

  const handleImageError = () => {
    setImageError(true);
  };

  const getImageUrl = (banner) => {
    const imageUrl = banner?.image_url || banner?.image || banner?.img || banner?.imageUrl;
    return imageUrl || defaultBannerImage[position] || defaultBannerImage.top;
  };

  const getButtonText = (banner) => {
    return banner?.button_text || banner?.buttonText || banner?.btn_text || 'Подробнее';
  };

  const getButtonLink = (banner) => {
    return banner?.button_link || banner?.link_url || banner?.link || '/products';
  };

  const getBannerImageUrl = (banner) => {
    return imageError
      ? defaultBannerImage[position]
      : getSafeImageUrl(getImageUrl(banner), 'banner');
  };

  if (loading) {
    return (
      <div className={`${position}-banner-loading w-full h-40 bg-gray-200 flex items-center justify-center`}>
        <p className="text-gray-500">Loading banner...</p>
      </div>
    );
  }

  if (error || !banners.length) {
    return null;
  }

  // Если нужно использовать слайдер, возвращаем HeroBannerSlider
  if (useSlider) {
    return (
      <HeroBannerSlider 
        position={position} 
        autoPlay={true}
        autoPlayInterval={banners[0]?.auto_play_interval || 5000}
        {...props}
      />
    );
  }

  // Иначе возвращаем обычный баннер (первый активный)
  const activeBanner = banners[0];

  return (
    <div
      className={`banner-section-wrapper w-full ${position === 'top' ? 'mt-0' : 'mb-8'}`}
      style={{ marginTop: '-1px' }}
    >
      <div ref={bannerRef} className={`full-width-banner ${position}-banner`}>
        <div className="banner-image-container">
          <img
            ref={imageRef}
            src={getBannerImageUrl(activeBanner)}
            alt={activeBanner.title || ''}
            className={`banner-image ${position}-banner-image`}
            onError={handleImageError}
            style={{
              transition: 'transform 0.05s ease-out',
              ...(position === 'bottom'
                ? {
                    objectPosition: 'center top',
                    transform: 'translateY(-150px)'
                  }
                : {})
            }}
          />
        </div>

        <div className="banner-overlay"></div>

        <div className="banner-content">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="banner-title">{activeBanner.title}</h2>
              {activeBanner.subtitle && (
                <p className="banner-subtitle">{activeBanner.subtitle}</p>
              )}
              <Link
                to={getButtonLink(activeBanner)}
                className="banner-button hover:bg-primary-dark"
              >
                {getButtonText(activeBanner)}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartBannerSection;
