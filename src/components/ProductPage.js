/* eslint-disable prettier/prettier */
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../supabaseClient';
import { Helmet } from 'react-helmet-async';
import { useCart } from '../context/CartContext';
import { useCompare } from '../context/CompareContext';
import { useWishlist } from '../context/WishlistContext';
import { Heart } from 'react-feather';
import ProductCard from './ProductCard';
// Импортируем компоненты для масштабирования изображений
import Zoom from 'react-medium-image-zoom';
import 'react-medium-image-zoom/dist/styles.css';
// Import icons for gallery navigation
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const ProductPage = () => {
  const { t } = useTranslation();
  const { productId } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [params, setParams] = useState([]);
  const [loading, setLoading] = useState(true);
  const { addToCart } = useCart();
  const { addToCompare, isInCompare } = useCompare();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();

  // Состояние для управления изображениями
  const [selectedImage, setSelectedImage] = useState('');
  const [productImages, setProductImages] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Состояние для рекомендаций
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [similarProducts, setSimilarProducts] = useState([]);
  const [recentlyViewed, setRecentlyViewed] = useState([]);

  // Функция навигации по изображениям
  const navigateImages = direction => {
    if (direction === 'next') {
      setCurrentImageIndex(prev => (prev + 1 >= productImages.length ? 0 : prev + 1));
    } else {
      setCurrentImageIndex(prev => (prev - 1 < 0 ? productImages.length - 1 : prev - 1));
    }
  };

  // Update selectedImage when image index changes
  useEffect(() => {
    if (productImages.length > 0 && currentImageIndex < productImages.length) {
      setSelectedImage(productImages[currentImageIndex]);
    }
  }, [currentImageIndex, productImages]);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const { data, error } = await supabase
          .from('products')
          .select(
            `
            *,
            categories (
              id,
              name
            )
          `
          )
          .eq('id', productId)
          .single();

        if (error) throw error;
        if (!data) {
          navigate('/404');
          return;
        }

        setProduct(data);

        // Устанавливаем основное изображение
        setSelectedImage(data.image || '/placeholder.png');

        // Загружаем дополнительные изображения товара
        if (data.image_gallery && Array.isArray(data.image_gallery)) {
          setProductImages([data.image, ...data.image_gallery]);
        } else if (data.image) {
          setProductImages([data.image]);
        }

        // Загружаем параметры товара из фида
        const { data: paramsData, error: paramsError } = await supabase
          .from('product_params')
          .select('*')
          .eq('product_id', productId)
          .order('category', { ascending: true })
          .order('name');

        if (!paramsError) {
          setParams(paramsData || []);
        } else {
          console.error('Error fetching product parameters:', paramsError);
        }

        // Загружаем связанные товары
        if (data.categories?.id) {
          const { data: relatedData } = await supabase
            .from('products')
            .select('id, name, price, image, categories(id, name)')
            .eq('category_id', data.categories.id)
            .neq('id', productId)
            .limit(4);

          if (relatedData?.length) {
            setRelatedProducts(relatedData);
          }
        }

        // Товары с похожими тегами
        if (data.tags && Array.isArray(data.tags) && data.tags.length) {
          const { data: similarData } = await supabase
            .from('products')
            .select('id, name, price, image, categories(id, name)')
            .contains('tags', data.tags.slice(0, 2))
            .neq('id', productId)
            .limit(4);

          if (similarData?.length) {
            setSimilarProducts(similarData);
          }
        }

        // Управление недавно просмотренными товарами
        try {
          // Получаем текущий список из localStorage
          const viewedProducts = JSON.parse(localStorage.getItem('recentlyViewed') || '[]');

          // Добавляем текущий товар в начало списка
          const currentProduct = {
            id: productId,
            name: data.name,
            price: data.price,
            image: data.image
          };

          // Удаляем текущий товар из списка, если он уже есть
          const filteredProducts = viewedProducts.filter(p => p.id !== productId);

          // Добавляем его в начало и ограничиваем список до 4 элементов
          const newViewedProducts = [currentProduct, ...filteredProducts].slice(0, 4);

          // Сохраняем обновленный список
          localStorage.setItem('recentlyViewed', JSON.stringify(newViewedProducts));

          // Устанавливаем список недавно просмотренных товаров (исключая текущий)
          setRecentlyViewed(filteredProducts.slice(0, 3));
        } catch (err) {
          console.error('Ошибка при обработке недавно просмотренных товаров:', err);
        }
      } catch (error) {
        console.error('Error fetching product:', error);
        navigate('/404');
      } finally {
        setLoading(false);
      }
    };

    if (productId) {
      fetchProduct();
    }
  }, [productId, navigate]);

  // Функция для изменения изображения при клике на миниатюру
  const handleImageChange = image => {
    setSelectedImage(image);
    const newIndex = productImages.findIndex(img => img === image);
    if (newIndex >= 0) {
      setCurrentImageIndex(newIndex);
    }
  };

  if (loading) {
    return <div className="text-center py-8">{t('loading', 'Загрузка...')}</div>;
  }

  if (!product) {
    return (
      <div className="text-center py-8 text-red-500">
        {t('product_not_found', 'Товар не найден')}
      </div>
    );
  }

  // Проверяем, добавлен ли товар в сравнение
  const productInCompare = isInCompare(product.id);

  // Проверяем, добавлен ли товар в избранное
  const productInWishlist = isInWishlist(product.id);

  // Функция для добавления товара в сравнение
  const handleAddToCompare = () => {
    // Создаем базовый объект товара для сравнения
    const productForCompare = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image
    };
    addToCompare(productForCompare);
  };

  // Функция для добавления/удаления из избранного
  const handleWishlistClick = () => {
    if (productInWishlist) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  return (
    <>
      <Helmet>
        <title>{`${product.name} - ${t('shop_name', 'Kitchen Shop')}`}</title>
        <meta
          name="description"
          content={product.description ? product.description.substring(0, 160) : product.name}
        />
      </Helmet>
      <div className="container mx-auto px-4 py-8">
        {/* Хлебные крошки */}
        <nav className="flex mb-4 text-sm text-gray-500">
          <Link to="/" className="hover:text-primary">
            {t('home', 'Главная')}
          </Link>
          <span className="mx-2">/</span>
          {product.categories && (
            <>
              <Link to={`/category/${product.categories.id}`} className="hover:text-primary">
                {product.categories.name}
              </Link>
              <span className="mx-2">/</span>
            </>
          )}
          <span className="text-gray-700">{product.name}</span>
        </nav>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="product-images relative">
            {/* Кнопки сравнения и избранного в углу над фото */}
            <div className="absolute top-4 right-4 z-20 flex gap-2">
              <button
                onClick={handleAddToCompare}
                className={`btn-icon border border-gray-200 shadow ${productInCompare ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-400 hover:bg-blue-50 hover:text-blue-700'}`}
                aria-label={
                  productInCompare ? t('in_compare', 'В сравнении') : t('compare', 'Сравнить')
                }
                title={productInCompare ? t('in_compare', 'В сравнении') : t('compare', 'Сравнить')}
              >
                <svg
                  width="22"
                  height="22"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  viewBox="0 0 24 24"
                >
                  <rect x="2" y="9" width="7" height="13" rx="2" />
                  <rect x="15" y="4" width="7" height="18" rx="2" />
                </svg>
              </button>
              <button
                onClick={handleWishlistClick}
                className={`btn-icon border border-gray-200 shadow ${productInWishlist ? 'bg-red-100 text-red-500' : 'bg-white text-gray-400 hover:bg-red-50 hover:text-red-500'}`}
                aria-label={
                  productInWishlist
                    ? t('remove_from_wishlist', 'Убрать из избранного')
                    : t('add_to_wishlist', 'В избранное')
                }
                title={
                  productInWishlist
                    ? t('remove_from_wishlist', 'Убрать из избранного')
                    : t('add_to_wishlist', 'В избранное')
                }
              >
                <Heart size={20} fill={productInWishlist ? 'red' : 'none'} />
              </button>
            </div>

            {/* Основное изображение с зумом */}
            <div className="mb-4">
              <Zoom>
                <img
                  src={selectedImage}
                  alt={product.name}
                  className="w-full rounded-lg shadow-md object-cover"
                  onError={e => {
                    e.target.src = '/placeholder.png';
                  }}
                  style={{ maxHeight: '500px', objectFit: 'contain' }}
                />
              </Zoom>

              {/* Навигация по изображениям */}
              {productImages.length > 1 && (
                <>
                  <button
                    onClick={() => navigateImages('prev')}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 hover:bg-opacity-100 p-2 rounded-r-lg shadow z-10"
                    aria-label="Previous image"
                  >
                    <FaChevronLeft className="text-gray-700" />
                  </button>

                  <button
                    onClick={() => navigateImages('next')}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 hover:bg-opacity-100 p-2 rounded-l-lg shadow z-10"
                    aria-label="Next image"
                  >
                    <FaChevronRight className="text-gray-700" />
                  </button>

                  {/* Счетчик изображений */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs">
                    {currentImageIndex + 1} / {productImages.length}
                  </div>
                </>
              )}
            </div>

            <div className="absolute top-2 left-2 bg-white rounded-full px-2 py-1 text-xs shadow">
              {t('hover_to_zoom', 'Наведите для увеличения')}
            </div>

            {/* Галерея миниатюр */}
            {productImages.length > 1 && (
              <div className="grid grid-cols-5 sm:grid-cols-6 gap-2 mt-4">
                {productImages.map((image, index) => (
                  <div
                    key={index}
                    className={`cursor-pointer overflow-hidden rounded transition-all ${
                      selectedImage === image
                        ? 'border-2 border-primary ring-2 ring-primary-light scale-105'
                        : 'border border-gray-200 opacity-80 hover:opacity-100'
                    }`}
                    onClick={() => handleImageChange(image)}
                  >
                    <img
                      src={image || '/placeholder.png'}
                      alt={`${product.name} - ${index + 1}`}
                      className="h-16 w-full object-cover"
                      onError={e => {
                        e.target.src = '/placeholder.png';
                      }}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          <div>
            <h1 className="text-3xl font-bold mb-4">{product.name}</h1>
            <p className="text-2xl font-bold text-primary mb-4">
              {new Intl.NumberFormat('uk-UA', {
                style: 'currency',
                currency: 'UAH'
              }).format(product.price)}
            </p>

            {product.vendor && (
              <p className="text-gray-600 mb-4">
                {t('vendor')}: {product.vendor}
              </p>
            )}

            <p className="text-gray-600 mb-4">
              {t('category')}: {product.categories?.name || t('no_category')}
            </p>

            <div className="prose max-w-none mb-6">
              {product.description && (
                <div dangerouslySetInnerHTML={{ __html: product.description }} />
              )}
            </div>

            {/* Кнопка добавления в корзину */}
            <div className="mt-6 flex flex-wrap gap-4 items-center">
              <button
                onClick={() =>
                  addToCart({
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    image: product.image,
                    quantity: 1
                  })
                }
                className="bg-primary hover:bg-primary-dark text-white py-2 px-6 rounded-lg shadow transition-colors"
              >
                {t('add_to_cart', 'Добавить в корзину')}
              </button>
            </div>

            {/* Характеристики товара из фида */}
            {params.length > 0 && (
              <div className="mt-8">
                <h2 className="text-xl font-semibold mb-4">
                  {t('specifications', 'Характеристики')}
                </h2>
                <div className="bg-gray-50 rounded-lg p-4 divide-y divide-gray-200">
                  {(() => {
                    // Группировка параметров по категориям
                    const categories = {};
                    params.forEach(param => {
                      const category = param.category || 'Основные';
                      if (!categories[category]) {
                        categories[category] = [];
                      }
                      categories[category].push(param);
                    });

                    // Отображаем параметры по категориям
                    return Object.entries(categories).map(([category, categoryParams], idx) => (
                      <details key={idx} className="p-2" open={idx === 0 ? true : false}>
                        <summary className="font-medium text-lg cursor-pointer py-2 flex items-center justify-between">
                          <span>{category}</span>
                          <svg
                            className="w-5 h-5 transition-transform details-toggle"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </summary>
                        <div className="pt-2 pb-1">
                          <table className="w-full text-sm">
                            <tbody>
                              {categoryParams.map((param, index) => (
                                <tr
                                  key={param.id}
                                  className={`border-b border-gray-100 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                                >
                                  <td className="py-3 px-4 font-medium text-gray-700 w-1/3">
                                    {param.name}
                                  </td>
                                  <td className="py-3 px-4 text-gray-600">{param.value}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </details>
                    ));
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Раздел рекомендаций товаров */}
        <div className="mt-16">
          {/* Похожие товары из той же категории */}
          {relatedProducts.length > 0 && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-6 relative pb-2 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-16 after:h-1 after:bg-primary">
                {t('related_products', 'Похожие товары')}
              </h2>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {relatedProducts.map(item => (
                  <ProductCard key={item.id} product={item} />
                ))}
              </div>
            </div>
          )}

          {/* Товары с похожими характеристиками */}
          {similarProducts.length > 0 && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold mb-6 relative pb-2 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-16 after:h-1 after:bg-primary">
                {t('similar_products', 'Сопутствующие товары')}
              </h2>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {similarProducts.map(item => (
                  <ProductCard key={item.id} product={item} />
                ))}
              </div>
            </div>
          )}

          {/* Недавно просмотренные товары */}
          {recentlyViewed.length > 0 && (
            <div>
              <h2 className="text-2xl font-bold mb-6 relative pb-2 after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-16 after:h-1 after:bg-primary">
                {t('recently_viewed', 'Вы недавно смотрели')}
              </h2>

              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {recentlyViewed.map(item => (
                  <Link
                    to={`/product/${item.id}`}
                    key={item.id}
                    className="flex items-center gap-4 bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow p-3 border border-gray-100"
                  >
                    <div className="relative h-20 w-20 flex-shrink-0 overflow-hidden rounded">
                      <img
                        src={item.image || '/placeholder.png'}
                        alt={item.name}
                        className="w-full h-full object-cover"
                        onError={e => {
                          e.target.src = '/placeholder.png';
                        }}
                      />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium line-clamp-2 group-hover:text-primary transition-colors">
                        {item.name}
                      </h3>
                      <p className="font-bold text-primary text-sm mt-1">
                        {new Intl.NumberFormat('uk-UA', {
                          style: 'currency',
                          currency: 'UAH'
                        }).format(item.price)}
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ProductPage;
