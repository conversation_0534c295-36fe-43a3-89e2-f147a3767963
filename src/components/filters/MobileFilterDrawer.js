import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import FilterPanel from './FilterPanel';

// Define the close icon component
const CloseIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const MobileFilterDrawer = ({
  isOpen,
  onClose,
  categoryId,
  onFilterChange,
  preloadedParams = []
}) => {
  const { t } = useTranslation();
  const [tempFilters, setTempFilters] = useState({});

  const resetFilters = () => {
    setTempFilters({});
    onFilterChange({});
  };

  const applyFilters = () => {
    onFilterChange(tempFilters);
    onClose();
  };

  const handleFilterChange = filters => {
    setTempFilters(filters);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          <motion.div
            className="fixed inset-y-0 right-0 w-full max-w-md bg-white shadow-xl z-50 overflow-y-auto"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            <div className="flex justify-between items-center p-4 border-b mt-12 pt-6">
              <h2 className="text-xl font-semibold">{t('filters', 'Filters')}</h2>
              <button onClick={onClose} className="text-gray-500">
                <CloseIcon />
              </button>
            </div>

            <div className="p-4 pb-24">
              <FilterPanel
                categoryId={categoryId}
                onFilterChange={handleFilterChange}
                preloadedParams={preloadedParams}
              />
            </div>

            <div className="fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200">
              <div className="flex justify-between">
                <button
                  onClick={resetFilters}
                  className="px-4 py-2 text-primary hover:text-primary-dark"
                >
                  {t('clear_all', 'Clear all')}
                </button>
                <button
                  onClick={applyFilters}
                  className="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary-dark"
                >
                  {t('apply_filters', 'Apply filters')}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MobileFilterDrawer;
