import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const SortDropdown = ({ onSortChange }) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState({
    id: 'default',
    name: t('sort_default', 'По умолчанию')
  });

  const sortOptions = [
    { id: 'default', name: t('sort_default', 'По умолчанию') },
    { id: 'price_asc', name: t('price_low_to_high', 'Цена (по возрастанию)') },
    { id: 'price_desc', name: t('price_high_to_low', 'Цена (по убыванию)') },
    { id: 'name_asc', name: t('name_a_z', 'Название (А-Я)') },
    { id: 'name_desc', name: t('name_z_a', 'Название (Я-А)') },
    { id: 'newest', name: t('newest', 'Сначала новые') }
  ];

  const handleOptionSelect = option => {
    setSelectedOption(option);
    setIsOpen(false);
    onSortChange(option.id);
  };

  return (
    <div className="relative">
      <button
        className="flex items-center justify-between w-56 px-4 py-2 bg-white border rounded-lg shadow-sm focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{selectedOption.name}</span>
        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d={isOpen ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'}
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 w-56 mt-1 bg-white border rounded-md shadow-lg">
          {sortOptions.map(option => (
            <button
              key={option.id}
              className={`block w-full text-left px-4 py-2 hover:bg-gray-100 ${
                selectedOption.id === option.id ? 'bg-gray-50 font-medium' : ''
              }`}
              onClick={() => handleOptionSelect(option)}
            >
              {option.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SortDropdown;
