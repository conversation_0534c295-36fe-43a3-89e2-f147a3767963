import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { trackFilterUse } from '../../utils/filterAnalytics';
import { useLocation } from 'react-router-dom';

// Use simple React components instead of complex SVGs to avoid formatting issues
const ChevronDownIcon = () => <span className="text-lg">▼</span>;

const ChevronUpIcon = () => <span className="text-lg">▲</span>;

const FilterPanel = ({ categoryId, onFilterChange, preloadedParams = [], initialFilters = {} }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const isProductsPage = location.pathname === '/products';
  const [loading, setLoading] = useState(true);
  const [priceRange, setPriceRange] = useState(initialFilters.price || { min: '', max: '' });
  const [vendors, setVendors] = useState([]);
  const [filteredVendors, setFilteredVendors] = useState([]);
  const [brandSearchTerm, setBrandSearchTerm] = useState('');
  const [selectedVendors, setSelectedVendors] = useState(initialFilters.vendors || []);
  const [error, setError] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [parameters, setParameters] = useState({});
  const [selectedParams, setSelectedParams] = useState(initialFilters.params || {});

  // Initialize filters only once
  useEffect(() => {
    if (isInitialized) return;

    const initializeFilters = async () => {
      try {
        setLoading(true);
        setError(null);

        // Сначала получаем все подкатегории, если есть categoryId
        let categoryIds = [];
        if (categoryId) {
          const getAllSubcategories = async parentId => {
            const { data, error } = await supabase
              .from('categories')
              .select('id')
              .or(`id.eq.${parentId},parent_id.eq.${parentId}`);

            if (error) throw error;

            const result = data.map(cat => cat.id);
            for (const cat of data) {
              if (cat.id !== parentId) {
                const childIds = await getAllSubcategories(cat.id);
                result.push(...childIds);
              }
            }
            return result;
          };

          categoryIds = await getAllSubcategories(categoryId);
          categoryIds = [...new Set(categoryIds)]; // Удаляем дубликаты
        }

        // Формируем базовый запрос
        let query = supabase
          .from('products')
          .select(
            `
            id,
            price,
            vendor,
            brand,
            attributes,
            category_id
          `
          )
          .eq('is_active', true);

        // Добавляем фильтр по категориям, если они есть
        if (categoryIds.length > 0) {
          query = query.in('category_id', categoryIds);
        }

        const { data: productsData, error: productsError } = await query;

        if (productsError) throw productsError;

        if (productsData?.length > 0) {
          // Extract price range
          const prices = productsData.map(p => p.price).filter(p => p !== null && p > 0);
          if (prices.length > 0) {
            setPriceRange(prev => ({
              ...prev,
              min: Math.floor(Math.min(...prices)),
              max: Math.ceil(Math.max(...prices))
            }));
          }

          // Extract vendors - merge both vendor and brand fields
          const vendorsFromData = productsData
            .map(p => p.vendor || p.brand)
            .filter(Boolean)
            .map(v => v.trim())
            .filter(v => v.length > 0);

          const uniqueVendors = Array.from(new Set(vendorsFromData)).sort();
          setVendors(uniqueVendors);
          setFilteredVendors(uniqueVendors);

          // Extract and process parameters from attributes
          const allParams = {};
          productsData.forEach(product => {
            if (product.attributes && typeof product.attributes === 'object') {
              Object.entries(product.attributes).forEach(([key, value]) => {
                if (value !== null && value !== undefined && key !== 'id' && key !== 'name') {
                  const normalizedName = key.trim();
                  const normalizedValue = String(value).trim();

                  if (normalizedValue) {
                    if (!allParams[normalizedName]) {
                      allParams[normalizedName] = new Set();
                    }
                    allParams[normalizedName].add(normalizedValue);
                  }
                }
              });
            }
          });

          // Convert Sets to sorted arrays and filter out empty parameters
          const processedParams = Object.entries(allParams)
            .filter(([_, values]) => values.size > 0)
            .reduce((acc, [name, values]) => {
              acc[name] = Array.from(values)
                .filter(Boolean)
                .sort((a, b) => {
                  // Сначала пробуем сортировать как числа
                  const numA = parseFloat(a);
                  const numB = parseFloat(b);
                  if (!isNaN(numA) && !isNaN(numB)) {
                    return numA - numB;
                  }
                  // Если не получилось, сортируем как строки
                  return a.localeCompare(b);
                });
              return acc;
            }, {});

          setParameters(processedParams);
        }
      } catch (err) {
        console.error('Error initializing filters:', err);
        setError(err.message);
      } finally {
        setLoading(false);
        setIsInitialized(true);
      }
    };

    initializeFilters();
  }, [isInitialized, categoryId]);

  // Handle filter changes with debouncing
  const triggerFilterChange = useCallback(
    (vendors, prices, params) => {
      onFilterChange({
        price: {
          min: Number(prices.min) || undefined,
          max: Number(prices.max) || undefined
        },
        vendors: vendors.length > 0 ? vendors : undefined,
        params: Object.keys(params).length > 0 ? params : undefined
      });
    },
    [onFilterChange]
  );

  // Effect to handle filter changes - with debouncing
  useEffect(() => {
    if (!isInitialized) return;

    const timer = setTimeout(() => {
      triggerFilterChange(selectedVendors, priceRange, selectedParams);
    }, 300);

    return () => clearTimeout(timer);
  }, [selectedVendors, priceRange, selectedParams, triggerFilterChange, isInitialized]);

  // Update filtered vendors when vendors or search term changes
  useEffect(() => {
    if (!brandSearchTerm) {
      setFilteredVendors(vendors);
      return;
    }

    const searchTerm = brandSearchTerm.toLowerCase();
    const filtered = vendors.filter(vendor => vendor.toLowerCase().includes(searchTerm));
    setFilteredVendors(filtered);
  }, [vendors, brandSearchTerm]);

  // Handle vendor selection changes
  const handleVendorChange = vendor => {
    setSelectedVendors(prev =>
      prev.includes(vendor) ? prev.filter(v => v !== vendor) : [...prev, vendor]
    );
  };

  // Handle parameter selection changes
  const handleParamChange = (paramName, value) => {
    setSelectedParams(prev => {
      const newParams = { ...prev };
      if (!newParams[paramName]) {
        newParams[paramName] = [value];
      } else if (newParams[paramName].includes(value)) {
        newParams[paramName] = newParams[paramName].filter(v => v !== value);
        if (newParams[paramName].length === 0) {
          delete newParams[paramName];
        }
      } else {
        newParams[paramName] = [...newParams[paramName], value];
      }
      return newParams;
    });
  };

  // Handle price range changes
  const handlePriceChange = (type, value) => {
    setPriceRange(prev => ({ ...prev, [type]: value }));
  };

  // Reset all filters
  const resetFilters = () => {
    setSelectedVendors([]);
    setPriceRange({ min: '', max: '' });
    setBrandSearchTerm('');
    setSelectedParams({});
  };

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-4 text-red-500">
        {t('error_loading_filters', 'Ошибка загрузки фильтров')}: {error}
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="h-8 bg-gray-200 rounded w-full mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-full mb-4"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="mb-8 flex justify-between items-center border-b pb-4">
        <h3 className="font-semibold text-xl text-gray-800">{t('filters', 'Фильтры')}</h3>
        {(selectedVendors.length > 0 ||
          priceRange.min ||
          priceRange.max ||
          (!isProductsPage && Object.keys(selectedParams).length > 0)) && (
          <button
            onClick={resetFilters}
            className="text-sm text-primary hover:text-primary-dark transition-colors duration-200 flex items-center"
          >
            <span className="mr-1">×</span>
            {t('reset_filters', 'Сбросить')}
          </button>
        )}
      </div>

      {/* Active filters summary */}
      {(selectedVendors.length > 0 ||
        priceRange.min ||
        priceRange.max ||
        (!isProductsPage && Object.keys(selectedParams).length > 0)) && (
        <div className="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-100">
          <h4 className="font-medium text-sm text-gray-600 mb-3">
            {t('active_filters', 'Активные фильтры')}:
          </h4>
          <div className="flex flex-wrap gap-2">
            {/* Price filter tags */}
            {(priceRange.min || priceRange.max) && (
              <div className="bg-blue-50 border border-blue-100 px-3 py-1.5 rounded-full text-sm flex items-center shadow-sm">
                <span className="text-blue-800 font-medium">
                  {t('price', 'Цена')}:{' '}
                  {priceRange.min && priceRange.max
                    ? `${priceRange.min} - ${priceRange.max}`
                    : priceRange.min
                      ? `${t('from', 'от')} ${priceRange.min}`
                      : `${t('to', 'до')} ${priceRange.max}`}
                </span>
                <button
                  onClick={() => setPriceRange({ min: '', max: '' })}
                  className="ml-2 text-blue-400 hover:text-blue-600 focus:outline-none"
                >
                  ×
                </button>
              </div>
            )}

            {/* Vendor filter tags */}
            {selectedVendors.map(vendor => (
              <div
                key={vendor}
                className="bg-green-50 border border-green-100 px-3 py-1.5 rounded-full text-sm flex items-center shadow-sm"
              >
                <span className="text-green-800 font-medium">
                  {t('vendor', 'Производитель')}: {vendor}
                </span>
                <button
                  onClick={() => handleVendorChange(vendor)}
                  className="ml-2 text-green-400 hover:text-green-600 focus:outline-none"
                >
                  ×
                </button>
              </div>
            ))}

            {/* Parameter filter tags - only show if not on products page */}
            {!isProductsPage &&
              Object.entries(selectedParams).map(([paramName, values]) =>
                values.map(value => (
                  <div
                    key={`${paramName}-${value}`}
                    className="bg-purple-50 border border-purple-100 px-3 py-1.5 rounded-full text-sm flex items-center shadow-sm"
                  >
                    <span className="text-purple-800 font-medium">
                      {paramName}: {value}
                    </span>
                    <button
                      onClick={() => handleParamChange(paramName, value)}
                      className="ml-2 text-purple-400 hover:text-purple-600 focus:outline-none"
                    >
                      ×
                    </button>
                  </div>
                ))
              )}
          </div>
        </div>
      )}

      {/* Price Range Filter */}
      <div className="mb-8">
        <h4 className="font-medium text-gray-700 mb-4">{t('price_range', 'Ценовой диапазон')}</h4>
        <div className="flex gap-4">
          <div className="flex-1">
            <input
              type="number"
              value={priceRange.min}
              onChange={e => handlePriceChange('min', e.target.value)}
              placeholder={t('min_price', 'Мин. цена')}
              className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors duration-200"
            />
          </div>
          <div className="flex-1">
            <input
              type="number"
              value={priceRange.max}
              onChange={e => handlePriceChange('max', e.target.value)}
              placeholder={t('max_price', 'Макс. цена')}
              className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors duration-200"
            />
          </div>
        </div>
      </div>

      {/* Vendor Filter */}
      {vendors.length > 0 && (
        <div className="mb-8">
          <h4 className="font-medium text-gray-700 mb-4">{t('vendors', 'Производители')}</h4>
          <input
            type="text"
            value={brandSearchTerm}
            onChange={e => setBrandSearchTerm(e.target.value)}
            placeholder={t('search_vendors', 'Поиск производителей')}
            className="w-full px-4 py-2.5 border border-gray-200 rounded-lg mb-4 focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors duration-200"
          />
          <div className="max-h-48 overflow-y-auto pr-2 space-y-1">
            {filteredVendors.map(vendor => (
              <label
                key={vendor}
                className="flex items-center py-2 px-3 hover:bg-gray-50 rounded-md cursor-pointer transition-colors duration-150 group"
              >
                <div className="relative flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedVendors.includes(vendor)}
                    onChange={() => handleVendorChange(vendor)}
                    className="w-5 h-5 border-2 border-gray-300 rounded text-primary focus:ring-primary focus:ring-2 focus:ring-offset-0 focus:outline-none transition-colors duration-200"
                  />
                  <span className="ml-3 text-[15px] text-gray-700 group-hover:text-gray-900 transition-colors duration-200">
                    {vendor}
                  </span>
                </div>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Parameters Filter - only show if not on products page */}
      {!isProductsPage &&
        Object.entries(parameters).map(([paramName, values]) => (
          <div key={paramName} className="mb-8">
            <h4 className="font-medium text-gray-700 mb-4">{paramName}</h4>
            <div className="max-h-48 overflow-y-auto pr-2 space-y-1">
              {values.map(value => (
                <label
                  key={value}
                  className="flex items-center py-2 px-3 hover:bg-gray-50 rounded-md cursor-pointer transition-colors duration-150 group"
                >
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedParams[paramName]?.includes(value) || false}
                      onChange={() => handleParamChange(paramName, value)}
                      className="w-5 h-5 border-2 border-gray-300 rounded text-primary focus:ring-primary focus:ring-2 focus:ring-offset-0 focus:outline-none transition-colors duration-200"
                    />
                    <span className="ml-3 text-[15px] text-gray-700 group-hover:text-gray-900 transition-colors duration-200">
                      {value}
                    </span>
                  </div>
                </label>
              ))}
            </div>
          </div>
        ))}
    </div>
  );
};

export default FilterPanel;
