import React from 'react';

const LoadingSpinner = ({ size = 'h-12 w-12', fullScreen = true, className = '' }) => {
  const spinnerClasses = `animate-spin rounded-full border-t-2 border-b-2 border-blue-500 ${size} ${className}`;

  if (fullScreen) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className={spinnerClasses}></div>
      </div>
    );
  }

  return (
    <div className={`flex justify-center items-center ${className}`}>
      <div
        className={
          spinnerClasses.replace(size, '') /* Remove default size if custom one is in className */
        }
      ></div>
    </div>
  );
};

export default LoadingSpinner;
