import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const PrivateRoute = ({ children, adminOnly = false }) => {
  const { user, isAdmin, loading } = useAuth();
  const location = useLocation();
  const [isReady, setIsReady] = useState(false);

  // Wait for auth to complete loading
  useEffect(() => {
    if (!loading) {
      setIsReady(true);
    }
  }, [loading]);

  // In development mode, bypass admin check
  if (process.env.NODE_ENV === 'development') {
    return children;
  }

  // Show loading indicator while authentication is in progress
  if (loading || !isReady) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // For production: require both authentication and admin status if adminOnly is true
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (adminOnly && !isAdmin) {
    return <Navigate to="/" replace />;
  }

  return children;
};

export default PrivateRoute;
