import React, { useState } from 'react';

/**
 * A component that renders an image with a fallback if the image fails to load
 */
const ImageWithFallback = ({
  src,
  alt,
  fallbackSrc = '/images/placeholder.png',
  className = '',
  style = {},
  ...props
}) => {
  const [error, setError] = useState(false);

  const handleError = () => {
    setError(true);
  };

  return (
    <img
      src={error ? fallbackSrc : src}
      alt={alt}
      onError={handleError}
      className={className}
      style={style}
      {...props}
    />
  );
};

export default ImageWithFallback;
