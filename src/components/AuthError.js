import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const AuthError = ({ message, onRetry }) => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="max-w-md w-full px-6 py-8 bg-white shadow-lg rounded-lg text-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          {t('auth_error', 'Ошибка аутентификации')}
        </h2>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="space-x-4">
          {onRetry && (
            <button
              onClick={onRetry}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              {t('try_again', 'Попробовать снова')}
            </button>
          )}
          <Link
            to="/login"
            className="inline-block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            {t('back_to_login', 'Вернуться к входу')}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AuthError;
