/* eslint-disable prettier/prettier */
import React from 'react'; // Removed useState, useEffect
import { Link as RouterLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Heart } from 'react-feather';
// import { supabase } from '../supabaseClient'; // Removed supabase import
import { useCart } from '../context/CartContext';
import { useCompare } from '../context/CompareContext';
import { useWishlist } from '../context/WishlistContext';
import { Text } from './ui/Typography';
import Button from './Button';

/**
 * Компонент карточки товара для отображения в списках, поиске и т.д.
 *
 * @param {Object} props
 * @param {Object} props.product - Объект товара
 * @param {Array} props.params - Массив параметров товара (опционально)
 */
const ProductCard = ({ product }) => {
  const { t } = useTranslation();
  const { addToCart } = useCart();
  const { addToCompare, removeFromCompare, isInCompare } = useCompare();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  // const [params, setParams] = useState([]); // Temporarily remove params logic
  // const [_loading, setLoading] = useState(false); // Temporarily remove loading state for params

  // Add a guard clause to prevent rendering if product or product.id is undefined
  if (!product || !product.id) {
    // Optionally, return a placeholder or null
    //
    return null; // Or a skeleton loader
  }

  // Проверяем, добавлен ли товар в избранное
  const productInWishlist = isInWishlist(product.id);
  const productInCompare = isInCompare(product.id);

  // useEffect(() => { // Temporarily remove params fetching
  //   // Загружаем характеристики товара из фида (product_params)
  //   const fetchParams = async () => {
  //     if (!product?.id) return;
  //
  //     setLoading(true);
  //     try {
  //       // Получаем ключевые характеристики (максимум 3-4 для карточки)
  //       const { data, error } = await supabase
  //         .from('product_params')
  //         .select('*')
  //         .eq('product_id', product.id)
  //         // .eq('is_key', true) // Выбираем только ключевые характеристики - temporarily removed
  //         .order('name')
  //         .limit(4);
  //
  //       if (!error && data) {
  //         setParams(data);
  //       }
  //     } catch (err) {
  //       console.error('Ошибка при загрузке параметров товара:', err);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //
  //   fetchParams();
  // }, [product?.id]);

  const handleAddToCart = e => {
    e.preventDefault();
    e.stopPropagation();
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    });
  };

  const handleAddToCompare = e => {
    e.preventDefault();
    e.stopPropagation();

    if (productInCompare) {
      // Если товар уже в сравнении - удаляем его
      removeFromCompare(product.id);
    } else {
      // Если товара нет в сравнении - добавляем его
      addToCompare({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image
      });
    }
  };

  const handleWishlistToggle = e => {
    e.preventDefault();
    e.stopPropagation();
    if (productInWishlist) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  return (
    <RouterLink
      to={`/product/${product.id}`}
      className="group bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all border border-gray-100 flex flex-col h-full"
    >
      {/* Изображение товара */}
      <div className="relative pt-[75%] overflow-hidden">
        <img
          src={product.image || '/placeholder.png'}
          alt={product.name}
          className="absolute inset-0 w-full h-full object-contain p-2 transition-transform group-hover:scale-105"
          onError={e => {
            e.target.src = '/placeholder.png';
          }}
        />

        {/* Кнопки действий (сравнение, избранное) */}
        <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={handleWishlistToggle}
            className={`p-2 rounded-full ${productInWishlist ? 'bg-primary/10 text-primary' : 'bg-white hover:bg-primary/5 hover:text-primary text-gray-500'} transition-colors shadow-sm`}
            aria-label={
              productInWishlist
                ? t('remove_from_wishlist', 'Убрать из избранного')
                : t('add_to_wishlist', 'В избранное')
            }
          >
            <Heart size={18} fill={productInWishlist ? 'currentColor' : 'none'} />
          </button>

          <button
            onClick={handleAddToCompare}
            className={`p-2 rounded-full ${productInCompare ? 'bg-blue-100 text-blue-600' : 'bg-white hover:bg-blue-50 hover:text-blue-500 text-gray-500'} transition-colors shadow-sm`}
            aria-label={
              productInCompare ? t('in_compare', 'В сравнении') : t('compare', 'Сравнить')
            }
          >
            <svg
              width="18"
              height="18"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              viewBox="0 0 24 24"
            >
              <rect x="2" y="9" width="7" height="13" rx="2" />
              <rect x="15" y="4" width="7" height="18" rx="2" />
            </svg>
          </button>
        </div>
      </div>

      <div className="p-4 flex flex-col flex-grow">
        {/* Название товара */}
        <Text
          as="h3"
          className="font-medium text-lg line-clamp-2 mb-2 group-hover:text-primary transition-colors"
        >
          {product.name}
        </Text>

        {/* Категория */}
        {product.categories && (
          <Text variant="small" className="mb-2">
            {product.categories.name || product.category_name}
          </Text>
        )}

        {/* Характеристики из фида (ключевые) - Temporarily removed */}
        {/* {params.length > 0 && (
          <div className="mt-2 mb-3 bg-gray-50 rounded p-2 text-sm">
            <ul className="divide-y divide-gray-100">
              {params.map(param => (
                <li key={param.id} className="py-1 flex justify-between">
                  <Text variant="small" className="text-gray-600">
                    {param.name}:
                  </Text>
                  <Text variant="small" className="font-medium text-gray-800 ml-2">
                    {param.value}
                  </Text>
                </li>
              ))}
            </ul>
          </div>
        )} */}

        {/* Растягивающий элемент для выравнивания цены внизу */}
        <div className="flex-grow"></div>

        <div className="mt-3 flex justify-between items-center">
          {/* Цена */}
          <div>
            {product.old_price && product.old_price > product.price ? (
              <>
                <Text className="text-lg font-bold text-primary">
                  {' '}
                  {/* Changed from text-red-600 */}
                  {new Intl.NumberFormat('uk-UA', {
                    style: 'currency',
                    currency: 'UAH',
                    minimumFractionDigits: 0
                  }).format(product.price)}
                </Text>
                <Text className="ml-2 text-sm text-gray-500 line-through">
                  {new Intl.NumberFormat('uk-UA', {
                    style: 'currency',
                    currency: 'UAH',
                    minimumFractionDigits: 0
                  }).format(product.old_price)}
                </Text>
              </>
            ) : (
              <Text className="text-lg font-bold text-primary">
                {new Intl.NumberFormat('uk-UA', {
                  style: 'currency',
                  currency: 'UAH',
                  minimumFractionDigits: 0
                }).format(product.price)}
              </Text>
            )}
          </div>

          {/* Кнопка Добавить в корзину */}
          <Button variant="primary" size="sm" onClick={handleAddToCart}>
            {t('add_to_cart_short', 'В корзину')}
          </Button>
        </div>
      </div>
    </RouterLink>
  );
};

export default ProductCard;
