import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRightIcon } from '@heroicons/react/20/solid';

const PageHeader = ({ title, description = null, breadcrumbs = [], actions = null }) => {
  return (
    <div className="mb-8">
      {/* Breadcrumbs */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <nav className="flex mb-4" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            {breadcrumbs.map((item, index) => {
              const isLast = index === breadcrumbs.length - 1;

              return (
                <li key={item.href} className="inline-flex items-center">
                  {index > 0 && <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-1" />}
                  {isLast ? (
                    <span className="text-sm font-medium text-gray-500">{item.name}</span>
                  ) : (
                    <Link
                      to={item.href}
                      className="text-sm font-medium text-blue-600 hover:text-blue-700"
                    >
                      {item.name}
                    </Link>
                  )}
                </li>
              );
            })}
          </ol>
        </nav>
      )}

      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {description && <p className="mt-1 text-gray-500">{description}</p>}
        </div>

        {actions && <div className="mt-4 md:mt-0 flex items-center space-x-3">{actions}</div>}
      </div>
    </div>
  );
};

export default PageHeader;
