import React, { useState, useEffect, _useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useKeenSlider } from 'keen-slider/react';
import 'keen-slider/keen-slider.min.css';
import { ShoppingCart, Heart, ChevronLeft, ChevronRight } from 'react-feather';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';

const RecommendationSlider = ({
  title,
  products = [],
  slidesPerViewProp = 5,
  withActions = true
}) => {
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const [loadedImages, setLoadedImages] = useState({});
  const [sliderKey, setSliderKey] = useState(Date.now());

  const handleImageLoad = productId => {
    setLoadedImages(prev => ({
      ...prev,
      [productId]: true
    }));
  };

  const [sliderRef, instanceRef] = useKeenSlider(
    {
      loop: false,
      slides: {
        perView: slidesPerViewProp,
        spacing: 16
      },
      breakpoints: {
        '(max-width: 1536px)': {
          slides: { perView: Math.min(slidesPerViewProp, 5), spacing: 16 }
        },
        '(max-width: 1280px)': {
          slides: { perView: Math.min(slidesPerViewProp, 4), spacing: 16 }
        },
        '(max-width: 1024px)': {
          slides: { perView: Math.min(slidesPerViewProp, 3), spacing: 12 }
        },
        '(max-width: 768px)': {
          slides: { perView: Math.min(slidesPerViewProp, 2), spacing: 12 }
        },
        '(max-width: 640px)': {
          slides: { perView: 1.5, spacing: 12 }
        }
      }
    },
    [
      slider => {
        let rafId;
        const observer = new ResizeObserver(() => {
          if (rafId) {
            cancelAnimationFrame(rafId);
          }
          rafId = requestAnimationFrame(() => {
            slider.update();
          });
        });
        slider.on('created', () => {
          observer.observe(slider.container);
        });
        slider.on('destroyed', () => {
          if (rafId) {
            cancelAnimationFrame(rafId);
          }
          observer.unobserve(slider.container);
        });
      }
    ]
  );

  useEffect(() => {
    setSliderKey(Date.now());
  }, [products, slidesPerViewProp]);

  if (!products || products.length === 0)
    return (
      <div className="bg-white rounded-xl p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">{title}</h2>
        <p className="text-gray-500">Нет доступных товаров.</p>
      </div>
    );

  return (
    <div className="bg-white rounded-xl p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        {products.length > slidesPerViewProp && (
          <div className="flex gap-2">
            <button
              onClick={e => {
                e.stopPropagation();
                instanceRef.current?.prev();
              }}
              disabled={!instanceRef.current || instanceRef.current.track.details.abs === 0}
              className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 hover:border-primary hover:text-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Previous"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={e => {
                e.stopPropagation();
                instanceRef.current?.next();
              }}
              disabled={
                !instanceRef.current ||
                instanceRef.current.track.details.abs ===
                  instanceRef.current.track.details.slides.length - 1
              }
              className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 hover:border-primary hover:text-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Next"
            >
              <ChevronRight size={20} />
            </button>
          </div>
        )}
      </div>

      <div key={sliderKey} ref={sliderRef} className="keen-slider">
        {products.map(product => (
          <div key={product.id} className="keen-slider__slide flex">
            <div className="bg-white h-full flex flex-col w-full p-1 group">
              <Link
                to={`/product/${product.id}`}
                className="block relative aspect-w-1 aspect-h-1 bg-gray-100 rounded-lg mb-3 overflow-hidden"
              >
                {!loadedImages[product.id] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
                    <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
                <img
                  src={product.image || '/placeholder.png'}
                  alt={product.name}
                  className={`w-full h-full object-contain mix-blend-multiply transition-all duration-300 p-2 group-hover:scale-105 ${
                    loadedImages[product.id] ? 'opacity-100' : 'opacity-0'
                  }`}
                  onLoad={() => handleImageLoad(product.id)}
                  onError={e => {
                    e.target.src = '/placeholder.png';
                    handleImageLoad(product.id);
                  }}
                />
                {product.discount > 0 && (
                  <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-semibold px-2 py-1 rounded-md shadow">
                    -{product.discount}%
                  </div>
                )}
              </Link>

              <div className="flex-1 flex flex-col justify-between">
                <div>
                  <Link to={`/product/${product.id}`} className="block">
                    <h3 className="text-sm font-medium text-gray-800 line-clamp-2 hover:text-primary transition-colors h-10 mb-1">
                      {product.name}
                    </h3>
                  </Link>
                </div>

                <div className="mt-1">
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex-1">
                      <div className="text-base font-bold text-gray-900">
                        {new Intl.NumberFormat('uk-UA', {
                          style: 'currency',
                          currency: 'UAH',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0
                        }).format(product.price)}
                      </div>
                      {product.old_price && product.old_price > product.price && (
                        <div className="text-xs text-gray-500 line-through">
                          {new Intl.NumberFormat('uk-UA', {
                            style: 'currency',
                            currency: 'UAH',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          }).format(product.old_price)}
                        </div>
                      )}
                    </div>

                    {withActions && (
                      <div className="flex gap-1">
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          whileHover={{ scale: 1.1 }}
                          onClick={e => {
                            e.preventDefault();
                            e.stopPropagation();
                            const inWishlist = isInWishlist(product.id);
                            if (inWishlist) {
                              removeFromWishlist(product.id);
                            } else {
                              addToWishlist(product);
                            }
                          }}
                          className="p-1.5 rounded-full hover:bg-gray-100 transition-colors"
                          title={
                            isInWishlist(product.id) ? 'Убрать из желаемого' : 'Добавить в желаемое'
                          }
                        >
                          <Heart
                            size={18}
                            className={`${isInWishlist(product.id) ? 'text-red-500 fill-current' : 'text-gray-400 hover:text-red-400'} transition-colors`}
                          />
                        </motion.button>
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          whileHover={{ scale: 1.1 }}
                          onClick={e => {
                            e.preventDefault();
                            e.stopPropagation();
                            addToCart({
                              id: product.id,
                              name: product.name,
                              price: product.price,
                              image: product.image,
                              quantity: 1
                            });
                          }}
                          className="p-1.5 rounded-full bg-primary-light text-primary hover:bg-primary hover:text-white transition-colors"
                          title="Добавить в корзину"
                        >
                          <ShoppingCart size={18} />
                        </motion.button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendationSlider;
