import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '../../supabaseClient';
import { TABLES } from '../../config/supabase';
import { Link } from 'react-router-dom';
import { FaPlus } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useSearchParams } from 'react-router-dom';
import ProductFilters from './ProductFilters';
import ProductTable from './ProductTable';
import DeleteConfirmationModal from './DeleteConfirmationModal'; // Added missing import

const ProductManagement = () => {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [categories, setCategories] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState({
    category: searchParams.get('category') || '',
    search: searchParams.get('search') || '',
    status: searchParams.get('status') || 'all'
  });

  // Define the pagination state
  const [pagination, setPagination] = useState({
    page: parseInt(searchParams.get('page') || '1'),
    pageSize: parseInt(searchParams.get('pageSize') || '10'),
    total: 0
  });

  // Define fetchCategories with useCallback
  const fetchCategories = useCallback(async () => {
    try {
      const { data, error } = await supabase.from(TABLES.CATEGORIES).select('*').order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  // Define updateSearchParams with useCallback
  const updateSearchParams = useCallback(() => {
    const params = new URLSearchParams();

    if (filters.category) params.set('category', filters.category);
    if (filters.search) params.set('search', filters.search);
    if (filters.status !== 'all') params.set('status', filters.status);

    params.set('page', pagination.page.toString());
    params.set('pageSize', pagination.pageSize.toString());

    setSearchParams(params);
  }, [filters, pagination.page, pagination.pageSize, setSearchParams]);

  // Define fetchProducts with useCallback to use it in the dependency array
  const fetchProducts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      let query = supabase.from(TABLES.PRODUCTS).select('*, categories(name)', { count: 'exact' });

      // Apply category filter
      if (filters.category) {
        query = query.eq('category_id', filters.category);
      }

      // Apply search filter
      if (filters.search) {
        query = query.ilike('name', `%${filters.search}%`);
      }

      // Apply status filter
      if (filters.status === 'in-stock') {
        query = query.gt('stock', 0);
      } else if (filters.status === 'out-of-stock') {
        query = query.eq('stock', 0);
      }

      // Add pagination
      const { data, error, count } = await query
        .range(
          (pagination.page - 1) * pagination.pageSize,
          pagination.page * pagination.pageSize - 1
        )
        .order('created_at', { ascending: false });

      if (error) throw error;

      setProducts(data || []);
      setPagination(prev => ({ ...prev, total: count || 0 }));

      // Update URL with filters and pagination
      updateSearchParams();
    } catch (err) {
      setError(err.message);
      toast.error(`Error loading products: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [filters, pagination.page, pagination.pageSize, updateSearchParams]);

  // Fetch products and categories on mount
  useEffect(() => {
    fetchCategories();
    fetchProducts();
  }, [fetchCategories, fetchProducts]);

  const handleFilterChange = newFilters => {
    // Reset to page 1 when filters change
    setPagination(prev => ({ ...prev, page: 1 }));
    setFilters(newFilters);
  };

  const handlePageChange = newPage => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handlePageSizeChange = newSize => {
    setPagination(prev => ({ ...prev, pageSize: newSize, page: 1 }));
  };

  const handleDeleteClick = product => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!productToDelete) return;

    try {
      const { error } = await supabase.from(TABLES.PRODUCTS).delete().eq('id', productToDelete.id);

      if (error) throw error;

      setProducts(products.filter(p => p.id !== productToDelete.id));
      toast.success('Product deleted successfully');
    } catch (error) {
      toast.error(`Error deleting product: ${error.message}`);
    } finally {
      setShowDeleteModal(false);
      setProductToDelete(null);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setProductToDelete(null);
  };

  return (
    <div className="container mx-auto px-6 py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Product Management</h1>
        <Link
          to="/admin/products/new"
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center"
        >
          <FaPlus className="mr-2" /> Add New Product
        </Link>
      </div>

      <ProductFilters
        filters={filters}
        categories={categories}
        onFilterChange={handleFilterChange}
      />

      <ProductTable
        products={products}
        isLoading={isLoading}
        error={error}
        pagination={pagination}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onDeleteClick={handleDeleteClick}
      />

      {showDeleteModal && (
        <DeleteConfirmationModal
          isOpen={showDeleteModal}
          onClose={cancelDelete}
          onConfirm={confirmDelete}
          title="Delete Product"
          message={`Are you sure you want to delete the product "${productToDelete?.name}"? This action cannot be undone.`}
        />
      )}
    </div>
  );
};

export default ProductManagement;
