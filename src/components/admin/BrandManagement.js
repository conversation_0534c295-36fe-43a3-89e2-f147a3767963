import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';

const BrandManagement = () => {
  const [brands, setBrands] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase.from('brands').select('*').order('position');

      if (error) throw error;

      setBrands(data || []);
    } catch (error) {
      console.error('Error fetching brands:', error);
      toast.error('Ошибка при загрузке брендов: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async brand => {
    try {
      const { error } = await supabase
        .from('brands')
        .update({ active: !brand.active })
        .eq('id', brand.id);

      if (error) throw error;

      toast.success(`Бренд ${brand.active ? 'деактивирован' : 'активирован'}`);
      fetchBrands();
    } catch (error) {
      console.error('Error toggling brand status:', error);
      toast.error('Ошибка при изменении статуса бренда: ' + error.message);
    }
  };

  const handleDelete = async brandId => {
    if (!window.confirm('Вы уверены, что хотите удалить этот бренд?')) return;

    try {
      const { error } = await supabase.from('brands').delete().eq('id', brandId);

      if (error) throw error;

      toast.success('Бренд успешно удален');
      fetchBrands();
    } catch (error) {
      console.error('Error deleting brand:', error);
      toast.error('Ошибка при удалении бренда: ' + error.message);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="font-semibold text-xl">Бренды</h2>
        <Link
          to="/admin/brands/create"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          <div className="flex items-center gap-2">
            <FaPlus />
            Добавить бренд
          </div>
        </Link>
      </div>

      {brands.length === 0 ? (
        <div className="p-8 text-center text-gray-500">Бренды отсутствуют</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Бренд
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Сайт
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Позиция
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Статус
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Действия
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {brands.map(brand => (
                <tr key={brand.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {brand.logo_url ? (
                        <img
                          src={brand.logo_url}
                          alt={brand.name}
                          className="h-12 w-auto object-contain mr-3"
                          onError={e => {
                            e.target.onerror = null;
                            e.target.src = 'https://placehold.co/150x150/EEE/31343C?text=Logo';
                          }}
                        />
                      ) : (
                        <div className="h-12 w-12 bg-gray-200 mr-3 flex items-center justify-center text-gray-500">
                          No Logo
                        </div>
                      )}
                      <div className="font-medium text-gray-900">{brand.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {brand.website_url ? (
                      <a
                        href={brand.website_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-900"
                      >
                        {brand.website_url}
                      </a>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">{brand.position}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        brand.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {brand.active ? 'Активен' : 'Неактивен'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => handleToggleStatus(brand)}
                        className={`${
                          brand.active ? 'text-gray-600' : 'text-green-600'
                        } hover:${brand.active ? 'text-gray-900' : 'text-green-900'}`}
                        title={brand.active ? 'Деактивировать' : 'Активировать'}
                      >
                        {brand.active ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
                      </button>
                      <Link
                        to={`/admin/brands/edit/${brand.id}`}
                        className="text-blue-600 hover:text-blue-900"
                        title="Редактировать"
                      >
                        <FaEdit size={18} />
                      </Link>
                      <button
                        onClick={() => handleDelete(brand.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Удалить"
                      >
                        <FaTrash size={18} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default BrandManagement;
