import React, { useState, useEffect, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';

const AdminMenuSidebar = ({ activePath = '/' }) => {
  const { _t } = useTranslation();
  const location = useLocation();
  const [pendingCount, setPendingCount] = useState(0);
  const [pendingReviews, setPendingReviews] = useState(0);
  const [collapsed, setCollapsed] = useState(true);

  const fetchPendingCount = useCallback(async () => {
    try {
      // Fetch pending products count
      const { count: productsCount, error: productsError } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('moderation_status', 'pending_approval');

      if (productsError) {
        console.error('Error fetching pending products count:', productsError);
      } else {
        setPendingCount(productsCount || 0);
      }

      // Fetch pending reviews count
      const { count: reviewsCount, error: reviewsError } = await supabase
        .from('reviews')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      if (reviewsError) {
        console.error('Error fetching pending reviews count:', reviewsError);
      } else {
        setPendingReviews(reviewsCount || 0);
      }
    } catch (err) {
      console.error('Error in fetchPendingCount:', err);
    }
  }, []);

  useEffect(() => {
    fetchPendingCount();
    const interval = setInterval(fetchPendingCount, 60000); // Update every minute
    return () => clearInterval(interval);
  }, [fetchPendingCount]);

  const _currentPath = location.pathname;

  const isActive = path => {
    if (path === '/admin' && location.pathname === '/admin') {
      return true;
    }
    return location.pathname.startsWith(path) && path !== '/admin';
  };

  const menuItems = [
    {
      path: '/admin',
      icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
      text: 'Dashboard'
    },
    {
      path: '/admin/products',
      icon: 'M20 7l-8-4-8 4m16 0v10a2 2 0 01-2 2H6a2 2 0 01-2-2V7m16 0H6',
      text: 'Products'
    },
    {
      path: '/admin/categories',
      icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z',
      text: 'Categories'
    },
    {
      path: '/admin/brands',
      icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
      text: 'Brands'
    },
    {
      path: '/admin/orders',
      icon: 'M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z',
      text: 'Orders'
    },
    {
      path: '/admin/banners',
      icon: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
      text: 'Banners'
    },
    {
      path: '/admin/feeds',
      icon: 'M6 5c7.18 0 13 5.82 13 13M6 11a7 7 0 017 7M6 17h.01',
      text: 'Feeds'
    },
    {
      path: '/admin/moderation',
      icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01',
      text: 'Moderation',
      badge: pendingCount > 0 ? pendingCount : null
    },
    {
      path: '/admin/reviews',
      icon: 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z',
      text: 'Reviews',
      badge: pendingReviews > 0 ? pendingReviews : null
    },
    {
      path: '/admin/inventory',
      icon: 'M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4',
      text: 'Inventory',
      inactive: true
    },
    {
      path: '/admin/users',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z',
      text: 'Users'
    },
    {
      path: '/admin/settings',
      icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
      text: 'Settings'
    },
    {
      path: '/admin/make-admin',
      icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      text: 'Make Admin'
    },
    {
      path: '/admin/system-diagnostics',
      icon: 'M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z',
      text: 'System Diagnostics'
    }
  ];

  return (
    <div
      className={`bg-white rounded-lg shadow overflow-hidden transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}
    >
      <div className="p-4 bg-gray-50 border-b">
        {collapsed ? (
          <div className="flex justify-center">
            <button
              onClick={() => setCollapsed(false)}
              className="p-1 rounded-full hover:bg-gray-200"
              title="Развернуть меню"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        ) : (
          <div className="flex justify-between items-center">
            <h2 className="font-bold text-lg">Меню администратора</h2>
            <button
              onClick={() => setCollapsed(true)}
              className="p-1 rounded-full hover:bg-gray-200"
              title="Свернуть меню"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 19l-7-7 7-7"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      <nav className="p-2">
        <ul className="space-y-1">
          {menuItems.map(item => (
            <li key={item.path}>
              {item.inactive ? (
                <div
                  className={`flex items-center gap-3 p-2 rounded-lg transition-colors 
                    text-gray-400 cursor-not-allowed ${collapsed ? 'justify-center' : ''}`}
                  title={collapsed ? `${item.text} (Temporarily disabled)` : 'Temporarily disabled'}
                >
                  <span className="flex-shrink-0 relative">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={item.icon}
                      />
                    </svg>
                  </span>

                  {!collapsed && (
                    <div className="flex-grow flex items-center justify-between">
                      <span>{item.text}</span>
                    </div>
                  )}
                </div>
              ) : (
                <Link
                  to={item.path}
                  className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                    isActive(item.path) ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                  } ${collapsed ? 'justify-center' : ''}`}
                  title={collapsed ? item.text : ''}
                >
                  <span className="flex-shrink-0 relative">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={item.icon}
                      />
                    </svg>

                    {/* Badge показываем только в свернутом состоянии на иконке */}
                    {item.badge && collapsed && (
                      <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                        {item.badge > 9 ? '9+' : item.badge}
                      </span>
                    )}
                  </span>

                  {!collapsed && (
                    <div className="flex-grow flex items-center justify-between">
                      <span>{item.text}</span>
                      {/* Badge показываем рядом с текстом в развернутом состоянии */}
                      {item.badge && (
                        <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </div>
                  )}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default AdminMenuSidebar;
