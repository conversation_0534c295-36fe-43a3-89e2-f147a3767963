import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const AdminSidebar = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  // Проверка активного пункта меню
  const isActive = path => {
    return location.pathname.includes(path);
  };

  return (
    <div className={`admin-sidebar transition-all duration-300 ${collapsed ? 'collapsed' : ''}`}>
      <div className="admin-sidebar-header">
        <h3 className="admin-sidebar-title">{!collapsed && 'Меню администратора'}</h3>
        <button
          className="collapse-button"
          onClick={() => setCollapsed(!collapsed)}
          aria-label={collapsed ? 'Развернуть меню' : 'Свернуть меню'}
        >
          <i className={`bi ${collapsed ? 'bi-chevron-right' : 'bi-chevron-left'}`}></i>
        </button>
      </div>

      <ul className="admin-menu">
        <li className={isActive('/admin/dashboard') ? 'active' : ''}>
          <Link to="/admin/dashboard">
            <i className="bi bi-house-door"></i>
            {!collapsed && <span>dashboard</span>}
          </Link>
        </li>
        <li className={isActive('/admin/products') ? 'active' : ''}>
          <Link to="/admin/products">
            <i className="bi bi-box"></i>
            {!collapsed && <span>products</span>}
          </Link>
        </li>
        <li className={isActive('/admin/categories') ? 'active' : ''}>
          <Link to="/admin/categories">
            <i className="bi bi-diagram-3"></i>
            {!collapsed && <span>Categories</span>}
          </Link>
        </li>
        <li className={isActive('/admin/orders') ? 'active' : ''}>
          <Link to="/admin/orders">
            <i className="bi bi-cart"></i>
            {!collapsed && <span>Orders</span>}
          </Link>
        </li>
        <li className={isActive('/admin/banners') ? 'active' : ''}>
          <Link to="/admin/banners">
            <i className="bi bi-image"></i>
            {!collapsed && <span>banners</span>}
          </Link>
        </li>
        <li className={isActive('/admin/feeds') ? 'active' : ''}>
          <Link to="/admin/feeds">
            <i className="bi bi-rss"></i>
            {!collapsed && <span>feeds</span>}
          </Link>
        </li>
        <li className={isActive('/admin/moderation') ? 'active' : ''}>
          <Link to="/admin/moderation">
            <i className="bi bi-check-square"></i>
            {!collapsed && <span>product_moderation</span>}
          </Link>
        </li>
        <li className={isActive('/admin/reviews') ? 'active' : ''}>
          <Link to="/admin/reviews">
            <i className="bi bi-star"></i>
            {!collapsed && <span>Reviews</span>}
          </Link>
        </li>
        <li className={isActive('/admin/inventory') ? 'active' : ''}>
          <Link to="/admin/inventory">
            <i className="bi bi-clipboard-data"></i>
            {!collapsed && <span>inventory</span>}
          </Link>
        </li>
        <li className={isActive('/admin/users') ? 'active' : ''}>
          <Link to="/admin/users">
            <i className="bi bi-people"></i>
            {!collapsed && <span>users</span>}
          </Link>
        </li>
        <li className={isActive('/admin/store_settings') ? 'active' : ''}>
          <Link to="/admin/store_settings">
            <i className="bi bi-gear"></i>
            {!collapsed && <span>store_settings</span>}
          </Link>
        </li>
      </ul>

      <style jsx>{`
        .admin-sidebar {
          background-color: #fff;
          width: 240px;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          overflow: hidden;
        }

        .admin-sidebar.collapsed {
          width: 60px;
        }

        .admin-sidebar-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 15px;
          border-bottom: 1px solid #f0f0f0;
        }

        .admin-sidebar-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }

        .collapse-button {
          background: transparent;
          border: none;
          cursor: pointer;
          color: #666;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.2s;
        }

        .collapse-button:hover {
          background-color: #f0f0f0;
        }

        .admin-menu {
          list-style-type: none;
          padding: 0;
          margin: 0;
        }

        .admin-menu li {
          margin: 0;
        }

        .admin-menu li a {
          display: flex;
          align-items: center;
          padding: 12px 15px;
          text-decoration: none;
          color: #555;
          transition: background-color 0.2s;
        }

        .admin-menu li a:hover {
          background-color: #f8f9fa;
        }

        .admin-menu li.active a {
          background-color: #f1f1f1;
          color: #d9b38c;
          font-weight: 500;
        }

        .admin-menu li i {
          margin-right: ${collapsed ? '0' : '10px'};
          font-size: 18px;
          min-width: 20px;
          text-align: center;
        }

        .admin-menu li a span {
          white-space: nowrap;
        }
      `}</style>
    </div>
  );
};

export default AdminSidebar;
