// src/components/admin/OrderManagement.js
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';

const OrderManagement = () => {
  const { t } = useTranslation();
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [ordersPerPage] = useState(5);
  const [expandedOrder, setExpandedOrder] = useState(null);

  // Получение заказов
  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch orders from your data source here
        const ordersList = []; // Replace with actual data fetching logic
        setOrders(ordersList);
        setFilteredOrders(ordersList);
      } catch (err) {
        setError(t('error_loading_data', 'Не удалось загрузить данные.'));
        console.error('Error fetching orders:', err);
        toast.error(t('error_loading_data', 'Не удалось загрузить данные.'));
      } finally {
        setLoading(false);
      }
    };
    fetchOrders();
  }, [t]);

  // Фильтрация заказов по статусу
  useEffect(() => {
    if (statusFilter === 'all') {
      setFilteredOrders(orders);
    } else {
      setFilteredOrders(orders.filter(order => order.status === statusFilter));
    }
    setCurrentPage(1); // Сбрасываем страницу при изменении фильтра
  }, [statusFilter, orders]);

  // Обновление статуса заказа
  const handleUpdateStatus = async (orderId, newStatus) => {
    try {
      // Update order status in your data source here
      setOrders(
        orders.map(order => (order.id === orderId ? { ...order, status: newStatus } : order))
      );
      toast.success(t('status_updated', 'Статус заказа обновлен!'));
    } catch (err) {
      console.error('Error updating order status:', err);
      toast.error(t('error_updating_status', 'Ошибка при обновлении статуса: ') + err.message);
    }
  };

  // Пагинация
  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0); // Прокручиваем страницу вверх
  };

  // Развернуть/свернуть детали заказа
  const toggleOrderDetails = orderId => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId);
  };

  return (
    <div className="container mx-auto px-6 py-12">
      <h2 className="text-2xl font-semibold mb-6">{t('manage_orders', 'Управление заказами')}</h2>

      {/* Фильтр по статусу */}
      <div className="mb-6">
        <label className="mr-2 text-gray-700">
          {t('filter_by_status', 'Фильтровать по статусу')}:
        </label>
        <select
          value={statusFilter}
          onChange={e => setStatusFilter(e.target.value)}
          className="p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        >
          <option value="all">{t('all', 'Все')}</option>
          <option value="pending">{t('pending', 'В ожидании')}</option>
          <option value="processing">{t('processing', 'В обработке')}</option>
          <option value="shipped">{t('shipped', 'Отправлен')}</option>
          <option value="delivered">{t('delivered', 'Доставлен')}</option>
          <option value="cancelled">{t('cancelled', 'Отменён')}</option>
        </select>
      </div>

      {/* Состояние загрузки и ошибки */}
      {loading && <div className="text-center text-gray-600">{t('loading', 'Загрузка...')}</div>}
      {error && <div className="text-red-500 mb-4">{error}</div>}

      {/* Список заказов */}
      <div className="space-y-4">
        {currentOrders.length === 0 ? (
          <p className="text-gray-600">{t('no_orders', 'Заказов пока нет.')}</p>
        ) : (
          currentOrders.map(order => (
            <div
              key={order.id}
              className="card p-4 bg-white shadow-md rounded-lg border border-gray-200"
            >
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <div>
                  <h3 className="text-lg font-semibold">
                    {t('order', 'Заказ')} #{order.id}
                  </h3>
                  <p className="text-gray-600">
                    {t('user', 'Пользователь')}: {order.userEmail}
                  </p>
                  <p className="text-gray-600">
                    {t('total', 'Итого')}: {order.total} EUR
                  </p>
                  <p className="text-gray-600">
                    {t('status', 'Статус')}: {t(order.status, order.status)}
                  </p>
                  <p className="text-gray-600">
                    {t('created_at', 'Дата создания')}: {new Date(order.createdAt).toLocaleString()}
                  </p>
                </div>
                <div className="mt-4 sm:mt-0 flex items-center space-x-2">
                  <select
                    value={order.status}
                    onChange={e => handleUpdateStatus(order.id, e.target.value)}
                    className="border p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="pending">{t('pending', 'В ожидании')}</option>
                    <option value="processing">{t('processing', 'В обработке')}</option>
                    <option value="shipped">{t('shipped', 'Отправлен')}</option>
                    <option value="delivered">{t('delivered', 'Доставлен')}</option>
                    <option value="cancelled">{t('cancelled', 'Отменён')}</option>
                  </select>
                  <button
                    onClick={() => toggleOrderDetails(order.id)}
                    className="px-3 py-1 bg-primary text-white rounded hover:bg-blue-700"
                  >
                    {expandedOrder === order.id
                      ? t('hide_details', 'Скрыть детали')
                      : t('show_details', 'Показать детали')}
                  </button>
                </div>
              </div>

              {/* Детали заказа (разворачиваемый список товаров) */}
              {expandedOrder === order.id && (
                <div className="mt-4">
                  <h4 className="font-semibold text-gray-700">{t('items', 'Товары')}:</h4>
                  <ul className="list-disc pl-5 mt-2">
                    {order.items.map((item, index) => (
                      <li key={index} className="text-gray-600">
                        {item.name} - {item.quantity} x {item.price} ={' '}
                        {(item.quantity * item.price).toFixed(2)} EUR
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Пагинация */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center space-x-2">
          {Array.from({ length: totalPages }, (_, index) => index + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-4 py-2 rounded ${
                currentPage === page
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default OrderManagement;
