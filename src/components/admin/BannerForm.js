import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { STORAGE_BUCKETS, TABLES } from '../../config/constants';

const BannerForm = ({ banner = {}, isEditing = false, onError }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    image: '',
    image_url: '',
    button_text: '',
    button_link: '',
    position: 1,
    active: true,
    ...banner
  });
  const [imageFile, setImageFile] = useState(null);

  // Effect to ensure both image fields are synced
  useEffect(() => {
    // If there's data loaded and one image field is empty but the other isn't
    if ((formData.image && !formData.image_url) || (!formData.image && formData.image_url)) {
      setFormData(prev => ({
        ...prev,
        image: prev.image || prev.image_url,
        image_url: prev.image_url || prev.image
      }));
    }
  }, [formData.image, formData.image_url]);

  const handleChange = e => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : type === 'number' ? parseInt(value) || 0 : value
    });
  };

  const handleImageChange = e => {
    if (!e.target.files || e.target.files.length === 0) return;
    setImageFile(e.target.files[0]);

    // Create temporary URL for preview
    const objectUrl = URL.createObjectURL(e.target.files[0]);
    setFormData({
      ...formData,
      _tempImageUrl: objectUrl
    });
  };

  const handleImageUpload = async () => {
    if (!imageFile) return formData.image || formData.image_url;

    try {
      // Generate unique filename
      const fileExt = imageFile.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `banners/${fileName}`;

      // Upload to storage - first try with STORAGE_BUCKETS.MEDIA
      let bucket = STORAGE_BUCKETS.MEDIA || 'media';
      let { error: uploadError, data } = await supabase.storage
        .from(bucket)
        .upload(filePath, imageFile);

      // If failed with STORAGE_BUCKETS.MEDIA, try with hardcoded 'media'
      if (uploadError && bucket !== 'media') {
        bucket = 'media';
        const result = await supabase.storage.from(bucket).upload(filePath, imageFile);

        uploadError = result.error;
        data = result.data;
      }

      if (uploadError) {
        console.error('Upload error:', uploadError);
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage.from(bucket).getPublicUrl(filePath);
      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }
  };

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);

    try {
      // Prepare data for saving
      const saveData = {
        title: formData.title,
        subtitle: formData.subtitle,
        button_text: formData.button_text,
        button_link: formData.button_link,
        position: formData.position,
        active: formData.active,
        updated_at: new Date().toISOString()
      };

      // Handle image URL
      if (imageFile) {
        try {
          const imageUrl = await handleImageUpload();
          if (!imageUrl) {
            throw new Error('Image upload failed - no URL returned');
          }
          saveData.image_url = imageUrl;
        } catch (uploadError) {
          throw new Error(`Image upload failed: ${uploadError.message}`);
        }
      } else if (formData.image_url) {
        // Validate URL format
        try {
          new URL(formData.image_url);
          saveData.image_url = formData.image_url;
        } catch (urlError) {
          throw new Error('Некорректный формат URL изображения');
        }
      } else {
        throw new Error('Необходимо указать URL изображения или загрузить файл');
      }

      if (isEditing) {
        const { error } = await supabase.from('banners').update(saveData).eq('id', banner.id);

        if (error) throw error;
        toast.success('Баннер успешно обновлен');
      } else {
        const { error } = await supabase.from('banners').insert([saveData]);

        if (error) throw error;
        toast.success('Баннер успешно создан');
      }

      navigate('/admin/banners');
    } catch (error) {
      console.error('Error in banner form:', error);
      const errorMessage = `Ошибка ${isEditing ? 'обновления' : 'создания'} баннера: ${error.message}`;
      toast.error(errorMessage);

      if (typeof onError === 'function') {
        onError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  // Clean up temporary URL objects when component unmounts
  useEffect(() => {
    return () => {
      if (formData._tempImageUrl) {
        URL.revokeObjectURL(formData._tempImageUrl);
      }
    };
  }, [formData._tempImageUrl]);

  // Choose the appropriate image URL to display
  const imagePreviewUrl = formData._tempImageUrl || formData.image_url || formData.image;

  return (
    <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Заголовок</label>
            <input
              type="text"
              name="title"
              value={formData.title || ''}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Подзаголовок</label>
            <input
              type="text"
              name="subtitle"
              value={formData.subtitle || ''}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Текст кнопки</label>
              <input
                type="text"
                name="button_text"
                value={formData.button_text || ''}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Ссылка кнопки</label>
              <input
                type="text"
                name="button_link"
                value={formData.button_link || ''}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
                placeholder="/category/sale"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Позиция (1-10)</label>
              <input
                type="number"
                name="position"
                min="1"
                max="10"
                value={formData.position || 1}
                onChange={handleChange}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              />
              <p className="text-xs text-gray-500 mt-1">
                1-5: верхние баннеры, 6-10: нижние баннеры
              </p>
            </div>

            <div className="flex items-center mt-8">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-900">
                Активный
              </label>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Изображение баннера
          </label>
          <div className="mb-4">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="block w-full text-sm text-gray-500 
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-md file:border-0
                        file:text-sm file:font-semibold
                        file:bg-blue-50 file:text-blue-700
                        hover:file:bg-blue-100"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">URL изображения</label>
            <input
              type="text"
              name="image_url"
              value={formData.image_url || ''}
              onChange={handleChange}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              placeholder="https://example.com/image.jpg"
            />
            <p className="text-xs text-gray-500 mt-1">
              Введите URL изображения или загрузите файл выше
            </p>
          </div>

          <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-4">
            {imagePreviewUrl ? (
              <div>
                <img
                  src={imagePreviewUrl}
                  alt="Предпросмотр баннера"
                  className="max-h-48 mx-auto object-contain"
                />
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                Выберите изображение для предпросмотра
              </div>
            )}
          </div>

          <p className="text-xs text-gray-500 mt-2">
            Рекомендуемый размер: 1920x600px для верхнего баннера, 1920x300px для нижнего
          </p>
        </div>
      </div>

      <div className="flex mt-6 space-x-3">
        <button
          type="button"
          onClick={() => navigate('/admin/banners')}
          className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          Отмена
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300"
        >
          {loading ? 'Сохранение...' : isEditing ? 'Обновить баннер' : 'Создать баннер'}
        </button>
      </div>

      <div className="mt-8 border-t pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Предпросмотр баннера</h3>
        <div className="bg-gray-100 p-4 rounded-lg">
          <div
            className="w-full h-40 bg-cover bg-center rounded flex items-center justify-center relative"
            style={{ backgroundImage: imagePreviewUrl ? `url('${imagePreviewUrl}')` : 'none' }}
          >
            {!imagePreviewUrl && (
              <div className="text-gray-400">Изображение баннера не выбрано</div>
            )}

            {imagePreviewUrl && <div className="absolute inset-0 bg-black bg-opacity-40"></div>}

            <div className="relative z-10 text-center px-4">
              {formData.title && (
                <h2 className="text-xl md:text-2xl font-bold mb-2 text-white">{formData.title}</h2>
              )}

              {formData.subtitle && (
                <p className="text-sm md:text-base mb-3 text-white">{formData.subtitle}</p>
              )}

              {formData.button_text && (
                <button
                  type="button"
                  className="bg-primary text-white font-bold py-1 px-4 rounded-lg text-sm"
                >
                  {formData.button_text}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </form>
  );
};

export default BannerForm;
