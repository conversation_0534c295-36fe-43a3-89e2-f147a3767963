import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useTranslation } from 'react-i18next';

const ImageUploader = ({ onImageChange, currentImage }) => {
  const { t } = useTranslation();

  const onDrop = useCallback(
    files => {
      if (files?.[0]) {
        onImageChange(files[0]);
      }
    },
    [onImageChange]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'image/*': ['.jpeg', '.jpg', '.png', '.webp'] },
    maxSize: 5242880,
    multiple: false
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
          isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300'
        }`}
      >
        <div className="space-y-2 text-center">
          <input {...getInputProps()} />
          {currentImage ? (
            <div className="space-y-2">
              <img
                src={currentImage}
                alt="Preview"
                className="mx-auto max-h-48 w-auto object-contain"
              />
              <p className="text-xs text-gray-500">
                {t('click_to_change', 'Нажмите для изменения')}
              </p>
            </div>
          ) : (
            <div className="text-gray-600">
              <p>{t('drag_drop_image', 'Перетащите изображение или кликните')}</p>
              <p className="text-xs">{t('image_requirements', 'PNG, JPG до 5MB')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageUploader;
