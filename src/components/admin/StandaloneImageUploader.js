import React, { useState } from 'react';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';

const ImageUploader = ({ onUploadComplete, currentImage = null }) => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleFileUpload = async event => {
    try {
      const file = event.target.files[0];
      if (!file) return;

      setUploading(true);
      setProgress(0);

      // Simple validation
      if (file.size > 5000000) {
        toast.error('Image size should be less than 5MB');
        setUploading(false);
        return;
      }

      if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
        toast.error('Only JPEG, PNG, and WEBP formats are supported');
        setUploading(false);
        return;
      }

      // Generate a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `product-images/${fileName}`;

      // Upload to storage
      toast.info('Uploading image...');
      const { error: uploadError } = await supabase.storage.from('public').upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
        onUploadProgress: event => {
          const progress = Math.round((event.loaded * 100) / event.total);
          setProgress(progress);
        }
      });

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data } = supabase.storage.from('public').getPublicUrl(filePath);
      const imageUrl = data.publicUrl;

      setUploading(false);
      toast.success('Image uploaded successfully');

      // Call the callback with the new image URL
      if (onUploadComplete) {
        onUploadComplete(imageUrl);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(`Error uploading image: ${error.message}`);
      setUploading(false);
    }
  };

  return (
    <div className="mt-2 mb-4">
      <div className="flex items-center">
        <label className="block">
          <span className="sr-only">Choose image</span>
          <input
            type="file"
            className="block w-full text-sm text-slate-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-full file:border-0
                      file:text-sm file:font-semibold
                      file:bg-blue-50 file:text-blue-700
                      hover:file:bg-blue-100"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading}
          />
        </label>
      </div>

      {uploading && (
        <div className="mt-2">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${progress}%` }}></div>
          </div>
          <p className="text-xs text-gray-500 mt-1">Uploading: {progress}%</p>
        </div>
      )}

      {currentImage && (
        <div className="mt-2">
          <p className="text-sm text-gray-500">Current image:</p>
          <div className="mt-1 relative">
            <img
              src={currentImage}
              alt="Current product"
              className="w-32 h-32 object-cover rounded border"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
