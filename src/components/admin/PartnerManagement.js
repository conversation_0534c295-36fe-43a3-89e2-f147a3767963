// src/components/admin/PartnerManagement.js
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { supabase } from '../../supabaseClient';

const PartnerManagement = () => {
  const { t } = useTranslation();
  const [partners, setPartners] = useState([]);
  const [newPartner, setNewPartner] = useState({ image: null, imageUrl: '', name: '' });
  const [editingPartner, setEditingPartner] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [partnersPerPage] = useState(5);

  // Получение списка партнеров
  useEffect(() => {
    const fetchPartners = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase.from('partners').select('*');
        if (error) throw error;
        setPartners(data || []);
      } catch (err) {
        setError(t('error_loading_data', 'Не удалось загрузить данные.'));
        console.error('Error fetching partners:', err);
        toast.error(t('error_loading_data', 'Не удалось загрузить данные.'));
      } finally {
        setLoading(false);
      }
    };
    fetchPartners();
  }, [t]);

  // Функция загрузки изображения на Cloudinary
  const uploadImageToCloudinary = async file => {
    if (!file) return null;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', 'your-upload-preset'); // Замените на ваш upload preset

    try {
      const response = await fetch(
        `https://api.cloudinary.com/v1_1/your-cloud-name/image/upload`, // Замените your-cloud-name
        {
          method: 'POST',
          body: formData
        }
      );
      const data = await response.json();
      if (data.secure_url) {
        return data.secure_url;
      } else {
        throw new Error('Ошибка загрузки изображения на Cloudinary');
      }
    } catch (err) {
      throw new Error(
        t('error_uploading_image', 'Не удалось загрузить изображение: ') + err.message
      );
    }
  };

  // Добавление нового партнера
  const handleAddPartner = async () => {
    if (!newPartner.image && !newPartner.imageUrl) {
      toast.error(t('image_required', 'Пожалуйста, выберите изображение или укажите URL.'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const imageUrl = newPartner.image
        ? await uploadImageToCloudinary(newPartner.image)
        : newPartner.imageUrl;

      const partnerData = {
        image: imageUrl,
        name: newPartner.name
      };

      const { error } = await supabase.from('partners').insert([partnerData]);
      if (error) throw error;
      const { data } = await supabase.from('partners').select('*');
      setPartners(data || []);
      toast.success(t('partner_added', 'Партнер добавлен!'));
    } catch (err) {
      setError(t('error_adding_partner', 'Не удалось добавить партнера.'));
      console.error('Error adding partner:', err);
      toast.error(t('error_adding_partner', 'Не удалось добавить партнера.'));
    } finally {
      setLoading(false);
    }
  };

  // Редактирование партнера
  const handleEditPartner = partner => {
    setEditingPartner(partner);
    setNewPartner({ image: null, imageUrl: partner.image, name: partner.name });
  };

  // Обновление партнера
  const handleUpdatePartner = async () => {
    if (!newPartner.image && !newPartner.imageUrl) {
      toast.error(t('image_required', 'Пожалуйста, выберите изображение или укажите URL.'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const imageUrl = newPartner.image
        ? await uploadImageToCloudinary(newPartner.image)
        : newPartner.imageUrl;

      const partnerData = {
        image: imageUrl,
        name: newPartner.name
      };

      const { error } = await supabase
        .from('partners')
        .update(partnerData)
        .eq('id', editingPartner.id);
      if (error) throw error;
      const { data } = await supabase.from('partners').select('*');
      setPartners(data || []);
      toast.success(t('partner_updated', 'Партнер обновлен!'));
    } catch (err) {
      setError(t('error_updating_partner', 'Не удалось обновить партнера: ') + err.message);
      console.error('Error updating partner:', err);
      toast.error(t('error_updating_partner', 'Не удалось обновить партнера: ') + err.message);
    } finally {
      setLoading(false);
    }
  };

  // Удаление партнера
  const handleDeletePartner = async id => {
    if (window.confirm(t('confirm_delete', 'Вы уверены, что хотите удалить этого партнера?'))) {
      setLoading(true);
      setError(null);
      try {
        const { error } = await supabase.from('partners').delete().eq('id', id);
        if (error) throw error;
        setPartners(partners.filter(partner => partner.id !== id));
        toast.success(t('partner_deleted', 'Партнер удален!'));
      } catch (err) {
        setError(t('error_deleting_partner', 'Не удалось удалить партнера: ') + err.message);
        console.error('Error deleting partner:', err);
        toast.error(t('error_deleting_partner', 'Не удалось удалить партнера: ') + err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  // Пагинация
  const indexOfLastPartner = currentPage * partnersPerPage;
  const indexOfFirstPartner = indexOfLastPartner - partnersPerPage;
  const currentPartners = partners.slice(indexOfFirstPartner, indexOfLastPartner);
  const totalPages = Math.ceil(partners.length / partnersPerPage);

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0); // Прокручиваем страницу вверх
  };

  return (
    <div className="container mx-auto px-6 py-12">
      <h2 className="text-2xl font-semibold mb-6">
        {t('manage_partners', 'Управление партнерами')}
      </h2>

      {/* Форма для добавления/редактирования */}
      <div className="mb-8 p-6 bg-gray-100 rounded-lg">
        {error && <div className="text-red-500 mb-4">{error}</div>}
        {loading && <div className="text-center text-gray-600">{t('loading', 'Загрузка...')}</div>}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block mb-2 text-gray-700">{t('image', 'Изображение')}</label>
            <input
              type="file"
              accept="image/*"
              onChange={e => setNewPartner({ ...newPartner, image: e.target.files[0] })}
              className="w-full p-2 border rounded-md"
            />
            {newPartner.imageUrl && !newPartner.image && (
              <p className="text-sm text-gray-600 mt-1">
                {t('current_image', 'Текущее изображение:')}{' '}
                <a
                  href={newPartner.imageUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600"
                >
                  {newPartner.imageUrl}
                </a>
              </p>
            )}
          </div>
          <div>
            <label className="block mb-2 text-gray-700">{t('name', 'Название')}</label>
            <input
              type="text"
              placeholder={t('partner_name', 'Название партнера')}
              value={newPartner.name}
              onChange={e => setNewPartner({ ...newPartner, name: e.target.value })}
              className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>
        </div>
        <button
          onClick={editingPartner ? handleUpdatePartner : handleAddPartner}
          className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-blue-700"
          disabled={loading}
        >
          {editingPartner ? t('update', 'Обновить') : t('add', 'Добавить')}
        </button>
      </div>

      {/* Список партнеров */}
      <div>
        <h3 className="text-xl font-semibold mb-4">{t('partner_list', 'Список партнеров')}</h3>
        {currentPartners.length === 0 ? (
          <p className="text-gray-600">{t('no_partners', 'Партнеры отсутствуют.')}</p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentPartners.map(partner => (
              <div
                key={partner.id}
                className="card p-4 bg-white shadow-md rounded-lg border border-gray-200 flex flex-col"
              >
                <img
                  src={partner.image}
                  alt={partner.name}
                  className="w-32 h-32 object-contain rounded-md mb-4 mx-auto"
                />
                <p className="text-gray-600 mb-4 text-center">
                  {t('name', 'Название')}: {partner.name}
                </p>
                <div className="flex space-x-2 justify-center">
                  <button
                    onClick={() => handleEditPartner(partner)}
                    className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    {t('edit', 'Редактировать')}
                  </button>
                  <button
                    onClick={() => handleDeletePartner(partner.id)}
                    className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    {t('delete', 'Удалить')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Пагинация */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center space-x-2">
          {Array.from({ length: totalPages }, (_, index) => index + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-4 py-2 rounded ${
                currentPage === page
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default PartnerManagement;
