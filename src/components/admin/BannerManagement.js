import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient'; // Fix import path
import { TABLES } from '../../config/constants'; // Import TABLES from constants
import { handleError as handleSupabaseError } from '../../utils/errors'; // Import error handling
import { toast } from 'react-toastify';
import { FaEdit, FaTrash, FaPlus, FaTimes, FaCheck, FaArrowUp, FaArrowDown } from 'react-icons/fa';

const BannerManagement = () => {
  const { t } = useTranslation();
  const [banners, setBanners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [formMode, setFormMode] = useState('add'); // 'add' или 'edit'

  const [bannerForm, setBannerForm] = useState({
    id: null,
    title: '',
    image_url: '',
    link_url: '',
    is_active: true,
    position: 0
  });

  // Fetch banners with proper error handling
  const fetchBanners = useCallback(async () => {
    setLoading(true);
    try {
      // Более безопасный запрос без сортировки сначала, чтобы проверить наличие таблицы
      const { data, error } = await supabase.from(TABLES.BANNERS).select('*');

      if (error) {
        console.error('Supabase error details:', error);

        if (error.code === '42P01') {
          // "Relation does not exist"
          toast.error('Таблица баннеров не существует. Запустите инициализацию базы данных');
          return;
        }

        throw error;
      }
      // Сортируем на стороне клиента
      const sortedData = data
        ? [...data].sort((a, b) => (a.position || 0) - (b.position || 0))
        : [];
      setBanners(sortedData);
    } catch (error) {
      console.error('Error fetching banners:', error);
      toast.error(
        `${t('error_fetching_banners', 'Ошибка при загрузке баннеров')}: ${handleSupabaseError(error)}`
      );
    } finally {
      setLoading(false);
    }
  }, [t]);

  useEffect(() => {
    fetchBanners();
  }, [fetchBanners]);

  const handleNewBanner = () => {
    // Find max position to place new banner at the end
    const maxPosition =
      banners.length > 0 ? Math.max(...banners.map(banner => banner.position || 0)) + 1 : 1;

    setBannerForm({
      id: null,
      title: '',
      image_url: '',
      link_url: '',
      is_active: true,
      position: maxPosition
    });
    setFormMode('add');
    setIsEditing(true);
  };

  const handleEditBanner = banner => {
    setBannerForm({
      id: banner.id,
      title: banner.title || '',
      image_url: banner.image_url || '',
      link_url: banner.link_url || '',
      is_active: banner.is_active !== false,
      position: banner.position || 0
    });
    setFormMode('edit');
    setIsEditing(true);
  };

  const handleDeleteBanner = async bannerId => {
    if (window.confirm(t('confirm_delete_banner', 'Вы уверены, что хотите удалить этот баннер?'))) {
      try {
        const { error } = await supabase.from(TABLES.BANNERS).delete().eq('id', bannerId);

        if (error) throw error;

        toast.success(t('banner_deleted', 'Баннер успешно удален'));
        fetchBanners();
      } catch (error) {
        console.error('Error deleting banner:', error);
        toast.error(
          `${t('error_deleting_banner', 'Ошибка при удалении баннера')}: ${handleSupabaseError(error)}`
        );
      }
    }
  };

  const handleSubmitBanner = async e => {
    e.preventDefault();

    try {
      const bannerData = {
        title: bannerForm.title,
        image_url: bannerForm.image_url,
        link_url: bannerForm.link_url,
        is_active: bannerForm.is_active,
        position: parseInt(bannerForm.position) || 0,
        updated_at: new Date()
      };

      if (formMode === 'add') {
        const { data, error } = await supabase.from(TABLES.BANNERS).insert([bannerData]).select();

        if (error) {
          console.error('Supabase insert error:', error);
          throw error;
        }
        toast.success(t('banner_added', 'Баннер успешно добавлен'));
      } else {
        const { error } = await supabase
          .from(TABLES.BANNERS)
          .update(bannerData)
          .eq('id', bannerForm.id);

        if (error) throw error;

        toast.success(t('banner_updated', 'Баннер успешно обновлен'));
      }

      fetchBanners();
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving banner:', error);
      toast.error(
        `${t('error_saving_banner', 'Ошибка при сохранении баннера')}: ${handleSupabaseError(error)}`
      );
    }
  };

  const handleToggleBannerStatus = async banner => {
    try {
      const { error } = await supabase
        .from(TABLES.BANNERS)
        .update({ is_active: !banner.is_active, updated_at: new Date() })
        .eq('id', banner.id);

      if (error) throw error;

      toast.success(
        banner.is_active
          ? t('banner_deactivated', 'Баннер деактивирован')
          : t('banner_activated', 'Баннер активирован')
      );
      fetchBanners();
    } catch (error) {
      console.error('Error toggling banner status:', error);
      toast.error(
        `${t('error_updating_banner', 'Ошибка при обновлении баннера')}: ${handleSupabaseError(error)}`
      );
    }
  };

  const handleMovePosition = async (banner, direction) => {
    // Find the banner to swap positions with
    const sortedBanners = [...banners].sort((a, b) => a.position - b.position);
    const currentIndex = sortedBanners.findIndex(b => b.id === banner.id);
    let swapIndex;

    if (direction === 'up' && currentIndex > 0) {
      swapIndex = currentIndex - 1;
    } else if (direction === 'down' && currentIndex < sortedBanners.length - 1) {
      swapIndex = currentIndex + 1;
    } else {
      return; // Can't move further in this direction
    }

    const bannerToSwap = sortedBanners[swapIndex];

    try {
      // Update both banners' positions
      const updates = [
        { id: banner.id, position: bannerToSwap.position, updated_at: new Date() },
        { id: bannerToSwap.id, position: banner.position, updated_at: new Date() }
      ];

      // Use transaction pattern for consistent updates
      for (const update of updates) {
        const { error } = await supabase
          .from(TABLES.BANNERS)
          .update({ position: update.position, updated_at: update.updated_at })
          .eq('id', update.id);

        if (error) throw error;
      }

      toast.success(t('positions_updated', 'Позиции обновлены'));
      fetchBanners();
    } catch (error) {
      console.error('Error updating positions:', error);
      toast.error(
        `${t('error_updating_positions', 'Ошибка при обновлении позиций')}: ${handleSupabaseError(error)}`
      );
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {isEditing ? (
        // Форма редактирования баннера
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">
              {formMode === 'add'
                ? t('add_banner', 'Добавление баннера')
                : t('edit_banner', 'Редактирование баннера')}
            </h2>
            <button onClick={handleCancelEdit} className="text-gray-600 hover:text-gray-800">
              <FaTimes size={18} />
            </button>
          </div>

          <form onSubmit={handleSubmitBanner} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('banner_title', 'Заголовок')}*
              </label>
              <input
                type="text"
                value={bannerForm.title}
                onChange={e => setBannerForm({ ...bannerForm, title: e.target.value })}
                className="w-full border rounded p-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('image_url', 'URL изображения')}*
              </label>
              <input
                type="text"
                value={bannerForm.image_url}
                onChange={e => setBannerForm({ ...bannerForm, image_url: e.target.value })}
                className="w-full border rounded p-2"
                required
                placeholder="https://example.com/banner.jpg"
              />
              {bannerForm.image_url && (
                <div className="mt-2">
                  <img
                    src={bannerForm.image_url}
                    alt="Preview"
                    className="h-32 w-auto object-contain"
                    onError={e => {
                      e.target.onerror = null;
                      e.target.src = 'https://placehold.co/600x200/EEE/31343C?text=Banner+Preview';
                    }}
                  />
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('link_url', 'URL ссылки')}
              </label>
              <input
                type="text"
                value={bannerForm.link_url}
                onChange={e => setBannerForm({ ...bannerForm, link_url: e.target.value })}
                className="w-full border rounded p-2"
                placeholder="https://example.com/product"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('position', 'Позиция')}
              </label>
              <input
                type="number"
                value={bannerForm.position}
                onChange={e => setBannerForm({ ...bannerForm, position: e.target.value })}
                className="w-full border rounded p-2"
                min="1"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={bannerForm.is_active}
                onChange={e => setBannerForm({ ...bannerForm, is_active: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                {t('active', 'Активен')}
              </label>
            </div>

            <div className="flex gap-3 pt-4 border-t">
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
              >
                {formMode === 'add' ? t('add', 'Добавить') : t('update', 'Обновить')}
              </button>

              <button
                type="button"
                onClick={handleCancelEdit}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
              >
                {t('cancel', 'Отмена')}
              </button>
            </div>
          </form>
        </div>
      ) : (
        // Список баннеров
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b flex justify-between items-center">
            <h2 className="font-semibold text-xl">{t('banners', 'Баннеры')}</h2>
            <button
              onClick={handleNewBanner}
              className="bg-primary text-white px-4 py-2 rounded hover:bg-primary-dark"
            >
              <div className="flex items-center gap-2">
                <FaPlus />
                {t('add_banner', 'Добавить баннер')}
              </div>
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : banners.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              {t('no_banners', 'Баннеры отсутствуют')}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('banner', 'Баннер')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('link', 'Ссылка')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('position', 'Позиция')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('status', 'Статус')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('actions', 'Действия')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {banners
                    .sort((a, b) => a.position - b.position)
                    .map(banner => (
                      <tr key={banner.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {banner.image_url ? (
                              <img
                                src={banner.image_url}
                                alt={banner.title}
                                className="h-16 w-32 object-cover mr-3"
                                onError={e => {
                                  e.target.onerror = null;
                                  e.target.src =
                                    'https://placehold.co/150x150/EEE/31343C?text=Image';
                                }}
                              />
                            ) : (
                              <div className="h-16 w-32 bg-gray-200 mr-3 flex items-center justify-center text-gray-500">
                                No Image
                              </div>
                            )}
                            <div className="font-medium text-gray-900">{banner.title}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {banner.link_url ? (
                            <a
                              href={banner.link_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {banner.link_url.length > 30
                                ? `${banner.link_url.substring(0, 30)}...`
                                : banner.link_url}
                            </a>
                          ) : (
                            <span className="text-gray-400">{t('no_link', 'Без ссылки')}</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className="mr-2">{banner.position}</span>
                            <div className="flex flex-col">
                              <button
                                onClick={() => handleMovePosition(banner, 'up')}
                                className="text-gray-600 hover:text-gray-900"
                              >
                                <FaArrowUp size={14} />
                              </button>
                              <button
                                onClick={() => handleMovePosition(banner, 'down')}
                                className="text-gray-600 hover:text-gray-900"
                              >
                                <FaArrowDown size={14} />
                              </button>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              banner.is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {banner.is_active ? t('active', 'Активен') : t('inactive', 'Неактивен')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end gap-2">
                            <button
                              onClick={() => handleToggleBannerStatus(banner)}
                              className={`${
                                banner.is_active ? 'text-gray-600' : 'text-green-600'
                              } hover:${banner.is_active ? 'text-gray-900' : 'text-green-900'}`}
                              title={
                                banner.is_active
                                  ? t('deactivate', 'Деактивировать')
                                  : t('activate', 'Активировать')
                              }
                            >
                              {banner.is_active ? <FaTimes size={18} /> : <FaCheck size={18} />}
                            </button>
                            <button
                              onClick={() => handleEditBanner(banner)}
                              className="text-blue-600 hover:text-blue-900"
                              title={t('edit', 'Редактировать')}
                            >
                              <FaEdit size={18} />
                            </button>
                            <button
                              onClick={() => handleDeleteBanner(banner.id)}
                              className="text-red-600 hover:text-red-900"
                              title={t('delete', 'Удалить')}
                            >
                              <FaTrash size={18} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BannerManagement;
