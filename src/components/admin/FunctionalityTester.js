import React, { useState, useEffect } from 'react';
import { supabase } from '../../supabaseClient';
import { OrderCreationService } from '../../utils/orderHelpers';
import { EmailService } from '../../services/emailService';

const FunctionalityTester = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[${type.toUpperCase()}] ${message}`);
  };

  // Тест аутентификации
  const testAuthentication = async () => {
    try {
      addLog('🔐 Тестирование аутентификации...', 'info');

      // Проверяем текущую сессию
      const {
        data: { session },
        error
      } = await supabase.auth.getSession();

      if (error) {
        throw error;
      }

      if (session) {
        addLog(`✅ Пользователь авторизован: ${session.user.email}`, 'success');
        return { status: 'passed', message: `Authenticated as ${session.user.email}` };
      } else {
        addLog('ℹ️ Пользователь не авторизован', 'info');
        return { status: 'info', message: 'No active session' };
      }
    } catch (error) {
      addLog(`❌ Ошибка аутентификации: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Тест CRUD операций с продуктами
  const testProductOperations = async () => {
    try {
      addLog('🛍️ Тестирование операций с продуктами...', 'info');

      // Чтение продуктов
      const { data: products, error: readError } = await supabase
        .from('products')
        .select('*')
        .limit(5);

      if (readError) {
        throw readError;
      }

      addLog(`✅ Загружено ${products.length} продуктов`, 'success');

      // Попытка создания тестового продукта (может не сработать из-за RLS)
      const testProduct = {
        name: 'Test Product',
        description: 'Test Description',
        price: 100,
        external_id: `test_${Date.now()}`
      };

      const { data: newProduct, error: createError } = await supabase
        .from('products')
        .insert([testProduct])
        .select();

      if (createError) {
        if (createError.code === '42501') {
          addLog('ℹ️ Создание продукта ограничено (RLS работает)', 'info');
          return { status: 'passed', message: 'Read access works, write restricted by RLS' };
        } else {
          throw createError;
        }
      } else {
        addLog('✅ Тестовый продукт создан', 'success');

        // Удаляем тестовый продукт
        await supabase.from('products').delete().eq('id', newProduct[0].id);

        addLog('✅ Тестовый продукт удален', 'success');
        return { status: 'passed', message: 'Full CRUD access works' };
      }
    } catch (error) {
      addLog(`❌ Ошибка операций с продуктами: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Тест системы заказов
  const testOrderSystem = async () => {
    try {
      addLog('📦 Тестирование системы заказов...', 'info');

      // Проверяем OrderCreationService
      if (!OrderCreationService) {
        throw new Error('OrderCreationService не найден');
      }

      // Читаем существующие заказы
      const { data: orders, error: readError } = await supabase.from('orders').select('*').limit(5);

      if (readError) {
        throw readError;
      }

      addLog(`✅ Загружено ${orders.length} заказов`, 'success');

      // Проверяем метод getOrderById
      if (orders.length > 0) {
        try {
          const orderDetails = await OrderCreationService.getOrderById(orders[0].id);
          if (orderDetails) {
            addLog('✅ Получение деталей заказа работает', 'success');
          } else {
            addLog('⚠️ Детали заказа не получены', 'warning');
          }
        } catch (error) {
          addLog(`⚠️ Ошибка получения деталей заказа: ${error.message}`, 'warning');
        }
      }

      return { status: 'passed', message: 'Order system partially works' };
    } catch (error) {
      addLog(`❌ Ошибка системы заказов: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Тест email системы
  const testEmailSystem = async () => {
    try {
      addLog('📧 Тестирование email системы...', 'info');

      // Проверяем EmailService
      if (!EmailService) {
        throw new Error('EmailService не найден');
      }

      // Проверяем инициализацию
      try {
        await EmailService.initializeEmailSystem();
        addLog('✅ Email система инициализирована', 'success');
      } catch (error) {
        addLog(`⚠️ Ошибка инициализации email: ${error.message}`, 'warning');
      }

      // Читаем логи email
      try {
        const logs = await EmailService.getEmailLogs();
        addLog(`✅ Загружено ${logs?.length || 0} email логов`, 'success');
      } catch (error) {
        addLog(`⚠️ Ошибка чтения email логов: ${error.message}`, 'warning');
      }

      return { status: 'passed', message: 'Email system partially works' };
    } catch (error) {
      addLog(`❌ Ошибка email системы: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Тест работы с категориями
  const testCategorySystem = async () => {
    try {
      addLog('📂 Тестирование системы категорий...', 'info');

      const { data: categories, error } = await supabase.from('categories').select('*').limit(10);

      if (error) {
        throw error;
      }

      addLog(`✅ Загружено ${categories.length} категорий`, 'success');

      // Проверяем иерархию категорий
      const parentCategories = categories.filter(cat => !cat.parent_id);
      const childCategories = categories.filter(cat => cat.parent_id);

      addLog(`✅ Родительских категорий: ${parentCategories.length}`, 'success');
      addLog(`✅ Дочерних категорий: ${childCategories.length}`, 'success');

      return { status: 'passed', message: `Categories loaded: ${categories.length}` };
    } catch (error) {
      addLog(`❌ Ошибка системы категорий: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Тест Storage
  const testStorageSystem = async () => {
    try {
      addLog('📦 Тестирование файлового хранилища...', 'info');

      // Получаем список bucket'ов
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

      if (bucketsError) {
        throw bucketsError;
      }

      addLog(`✅ Найдено ${buckets.length} storage bucket(ов)`, 'success');

      // Проверяем каждый bucket
      for (const bucket of buckets) {
        try {
          const { data: files, error: filesError } = await supabase.storage
            .from(bucket.name)
            .list('', { limit: 5 });

          if (filesError) {
            addLog(`⚠️ Ошибка доступа к bucket ${bucket.name}: ${filesError.message}`, 'warning');
          } else {
            addLog(`✅ Bucket ${bucket.name}: ${files.length} файлов`, 'success');
          }
        } catch (error) {
          addLog(`⚠️ Ошибка bucket ${bucket.name}: ${error.message}`, 'warning');
        }
      }

      return { status: 'passed', message: `Storage buckets: ${buckets.length}` };
    } catch (error) {
      addLog(`❌ Ошибка storage системы: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Тест производительности
  const testPerformance = async () => {
    try {
      addLog('⚡ Тестирование производительности...', 'info');

      const startTime = performance.now();

      // Выполняем несколько запросов параллельно
      const promises = [
        supabase.from('products').select('id').limit(10),
        supabase.from('categories').select('id').limit(10),
        supabase.from('brands').select('id').limit(10)
      ];

      await Promise.all(promises);

      const endTime = performance.now();
      const duration = Math.round(endTime - startTime);

      if (duration < 1000) {
        addLog(`✅ Производительность отличная: ${duration}ms`, 'success');
        return { status: 'passed', message: `Response time: ${duration}ms` };
      } else if (duration < 3000) {
        addLog(`⚠️ Производительность приемлемая: ${duration}ms`, 'warning');
        return { status: 'warning', message: `Response time: ${duration}ms` };
      } else {
        addLog(`❌ Медленный отклик: ${duration}ms`, 'error');
        return { status: 'failed', message: `Slow response: ${duration}ms` };
      }
    } catch (error) {
      addLog(`❌ Ошибка теста производительности: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Основная функция тестирования
  const runFunctionalityTests = async () => {
    setIsRunning(true);
    setLogs([]);
    setTestResults({});

    addLog('🚀 Начинаем функциональные тесты...', 'info');

    const tests = [
      { name: 'authentication', test: testAuthentication },
      { name: 'products', test: testProductOperations },
      { name: 'orders', test: testOrderSystem },
      { name: 'email', test: testEmailSystem },
      { name: 'categories', test: testCategorySystem },
      { name: 'storage', test: testStorageSystem },
      { name: 'performance', test: testPerformance }
    ];

    const results = {};

    for (const { name, test } of tests) {
      try {
        results[name] = await test();
      } catch (error) {
        results[name] = {
          status: 'failed',
          message: error.message
        };
        addLog(`❌ Тест ${name} упал: ${error.message}`, 'error');
      }
    }

    setTestResults(results);

    const summary = Object.values(results).reduce(
      (acc, result) => {
        if (result.status === 'passed') acc.passed++;
        else if (result.status === 'failed') acc.failed++;
        else if (result.status === 'warning') acc.warnings++;
        else acc.info++;
        return acc;
      },
      { passed: 0, failed: 0, warnings: 0, info: 0 }
    );

    addLog(
      `✅ Тесты завершены: ${summary.passed} успешных, ${summary.failed} ошибок, ${summary.warnings} предупреждений`,
      'info'
    );

    setIsRunning(false);
  };

  const getStatusColor = status => {
    switch (status) {
      case 'passed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = status => {
    switch (status) {
      case 'passed':
        return '✅';
      case 'failed':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '⏳';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">Функциональные тесты</h2>

        <button
          onClick={runFunctionalityTests}
          disabled={isRunning}
          className={`px-4 py-2 rounded font-medium ${
            isRunning
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
        >
          {isRunning ? '🧪 Тестируем...' : '🧪 Запустить тесты'}
        </button>
      </div>

      {/* Результаты тестов */}
      {Object.keys(testResults).length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Результаты тестов</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(testResults).map(([testName, result]) => (
              <div key={testName} className="bg-white rounded-lg shadow p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium capitalize">{testName}</h4>
                  <span className={`${getStatusColor(result.status)} flex items-center`}>
                    {getStatusIcon(result.status)}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{result.message}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Логи */}
      {logs.length > 0 && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold mb-3">📄 Логи тестирования</h3>
          <div className="max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="flex items-start py-1 text-sm">
                <span className="text-gray-500 mr-2 min-w-[70px]">{log.timestamp}</span>
                <span
                  className={`${
                    log.type === 'error'
                      ? 'text-red-600'
                      : log.type === 'success'
                        ? 'text-green-600'
                        : log.type === 'warning'
                          ? 'text-yellow-600'
                          : 'text-gray-600'
                  }`}
                >
                  {log.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FunctionalityTester;
