import React, { useState } from 'react';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { TABLES } from '../../config/constants';

const DatabaseDiagnostic = () => {
  const [diagnosing, setDiagnosing] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);

  const checkTable = async tableName => {
    try {
      // Use a simpler query that's less likely to fail
      const { count, error } = await supabase
        .from(tableName)
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error(`Error checking table ${tableName}:`, error);
        return { exists: false, error };
      }

      return { exists: true, error: null };
    } catch (err) {
      console.error(`Exception checking table ${tableName}:`, err);
      return { exists: false, error: err };
    }
  };

  const runDiagnostic = async () => {
    try {
      setDiagnosing(true);
      setResults(null);
      setError(null);

      // Try both table names - direct 'banners' and from TABLES constant
      const directCheck = await checkTable('banners');
      const constantCheck =
        TABLES && TABLES.BANNERS ? await checkTable(TABLES.BANNERS) : { exists: false };

      const hasBannersTable = directCheck.exists || constantCheck.exists;
      const tableToUse = directCheck.exists
        ? 'banners'
        : constantCheck.exists
          ? TABLES.BANNERS
          : null;

      // If no table exists
      if (!hasBannersTable) {
        setResults({
          hasBannersTable,
          columns: [],
          sampleBanner: null,
          tableName: null,
          errors: {
            direct: directCheck.error,
            constant: constantCheck.error
          }
        });

        // Only show toast if both checks failed
        if (!directCheck.exists && !constantCheck.exists) {
          toast.warn("Banners table doesn't seem to exist or is inaccessible");
        }
        return;
      }

      // 2. If table exists, check its content
      let columns = [];
      let sampleBanner = null;

      // Get sample data from the working table
      const { data: bannerData, error: bannerError } = await supabase
        .from(tableToUse)
        .select('*')
        .limit(1);

      if (!bannerError && bannerData && bannerData.length > 0) {
        sampleBanner = bannerData[0];
        // Extract columns from the sample data
        columns = Object.keys(sampleBanner).map(key => ({
          column_name: key,
          data_type: typeof sampleBanner[key]
        }));
      }

      setResults({
        hasBannersTable,
        columns,
        sampleBanner,
        tableName: tableToUse,
        errors: {
          direct: directCheck.error,
          constant: constantCheck.error
        }
      });

      toast.success('Diagnostic completed');
    } catch (err) {
      console.error('Diagnostic error:', err);
      setError(err.message || 'An unknown error occurred');
      toast.error('Error running diagnostic');
    } finally {
      setDiagnosing(false);
    }
  };

  return (
    <div className="bg-white p-4 rounded shadow mb-6">
      <h3 className="text-lg font-medium mb-4">Banner System Diagnostic</h3>

      <button
        onClick={runDiagnostic}
        disabled={diagnosing}
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark disabled:opacity-50"
      >
        {diagnosing ? 'Running...' : 'Run Diagnostic'}
      </button>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {results && (
        <div className="mt-4">
          <h4 className="font-medium mb-2">Results:</h4>

          <div className="p-3 bg-sectionBackground rounded mb-2">
            <p>
              <strong>Banners table exists:</strong> {results.hasBannersTable ? 'Yes ✅' : 'No ❌'}
            </p>

            {results.tableName && (
              <p className="text-sm text-gray-600">
                Using table name:{' '}
                <code className="bg-gray-100 px-1 py-0.5 rounded">{results.tableName}</code>
              </p>
            )}

            {results.hasBannersTable && (
              <>
                <p className="mt-2">
                  <strong>Table structure:</strong>
                </p>
                <div className="mt-1 p-2 bg-white rounded overflow-x-auto max-h-40">
                  <table className="min-w-full">
                    <thead>
                      <tr>
                        <th className="text-left px-2 py-1 bg-gray-100">Column</th>
                        <th className="text-left px-2 py-1 bg-gray-100">Type</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.columns.map((col, idx) => (
                        <tr key={idx} className="border-t">
                          <td className="px-2 py-1">{col.column_name}</td>
                          <td className="px-2 py-1">{col.data_type}</td>
                        </tr>
                      ))}
                      {results.columns.length === 0 && (
                        <tr>
                          <td colSpan="2" className="px-2 py-2 text-center text-gray-500">
                            No column information available
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {results.sampleBanner && (
                  <div className="mt-4">
                    <p>
                      <strong>Sample banner found:</strong>
                    </p>
                    <div className="mt-1 p-2 bg-white rounded overflow-x-auto">
                      <pre className="text-xs">{JSON.stringify(results.sampleBanner, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </>
            )}

            {!results.hasBannersTable && (
              <div className="mt-2">
                <p className="text-red-600 font-medium">
                  Banners table is missing or inaccessible!
                </p>
                <p className="mt-1">
                  Please run the database initialization script or create the table manually.
                </p>
                <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                  <p>
                    <strong>Direct 'banners' check error:</strong>
                  </p>
                  <pre className="overflow-x-auto">
                    {results.errors.direct
                      ? JSON.stringify(results.errors.direct, null, 2)
                      : 'None'}
                  </pre>

                  <p className="mt-2">
                    <strong>TABLES.BANNERS check error:</strong>
                  </p>
                  <pre className="overflow-x-auto">
                    {results.errors.constant
                      ? JSON.stringify(results.errors.constant, null, 2)
                      : 'None'}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DatabaseDiagnostic;
