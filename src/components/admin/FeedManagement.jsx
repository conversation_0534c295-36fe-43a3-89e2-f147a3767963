/* eslint-disable prettier/prettier */
import React, { useState, useEffect } from 'react';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { useAuth } from '../../context/AuthContext';

const FeedManagement = () => {
  const { user } = useAuth();
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [processResult, setProcessResult] = useState(null);

  const fetchJobs = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/feeds');
      if (!res.ok) throw new Error(`Ошибка ${res.status}`);
      const data = await res.json();
      setJobs(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleProcess = async () => {
    setProcessing(true);
    setProcessResult(null);
    try {
      const res = await fetch('/api/feeds/process', { method: 'POST' });
      if (!res.ok) throw new Error(`Ошибка ${res.status}`);
      const data = await res.json();
      setProcessResult(data);
      await fetchJobs();
    } catch (err) {
      setError(err.message);
    } finally {
      setProcessing(false);
    }
  };

  useEffect(() => {
    if (user) fetchJobs();
  }, [user]);

  if (!user) return null;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Управление фидами</h1>
      <button
        onClick={handleProcess}
        disabled={processing}
        className="mb-4 bg-primary text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {processing ? 'Обработка...' : 'Запустить обработку фидов'}
      </button>
      {error && <div className="text-red-600 mb-4">{error}</div>}
      {processResult && (
        <div className="mb-4">
          <h2 className="font-semibold">Результаты:</h2>
          <ul className="list-disc pl-5">
            {processResult.map((r, i) => (
              <li key={i}>
                {r.feed}:{' '}
                {r.error
                  ? r.error
                  : `Создано ${r.stats.created}, Обновлено ${r.stats.updated}, Ошибок ${r.stats.failed}`}
              </li>
            ))}
          </ul>
        </div>
      )}
      {loading ? (
        <LoadingSpinner />
      ) : (
        <table className="w-full table-auto border-collapse">
          <thead>
            <tr>
              <th className="border px-2 py-1">ID задачи</th>
              <th className="border px-2 py-1">Статус</th>
              <th className="border px-2 py-1">Обработано</th>
              <th className="border px-2 py-1">Создано</th>
              <th className="border px-2 py-1">Обновлено</th>
              <th className="border px-2 py-1">Ошибок</th>
              <th className="border px-2 py-1">Начато</th>
              <th className="border px-2 py-1">Завершено</th>
            </tr>
          </thead>
          <tbody>
            {jobs.map(job => (
              <tr key={job.id}>
                <td className="border px-2 py-1">{job.id}</td>
                <td className="border px-2 py-1">{job.status}</td>
                <td className="border px-2 py-1">{job.items_processed}</td>
                <td className="border px-2 py-1">{job.items_created}</td>
                <td className="border px-2 py-1">{job.items_updated}</td>
                <td className="border px-2 py-1">{job.items_failed}</td>
                <td className="border px-2 py-1">
                  {job.started_at && new Date(job.started_at).toLocaleString()}
                </td>
                <td className="border px-2 py-1">
                  {job.finished_at && new Date(job.finished_at).toLocaleString()}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default FeedManagement;
