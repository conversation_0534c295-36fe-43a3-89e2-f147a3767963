import React, { useState } from 'react';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';

const DatabaseHealthCheck = () => {
  const [healthStatus, setHealthStatus] = useState({
    connection: 'checking',
    tables: {},
    relationships: {},
    indexes: {},
    rls: {},
    functions: {},
    storage: {},
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0
    }
  });

  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[${type.toUpperCase()}] ${message}`);
  };

  // Проверка подключения к базе данных
  const checkDatabaseConnection = async () => {
    try {
      if (!supabase) {
        throw new Error('Supabase client not initialized');
      }

      const { error } = await supabase
        .from('profiles')
        .select('count', { count: 'exact', head: true });

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      addLog('✅ Подключение к базе данных успешно', 'success');
      return { status: 'passed', message: 'Connection successful' };
    } catch (error) {
      addLog(`❌ Ошибка подключения к БД: ${error.message}`, 'error');
      return { status: 'failed', message: error.message };
    }
  };

  // Проверка основных таблиц
  const checkRequiredTables = async () => {
    const requiredTables = [
      'profiles',
      'products',
      'categories',
      'orders',
      'order_items',
      'brands',
      'banners',
      'email_logs'
    ];

    const results = {};

    for (const table of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });

        if (error) {
          results[table] = {
            status: 'failed',
            message: error.message,
            count: 0
          };
          addLog(`❌ Таблица ${table}: ${error.message}`, 'error');
        } else {
          results[table] = {
            status: 'passed',
            message: 'Table exists and accessible',
            count: data?.length || 0
          };
          addLog(`✅ Таблица ${table}: доступна`, 'success');
        }
      } catch (error) {
        results[table] = {
          status: 'failed',
          message: error.message,
          count: 0
        };
        addLog(`❌ Таблица ${table}: ${error.message}`, 'error');
      }
    }

    return results;
  };

  // Проверка RLS политик
  const checkRLSPolicies = async () => {
    const results = {};

    try {
      // Проверяем RLS на основных таблицах
      const tables = ['profiles', 'products', 'orders', 'order_items'];

      for (const table of tables) {
        try {
          // Пытаемся выполнить операции с ограниченными правами
          const { error } = await supabase.from(table).select('*').limit(1);

          if (error && error.code === '42501') {
            results[table] = {
              status: 'passed',
              message: 'RLS enabled and working'
            };
            addLog(`✅ RLS для ${table}: активен`, 'success');
          } else if (!error) {
            results[table] = {
              status: 'warning',
              message: 'Table accessible - check RLS configuration'
            };
            addLog(`⚠️ RLS для ${table}: требует проверки`, 'warning');
          } else {
            results[table] = {
              status: 'failed',
              message: error.message
            };
            addLog(`❌ RLS для ${table}: ${error.message}`, 'error');
          }
        } catch (error) {
          results[table] = {
            status: 'failed',
            message: error.message
          };
          addLog(`❌ RLS для ${table}: ${error.message}`, 'error');
        }
      }
    } catch (error) {
      addLog(`❌ Ошибка проверки RLS: ${error.message}`, 'error');
    }

    return results;
  };

  // Проверка функций базы данных
  const checkDatabaseFunctions = async () => {
    const results = {};

    // Проверяем функцию exec_sql - теперь используем улучшенную версию
    try {
      // Тестируем основную функцию exec_sql(text)
      const { error: testError } = await supabase.rpc('exec_sql', {
        query_text: 'SELECT 1'
      });

      if (!testError) {
        results.exec_sql = {
          status: 'passed',
          message: 'Function works correctly with improved signature'
        };
        addLog(`✅ Функция exec_sql: работает (улучшенная версия)`, 'success');
      } else if (testError.code === 'PGRST203') {
        // Multiple signatures detected - this is actually OK now
        results.exec_sql = {
          status: 'passed',
          message: 'Multiple function signatures available (normal after fix)'
        };
        addLog(`✅ Функция exec_sql: несколько сигнатур доступно (исправлено)`, 'success');
      } else {
        results.exec_sql = {
          status: 'warning',
          message: `Function issue: ${testError.message}`
        };
        addLog(`⚠️ Функция exec_sql: ${testError.message}`, 'warning');
      }
    } catch (error) {
      results.exec_sql = {
        status: 'failed',
        message: error.message
      };
      addLog(`❌ Функция exec_sql: ${error.message}`, 'error');
    }

    return results;
  };

  // Проверка хранилища (Storage)
  const checkStorageBuckets = async () => {
    const results = {};

    try {
      const { data: buckets, error } = await supabase.storage.listBuckets();

      if (error) {
        results.buckets = {
          status: 'failed',
          message: error.message
        };
        addLog(`❌ Storage: ${error.message}`, 'error');
      } else {
        results.buckets = {
          status: 'passed',
          message: `Found ${buckets.length} buckets`,
          buckets: buckets.map(b => b.name)
        };
        addLog(`✅ Storage: найдено ${buckets.length} bucket(ов)`, 'success');
      }
    } catch (error) {
      results.buckets = {
        status: 'failed',
        message: error.message
      };
      addLog(`❌ Storage: ${error.message}`, 'error');
    }

    return results;
  };

  // Проверка связей между таблицами
  const checkTableRelationships = async () => {
    const results = {};

    try {
      // Проверяем связь products -> categories
      const { error: prodCatError } = await supabase
        .from('products')
        .select('id, category_id, categories(name)')
        .limit(5);

      if (prodCatError) {
        results.products_categories = {
          status: 'failed',
          message: prodCatError.message
        };
        addLog(`❌ Связь products->categories: ${prodCatError.message}`, 'error');
      } else {
        results.products_categories = {
          status: 'passed',
          message: 'Foreign key relationship works'
        };
        addLog(`✅ Связь products->categories: работает`, 'success');
      }

      // Проверяем связь orders -> profiles с помощью новой helper функции
      try {
        const { data: foreignKeys, error: fkError } = await supabase.rpc('get_foreign_keys', {
          table_name_param: 'orders'
        });

        if (fkError) {
          // Fallback to old method if helper function doesn't exist
          const { data: joinTest, error: joinError } = await supabase
            .from('orders')
            .select('id, user_id, profiles!inner(email)')
            .limit(1);

          if (joinError) {
            results.orders_profiles = {
              status: 'warning',
              message: 'Поле user_id существует, но может не иметь внешнего ключа'
            };
            addLog(`⚠️ Связь orders->profiles: требует проверки FK`, 'warning');
          } else {
            results.orders_profiles = {
              status: 'passed',
              message: 'Relationship with profiles table works'
            };
            addLog(`✅ Связь orders->profiles: работает`, 'success');
          }
        } else {
          // Check if foreign key constraint exists
          const hasUserIdFK =
            foreignKeys &&
            foreignKeys.some(
              fk => fk.column_name === 'user_id' && fk.foreign_table_name === 'profiles'
            );

          if (hasUserIdFK) {
            results.orders_profiles = {
              status: 'passed',
              message: 'Foreign key constraint exists: orders.user_id -> profiles.id'
            };
            addLog(`✅ Связь orders->profiles: FK ограничение установлено`, 'success');
          } else {
            results.orders_profiles = {
              status: 'warning',
              message: 'Поле user_id существует, но нет FK ограничения'
            };
            addLog(`⚠️ Связь orders->profiles: поле есть, но нет FK`, 'warning');
          }
        }
      } catch (error) {
        results.orders_profiles = {
          status: 'warning',
          message: 'Cannot verify relationship - table structure may need updating'
        };
        addLog(`⚠️ Связь orders->profiles: ошибка проверки`, 'warning');
      }
    } catch (error) {
      addLog(`❌ Ошибка проверки связей: ${error.message}`, 'error');
    }

    return results;
  };

  // Функция для исправления ошибки создания заказов
  const fixOrderCreationError = async () => {
    setIsRunning(true);
    addLog('🔧 Исправление ошибки создания заказов...', 'info');

    try {
      // Удаление проблематичных триггеров email
      addLog('📧 Удаление проблематичных триггеров электронной почты...', 'info');

      const removeTriggersSQL = `
        -- Remove email triggers that use net.http_post
        DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders;
        DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders;
        
        -- Remove email trigger functions
        DROP FUNCTION IF EXISTS send_order_confirmation_email();
        DROP FUNCTION IF EXISTS send_status_update_email();
      `;

      // Попробуем разные варианты параметров для exec_sql
      let triggerError = null;

      try {
        const { error } = await supabase.rpc('exec_sql', { query: removeTriggersSQL });
        triggerError = error;
      } catch (e) {
        try {
          const { error } = await supabase.rpc('exec_sql', { query_text: removeTriggersSQL });
          triggerError = error;
        } catch (e2) {
          triggerError = e2;
        }
      }

      if (triggerError) {
        addLog(`⚠️ Предупреждение при удалении триггеров: ${triggerError.message}`, 'warning');
      } else {
        addLog('✅ Проблематичные триггеры удалены', 'success');
      }

      // Тестирование создания заказа
      addLog('🧪 Тестирование создания заказа...', 'info');

      const testOrderData = {
        customer_name: 'Test Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+1234567890',
        shipping_address: { city: 'Test City' },
        total_amount: 100.0,
        status: 'pending',
        payment_method: 'cash_on_delivery',
        payment_status: 'pending',
        notes: 'Test order for diagnostics'
      };

      const { data: testOrder, error: orderError } = await supabase
        .from('orders')
        .insert([testOrderData])
        .select()
        .single();

      if (orderError) {
        addLog(`❌ Ошибка создания заказа: ${orderError.message}`, 'error');
        throw orderError;
      } else {
        addLog(`✅ Тестовый заказ создан: ${testOrder.id}`, 'success');

        // Тестирование товаров заказа
        const { error: itemsError } = await supabase.from('order_items').insert([
          {
            order_id: testOrder.id,
            product_id: null,
            product_name: 'Test Product',
            quantity: 1,
            price: 100.0
          }
        ]);

        if (itemsError) {
          addLog(`❌ Ошибка создания товаров: ${itemsError.message}`, 'error');
        } else {
          addLog('✅ Товары заказа созданы успешно', 'success');
        }

        // Очистка тестовых данных
        await supabase.from('order_items').delete().eq('order_id', testOrder.id);
        await supabase.from('orders').delete().eq('id', testOrder.id);
        addLog('✅ Тестовые данные очищены', 'success');
      }

      addLog('🎉 Ошибка создания заказов исправлена!', 'success');
      toast.success('Проблема создания заказов решена!');
    } catch (error) {
      addLog(`❌ Ошибка исправления: ${error.message}`, 'error');
      toast.error('Не удалось исправить ошибку создания заказов');
    }

    setIsRunning(false);
  };

  // Функция для автоматического исправления проблем
  const repairDatabaseIssues = async () => {
    setIsRunning(true);
    addLog('🔧 Начинаем автоматическое исправление проблем...', 'info');

    try {
      // Исправление FK ограничения для orders.user_id
      addLog('🔗 Исправление внешних ключей...', 'info');

      const repairFKSQL = `
        DO $$
        BEGIN
            -- Очистка недопустимых user_id
            UPDATE orders SET user_id = NULL 
            WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM profiles);
            
            -- Добавление FK ограничения если его нет
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE constraint_name = 'orders_user_id_fkey' 
                AND table_name = 'orders'
            ) THEN
                ALTER TABLE orders ADD CONSTRAINT orders_user_id_fkey 
                FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL;
                RAISE NOTICE 'Added FK constraint: orders.user_id -> profiles.id';
            END IF;
        END $$;
      `;

      const { error: fkError } = await supabase.rpc('exec_sql', {
        query_text: repairFKSQL
      });

      if (fkError) {
        addLog(`⚠️ Не удалось исправить FK: ${fkError.message}`, 'warning');
      } else {
        addLog('✅ Внешние ключи исправлены', 'success');
      }

      // Создание недостающих индексов
      addLog('📊 Создание недостающих индексов...', 'info');

      const indexSQL = `
        CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
        CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
        CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
        CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
      `;

      const { error: indexError } = await supabase.rpc('exec_sql', {
        query_text: indexSQL
      });

      if (indexError) {
        addLog(`⚠️ Не удалось создать индексы: ${indexError.message}`, 'warning');
      } else {
        addLog('✅ Индексы созданы', 'success');
      }

      // Обновление схемы
      addLog('🔄 Обновление схемы PostgREST...', 'info');

      const { error: refreshError } = await supabase.rpc('exec_sql', {
        query_text: "NOTIFY pgrst, 'reload schema';"
      });

      if (!refreshError) {
        addLog('✅ Схема обновлена', 'success');
      }

      addLog('🎉 Автоматическое исправление завершено', 'success');
      toast.success('Исправления применены! Запустите диагностику повторно.');
    } catch (error) {
      addLog(`❌ Ошибка исправления: ${error.message}`, 'error');
      toast.error('Ошибка при исправлении проблем');
    }

    setIsRunning(false);
  };

  // Основная функция проверки
  const runHealthCheck = async () => {
    setIsRunning(true);
    setLogs([]);

    addLog('🔍 Начинаем комплексную проверку базы данных...', 'info');

    try {
      // Проверка подключения
      const connection = await checkDatabaseConnection();

      // Проверка таблиц
      addLog('📋 Проверяем основные таблицы...', 'info');
      const tables = await checkRequiredTables();

      // Проверка RLS
      addLog('🔒 Проверяем RLS политики...', 'info');
      const rls = await checkRLSPolicies();

      // Проверка функций
      addLog('⚙️ Проверяем функции базы данных...', 'info');
      const functions = await checkDatabaseFunctions();

      // Проверка хранилища
      addLog('📦 Проверяем Storage...', 'info');
      const storage = await checkStorageBuckets();

      // Проверка связей
      addLog('🔗 Проверяем связи между таблицами...', 'info');
      const relationships = await checkTableRelationships();

      // Подсчет результатов
      const allResults = {
        connection,
        ...tables,
        ...rls,
        ...functions,
        ...storage,
        ...relationships
      };

      const summary = Object.values(allResults).reduce(
        (acc, result) => {
          acc.total++;
          if (result.status === 'passed') acc.passed++;
          else if (result.status === 'failed') acc.failed++;
          else if (result.status === 'warning') acc.warnings++;
          return acc;
        },
        { total: 0, passed: 0, failed: 0, warnings: 0 }
      );

      setHealthStatus({
        connection,
        tables,
        relationships,
        rls,
        functions,
        storage,
        summary
      });

      addLog(
        `✅ Проверка завершена: ${summary.passed} успешных, ${summary.failed} ошибок, ${summary.warnings} предупреждений`,
        'info'
      );
    } catch (error) {
      addLog(`❌ Критическая ошибка: ${error.message}`, 'error');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = status => {
    switch (status) {
      case 'passed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = status => {
    switch (status) {
      case 'passed':
        return '✅';
      case 'failed':
        return '❌';
      case 'warning':
        return '⚠️';
      default:
        return '⏳';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">Диагностика базы данных</h2>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={runHealthCheck}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? '🔍 Проверяем...' : '🚀 Запустить проверку'}
          </button>

          <button
            onClick={fixOrderCreationError}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-red-600 text-white hover:bg-red-700'
            }`}
          >
            {isRunning ? '🔧 Исправляем...' : '🛠️ Исправить заказы'}
          </button>

          <button
            onClick={repairDatabaseIssues}
            disabled={isRunning || healthStatus.summary.total === 0}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning || healthStatus.summary.total === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isRunning ? '🔧 Исправляем...' : '🔧 Исправить проблемы'}
          </button>
        </div>
      </div>

      {/* Сводка */}
      {healthStatus.summary.total > 0 && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Сводка результатов</h3>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{healthStatus.summary.total}</div>
              <div className="text-sm text-gray-600">Всего проверок</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{healthStatus.summary.passed}</div>
              <div className="text-sm text-gray-600">Успешно</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{healthStatus.summary.failed}</div>
              <div className="text-sm text-gray-600">Ошибки</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {healthStatus.summary.warnings}
              </div>
              <div className="text-sm text-gray-600">Предупреждения</div>
            </div>
          </div>
        </div>
      )}

      {/* Детальные результаты */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Таблицы */}
        {Object.keys(healthStatus.tables).length > 0 && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-lg font-semibold mb-3">📋 Таблицы</h3>
            {Object.entries(healthStatus.tables).map(([table, result]) => (
              <div key={table} className="flex justify-between items-center py-2 border-b">
                <span className="font-medium">{table}</span>
                <span className={`${getStatusColor(result.status)} flex items-center`}>
                  {getStatusIcon(result.status)} {result.status}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* RLS Политики */}
        {Object.keys(healthStatus.rls).length > 0 && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-lg font-semibold mb-3">🔒 RLS Политики</h3>
            {Object.entries(healthStatus.rls).map(([table, result]) => (
              <div key={table} className="flex justify-between items-center py-2 border-b">
                <span className="font-medium">{table}</span>
                <span className={`${getStatusColor(result.status)} flex items-center`}>
                  {getStatusIcon(result.status)} {result.status}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Функции */}
        {Object.keys(healthStatus.functions).length > 0 && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-lg font-semibold mb-3">⚙️ Функции</h3>
            {Object.entries(healthStatus.functions).map(([func, result]) => (
              <div key={func} className="flex justify-between items-center py-2 border-b">
                <span className="font-medium">{func}</span>
                <span className={`${getStatusColor(result.status)} flex items-center`}>
                  {getStatusIcon(result.status)} {result.status}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Storage */}
        {Object.keys(healthStatus.storage).length > 0 && (
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-lg font-semibold mb-3">📦 Storage</h3>
            {Object.entries(healthStatus.storage).map(([bucket, result]) => (
              <div key={bucket} className="flex justify-between items-center py-2 border-b">
                <span className="font-medium">{bucket}</span>
                <span className={`${getStatusColor(result.status)} flex items-center`}>
                  {getStatusIcon(result.status)} {result.status}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Логи */}
      {logs.length > 0 && (
        <div className="bg-white rounded-lg shadow p-4">
          <h3 className="text-lg font-semibold mb-3">📄 Логи проверки</h3>
          <div className="max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="flex items-start py-1 text-sm">
                <span className="text-gray-500 mr-2">{log.timestamp}</span>
                <span
                  className={`${
                    log.type === 'error'
                      ? 'text-red-600'
                      : log.type === 'success'
                        ? 'text-green-600'
                        : log.type === 'warning'
                          ? 'text-yellow-600'
                          : 'text-gray-600'
                  }`}
                >
                  {log.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DatabaseHealthCheck;
