import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { FaSave, FaReset, FaCog } from 'react-icons/fa';

const BannerEffectsSettings = () => {
  const { t } = useTranslation();
  const [settings, setSettings] = useState({
    // Общие настройки
    sticky_enabled: true,
    sticky_threshold: 100,
    parallax_enabled: true,
    parallax_speed: 0.3,
    
    // Настройки слайдера
    auto_play_default: true,
    auto_play_interval_default: 5000,
    transition_duration: 300,
    show_nav_arrows: true,
    show_indicators: true,
    show_play_pause: true,
    
    // Анимации
    fade_in_enabled: true,
    slide_animation_enabled: true,
    hover_effects_enabled: true,
    
    // Адаптивность
    mobile_auto_play: false,
    mobile_nav_always_visible: true,
    reduced_motion_support: true
  });
  
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // Попробуем загрузить настройки из таблицы settings или создать значения по умолчанию
      const { data, error } = await supabase
        .from('settings')
        .select('*')
        .eq('category', 'banner_effects')
        .single();

      if (!error && data) {
        setSettings({ ...settings, ...JSON.parse(data.value || '{}') });
      }
    } catch (error) {
      console.log('Настройки эффектов не найдены, используем значения по умолчанию');
    }
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('settings')
        .upsert({
          category: 'banner_effects',
          key: 'effects_config',
          value: JSON.stringify(settings),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'category,key'
        });

      if (error) throw error;

      toast.success('Настройки эффектов сохранены');
      setHasChanges(false);
    } catch (error) {
      console.error('Ошибка сохранения настроек:', error);
      toast.error('Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const resetSettings = () => {
    setSettings({
      sticky_enabled: true,
      sticky_threshold: 100,
      parallax_enabled: true,
      parallax_speed: 0.3,
      auto_play_default: true,
      auto_play_interval_default: 5000,
      transition_duration: 300,
      show_nav_arrows: true,
      show_indicators: true,
      show_play_pause: true,
      fade_in_enabled: true,
      slide_animation_enabled: true,
      hover_effects_enabled: true,
      mobile_auto_play: false,
      mobile_nav_always_visible: true,
      reduced_motion_support: true
    });
    setHasChanges(true);
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center gap-2 mb-6">
        <FaCog className="text-blue-600" />
        <h2 className="text-xl font-semibold">Настройки эффектов баннеров</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Эффекты прокрутки */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
            Эффекты прокрутки
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Эффект прилипания (Sticky)
              </label>
              <input
                type="checkbox"
                checked={settings.sticky_enabled}
                onChange={e => updateSetting('sticky_enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            {settings.sticky_enabled && (
              <div className="ml-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Порог активации (px)
                </label>
                <input
                  type="number"
                  value={settings.sticky_threshold}
                  onChange={e => updateSetting('sticky_threshold', parseInt(e.target.value))}
                  className="w-24 border rounded p-2"
                  min="0"
                  max="500"
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Параллакс эффект
              </label>
              <input
                type="checkbox"
                checked={settings.parallax_enabled}
                onChange={e => updateSetting('parallax_enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            {settings.parallax_enabled && (
              <div className="ml-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Скорость параллакса
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1"
                  step="0.1"
                  value={settings.parallax_speed}
                  onChange={e => updateSetting('parallax_speed', parseFloat(e.target.value))}
                  className="w-full"
                />
                <span className="text-xs text-gray-500">{settings.parallax_speed}</span>
              </div>
            )}
          </div>
        </div>

        {/* Настройки слайдера */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
            Настройки слайдера
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Автопроигрывание по умолчанию
              </label>
              <input
                type="checkbox"
                checked={settings.auto_play_default}
                onChange={e => updateSetting('auto_play_default', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Интервал автопроигрывания (мс)
              </label>
              <input
                type="number"
                value={settings.auto_play_interval_default}
                onChange={e => updateSetting('auto_play_interval_default', parseInt(e.target.value))}
                className="w-32 border rounded p-2"
                min="1000"
                max="30000"
                step="1000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Длительность перехода (мс)
              </label>
              <input
                type="number"
                value={settings.transition_duration}
                onChange={e => updateSetting('transition_duration', parseInt(e.target.value))}
                className="w-32 border rounded p-2"
                min="100"
                max="2000"
                step="100"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Показывать стрелки</label>
                <input
                  type="checkbox"
                  checked={settings.show_nav_arrows}
                  onChange={e => updateSetting('show_nav_arrows', e.target.checked)}
                  className="h-4 w-4 text-blue-600 rounded"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Показывать индикаторы</label>
                <input
                  type="checkbox"
                  checked={settings.show_indicators}
                  onChange={e => updateSetting('show_indicators', e.target.checked)}
                  className="h-4 w-4 text-blue-600 rounded"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Кнопка play/pause</label>
                <input
                  type="checkbox"
                  checked={settings.show_play_pause}
                  onChange={e => updateSetting('show_play_pause', e.target.checked)}
                  className="h-4 w-4 text-blue-600 rounded"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Анимации */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
            Анимации
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Эффект появления
              </label>
              <input
                type="checkbox"
                checked={settings.fade_in_enabled}
                onChange={e => updateSetting('fade_in_enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Анимация слайдов
              </label>
              <input
                type="checkbox"
                checked={settings.slide_animation_enabled}
                onChange={e => updateSetting('slide_animation_enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Эффекты при наведении
              </label>
              <input
                type="checkbox"
                checked={settings.hover_effects_enabled}
                onChange={e => updateSetting('hover_effects_enabled', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>
          </div>
        </div>

        {/* Мобильная адаптивность */}
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
            Мобильная версия
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Автопроигрывание на мобильных
              </label>
              <input
                type="checkbox"
                checked={settings.mobile_auto_play}
                onChange={e => updateSetting('mobile_auto_play', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Всегда показывать навигацию
              </label>
              <input
                type="checkbox"
                checked={settings.mobile_nav_always_visible}
                onChange={e => updateSetting('mobile_nav_always_visible', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Поддержка reduced-motion
              </label>
              <input
                type="checkbox"
                checked={settings.reduced_motion_support}
                onChange={e => updateSetting('reduced_motion_support', e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Кнопки управления */}
      <div className="flex gap-4 mt-8 pt-6 border-t">
        <button
          onClick={saveSettings}
          disabled={!hasChanges || loading}
          className={`flex items-center gap-2 px-4 py-2 rounded font-medium ${
            hasChanges && !loading
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <FaSave />
          {loading ? 'Сохранение...' : 'Сохранить'}
        </button>

        <button
          onClick={resetSettings}
          className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded font-medium text-gray-700 hover:bg-gray-50"
        >
          <FaReset />
          Сбросить
        </button>
      </div>

      {hasChanges && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            У вас есть несохраненные изменения. Не забудьте сохранить настройки.
          </p>
        </div>
      )}
    </div>
  );
};

export default BannerEffectsSettings;
