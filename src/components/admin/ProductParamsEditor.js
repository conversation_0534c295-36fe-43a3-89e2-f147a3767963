import React, { useState, useEffect, useCallback } from 'react';
import { supabase, convertToBigInt, convertFromBigInt } from '../../supabaseClient';
import { handleError } from '../../utils/errors';
import { FaPlus, FaTrash, Fa<PERSON>ey } from 'react-icons/fa';

const ProductParamsEditor = ({ productId, existingParams = [], onChange }) => {
  const [params, setParams] = useState(existingParams || []);

  // Use useCallback to memoize the fetchProductParams function
  const fetchProductParams = useCallback(async () => {
    if (!productId) return;

    try {
      // Convert UUID to BigInt string format for the query
      const bigIntProductId = convertToBigInt(productId);

      const { data, error } = await supabase
        .from('product_params')
        .select('*')
        .eq('product_id', bigIntProductId);

      if (error) throw error;
      setParams(data || []);
      if (onChange) onChange(data || []);
    } catch (error) {
      handleError(error);
    }
  }, [productId, onChange]);

  useEffect(() => {
    if (existingParams && existingParams.length > 0) {
      setParams(existingParams);
    } else if (productId) {
      // Fetch params if not provided
      fetchProductParams();
    }
  }, [productId, existingParams, fetchProductParams]);

  const addParam = () => {
    // Convert UUID to BigInt string format when adding a new parameter
    const bigIntProductId = convertToBigInt(productId);

    const newParam = {
      id: `temp_${Date.now()}`,
      product_id: bigIntProductId,
      name: '',
      value: '',
      is_key: false, // Default value for new parameters
      isNew: true
    };

    const updatedParams = [...params, newParam];
    setParams(updatedParams);
    if (onChange) onChange(updatedParams);
  };

  const updateParam = (index, field, value) => {
    const updatedParams = [...params];
    updatedParams[index] = {
      ...updatedParams[index],
      [field]: value,
      isModified: !updatedParams[index].isNew
    };

    setParams(updatedParams);
    if (onChange) onChange(updatedParams);
  };

  const removeParam = index => {
    const updatedParams = [...params];
    const removedParam = updatedParams.splice(index, 1)[0];

    // Mark for deletion if it exists in the database
    if (removedParam.id && !removedParam.isNew) {
      updatedParams.push({
        ...removedParam,
        _deleted: true
      });
    }

    setParams(updatedParams.filter(p => !p._deleted));
    if (onChange) onChange(updatedParams);
  };

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-medium">Параметры товара</h3>
        <button
          type="button"
          onClick={addParam}
          className="px-2 py-1 bg-primary text-white rounded text-sm flex items-center"
        >
          <FaPlus className="mr-1" /> Добавить параметр
        </button>
      </div>

      {params.length === 0 ? (
        <p className="text-gray-500 text-sm">У этого товара нет параметров</p>
      ) : (
        <div className="space-y-3">
          <div className="grid grid-cols-12 gap-2 text-sm font-medium text-gray-500 mb-1 px-2">
            <div className="col-span-5">Название параметра</div>
            <div className="col-span-5">Значение</div>
            <div className="col-span-1 text-center" title="Ключевой параметр">
              Ключ
            </div>
            <div className="col-span-1"></div>
          </div>
          {params
            .filter(p => !p._deleted)
            .map((param, index) => (
              <div key={param.id || index} className="grid grid-cols-12 gap-2 items-center">
                <input
                  type="text"
                  value={param.name || ''}
                  onChange={e => updateParam(index, 'name', e.target.value)}
                  placeholder="Название параметра"
                  className="col-span-5 border rounded p-2"
                />
                <input
                  type="text"
                  value={param.value || ''}
                  onChange={e => updateParam(index, 'value', e.target.value)}
                  placeholder="Значение"
                  className="col-span-5 border rounded p-2"
                />
                <div className="col-span-1 flex justify-center">
                  <input
                    type="checkbox"
                    checked={param.is_key || false}
                    onChange={e => updateParam(index, 'is_key', e.target.checked)}
                    title="Отметьте для отображения в разделе ключевых характеристик"
                    className="w-5 h-5 text-primary focus:ring-primary"
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeParam(index)}
                  className="col-span-1 p-2 text-red-500 hover:text-red-700"
                >
                  <FaTrash />
                </button>
              </div>
            ))}
        </div>
      )}
      <div className="mt-4 text-sm text-gray-500">
        <p className="flex items-center">
          <span className="inline-block w-5 h-5 bg-gray-200 rounded mr-2 flex items-center justify-center">
            <FaKey size={10} className="text-gray-500" />
          </span>
          Ключевые параметры будут отображаться в выделенной секции на странице товара
        </p>
      </div>
    </div>
  );
};

export default ProductParamsEditor;
