import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { supabase, getAdminClient, convertUUIDToBigInt } from '../../supabaseClient';

const ProductForm = ({ product, isNew = false, onSuccess }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  // Define initial form state
  const initialFormState = {
    id: '',
    name: '',
    description: '',
    price: '',
    stock: 0,
    min_stock: 5,
    sku: '',
    barcode: '',
    category_id: '',
    brand_id: '',
    vendor: '',
    image: '',
    image_gallery: [],
    url: '',
    is_bestseller: false,
    is_new: false,
    is_on_sale: false,
    is_active: true,
    display_order: 0,
    original_price: '',
    discount: 0,
    status: 'active',
    weight: '',
    dimensions: '',
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    tags: [],
    params: [],
    featured_image: '',
    gallery_images: [],
    rating: 0,
    review_count: 0
  };

  const [formData, setFormData] = useState(initialFormState);
  const [imagePreview, setImagePreview] = useState('');
  const [categories, setCategories] = useState([]);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const { data, error } = await supabase.from('categories').select('*').order('name');

        if (error) throw error;
        setCategories(data);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error(t('error_loading_data', 'Ошибка при загрузке данных'));
      }
    };

    fetchData();
  }, []);

  // Load product data if editing
  useEffect(() => {
    if (product) {
      // Parse image_gallery from JSON string if it exists
      let imageGallery = [];
      if (product.image_gallery) {
        try {
          imageGallery =
            typeof product.image_gallery === 'string'
              ? JSON.parse(product.image_gallery)
              : product.image_gallery;
        } catch (error) {
          console.error('Error parsing image gallery:', error);
          imageGallery = [];
        }
      }

      setFormData({
        ...initialFormState,
        ...product,
        id: product.id,
        name: product.name || '',
        description: product.description || '',
        price: product.price || '',
        stock: product.stock || 0,
        min_stock: product.min_stock || 5,
        sku: product.sku || '',
        category_id: product.category_id || '',
        vendor: product.vendor || '',
        image: product.image || '',
        url: product.url || '',
        is_bestseller: product.is_bestseller || false,
        is_new: product.is_new || false,
        is_on_sale: product.is_on_sale || false,
        display_order: product.display_order || 0,
        original_price: product.original_price || '',
        discount: product.discount || 0,
        status: product.status || 'active',
        weight: product.weight || '',
        dimensions: product.dimensions || '',
        meta_title: product.meta_title || '',
        meta_description: product.meta_description || '',
        tags: product.tags || [],
        params: product.params || [],
        image_gallery: imageGallery
      });

      if (product.image) {
        setImagePreview(product.image);
      }
    }
  }, [product]);

  // Fetch product parameters if editing
  useEffect(() => {
    const fetchProductParams = async () => {
      if (!isNew && product?.id) {
        try {
          // Convert UUID to BigInt format for product_params table query
          const bigIntProductId = convertUUIDToBigInt(product.id);

          const { data, error } = await supabase
            .from('product_params')
            .select('*')
            .eq('product_id', bigIntProductId);

          if (error) throw error;

          if (data?.length) {
            setFormData(prev => ({
              ...prev,
              params: data.map(param => ({
                name: param.name || '',
                value: param.value || '',
                is_key: param.is_key || false
              }))
            }));
          } else {
          }
        } catch (error) {
          console.error('Error fetching product parameters:', error);
          toast.error(t('error_loading_params', 'Ошибка при загрузке параметров товара'));
        }
      }
    };

    fetchProductParams();
  }, [product?.id, isNew, t]);

  // Handle form field changes
  const handleChange = e => {
    const { name, value, type, checked } = e.target;

    // If is_on_sale is checked, ensure original_price is set to at least the current price
    if (name === 'is_on_sale' && checked && !formData.original_price) {
      setFormData(prev => ({
        ...prev,
        [name]: checked,
        original_price: prev.price // Set original price to current price if not already set
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  // Add function to handle parameter changes
  const handleParamChange = (index, field, value) => {
    setFormData(prev => {
      const updatedParams = [...(prev.params || [])];
      if (!updatedParams[index]) {
        updatedParams[index] = {};
      }
      updatedParams[index] = {
        ...updatedParams[index],
        [field]: value
      };
      return { ...prev, params: updatedParams };
    });
  };

  // Add function to add a new parameter
  const addParam = () => {
    setFormData(prev => ({
      ...prev,
      params: [...(prev.params || []), { name: '', value: '', is_key: false }]
    }));
  };

  // Add function to remove a parameter
  const removeParam = index => {
    setFormData(prev => {
      const updatedParams = [...prev.params];
      updatedParams.splice(index, 1);
      return { ...prev, params: updatedParams };
    });
  };

  // Add functions for gallery image management
  const handleGalleryImageUpload = async e => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `gallery-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `products/${fileName}`;

      // Upload to storage
      const { error: uploadError } = await supabase.storage.from('products').upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const {
        data: { publicUrl }
      } = supabase.storage.from('products').getPublicUrl(filePath);

      // Add to image gallery
      setFormData(prev => ({
        ...prev,
        image_gallery: [...(prev.image_gallery || []), publicUrl]
      }));
    } catch (error) {
      console.error('Error uploading gallery image:', error);
      toast.error(t('error_uploading_image', 'Ошибка при загрузке изображения'));
    }
  };

  const removeGalleryImage = index => {
    setFormData(prev => {
      const updatedGallery = [...prev.image_gallery];
      updatedGallery.splice(index, 1);
      return { ...prev, image_gallery: updatedGallery };
    });
  };

  // Form validation
  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error(t('name_required', 'Название товара обязательно'));
      return false;
    }
    if (!formData.price || formData.price <= 0) {
      toast.error(t('invalid_price', 'Укажите корректную цену'));
      return false;
    }
    if (!formData.category_id) {
      toast.error(t('category_required', 'Выберите категорию'));
      return false;
    }
    return true;
  };

  // Handle image upload
  const handleImageUpload = async e => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `products/${fileName}`;

      // Create temporary URL for preview
      const objectUrl = URL.createObjectURL(file);
      setImagePreview(objectUrl);

      // Upload to storage
      const { error: uploadError } = await supabase.storage.from('products').upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const {
        data: { publicUrl }
      } = supabase.storage.from('products').getPublicUrl(filePath);

      setFormData(prev => ({
        ...prev,
        image: publicUrl
      }));
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(t('error_uploading_image', 'Ошибка при загрузке изображения'));
    }
  };

  // Save product parameters
  const saveProductParams = async (productId, params) => {
    if (!params?.length) return true;

    try {
      // Convert UUID to BigInt format for product_params table operations
      const bigIntProductId = convertUUIDToBigInt(productId);

      // Use admin client for admin operations
      const clientToUse = getAdminClient();

      console.log('🔍 Saving product parameters with client:', clientToUse ? 'admin' : 'regular');

      // Delete existing parameters
      if (!isNew) {
        const { error: deleteError } = await clientToUse
          .from('product_params')
          .delete()
          .eq('product_id', bigIntProductId);

        if (deleteError) {
          console.warn('Could not delete existing parameters:', deleteError.message);
          // Continue anyway - this might be expected in development
        }
      }

      // Add new parameters
      const newParams = params.map(p => ({
        product_id: bigIntProductId, // Use converted BigInt ID here
        name: p.name || '',
        value: p.value || ''
      }));

      console.log('🔍 Attempting to insert parameters:', newParams.length, 'items');

      const { error: insertError } = await clientToUse.from('product_params').insert(newParams);

      if (insertError) {
        console.error('Product parameters save error:', insertError);

        // Check if it's a specific RLS error (code 42501)
        if (insertError.code === '42501') {
          // This is a Row Level Security error
          console.log('🔒 RLS policy error when saving parameters');

          // Instead of failing, let's show a warning but continue
          toast.warning(
            'Продукт сохранен, но параметры не удалось сохранить (ограничения безопасности). Попробуйте обновить страницу и повторить.',
            {
              autoClose: 5000
            }
          );

          return true; // Don't fail the product creation
        }

        // For other errors, show error but don't fail
        toast.error(`Ошибка при сохранении параметров: ${insertError.message}`);
        return true; // Don't fail the product creation
      }

      console.log('✅ Product parameters saved successfully');
      return true;

      return true;
    } catch (error) {
      console.error('Error saving parameters:', error);

      // In development mode, show warning but don't fail
      if (process.env.NODE_ENV === 'development') {
        toast.warning('Продукт сохранен, но параметры не удалось сохранить');
        return true;
      }

      toast.error(`Ошибка при сохранении параметров: ${error.message}`);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async e => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Prepare the data for submission
      const productData = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price) || 0,
        category_id: formData.category_id || null,
        image: formData.image || '',
        url: formData.url || '',
        is_bestseller: formData.is_bestseller || false,
        is_new: formData.is_new || false,
        is_on_sale: formData.is_on_sale || false,
        is_active: formData.is_active || false,
        original_price: parseFloat(formData.original_price) || null,
        updated_at: new Date().toISOString()
      };

      let result;
      // Use admin client for admin operations
      const clientToUse = getAdminClient();

      if (isNew) {
        const { data, error } = await clientToUse
          .from('products')
          .insert([productData])
          .select()
          .single();

        if (error) throw error;
        result = data;
      } else {
        const { data, error } = await clientToUse
          .from('products')
          .update(productData)
          .eq('id', product.id)
          .select()
          .single();

        if (error) throw error;
        result = data;
      }

      // Save parameters if any
      if (formData.params?.length > 0 && result?.id) {
        await saveProductParams(result.id, formData.params);
      }

      toast.success(
        isNew
          ? t('product_created', 'Товар успешно создан')
          : t('product_updated', 'Товар успешно обновлен')
      );

      if (onSuccess) {
        onSuccess(result);
      } else {
        navigate('/admin/products');
      }
    } catch (error) {
      console.error('Error saving product:', error);
      toast.error(`${t('error_saving_product', 'Ошибка при сохранении товара')}: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isNew ? t('new_product', 'Новый товар') : t('edit_product', 'Редактирование товара')}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Basic Info Section */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">
                {t('basic_info', 'Основная информация')}
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('product_name', 'Название товара')}*
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('description', 'Описание')}
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                    className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('sku', 'Артикул')}
                  </label>
                  <input
                    type="text"
                    name="sku"
                    value={formData.sku}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                  />
                </div>
              </div>
            </div>

            {/* Pricing Section */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">{t('pricing', 'Цены')}</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('price', 'Цена')}*
                    </label>
                    <input
                      type="number"
                      name="price"
                      value={formData.price}
                      onChange={handleChange}
                      min="0"
                      step="0.01"
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('original_price', 'Старая цена')}
                    </label>
                    <input
                      type="number"
                      name="original_price"
                      value={formData.original_price}
                      onChange={handleChange}
                      min="0"
                      step="0.01"
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    />
                  </div>
                </div>

                <div>
                  <label className="inline-flex items-center">
                    <input
                      type="checkbox"
                      name="is_on_sale"
                      checked={formData.is_on_sale}
                      onChange={handleChange}
                      className="rounded border-gray-300 text-indigo-600 shadow-sm"
                    />
                    <span className="ml-2 text-sm text-gray-600">
                      {t('on_sale', 'Товар со скидкой')}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Stock Section */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">{t('inventory', 'Склад')}</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('stock', 'Количество')}
                    </label>
                    <input
                      type="number"
                      name="stock"
                      value={formData.stock}
                      onChange={handleChange}
                      min="0"
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('min_stock', 'Мин. количество')}
                    </label>
                    <input
                      type="number"
                      name="min_stock"
                      value={formData.min_stock}
                      onChange={handleChange}
                      min="0"
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Categories & Brands Section */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">{t('category', 'Категория')}</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('category', 'Категория')}*
                  </label>
                  <select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  >
                    <option value="">{t('select_category', 'Выберите категорию')}</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('vendor', 'Поставщик')}
                  </label>
                  <input
                    type="text"
                    name="vendor"
                    value={formData.vendor}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                  />
                </div>
              </div>
            </div>

            {/* Product Status */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">{t('product_status', 'Статус товара')}</h2>

              <div className="space-y-2">
                <label className="inline-flex items-center block">
                  <input
                    type="checkbox"
                    name="is_new"
                    checked={formData.is_new}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-indigo-600 shadow-sm"
                  />
                  <span className="ml-2 text-sm text-gray-600">
                    {t('mark_as_new', 'Пометить как новинку')}
                  </span>
                </label>

                <label className="inline-flex items-center block">
                  <input
                    type="checkbox"
                    name="is_bestseller"
                    checked={formData.is_bestseller}
                    onChange={handleChange}
                    className="rounded border-gray-300 text-indigo-600 shadow-sm"
                  />
                  <span className="ml-2 text-sm text-gray-600">
                    {t('mark_as_bestseller', 'Пометить как хит продаж')}
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Image Upload Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">{t('images', 'Изображения')}</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('main_image', 'Основное изображение')}
              </label>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {imagePreview && (
                    <img
                      src={imagePreview}
                      alt={t('product_image', 'Изображение товара')}
                      className="h-24 w-24 object-cover rounded-lg"
                    />
                  )}
                </div>

                <div className="flex-grow">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-full file:border-0
                      file:text-sm file:font-semibold
                      file:bg-indigo-50 file:text-indigo-700
                      hover:file:bg-indigo-100"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('image_url', 'URL изображения')}
              </label>
              <input
                type="url"
                name="image"
                value={formData.image}
                onChange={handleChange}
                placeholder="https://"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">
            {t('additional_info', 'Дополнительная информация')}
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('product_url', 'URL товара')}
              </label>
              <input
                type="url"
                name="url"
                value={formData.url}
                onChange={handleChange}
                placeholder="https://"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('display_order', 'Порядок отображения')}
              </label>
              <input
                type="number"
                name="display_order"
                value={formData.display_order}
                onChange={handleChange}
                min="0"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              />
            </div>
          </div>
        </div>

        {/* Product Parameters Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">
            {t('product_parameters', 'Параметры товара')}
          </h2>

          <div className="space-y-4">
            {formData.params && formData.params.length > 0 ? (
              formData.params.map((param, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-2 border-b pb-4 mb-2 last:border-b-0"
                >
                  <div className="flex-grow grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">
                        {t('parameter_name', 'Название параметра')}
                      </label>
                      <input
                        type="text"
                        value={param.name || ''}
                        onChange={e => handleParamChange(index, 'name', e.target.value)}
                        className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                        placeholder="Например: Цвет"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">
                        {t('parameter_value', 'Значение')}
                      </label>
                      <input
                        type="text"
                        value={param.value || ''}
                        onChange={e => handleParamChange(index, 'value', e.target.value)}
                        className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                        placeholder="Например: Красный"
                      />
                    </div>
                  </div>
                  <div className="flex items-center pt-6">
                    <label className="inline-flex items-center mr-2">
                      <input
                        type="checkbox"
                        checked={param.is_key || false}
                        onChange={e => handleParamChange(index, 'is_key', e.target.checked)}
                        className="rounded border-gray-300 text-indigo-600 shadow-sm"
                      />
                      <span className="ml-2 text-sm text-gray-600">
                        {t('key_parameter', 'Ключевой')}
                      </span>
                    </label>
                    <button
                      type="button"
                      onClick={() => removeParam(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-sm italic">
                {t('no_parameters', 'У этого товара еще нет параметров')}
              </p>
            )}

            <button
              type="button"
              onClick={addParam}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="-ml-0.5 mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              {t('add_parameter', 'Добавить параметр')}
            </button>
          </div>
        </div>

        {/* Image Gallery Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">
            {t('image_gallery', 'Галерея изображений')}
          </h2>

          <div className="space-y-4">
            {/* Gallery grid */}
            {formData.image_gallery && formData.image_gallery.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {formData.image_gallery.map((image, index) => (
                  <div
                    key={index}
                    className="relative rounded-lg overflow-hidden border border-gray-200 bg-white p-1 shadow-sm"
                  >
                    <img
                      src={image}
                      alt={`${formData.name} - ${index + 1}`}
                      className="w-full h-32 object-cover object-center"
                    />
                    <button
                      type="button"
                      onClick={() => removeGalleryImage(index)}
                      className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-red-500 hover:text-red-700"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-sm italic">
                {t('no_gallery_images', 'В галерее ещё нет изображений')}
              </p>
            )}

            {/* Upload new image */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('add_gallery_image', 'Добавить изображение в галерею')}
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleGalleryImageUpload}
                className="w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-full file:border-0
                  file:text-sm file:font-semibold
                  file:bg-indigo-50 file:text-indigo-700
                  hover:file:bg-indigo-100"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={() => navigate('/admin/products')}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {t('cancel', 'Отмена')}
          </button>

          <button
            type="submit"
            disabled={loading}
            className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
              loading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {loading ? t('saving', 'Сохранение...') : t('save', 'Сохранить')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
