import React, { useState } from 'react';
import { getAdminClient } from '../../supabaseClient';
import { toast } from 'react-toastify';

const ProductFlagsManager = ({ product, onUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [isNew, setIsNew] = useState(product.is_new || false);
  const [isOnSale, setIsOnSale] = useState(product.is_on_sale || false);
  const [isBestseller, setIsBestseller] = useState(product.is_bestseller || false);
  const [originalPrice, setOriginalPrice] = useState(product.original_price || product.price || 0);
  const [salePrice, setSalePrice] = useState(product.price || 0);

  const handleToggleFlag = async (flag, value) => {
    setLoading(true);
    try {
      const updates = { [flag]: value };

      // If toggling on sale, ensure original_price is set
      if (flag === 'is_on_sale' && value === true) {
        // Only set original price if lower than sale price or not set
        if (!product.original_price || product.original_price <= product.price) {
          updates.original_price = originalPrice;
        }
      }

      // If toggling off sale, may need to clear original price
      if (flag === 'is_on_sale' && value === false) {
        // Ask user if they want to clear original price
        if (window.confirm('Убрать оригинальную цену товара?')) {
          updates.original_price = null;
        }
      }

      // Use admin client for admin operations
      const adminClient = getAdminClient();
      const { error } = await adminClient.from('products').update(updates).eq('id', product.id);

      if (error) throw error;

      // Update local state
      if (flag === 'is_new') setIsNew(value);
      if (flag === 'is_on_sale') setIsOnSale(value);
      if (flag === 'is_bestseller') setIsBestseller(value);

      toast.success('Товар успешно обновлен');
      if (onUpdate) onUpdate({ ...product, ...updates });
    } catch (error) {
      toast.error('Ошибка при обновлении товара: ' + error.message);
      console.error('Error updating product:', error);
    } finally {
      setLoading(false);
    }
  };

  const updatePrices = async () => {
    if (salePrice <= 0) {
      toast.error('Цена продажи должна быть больше нуля');
      return;
    }

    if (isOnSale && originalPrice <= salePrice) {
      toast.error('Оригинальная цена должна быть выше цены со скидкой');
      return;
    }

    setLoading(true);
    try {
      const updates = {
        price: salePrice,
        original_price: isOnSale ? originalPrice : null
      };

      // Use admin client for admin operations
      const adminClient = getAdminClient();
      const { error } = await adminClient.from('products').update(updates).eq('id', product.id);

      if (error) throw error;

      toast.success('Цены обновлены');
      if (onUpdate) onUpdate({ ...product, ...updates });
    } catch (error) {
      toast.error('Ошибка при обновлении цен: ' + error.message);
      console.error('Error updating prices:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow mb-4">
      <h3 className="font-bold text-lg mb-4">Специальные отметки товара</h3>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="border p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-5 w-5 text-blue-600"
                checked={isNew}
                onChange={e => handleToggleFlag('is_new', e.target.checked)}
                disabled={loading}
              />
              <span className="ml-2 text-gray-700">Новинка</span>
            </label>
            {isNew && (
              <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                Активно
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Товар будет отображаться в разделе &quot;Новые поступления&quot;
          </p>
        </div>

        <div className="border p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-5 w-5 text-blue-600"
                checked={isOnSale}
                onChange={e => handleToggleFlag('is_on_sale', e.target.checked)}
                disabled={loading}
              />
              <span className="ml-2 text-gray-700">Акция / Скидка</span>
            </label>
            {isOnSale && (
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                Активно
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Товар будет отображаться в разделе &quot;Акции&quot;
          </p>
        </div>

        <div className="border p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-5 w-5 text-blue-600"
                checked={isBestseller}
                onChange={e => handleToggleFlag('is_bestseller', e.target.checked)}
                disabled={loading}
              />
              <span className="ml-2 text-gray-700">Хит продаж</span>
            </label>
            {isBestseller && (
              <span className="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-0.5 rounded">
                Активно
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Товар будет отображаться в разделе &quot;Хиты продаж&quot;
          </p>
        </div>
      </div>

      {/* Price section - only show detailed price when isOnSale is true */}
      <div className="mt-6">
        <h4 className="font-semibold mb-3">Управление ценами</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Цена продажи</label>
            <input
              type="number"
              min="0"
              step="0.01"
              value={salePrice}
              onChange={e => setSalePrice(parseFloat(e.target.value) || 0)}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
            />
          </div>

          {isOnSale && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Оригинальная цена (до скидки)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={originalPrice}
                onChange={e => setOriginalPrice(parseFloat(e.target.value) || 0)}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3"
              />

              {originalPrice > 0 && salePrice > 0 && (
                <div className="mt-1 text-sm text-gray-500">
                  Скидка: {Math.round((1 - salePrice / originalPrice) * 100)}%
                </div>
              )}
            </div>
          )}
        </div>

        <button
          onClick={updatePrices}
          disabled={loading}
          className="mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors disabled:bg-blue-300"
        >
          {loading ? 'Обновление...' : 'Обновить цены'}
        </button>
      </div>
    </div>
  );
};

export default ProductFlagsManager;
