import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaHome, FaBoxOpen, FaListAlt, FaImages, FaUsers, FaCog } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const AdminSidebar = () => {
  const location = useLocation();
  const { t } = useTranslation();

  const isActive = path => location.pathname === path || location.pathname.startsWith(`${path}/`);

  const menuItems = [
    { path: '/admin', icon: <FaHome size={20} />, label: t('dashboard', 'Главная') },
    { path: '/admin/products', icon: <FaBoxOpen size={20} />, label: t('products', 'Товары') },
    {
      path: '/admin/categories',
      icon: <FaListAlt size={20} />,
      label: t('categories', 'Категории')
    },
    { path: '/admin/banners', icon: <FaImages size={20} />, label: t('banners', 'Баннеры') },
    { path: '/admin/users', icon: <FaUsers size={20} />, label: t('users', 'Пользователи') },
    { path: '/admin/settings', icon: <FaCog size={20} />, label: t('settings', 'Настройки') }
  ];

  return (
    <div className="bg-gray-800 text-white w-64 min-h-screen fixed left-0 top-0 shadow-lg">
      <div className="p-4 border-b border-gray-700">
        <h2 className="text-xl font-semibold">{t('admin_panel', 'Панель администратора')}</h2>
      </div>
      <nav className="mt-4">
        <ul>
          {menuItems.map((item, index) => (
            <li key={index}>
              <Link
                to={item.path}
                className={`flex items-center px-4 py-3 hover:bg-gray-700 transition-colors ${
                  isActive(item.path) ? 'bg-gray-700' : ''
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      <div className="mt-auto p-4 border-t border-gray-700 absolute bottom-0 w-full">
        <Link to="/" className="text-gray-400 hover:text-white">
          {t('back_to_site', 'Вернуться на сайт')}
        </Link>
      </div>
    </div>
  );
};

export const AdminLayout = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* AdminSidebar теперь рендерится в AdminDashboard.js */}
      {/* <AdminSidebar /> */}
      {/* Убираем ml-64, так как сайдбар больше не является частью этого лейаута напрямую */}
      <div className="p-6">{children}</div>
    </div>
  );
};

export default AdminLayout;
