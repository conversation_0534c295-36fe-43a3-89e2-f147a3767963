import React from 'react';
import { Link } from 'react-router-dom';
import { FaEdit, FaTrash } from 'react-icons/fa';
import { formatPrice } from '../../utils/formatters';

const ProductTable = ({
  products,
  isLoading,
  error,
  pagination,
  onPageChange,
  onPageSizeChange,
  onDeleteClick
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center py-10">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded my-4">
        <p>{error}</p>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className="bg-gray-100 border border-gray-200 text-gray-700 px-4 py-8 rounded my-4 text-center">
        <p>No products found. Try adjusting your filters or add new products.</p>
      </div>
    );
  }

  const totalPages = Math.ceil(pagination.total / pagination.pageSize);

  const renderPagination = () => {
    return (
      <div className="flex items-center justify-between mt-4">
        <div>
          <span className="text-sm text-gray-700">
            Showing {(pagination.page - 1) * pagination.pageSize + 1} to{' '}
            {Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
            {pagination.total} entries
          </span>
          <select
            className="ml-2 border rounded px-2 py-1 text-sm"
            value={pagination.pageSize}
            onChange={e => onPageSizeChange(Number(e.target.value))}
          >
            {[10, 25, 50, 100].map(size => (
              <option key={size} value={size}>
                {size} per page
              </option>
            ))}
          </select>
        </div>

        <div className="flex space-x-1">
          <button
            onClick={() => onPageChange(1)}
            disabled={pagination.page === 1}
            className="px-3 py-1 border rounded text-sm disabled:opacity-50"
          >
            First
          </button>
          <button
            onClick={() => onPageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            className="px-3 py-1 border rounded text-sm disabled:opacity-50"
          >
            Prev
          </button>

          {/* Page numbers */}
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (pagination.page <= 3) {
              pageNum = i + 1;
            } else if (pagination.page >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = pagination.page - 2 + i;
            }

            return (
              <button
                key={i}
                onClick={() => onPageChange(pageNum)}
                className={`px-3 py-1 border rounded text-sm ${
                  pagination.page === pageNum ? 'bg-blue-500 text-white' : ''
                }`}
              >
                {pageNum}
              </button>
            );
          })}

          <button
            onClick={() => onPageChange(pagination.page + 1)}
            disabled={pagination.page === totalPages}
            className="px-3 py-1 border rounded text-sm disabled:opacity-50"
          >
            Next
          </button>
          <button
            onClick={() => onPageChange(totalPages)}
            disabled={pagination.page === totalPages}
            className="px-3 py-1 border rounded text-sm disabled:opacity-50"
          >
            Last
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead>
          <tr className="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
            <th className="py-3 px-6 text-left">Img</th>
            <th className="py-3 px-6 text-left">Name</th>
            <th className="py-3 px-6 text-left">Price</th>
            <th className="py-3 px-6 text-left">Stock</th>
            <th className="py-3 px-6 text-left">Category</th>
            <th className="py-3 px-6 text-left">Actions</th>
          </tr>
        </thead>
        <tbody className="text-gray-600 text-sm">
          {products.map(product => (
            <tr key={product.id} className="border-b border-gray-200 hover:bg-gray-50">
              <td className="py-3 px-6 text-left">
                <img
                  src={product.image || '/placeholder.png'}
                  alt={product.name}
                  className="w-16 h-16 object-cover rounded"
                  onError={e => {
                    e.target.onerror = null;
                    e.target.src = '/placeholder.png';
                  }}
                />
              </td>
              <td className="py-3 px-6 text-left">{product.name}</td>
              <td className="py-3 px-6 text-left">{formatPrice(product.price)}</td>
              <td className="py-3 px-6 text-left">
                <span
                  className={`px-2 py-1 rounded text-xs ${
                    product.stock > product.min_stock
                      ? 'bg-green-100 text-green-800'
                      : product.stock > 0
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                  }`}
                >
                  {product.stock}
                </span>
              </td>
              <td className="py-3 px-6 text-left">{product.categories?.name || 'Uncategorized'}</td>
              <td className="py-3 px-6 text-left flex">
                <Link
                  to={`/admin/products/edit/${product.id}`} // Must match the route pattern
                  className="text-blue-600 hover:text-blue-900 mr-3"
                >
                  <FaEdit />
                </Link>
                <button
                  onClick={() => onDeleteClick(product)}
                  className="text-red-600 hover:text-red-900"
                >
                  <FaTrash />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {renderPagination()}
    </div>
  );
};

export default ProductTable;
