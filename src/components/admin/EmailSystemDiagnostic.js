import React, { useState } from 'react';
import { EmailService } from '../../services/emailService';
import { MockEmailService } from '../../services/mockEmailService';
import {
  FaEnvelope,
  FaCheckCircle,
  FaTimesCircle,
  FaExclamationTriangle,
  FaCog
} from 'react-icons/fa';
import { toast } from 'react-toastify';

const EmailSystemDiagnostic = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState(null);
  const [testEmail, setTestEmail] = useState('<EMAIL>');

  const runDiagnostics = async () => {
    setTesting(true);
    setResults(null);

    const diagnosticResults = {
      emailServiceAvailable: false,
      mockServiceAvailable: false,
      edgeFunctionWorking: false,
      testEmailSent: false,
      errors: []
    };

    try {
      // Test 1: Check if EmailService works
      console.log('🧪 Testing EmailService...');
      try {
        await EmailService.sendTestEmail?.(testEmail, 'Diagnostic Test Email');
        diagnosticResults.emailServiceAvailable = true;
        diagnosticResults.edgeFunctionWorking = true;
        console.log('✅ EmailService works');
      } catch (error) {
        console.warn('⚠️ EmailService failed:', error.message);
        diagnosticResults.errors.push(`EmailService: ${error.message}`);
      }

      // Test 2: Check if MockEmailService works
      console.log('🧪 Testing MockEmailService...');
      try {
        await MockEmailService.sendTestEmail(testEmail, 'Mock Diagnostic Test Email');
        diagnosticResults.mockServiceAvailable = true;
        console.log('✅ MockEmailService works');
      } catch (error) {
        console.warn('⚠️ MockEmailService failed:', error.message);
        diagnosticResults.errors.push(`MockEmailService: ${error.message}`);
      }

      // Test 3: Try sending actual test email
      console.log('🧪 Sending test email...');
      try {
        if (diagnosticResults.emailServiceAvailable) {
          await EmailService.sendTestEmail?.(testEmail, 'Test Email from Admin Panel');
          diagnosticResults.testEmailSent = true;
        } else if (diagnosticResults.mockServiceAvailable) {
          await MockEmailService.sendTestEmail(testEmail, 'Mock Test Email from Admin Panel');
          diagnosticResults.testEmailSent = true;
        }
        console.log('✅ Test email sent');
      } catch (error) {
        console.warn('⚠️ Test email failed:', error.message);
        diagnosticResults.errors.push(`Test email: ${error.message}`);
      }
    } catch (error) {
      console.error('❌ Diagnostic error:', error);
      diagnosticResults.errors.push(`General error: ${error.message}`);
    }

    setResults(diagnosticResults);
    setTesting(false);

    // Show summary toast
    if (diagnosticResults.emailServiceAvailable) {
      toast.success('✅ Email система работает корректно!');
    } else if (diagnosticResults.mockServiceAvailable) {
      toast.warning('⚠️ Email работает в тестовом режиме');
    } else {
      toast.error('❌ Email система не работает');
    }
  };

  const getStatusIcon = status => {
    if (status === true) return <FaCheckCircle className="text-green-500" />;
    if (status === false) return <FaTimesCircle className="text-red-500" />;
    return <FaExclamationTriangle className="text-yellow-500" />;
  };

  const getStatusText = status => {
    if (status === true) return 'Работает';
    if (status === false) return 'Не работает';
    return 'Неизвестно';
  };

  const getStatusColor = status => {
    if (status === true) return 'text-green-600';
    if (status === false) return 'text-red-600';
    return 'text-yellow-600';
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center mb-4">
        <FaEnvelope className="text-blue-500 text-xl mr-3" />
        <h3 className="text-lg font-medium">Email System Diagnostic</h3>
      </div>

      <div className="space-y-4">
        {/* Test Email Input */}
        <div>
          <label htmlFor="testEmail" className="block text-sm font-medium text-gray-700 mb-1">
            Email для тестирования:
          </label>
          <input
            type="email"
            id="testEmail"
            value={testEmail}
            onChange={e => setTestEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>

        {/* Run Diagnostics Button */}
        <button
          onClick={runDiagnostics}
          disabled={testing}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
        >
          {testing ? (
            <>
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              Тестирование...
            </>
          ) : (
            <>
              <FaCog className="mr-2" />
              Запустить диагностику
            </>
          )}
        </button>

        {/* Results */}
        {results && (
          <div className="mt-6 space-y-3">
            <h4 className="font-medium text-gray-800">Результаты диагностики:</h4>

            {/* Email Service Status */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm">Supabase Edge Function (EmailService)</span>
              <div className="flex items-center">
                {getStatusIcon(results.emailServiceAvailable)}
                <span className={`ml-2 text-sm ${getStatusColor(results.emailServiceAvailable)}`}>
                  {getStatusText(results.emailServiceAvailable)}
                </span>
              </div>
            </div>

            {/* Mock Service Status */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm">Mock Email Service</span>
              <div className="flex items-center">
                {getStatusIcon(results.mockServiceAvailable)}
                <span className={`ml-2 text-sm ${getStatusColor(results.mockServiceAvailable)}`}>
                  {getStatusText(results.mockServiceAvailable)}
                </span>
              </div>
            </div>

            {/* Test Email Status */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm">Тестовый email отправлен</span>
              <div className="flex items-center">
                {getStatusIcon(results.testEmailSent)}
                <span className={`ml-2 text-sm ${getStatusColor(results.testEmailSent)}`}>
                  {getStatusText(results.testEmailSent)}
                </span>
              </div>
            </div>

            {/* Errors */}
            {results.errors.length > 0 && (
              <div className="mt-4">
                <h5 className="font-medium text-red-600 mb-2">Ошибки:</h5>
                <ul className="space-y-1">
                  {results.errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Recommendations */}
            <div className="mt-4 p-4 bg-blue-50 rounded">
              <h5 className="font-medium text-blue-800 mb-2">Рекомендации:</h5>
              {results.emailServiceAvailable ? (
                <p className="text-sm text-blue-700">
                  ✅ Email система работает корректно! Все уведомления будут отправляться.
                </p>
              ) : results.mockServiceAvailable ? (
                <div className="text-sm text-blue-700">
                  <p>⚠️ Работает только Mock Email Service.</p>
                  <p className="mt-1">
                    Для полной функциональности настройте Resend API в Supabase Edge Functions.
                  </p>
                  <p className="mt-1">
                    📋 Инструкции: <code>EMAIL_SETUP_INSTRUCTIONS.md</code>
                  </p>
                </div>
              ) : (
                <div className="text-sm text-red-700">
                  <p>❌ Email система не работает.</p>
                  <p className="mt-1">Проверьте настройки Supabase и Resend API.</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailSystemDiagnostic;
