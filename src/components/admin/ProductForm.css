.product-form-container {
  max-width: 1400px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.admin-header {
  background-color: #fff;
  padding: 1rem 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #212529;
}

.form-label.required:after {
  content: '*';
  color: #dc3545;
  margin-left: 4px;
}

.form-control,
.form-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-control-lg {
  height: 48px;
  font-size: 1rem;
}

.card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  border-bottom: 1px solid #e0e0e0;
}

.nav-tabs .nav-link {
  color: #666;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border: none;
  border-bottom: 2px solid transparent;
}

.nav-tabs .nav-link:hover {
  color: #333;
  background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
  color: #0d6efd;
  border-bottom: 2px solid #0d6efd;
  font-weight: 700;
}

.product-image-card {
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  overflow: hidden;
}

.product-image-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}

.table-responsive {
  border-radius: 0.5rem;
  overflow: hidden;
}

textarea.form-control {
  min-height: 100px;
}

/* Загрузка изображений */
.upload-zone {
  background-color: #f8f9fa;
  border: 2px dashed #ced4da;
  border-radius: 8px;
  padding: 30px;
  transition: all 0.3s;
  cursor: pointer;
}

.upload-zone:hover {
  border-color: #0d6efd;
  background-color: #f1f8ff;
}

.upload-icon {
  color: #6c757d;
  transition: color 0.3s;
}

.upload-zone:hover .upload-icon {
  color: #0d6efd;
}

/* Карточка изображения */
.image-card {
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.image-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-card .btn-danger {
  opacity: 0;
  transition: opacity 0.2s;
}

.image-card:hover .btn-danger {
  opacity: 1;
}

/* Таблица характеристик */
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
}

.table {
  margin-bottom: 0;
}

.table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.table td .form-control {
  background-color: transparent;
}

.table td .form-control:focus {
  background-color: #fff;
}

/* Предпросмотр загружаемого изображения */
.preview-image {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.preview-image img {
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* Лоадеры */
.loading-spinner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* Сообщения */
.alert {
  border-radius: 8px;
  padding: 12px 20px;
  margin-bottom: 20px;
}

.alert-success {
  background-color: #d1e7dd;
  border-color: #badbcc;
  color: #0f5132;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
}

/* Адаптивность */
@media (max-width: 992px) {
  .admin-topbar {
    position: static;
  }

  .admin-content {
    padding: 15px 0;
  }

  .admin-tabs .nav-link {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .admin-page-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .admin-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    white-space: nowrap;
  }

  .admin-tabs .nav-link {
    padding: 10px;
  }

  .admin-content {
    padding: 10px 0;
  }

  .admin-card .card-body {
    padding: 15px;
  }

  .form-control-lg {
    height: 42px;
  }
}

/* Улучшенные переключатели */
.form-check-input {
  width: 2em;
  height: 1em;
  margin-top: 0.25em;
}

.form-switch .form-check-input {
  width: 3em;
  margin-left: -3.5em;
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* Кнопки */
.btn {
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
}

.btn-outline-primary:hover {
  background-color: #0d6efd;
  color: #fff;
}

.btn-danger,
.btn-outline-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.btn-outline-danger {
  background-color: transparent;
  color: #dc3545;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  color: #fff;
}

/* Вспомогательные классы */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
