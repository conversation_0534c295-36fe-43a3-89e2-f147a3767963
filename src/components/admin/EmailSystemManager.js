import React, { useState, useEffect } from 'react';
import { EmailService } from '../../services/emailService';
import {
  FaEnvelope,
  FaCheckCircle,
  FaExclamationTriangle,
  Fa<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON>ye,
  FaPaperPlane
} from 'react-icons/fa';
import { toast } from 'react-toastify';

const EmailSystemManager = () => {
  const [loading, setLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [emailLogs, setEmailLogs] = useState([]);
  const [systemStatus, setSystemStatus] = useState(null);
  const [selectedLog, setSelectedLog] = useState(null);
  const [showLogDetails, setShowLogDetails] = useState(false);

  // Initialize and check email system status
  useEffect(() => {
    checkSystemStatus();
    fetchEmailLogs();
  }, []);

  const checkSystemStatus = async () => {
    try {
      const status = await EmailService.initializeEmailSystem();
      setSystemStatus(status);
    } catch (error) {
      console.error('Error checking system status:', error);
      setSystemStatus({ success: false, message: 'Error checking system status' });
    }
  };

  const fetchEmailLogs = async () => {
    try {
      const logs = await EmailService.getEmailLogs({ limit: 50 });
      setEmailLogs(logs);
    } catch (error) {
      console.error('Error fetching email logs:', error);
      toast.error('Ошибка при загрузке логов email');
    }
  };

  const handleSendTestEmail = async () => {
    if (!testEmail) {
      toast.error('Введите email адрес для тестирования');
      return;
    }

    setLoading(true);
    try {
      await EmailService.sendTestEmail(testEmail);
      toast.success('Тестовое письмо отправлено успешно!');
      setTestEmail('');
      fetchEmailLogs(); // Refresh logs
    } catch (error) {
      console.error('Error sending test email:', error);
      toast.error('Ошибка при отправке тестового письма: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = status => {
    switch (status) {
      case 'sent':
        return <FaCheckCircle className="text-green-500" />;
      case 'failed':
        return <FaExclamationTriangle className="text-red-500" />;
      case 'pending':
        return <FaSpinner className="text-yellow-500 animate-spin" />;
      default:
        return <FaEnvelope className="text-gray-500" />;
    }
  };

  const getStatusBadgeClass = status => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmailTypeLabel = type => {
    switch (type) {
      case 'order_confirmation':
        return 'Подтверждение заказа';
      case 'status_update':
        return 'Обновление статуса';
      default:
        return type;
    }
  };

  const formatDate = dateString => {
    return new Date(dateString).toLocaleString('ru-RU');
  };

  const handleViewLogDetails = log => {
    setSelectedLog(log);
    setShowLogDetails(true);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          <FaEnvelope className="inline mr-2" />
          Управление Email Уведомлениями
        </h2>
        <p className="text-gray-600">
          Система автоматических email уведомлений через Supabase Edge Functions
        </p>
      </div>

      {/* System Status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Статус системы</h3>
        {systemStatus && (
          <div
            className={`p-4 rounded-lg flex items-center ${
              systemStatus.success
                ? 'bg-green-50 text-green-800 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}
          >
            {systemStatus.success ? (
              <FaCheckCircle className="mr-2" />
            ) : (
              <FaExclamationTriangle className="mr-2" />
            )}
            {systemStatus.message}
          </div>
        )}
      </div>

      {/* Test Email Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Тестирование Email</h3>
        <div className="flex gap-3">
          <input
            type="email"
            value={testEmail}
            onChange={e => setTestEmail(e.target.value)}
            placeholder="Введите email для тестирования"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleSendTestEmail}
            disabled={loading || !testEmail}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
          >
            {loading ? (
              <FaSpinner className="animate-spin mr-2" />
            ) : (
              <FaPaperPlane className="mr-2" />
            )}
            Отправить тест
          </button>
        </div>
        <p className="text-sm text-gray-500 mt-2">
          Отправит тестовое письмо подтверждения заказа на указанный email
        </p>
      </div>

      {/* Email Logs */}
      <div>
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold">Логи Email Отправок</h3>
          <button
            onClick={fetchEmailLogs}
            className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
          >
            Обновить
          </button>
        </div>

        {emailLogs.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FaEnvelope className="mx-auto text-4xl mb-3 opacity-50" />
            <p>Пока нет отправленных писем</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Статус
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Тип письма
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Получатель
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Тема
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Дата отправки
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Действия
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {emailLogs.map(log => (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(log.status)}
                        <span
                          className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(log.status)}`}
                        >
                          {log.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {getEmailTypeLabel(log.email_type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.recipient}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                      {log.subject}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.sent_at ? formatDate(log.sent_at) : formatDate(log.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button
                        onClick={() => handleViewLogDetails(log)}
                        className="text-blue-600 hover:text-blue-900 flex items-center"
                      >
                        <FaEye className="mr-1" />
                        Подробнее
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Log Details Modal */}
      {showLogDetails && selectedLog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-2xl w-full max-h-96 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Детали отправки email</h3>
              <button
                onClick={() => setShowLogDetails(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="space-y-3">
              <div>
                <strong>ID:</strong> {selectedLog.id}
              </div>
              <div>
                <strong>Заказ:</strong> {selectedLog.order_id || 'Не связано с заказом'}
              </div>
              <div>
                <strong>Тип письма:</strong> {getEmailTypeLabel(selectedLog.email_type)}
              </div>
              <div>
                <strong>Получатель:</strong> {selectedLog.recipient}
              </div>
              <div>
                <strong>Тема:</strong> {selectedLog.subject}
              </div>
              <div>
                <strong>Статус:</strong>
                <span
                  className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusBadgeClass(selectedLog.status)}`}
                >
                  {selectedLog.status}
                </span>
              </div>
              {selectedLog.sent_at && (
                <div>
                  <strong>Дата отправки:</strong> {formatDate(selectedLog.sent_at)}
                </div>
              )}
              {selectedLog.error_message && (
                <div>
                  <strong>Ошибка:</strong>
                  <span className="text-red-600 ml-2">{selectedLog.error_message}</span>
                </div>
              )}
              {selectedLog.external_id && (
                <div>
                  <strong>External ID:</strong> {selectedLog.external_id}
                </div>
              )}
              <div>
                <strong>Создано:</strong> {formatDate(selectedLog.created_at)}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailSystemManager;
