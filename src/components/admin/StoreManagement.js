import { useState } from 'react';
import { resetAndPopulateStore, clearStore } from '../../utils/xmlParser'; // Removed unused import
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { fetchProducts } from '../../store/slices/productsSlice';
import { supabase } from '../../supabaseClient';

const StoreManagement = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(null);
  const [customXmlUrl, setCustomXmlUrl] = useState('');
  const [importStats, setImportStats] = useState(null);
  const dispatch = useDispatch();

  const handlePopulateStore = async () => {
    setLoading(true);
    setProgress({ status: 'Очистка данных магазина...', percentage: 5 });
    try {
      const result = await resetAndPopulateStore();
      setProgress({ status: 'Обновление витрины...', percentage: 90 });
      await dispatch(fetchProducts());
      setImportStats(result);
      setProgress({ status: 'Завершено!', percentage: 100 });
      toast.success(
        t(
          'store_populated',
          `Магазин успешно заполнен! Категорий: ${result.categories}, Товаров: ${result.products}, Параметров: ${result.params}`
        )
      );
    } catch (error) {
      console.error('Error populating store:', error);
      toast.error(t('error_populating_store', 'Ошибка при заполнении магазина.'));
      setProgress(null);
    } finally {
      setLoading(false);
      setTimeout(() => setProgress(null), 3000);
    }
  };

  const handleClearStore = async () => {
    if (
      !window.confirm(
        t(
          'confirm_clear_store',
          'Вы уверены, что хотите очистить магазин? Все товары и категории будут удалены.'
        )
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      await clearStore();
      await dispatch(fetchProducts());
      setImportStats(null);
      toast.success(t('store_cleared', 'Магазин успешно очищен'));
    } catch (error) {
      console.error('Error clearing store:', error);
      toast.error(t('error_clearing_store', 'Ошибка при очистке магазина'));
    } finally {
      setLoading(false);
    }
  };

  const handleExportProductParams = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.from('product_params').select('name').limit(1000);

      if (error) throw error;

      // Get unique parameter names
      const uniqueParams = [...new Set(data.map(item => item.name))];

      // Create CSV content
      const csvContent = 'data:text/csv;charset=utf-8,' + uniqueParams.join('\n');

      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', 'product_parameters.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(t('params_exported', 'Параметры товаров экспортированы!'));
    } catch (error) {
      console.error('Error exporting parameters:', error);
      toast.error(t('error_exporting_params', 'Ошибка при экспорте параметров'));
    } finally {
      setLoading(false);
    }
  };

  // Define the missing functions referenced in the UI
  const handleCustomImport = () => {
    // Placeholder for custom import functionality
    toast.info(t('feature_coming_soon', 'Эта функция будет доступна в ближайшее время'));
  };

  const handleUpdateFeaturedProducts = () => {
    // Placeholder for featured products update functionality
    toast.info(t('feature_coming_soon', 'Эта функция будет доступна в ближайшее время'));
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold mb-4">
          {t('store_management', 'Управление магазином')}
        </h2>

        {progress && (
          <div className="mb-6">
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div
                className="bg-primary h-4 rounded-full transition-all duration-500"
                style={{ width: `${progress.percentage}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600">{progress.status}</p>
          </div>
        )}

        {importStats && (
          <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <h3 className="font-medium text-lg mb-2">
              {t('import_statistics', 'Статистика импорта')}
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-white p-3 rounded shadow-sm">
                <p className="text-gray-500">{t('categories', 'Категории')}</p>
                <p className="text-2xl font-bold">{importStats.categories}</p>
              </div>
              <div className="bg-white p-3 rounded shadow-sm">
                <p className="text-gray-500">{t('products', 'Товары')}</p>
                <p className="text-2xl font-bold">{importStats.products}</p>
              </div>
              <div className="bg-white p-3 rounded shadow-sm">
                <p className="text-gray-500">{t('parameters', 'Параметры')}</p>
                <p className="text-2xl font-bold">{importStats.params || 0}</p>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
          <div className="flex flex-col gap-4 p-4 border rounded-md">
            <h3 className="font-medium">{t('default_import', 'Стандартный импорт')}</h3>
            <button onClick={handlePopulateStore} className="btn-primary" disabled={loading}>
              {loading ? t('loading', 'Загрузка...') : t('populate_store', 'Заполнить магазин')}
            </button>
          </div>

          <div className="flex flex-col gap-4 p-4 border rounded-md">
            <h3 className="font-medium">{t('custom_import', 'Пользовательский импорт')}</h3>
            <div className="flex gap-2">
              <input
                type="text"
                className="flex-grow p-2 border rounded"
                placeholder={t('enter_xml_url', 'Введите URL XML файла')}
                value={customXmlUrl}
                onChange={e => setCustomXmlUrl(e.target.value)}
                disabled={loading}
              />
              <button
                onClick={handleCustomImport}
                className="btn-secondary"
                disabled={loading || !customXmlUrl}
              >
                {t('import', 'Импорт')}
              </button>
            </div>
          </div>

          <div className="flex flex-wrap gap-4 p-4 border rounded-md">
            <h3 className="w-full font-medium mb-2">{t('other_actions', 'Другие действия')}</h3>
            <button
              onClick={handleUpdateFeaturedProducts}
              className="btn-secondary"
              disabled={loading}
            >
              {t('update_featured', 'Обновить избранные товары')}
            </button>
            <button onClick={handleExportProductParams} className="btn-outline" disabled={loading}>
              {t('export_parameters', 'Экспорт параметров товаров')}
            </button>
            <button
              onClick={handleClearStore}
              className="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded"
              disabled={loading}
            >
              {t('clear_store', 'Очистить магазин')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreManagement;
