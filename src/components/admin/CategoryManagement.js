import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';
import { FaEdit, FaTrash, FaChevronRight, FaChevronDown, FaBoxOpen } from 'react-icons/fa';

const CategoryManagement = () => {
  const { t } = useTranslation();
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [categoryForm, setCategoryForm] = useState({
    id: null,
    name: '',
    parent_id: null,
    image: ''
  });
  const [formMode, setFormMode] = useState('add'); // 'add' или 'edit'
  const [expandedCategories, setExpandedCategories] = useState({});
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categoryProducts, setCategoryProducts] = useState([]);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [productSearchQuery, setProductSearchQuery] = useState('');
  const [categoryProductCounts, setCategoryProductCounts] = useState({});

  // Define fetchCategories and fetchCategoryProductCounts before using them in useEffect
  const fetchCategories = useCallback(async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.from('categories').select('*').order('name');
      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error(t('error_fetching_categories', 'Ошибка при загрузке категорий'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  const fetchCategoryProductCounts = useCallback(async () => {
    try {
      const { data, error } = await supabase.from('products').select('category_id');

      if (error) throw error;

      const counts = {};
      data.forEach(product => {
        if (product.category_id) {
          counts[product.category_id] = (counts[product.category_id] || 0) + 1;
        }
      });

      // Calculate aggregated counts for parent categories
      const aggregatedCounts = { ...counts };

      // Function to calculate total products in a category and all its subcategories
      const calculateTotalProducts = categoryId => {
        // Start with direct products in this category
        let total = counts[categoryId] || 0;

        // Find all subcategories
        const subcategories = categories.filter(c => c.parent_id === categoryId);

        // Add products from each subcategory
        for (const subcategory of subcategories) {
          total += calculateTotalProducts(subcategory.id);
        }

        return total;
      };

      // Calculate aggregated counts for all categories
      for (const category of categories) {
        if (!category.parent_id) {
          // Only for root categories
          aggregatedCounts[category.id] = calculateTotalProducts(category.id);
        }
      }

      setCategoryProductCounts(aggregatedCounts);
    } catch (error) {
      console.error('Error fetching product counts:', error);
    }
  }, [categories]);

  // Now use the callbacks in useEffect
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Separate useEffect to ensure categories are loaded before counting products
  useEffect(() => {
    if (categories.length > 0) {
      fetchCategoryProductCounts();
    }
  }, [categories, fetchCategoryProductCounts]);

  const fetchCategoryProducts = useCallback(
    async categoryId => {
      setLoadingProducts(true);
      try {
        let query = supabase
          .from('products')
          .select('*')
          .eq('category_id', categoryId)
          .order('name');

        if (productSearchQuery) {
          query = query.ilike('name', `%${productSearchQuery}%`);
        }

        const { data, error } = await query;
        if (error) throw error;
        setCategoryProducts(data || []);
      } catch (error) {
        console.error('Error fetching category products:', error);
        toast.error(t('error_fetching_products', 'Ошибка при загрузке товаров категории'));
      } finally {
        setLoadingProducts(false);
      }
    },
    [productSearchQuery, t]
  );

  useEffect(() => {
    if (selectedCategory) {
      fetchCategoryProducts(selectedCategory);
    }
  }, [selectedCategory, productSearchQuery, fetchCategoryProducts]);

  const handleSubmit = async e => {
    e.preventDefault();

    try {
      const categoryData = {
        name: categoryForm.name,
        parent_id: categoryForm.parent_id || null,
        image: categoryForm.image || null
      };

      if (formMode === 'add') {
        const { data: createdCategory, error } = await supabase
          .from('categories')
          .insert([categoryData])
          .select();
        if (error) throw error;
        toast.success(t('category_added', 'Категория успешно добавлена'));
      } else {
        const { error } = await supabase
          .from('categories')
          .update(categoryData)
          .eq('id', categoryForm.id);
        if (error) throw error;
        toast.success(t('category_updated', 'Категория успешно обновлена'));
      }

      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('Error saving category:', error);
      toast.error(t('error_saving_category', 'Ошибка при сохранении категории'));
    }
  };

  const handleEdit = category => {
    setCategoryForm({
      id: category.id,
      name: category.name,
      parent_id: category.parent_id,
      image: category.image
    });
    setFormMode('edit');
  };

  const handleDelete = async categoryId => {
    if (
      window.confirm(t('confirm_delete_category', 'Вы уверены, что хотите удалить эту категорию?'))
    ) {
      try {
        // Сначала проверяем, есть ли товары в этой категории
        const { count: productCount, error: countError } = await supabase
          .from('products')
          .select('id', { count: 'exact', head: true })
          .eq('category_id', categoryId);

        if (countError) throw countError;

        if (productCount > 0) {
          if (
            !window.confirm(
              t(
                'category_has_products',
                `В этой категории есть ${productCount} товаров. Вы уверены, что хотите удалить категорию?`
              )
            )
          ) {
            return;
          }
        }

        // Проверяем дочерние категории
        const { data: childCategories, error: childError } = await supabase
          .from('categories')
          .select('id')
          .eq('parent_id', categoryId);

        if (childError) throw childError;

        if (childCategories && childCategories.length > 0) {
          if (
            !window.confirm(
              t(
                'category_has_children',
                'У этой категории есть подкатегории. Вы уверены, что хотите удалить категорию?'
              )
            )
          ) {
            return;
          }
          // Обновляем дочерние категории, убирая parent_id
          await supabase.from('categories').update({ parent_id: null }).eq('parent_id', categoryId);
        }

        // Теперь удаляем категорию
        const { error } = await supabase.from('categories').delete().eq('id', categoryId);
        if (error) throw error;

        toast.success(t('category_deleted', 'Категория успешно удалена'));
        fetchCategories();
        if (selectedCategory === categoryId) {
          setSelectedCategory(null);
          setCategoryProducts([]);
        }
      } catch (error) {
        console.error('Error deleting category:', error);
        toast.error(t('error_deleting_category', 'Ошибка при удалении категории'));
      }
    }
  };

  const resetForm = () => {
    setCategoryForm({
      id: null,
      name: '',
      parent_id: null,
      image: ''
    });
    setFormMode('add');
  };

  const toggleCategory = categoryId => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const handleSelectCategory = categoryId => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
    setProductSearchQuery('');
  };

  // Строим иерархическое дерево категорий
  const buildCategoryTree = (parentId = null, level = 0) => {
    return categories
      .filter(category => category.parent_id === parentId)
      .map(category => {
        const hasChildren = categories.some(c => c.parent_id === category.id);
        const isExpanded = expandedCategories[category.id];
        const isSelected = selectedCategory === category.id;
        const productCount = categoryProductCounts[category.id] || 0;

        return (
          <div key={category.id} style={{ marginLeft: `${level * 20}px` }}>
            <div
              className={`flex items-center py-2 px-2 border-b ${isSelected ? 'bg-blue-50' : ''}`}
            >
              {hasChildren ? (
                <button
                  onClick={() => toggleCategory(category.id)}
                  className="mr-2 text-gray-600 focus:outline-none"
                >
                  {isExpanded ? <FaChevronDown size={14} /> : <FaChevronRight size={14} />}
                </button>
              ) : (
                <span className="mr-2 w-[14px]"></span>
              )}

              <div
                className="flex-1 cursor-pointer flex items-center"
                onClick={() => handleSelectCategory(category.id)}
              >
                <span className="font-medium">{category.name}</span>
                {category.image && (
                  <img
                    src={category.image}
                    alt={category.name}
                    className="ml-2 w-6 h-6 object-cover rounded"
                  />
                )}
                <span className="ml-2 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {productCount}
                </span>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => handleEdit(category)}
                  className="text-blue-600 hover:text-blue-800"
                  title={t('edit', 'Редактировать')}
                >
                  <FaEdit />
                </button>
                <button
                  onClick={() => handleDelete(category.id)}
                  className="text-red-600 hover:text-red-800"
                  title={t('delete', 'Удалить')}
                >
                  <FaTrash />
                </button>
                <button
                  onClick={() => handleSelectCategory(category.id)}
                  className="text-green-600 hover:text-green-800"
                  title={t('view_products', 'Просмотр товаров')}
                >
                  <FaBoxOpen />
                </button>
              </div>
            </div>

            {hasChildren && isExpanded && buildCategoryTree(category.id, level + 1)}
          </div>
        );
      });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Левая колонка: категории и форма создания/редактирования */}
      <div>
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="p-4 border-b">
            <h2 className="font-semibold text-lg">{t('categories', 'Категории')}</h2>
          </div>

          <div className="p-2 max-h-[500px] overflow-y-auto">
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : categories.length === 0 ? (
              <p className="text-center py-4 text-gray-500">
                {t('no_categories', 'Нет категорий')}
              </p>
            ) : (
              <div className="border rounded">{buildCategoryTree()}</div>
            )}
          </div>
        </div>

        {/* Форма добавления/редактирования категории */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b">
            <h2 className="font-semibold text-lg">
              {formMode === 'add'
                ? t('add_category', 'Добавление категории')
                : t('edit_category', 'Редактирование категории')}
            </h2>
          </div>

          <form onSubmit={handleSubmit} className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('category_name', 'Название категории')}
              </label>
              <input
                type="text"
                value={categoryForm.name}
                onChange={e => setCategoryForm({ ...categoryForm, name: e.target.value })}
                className="w-full border rounded p-2"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('parent_category', 'Родительская категория')}
              </label>
              <select
                value={categoryForm.parent_id || ''}
                onChange={e =>
                  setCategoryForm({
                    ...categoryForm,
                    parent_id: e.target.value === '' ? null : e.target.value
                  })
                }
                className="w-full border rounded p-2"
              >
                <option value="">{t('no_parent', 'Нет родительской категории')}</option>
                {categories
                  .filter(category => category.id !== categoryForm.id)
                  .map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('image_url', 'URL изображения')}
              </label>
              <input
                type="text"
                value={categoryForm.image || ''}
                onChange={e => setCategoryForm({ ...categoryForm, image: e.target.value })}
                className="w-full border rounded p-2"
                placeholder="https://example.com/image.jpg"
              />
              {categoryForm.image && (
                <div className="mt-2">
                  <img
                    src={categoryForm.image}
                    alt="Preview"
                    className="h-20 w-auto object-contain"
                    onError={e => {
                      e.target.onerror = null;
                      e.target.src = 'https://placehold.co/150x150/EEE/31343C?text=Image';
                    }}
                  />
                </div>
              )}
            </div>

            <div className="flex gap-2 pt-2">
              <button
                type="submit"
                className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
              >
                {formMode === 'add' ? t('add', 'Добавить') : t('update', 'Обновить')}
              </button>

              {formMode === 'edit' && (
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                >
                  {t('cancel', 'Отмена')}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>

      {/* Правая колонка: товары в выбранной категории */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <h2 className="font-semibold text-lg">
            {selectedCategory
              ? t('products_in_category', 'Товары в категории') +
                ': ' +
                (categories.find(cat => cat.id === selectedCategory)?.name || '')
              : t('select_category', 'Выберите категорию для просмотра товаров')}
          </h2>
        </div>

        {selectedCategory && (
          <div className="p-4">
            <div className="mb-4">
              <input
                type="text"
                placeholder={t('search_products', 'Поиск товаров...')}
                value={productSearchQuery}
                onChange={e => setProductSearchQuery(e.target.value)}
                className="w-full border rounded p-2"
              />
            </div>

            {loadingProducts ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : categoryProducts.length === 0 ? (
              <p className="text-center py-4 text-gray-500">
                {productSearchQuery
                  ? t('no_products_match', 'Нет товаров, соответствующих поиску')
                  : t('no_products_in_category', 'В этой категории нет товаров')}
              </p>
            ) : (
              <div className="max-h-[500px] overflow-y-auto">
                <table className="w-full border-collapse">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="p-2 text-left border">{t('product', 'Товар')}</th>
                      <th className="p-2 text-left border">{t('price', 'Цена')}</th>
                      <th className="p-2 text-left border">{t('stock', 'Остаток')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categoryProducts.map(product => (
                      <tr key={product.id} className="hover:bg-gray-50">
                        <td className="p-2 border">
                          <div className="flex items-center">
                            {product.image && (
                              <img
                                src={product.image}
                                alt={product.name}
                                className="w-10 h-10 object-cover rounded mr-2"
                                onError={e => {
                                  e.target.onerror = null;
                                  e.target.src = 'https://placehold.co/50x50/EEE/31343C?text=Icon';
                                }}
                              />
                            )}
                            <span>{product.name}</span>
                          </div>
                        </td>
                        <td className="p-2 border">{product.price?.toFixed(2) || '0.00'} ₴</td>
                        <td className="p-2 border">{product.stock || 0}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryManagement;
