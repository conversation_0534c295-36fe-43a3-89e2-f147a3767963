.admin-sidebar {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  overflow: hidden;
  width: 240px;
}

.admin-sidebar.collapsed {
  width: 60px;
}

.admin-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.admin-sidebar-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.sidebar-toggle {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.sidebar-toggle:hover {
  background-color: #f0f0f0;
}

.admin-menu {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.admin-menu li {
  margin: 0;
}

.admin-menu-link {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  text-decoration: none;
  color: #555;
  transition:
    background-color 0.2s,
    color 0.2s;
  position: relative;
}

.admin-menu-link:hover {
  background-color: #f8f9fa;
  color: #d9b38c;
}

.admin-menu li.active .admin-menu-link {
  background-color: #f1f1f1;
  color: #d9b38c;
  font-weight: 500;
}

.admin-menu-link i {
  font-size: 18px;
  width: 24px;
  text-align: center;
  margin-right: 15px;
}

.admin-sidebar.collapsed .admin-menu-link i {
  margin-right: 0;
}

.admin-menu-link span {
  white-space: nowrap;
  transition: opacity 0.2s;
}

.admin-sidebar.collapsed .admin-menu-link span {
  opacity: 0;
  position: absolute;
  left: -9999px;
}

/* Стиль для счетчика элементов */
.counter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  border-radius: 10px;
  background-color: #d9b38c;
  color: white;
  font-size: 12px;
  font-weight: bold;
  margin-left: auto;
}

.counter-collapsed {
  position: absolute;
  top: 5px;
  right: 5px;
  min-width: 16px;
  height: 16px;
  font-size: 10px;
  padding: 0 4px;
}

/* Подсказка при наведении в свернутом режиме */
.admin-sidebar.collapsed .admin-menu-link:hover::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  margin-left: 8px;
  white-space: nowrap;
  z-index: 1000;
  font-size: 14px;
}

.admin-sidebar.collapsed .admin-menu-link:hover::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  transform: translateY(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: transparent transparent transparent #333;
  margin-left: -4px;
  z-index: 1001;
}

/* Адаптивность */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    border-radius: 0;
    margin-bottom: 20px;
  }

  .admin-sidebar.collapsed {
    width: 100%;
  }

  .admin-sidebar-header {
    justify-content: space-between;
  }

  .admin-sidebar.collapsed .admin-sidebar-header h5 {
    display: block;
    opacity: 1;
  }

  .admin-sidebar.collapsed .admin-menu-link span {
    opacity: 1;
    position: static;
  }

  .admin-sidebar.collapsed .admin-menu-link i {
    margin-right: 15px;
  }

  .sidebar-toggle i {
    transform: rotate(90deg);
  }

  .admin-sidebar.collapsed .sidebar-toggle i {
    transform: rotate(-90deg);
  }
}
