import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { supabase } from '../../supabaseClient';
import { toast } from 'react-toastify';

const BrandForm = ({ brand, onSuccess }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    logo_url: '',
    website_url: '',
    position: 0,
    active: true
  });
  const [loading, setLoading] = useState(false);
  const [logoFile, setLogoFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');

  useEffect(() => {
    if (brand) {
      setFormData({
        name: brand.name || '',
        logo_url: brand.logo_url || '',
        website_url: brand.website_url || '',
        position: brand.position || 0,
        active: brand.active ?? true
      });
      setPreviewUrl(brand.logo_url || '');
    }
  }, [brand]);

  const handleInputChange = e => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleLogoChange = e => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const uploadLogo = async file => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `${fileName}`;

      const { error: uploadError } = await supabase.storage.from('brands').upload(filePath, file);

      if (uploadError) throw uploadError;

      const {
        data: { publicUrl }
      } = supabase.storage.from('brands').getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading logo:', error);
      throw error;
    }
  };

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);

    try {
      let logoUrl = formData.logo_url;

      if (logoFile) {
        logoUrl = await uploadLogo(logoFile);
      }

      const saveData = {
        name: formData.name,
        logo_url: logoUrl,
        website_url: formData.website_url,
        position: parseInt(formData.position, 10) || 0,
        active: formData.active,
        updated_at: new Date().toISOString()
      };

      if (brand) {
        // Update existing brand
        const { error } = await supabase.from('brands').update(saveData).eq('id', brand.id);

        if (error) throw error;
        toast.success(t('brand_updated', 'Бренд успешно обновлен'));
      } else {
        // Create new brand
        const { error } = await supabase.from('brands').insert([saveData]);

        if (error) throw error;
        toast.success(t('brand_created', 'Бренд успешно создан'));
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error in brand form:', error);
      toast.error(t('error_saving_brand', 'Ошибка при сохранении бренда'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl">
      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('brand_name', 'Название бренда')}
        </label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          required
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('brand_logo', 'Логотип бренда')}
        </label>
        <input
          type="file"
          accept="image/*"
          onChange={handleLogoChange}
          className="mt-1 block w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-md file:border-0
            file:text-sm file:font-semibold
            file:bg-primary file:text-white
            hover:file:bg-primary-dark"
        />
        {previewUrl && (
          <div className="mt-2">
            <img src={previewUrl} alt="Logo preview" className="h-20 w-auto object-contain" />
          </div>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('website_url', 'URL веб-сайта')}
        </label>
        <input
          type="url"
          name="website_url"
          value={formData.website_url}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">
          {t('position', 'Позиция')}
        </label>
        <input
          type="number"
          name="position"
          value={formData.position}
          onChange={handleInputChange}
          min="0"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
        />
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          name="active"
          checked={formData.active}
          onChange={handleInputChange}
          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
        />
        <label className="ml-2 block text-sm text-gray-900">{t('active', 'Активен')}</label>
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={() => window.history.back()}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          {t('cancel', 'Отмена')}
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {loading ? t('saving', 'Сохранение...') : t('save', 'Сохранить')}
        </button>
      </div>
    </form>
  );
};

export default BrandForm;
