import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

const ProductParamManager = ({ params = [], onChange }) => {
  const { t } = useTranslation();

  const handleParamChange = useCallback(
    (index, field, value) => {
      onChange(prevParams => {
        const updatedParams = [...prevParams];
        updatedParams[index] = {
          ...updatedParams[index],
          [field]: value,
          isModified: true
        };
        return updatedParams;
      });
    },
    [onChange]
  );

  const handleAddParam = useCallback(() => {
    onChange(prevParams => [...prevParams, { name: '', value: '', is_key: false, isNew: true }]);
  }, [onChange]);

  const handleRemoveParam = useCallback(
    index => {
      onChange(prevParams => {
        const updatedParams = [...prevParams];
        if (updatedParams[index].isNew) {
          updatedParams.splice(index, 1);
        } else {
          updatedParams[index]._deleted = true;
        }
        return updatedParams;
      });
    },
    [onChange]
  );

  return (
    <div className="space-y-4">
      {params
        .filter(p => !p._deleted)
        .map((param, index) => (
          <div key={index} className="flex gap-4 items-start">
            <div className="flex-1">
              <input
                type="text"
                value={param.name}
                onChange={e => handleParamChange(index, 'name', e.target.value)}
                placeholder={t('param_name', 'Название параметра')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div className="flex-1">
              <input
                type="text"
                value={param.value}
                onChange={e => handleParamChange(index, 'value', e.target.value)}
                placeholder={t('param_value', 'Значение')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id={`is_key_${index}`}
                checked={param.is_key || false}
                onChange={e => handleParamChange(index, 'is_key', e.target.checked)}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label className="ml-2 text-sm text-gray-700" htmlFor={`is_key_${index}`}>
                {t('is_key_param', 'Ключевой')}
              </label>
            </div>
            <button
              type="button"
              onClick={() => handleRemoveParam(index)}
              className="px-2 py-2 text-red-600 hover:text-red-800"
            >
              {t('remove', 'Удалить')}
            </button>
          </div>
        ))}
      <button
        type="button"
        onClick={handleAddParam}
        className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
      >
        {t('add_parameter', 'Добавить параметр')}
      </button>
    </div>
  );
};

export default React.memo(ProductParamManager);
