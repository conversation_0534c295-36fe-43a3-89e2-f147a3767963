import React, { useEffect, useState } from 'react';
import { getAdminClient } from '../supabaseClient';

const AdminClientTest = () => {
  const [status, setStatus] = useState('Testing...');
  const [logs, setLogs] = useState([]);

  const addLog = message => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    const testAdminClient = async () => {
      try {
        addLog('🧪 Starting admin client test...');

        const adminClient = getAdminClient();
        addLog(`✅ Admin client obtained: ${!!adminClient}`);

        // Test 1: Read products
        addLog('📖 Testing products read...');
        const { data: products, error: readError } = await adminClient
          .from('products')
          .select('id, name')
          .limit(1);

        if (readError) {
          addLog(`❌ Products read failed: ${readError.message}`);
          setStatus('❌ Admin client test failed');
          return;
        }

        addLog(`✅ Products read successful (${products.length} products)`);

        if (products.length > 0) {
          // Test 2: Test product_params access
          addLog('🧪 Testing product_params access...');

          const testParam = {
            product_id: 999999,
            name: 'admin_test_param',
            value: 'test_value'
          };

          const { data: insertData, error: insertError } = await adminClient
            .from('product_params')
            .insert(testParam)
            .select();

          if (insertError) {
            addLog(
              `❌ product_params insert failed: ${insertError.message} (Code: ${insertError.code})`
            );

            if (insertError.code === '42501') {
              addLog('🔒 This is an RLS policy error - the 403 issue is still present');
              setStatus('❌ 403 Error Still Present');
            } else {
              setStatus('❌ Other Error');
            }
          } else {
            addLog('✅ product_params insert successful!');
            addLog('🎯 403 errors are FIXED!');
            setStatus('✅ 403 Error Fixed!');

            // Clean up
            if (insertData && insertData[0]) {
              await adminClient.from('product_params').delete().eq('id', insertData[0].id);
              addLog('🧹 Test data cleaned up');
            }
          }
        }
      } catch (error) {
        addLog(`❌ Test exception: ${error.message}`);
        setStatus('❌ Test Failed');
      }
    };

    testAdminClient();
  }, []);

  return (
    <div
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        width: '400px',
        background: 'white',
        border: '2px solid #ccc',
        borderRadius: '8px',
        padding: '16px',
        zIndex: 9999,
        maxHeight: '500px',
        overflow: 'auto',
        fontFamily: 'monospace',
        fontSize: '12px'
      }}
    >
      <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>Admin Client Test</h3>
      <div
        style={{
          padding: '8px',
          background: status.includes('✅')
            ? '#d4edda'
            : status.includes('❌')
              ? '#f8d7da'
              : '#fff3cd',
          borderRadius: '4px',
          marginBottom: '10px',
          fontWeight: 'bold'
        }}
      >
        Status: {status}
      </div>

      <div style={{ maxHeight: '300px', overflow: 'auto' }}>
        {logs.map((log, index) => (
          <div
            key={index}
            style={{
              marginBottom: '4px',
              padding: '2px',
              backgroundColor: log.includes('❌')
                ? '#ffe6e6'
                : log.includes('✅')
                  ? '#e6ffe6'
                  : 'transparent'
            }}
          >
            {log}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AdminClientTest;
