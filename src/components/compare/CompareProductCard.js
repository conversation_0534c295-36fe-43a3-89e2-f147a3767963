import React, { useState } from 'react';
import { useCompare } from '../../context/CompareContext';
import { useCart } from '../../context/CartContext';
import { Link } from 'react-router-dom';

/**
 * Компонент карточки товара для мобильного представления страницы сравнения
 *
 * @param {Object} props
 * @param {Object} props.product - Объект товара
 * @param {Function} props.getBestValue - Функция определения лучшего значения
 * @param {Function} props.getParamValue - Функция для получения значения параметра
 * @param {Array<Object>} props.paramGroups - Группы параметров для отображения
 * @param {Function} props.t - Функция перевода
 */
const CompareProductCard = ({ product, getBestValue, getParamValue, paramGroups, t }) => {
  const { removeFromCompare } = useCompare();
  const { addToCart } = useCart();
  const [expandedGroup, setExpandedGroup] = useState('main');

  // Определяем, является ли товар лучшим по цене
  const isBestPrice = product.price === getBestValue('price');

  // Функция для локализации названий параметров
  const getParamTitle = paramName => {
    const paramTitles = {
      name: 'Название',
      price: 'Цена',
      brand: 'Бренд',
      material: 'Материал',
      dimensions: 'Размеры',
      weight: 'Вес',
      color: 'Цвет',
      warranty: 'Гарантия',
      features: 'Особенности',
      inStock: 'Наличие'
    };

    return t(`param_${paramName}`, paramTitles[paramName] || paramName);
  };

  // Функция для форматированного отображения параметра
  const formatParamValue = (paramName, value) => {
    if (value === undefined || value === null || value === '') {
      return '-';
    }

    // Для массивов (например, features) выводим через запятую
    if (Array.isArray(value)) {
      return value.join(', ');
    }

    return value;
  };

  // Получаем основные параметры для мобильного представления
  const mainParams = ['brand', 'material', 'color', 'warranty', 'weight', 'dimensions'];

  return (
    <div className="border rounded-lg shadow-md p-5 mb-6 bg-white">
      {/* Верхняя часть карточки с изображением и названием */}
      <div className="flex flex-col items-center mb-4">
        <Link
          to={`/product/${product.id}`}
          className="block cursor-pointer hover:opacity-90 transition-opacity"
        >
          <div className="w-40 h-40 bg-gray-50 rounded-md p-3 flex items-center justify-center mb-4">
            <img
              src={product.image || 'https://placehold.co/120x120/EEE/31343C?text=Item'}
              alt={product.name || 'Товар'}
              className="max-w-full max-h-full object-contain"
              onError={e => {
                e.target.onerror = null;
                e.target.src = 'https://placehold.co/120x120/EEE/31343C?text=Item';
              }}
            />
          </div>
        </Link>
        <Link
          to={`/product/${product.id}`}
          className="text-lg font-medium text-blue-800 text-center hover:text-blue-600 hover:underline transition-colors"
        >
          {product.name}
        </Link>

        <div className="flex items-center mt-3 gap-3">
          <span className={`text-xl font-bold ${isBestPrice ? 'text-green-600' : 'text-gray-800'}`}>
            {product.price ? `${product.price} ₴` : 'Цена недоступна'}
          </span>
          {isBestPrice && (
            <div className="flex items-center text-xs text-green-600">
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
              Лучшая цена
            </div>
          )}
        </div>
      </div>

      {/* Кнопки действий */}
      <div className="flex justify-between gap-2 mb-6">
        <button
          onClick={() => addToCart(product)}
          className="flex-1 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition text-center flex items-center justify-center"
        >
          <svg
            className="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
            ></path>
          </svg>
          {t('add_to_cart', 'В корзину')}
        </button>
        <button
          onClick={() => removeFromCompare(product.id)}
          className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition flex items-center justify-center"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      {/* Основные параметры */}
      <div className="border-t pt-4">
        <div className="grid grid-cols-2 gap-4">
          {/* Основные параметры отображаем в сетке 2x2 */}
          {mainParams.map((paramName, _index) => {
            const value = getParamValue(product, paramName);

            if (paramName === 'dimensions') return null; // dimensions показываем отдельно ниже

            return (
              <div key={paramName} className="flex flex-col">
                <span className="text-sm text-gray-500 mb-1">{getParamTitle(paramName)}</span>
                <span className="font-medium">{formatParamValue(paramName, value)}</span>
              </div>
            );
          })}
        </div>

        {/* Размеры */}
        <div className="mt-4">
          <span className="text-sm text-gray-500 mb-1 block">{getParamTitle('dimensions')}</span>
          <p className="font-medium">
            {formatParamValue('dimensions', getParamValue(product, 'dimensions'))}
          </p>
        </div>

        {/* Группы параметров (аккордеон) */}
        {paramGroups
          .filter(group => group.id !== 'main')
          .map(group => (
            <div key={group.id} className="mt-4 border-t pt-3">
              <button
                onClick={() => setExpandedGroup(expandedGroup === group.id ? null : group.id)}
                className="flex justify-between items-center w-full text-left mb-2"
              >
                <span className="font-medium text-blue-700">{group.title}</span>
                <span>{expandedGroup === group.id ? '▼' : '►'}</span>
              </button>

              {expandedGroup === group.id && (
                <div className="bg-gray-50 p-3 rounded-md mt-2 space-y-3">
                  {group.params.map(param => (
                    <div
                      key={param}
                      className="border-b border-gray-100 pb-2 last:border-0 last:pb-0"
                    >
                      <span className="text-sm text-gray-500 block">{getParamTitle(param)}</span>
                      <span className="mt-1">
                        {formatParamValue(param, getParamValue(product, param))}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
      </div>

      {/* Кнопка "Подробнее о товаре" */}
      <div className="mt-4 border-t pt-4">
        <Link
          to={`/product/${product.id}`}
          className="block w-full py-2 bg-gray-100 hover:bg-gray-200 text-center text-gray-700 rounded-md transition font-medium"
        >
          {t('view_product_details', 'Подробнее о товаре')}
        </Link>
      </div>
    </div>
  );
};

export default CompareProductCard;
