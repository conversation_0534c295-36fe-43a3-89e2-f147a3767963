import React, { useState } from 'react';

/**
 * Компонент для группировки параметров в таблице сравнения
 *
 * @param {Object} props
 * @param {string} props.title - Название группы параметров
 * @param {Array<string>} props.params - Массив названий параметров
 * @param {Array<Object>} props.items - Массив товаров для сравнения
 * @param {boolean} props.showOnlyDifferences - Флаг для скрытия одинаковых параметров
 * @param {Function} props.areValuesEqual - Функция для проверки равенства значений параметра
 * @param {Function} props.getBestValue - Функция для определения лучшего значения
 * @param {Function} props.getParamValue - Функция для получения значения параметра
 * @param {Function} props.t - Функция перевода
 */
const CompareParamGroup = ({
  title,
  params,
  items,
  showOnlyDifferences,
  areValuesEqual,
  getBestValue,
  getParamValue,
  t
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Фильтруем параметры, если нужно показывать только различия
  const filteredParams = showOnlyDifferences
    ? params.filter(param => !areValuesEqual(param))
    : params;

  // Если нет параметров для отображения, не показываем группу
  if (filteredParams.length === 0) {
    return null;
  }

  // Функция для форматированного отображения параметра
  const formatParamValue = (paramName, value) => {
    if (value === undefined || value === null || value === '') {
      return '-';
    }

    // Для массивов (например, features) выводим через запятую
    if (Array.isArray(value)) {
      return value.join(', ');
    }

    return value;
  };

  // Функция для локализации названий параметров
  const getParamTitle = paramName => {
    const paramTitles = {
      name: 'Название',
      price: 'Цена',
      brand: 'Бренд',
      material: 'Материал',
      dimensions: 'Размеры',
      weight: 'Вес',
      color: 'Цвет',
      warranty: 'Гарантия',
      features: 'Особенности',
      inStock: 'Наличие'
    };

    return t(`param_${paramName}`, paramTitles[paramName] || paramName);
  };

  return (
    <>
      {/* Заголовок группы параметров (с возможностью сворачивания/разворачивания) */}
      <tr className="bg-gray-50 border-t border-gray-100">
        <td
          colSpan={items.length + 1}
          className="p-4 font-semibold cursor-pointer text-gray-800 tracking-tight"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center">
            <span className="mr-2 text-blue-700">{isExpanded ? '▼' : '►'}</span>
            {title}
          </div>
        </td>
      </tr>

      {/* Параметры группы (видимы только если группа развернута) */}
      {isExpanded &&
        filteredParams.map(paramName => {
          // Получаем лучшее значение для этого параметра (если применимо)
          const bestValue = getBestValue(paramName);

          return (
            <tr key={paramName} className="border-b hover:bg-gray-50">
              <td className="p-4 font-medium text-gray-700">{getParamTitle(paramName)}</td>

              {items.map((item, index) => {
                // Получаем значение параметра с использованием функции getParamValue
                const paramValue = getParamValue(item, paramName);

                // Проверяем, является ли значение этого элемента лучшим
                const isBest =
                  bestValue !== null &&
                  (paramName === 'price'
                    ? parseFloat(paramValue) === bestValue
                    : parseFloat(paramValue) === bestValue);

                return (
                  <td
                    key={`${item.id}-${index}-${paramName}`}
                    className={`p-4 text-center border-l border-gray-100 ${isBest ? 'bg-green-50' : ''}`}
                  >
                    <span
                      className={`${isBest ? 'font-semibold text-green-600' : 'font-medium text-gray-800'}`}
                    >
                      {formatParamValue(paramName, paramValue)}
                    </span>

                    {isBest && paramName === 'price' && (
                      <div className="text-xs text-green-600 mt-1 flex items-center justify-center">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          ></path>
                        </svg>
                        Лучшая цена
                      </div>
                    )}
                  </td>
                );
              })}
            </tr>
          );
        })}
    </>
  );
};

export default CompareParamGroup;
