// filepath: /Users/<USER>/e-com_new/online-store/src/components/compare/CompareIcons.js
import React from 'react';

/**
 * Компонент для отображения иконок и визуальных подсказок в сравнении товаров
 */
export const CompareIcons = {
  /**
   * Иконка для лучшей цены
   * @returns {JSX.Element} Иконка лучшей цены
   */
  BestPrice: () => (
    <span className="inline-flex items-center justify-center text-green-600" title="Лучшая цена">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        viewBox="0 0 16 16"
      >
        <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0zm0 14.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13z" />
        <path d="M7 7a1 1 0 0 0-1 1h2a1 1 0 1 1-1 1v1h2v-.5a1.5 1.5 0 1 0-3 0V8a2 2 0 1 1 4 0h-1a1 1 0 0 0-1-1zm1-3a1 1 0 1 0 0-2 1 1 0 0 0 0 2z" />
      </svg>
    </span>
  ),

  /**
   * Иконка для наличия товара
   * @param {boolean} inStock - Флаг наличия товара
   * @returns {JSX.Element} Иконка наличия товара
   */
  InStock: ({ inStock }) => (
    <span
      className={`inline-flex items-center justify-center ${inStock ? 'text-green-600' : 'text-red-500'}`}
    >
      {inStock ? (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          fill="currentColor"
          viewBox="0 0 16 16"
          title="В наличии"
        >
          <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425z" />
        </svg>
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          fill="currentColor"
          viewBox="0 0 16 16"
          title="Нет в наличии"
        >
          <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z" />
        </svg>
      )}
    </span>
  ),

  /**
   * Иконка для высокого рейтинга
   * @returns {JSX.Element} Иконка высокого рейтинга
   */
  HighRating: () => (
    <span
      className="inline-flex items-center justify-center text-yellow-500"
      title="Высокий рейтинг"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        viewBox="0 0 16 16"
      >
        <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.283.95l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
      </svg>
    </span>
  ),

  /**
   * Иконка для экологичности товара
   * @returns {JSX.Element} Иконка экологичности
   */
  Eco: () => (
    <span
      className="inline-flex items-center justify-center text-green-600"
      title="Экологичный товар"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        viewBox="0 0 16 16"
      >
        <path d="M8 1a7 7 0 1 0 0 14A7 7 0 0 0 8 1zm-1 4a1 1 0 0 1 2 0v4a1 1 0 1 1-2 0V5zm1-3a8 8 0 1 1 0 16A8 8 0 0 1 8 2z" />
        <path d="M4.285 9.567a.5.5 0 0 1 .683.183A3.498 3.498 0 0 0 8 11.5a3.498 3.498 0 0 0 3.032-1.75.5.5 0 1 1 .866.5A4.498 4.498 0 0 1 8 12.5a4.498 4.498 0 0 1-3.898-2.25.5.5 0 0 1 .183-.683zM8 6.5a.5.5 0 0 1 .5.5v1.5a.5.5 0 0 1-1 0V7a.5.5 0 0 1 .5-.5z" />
      </svg>
    </span>
  ),

  /**
   * Иконка для выделения параметров, отличающихся от других товаров
   * @returns {JSX.Element} Иконка отличия
   */
  Difference: () => (
    <span
      className="inline-flex items-center justify-center text-blue-500"
      title="Отличается от других товаров"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        viewBox="0 0 16 16"
      >
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
        <path d="M5.255 5.786a.237.237 0 0 0 .241.247h.825c.138 0 .248-.113.266-.25.09-.656.54-1.134 1.342-1.134.686 0 1.314.343 1.314 1.168 0 .635-.374.927-.965 1.371-.673.489-1.206 1.06-1.168 1.987l.003.217a.25.25 0 0 0 .25.246h.811a.25.25 0 0 0 .25-.25v-.105c0-.718.273-.927 1.01-1.486.609-.463 1.244-.977 1.244-2.056 0-1.511-1.276-2.241-2.673-2.241-1.267 0-2.655.59-2.75 2.286zm1.557 5.763c0 .533.425.927 1.01.927.609 0 1.028-.394 1.028-.927 0-.552-.42-.94-1.029-.94-.584 0-1.009.388-1.009.94z" />
      </svg>
    </span>
  )
};

export default CompareIcons;
