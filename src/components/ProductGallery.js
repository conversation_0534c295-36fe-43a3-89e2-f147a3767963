import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiX,
  FiZoomIn,
  FiChevronLeft,
  FiChevronRight,
  FiArrowUp,
  FiArrowDown
} from 'react-icons/fi';
import { useSwipeable } from 'react-swipeable';
import { _useHotkeys } from 'react-hotkeys-hook';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { Heart, BarChart2, Maximize2 } from 'react-feather';
import { useWishlist } from '../context/WishlistContext';
import { useCompare } from '../context/CompareContext';
import { useTranslation } from 'react-i18next';

const ProductGallery = ({ images = [], productName = '', productId = '' }) => {
  const { t } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [_isZoomed, _setIsZoomed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState({});
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const [zoomLevel, setZoomLevel] = useState(1);
  const [thumbnailsStartIndex, setThumbnailsStartIndex] = useState(0);

  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { addToCompare, removeFromCompare, isInCompare } = useCompare();

  const productInWishlist = isInWishlist(productId);
  const productInCompare = isInCompare(productId);

  // Maximum number of thumbnails to display at once
  const maxVisibleThumbnails = 5;
  const thumbnailHeight = 100; // from h-[100px]
  const thumbnailGap = 12; // from gap-3 (0.75rem)
  const thumbnailScrollAmount = thumbnailHeight + thumbnailGap;
  const visibleThumbnailsHeight =
    maxVisibleThumbnails * thumbnailHeight + (maxVisibleThumbnails - 1) * thumbnailGap;

  // Remove duplicate images and filter out invalid URLs
  const uniqueImages = [...new Set(images)].filter(img => img && typeof img === 'string');

  const hasMoreThumbnailsUp = thumbnailsStartIndex > 0;
  const hasMoreThumbnailsDown = thumbnailsStartIndex + maxVisibleThumbnails < uniqueImages.length;

  // Handle wishlist click
  const handleWishlistClick = useCallback(() => {
    if (productInWishlist) {
      removeFromWishlist(productId);
    } else {
      addToWishlist({
        id: productId,
        name: productName,
        image: images[0]
      });
    }
  }, [productInWishlist, removeFromWishlist, addToWishlist, productId, productName, images]);

  // Handle compare click
  const handleCompareClick = useCallback(() => {
    if (productInCompare) {
      removeFromCompare(productId);
    } else {
      addToCompare({
        id: productId,
        name: productName,
        image: images[0]
      });
    }
  }, [productInCompare, removeFromCompare, addToCompare, productId, productName, images]);

  const handleImageLoad = index => {
    setImageLoaded(prev => ({
      ...prev,
      [index]: true
    }));
    if (index === currentIndex) {
      setLoading(false);
    }
  };

  const handleImageError = () => {
    setLoading(false);
    setError(true);
  };

  // Swipe handlers
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => handleNavigate('next'),
    onSwipedRight: () => handleNavigate('prev'),
    preventDefaultTouchmoveEvent: true,
    trackMouse: false
  });

  const handleNavigate = useCallback(
    direction => {
      if (images.length <= 1) return;
      if (direction === 'next') {
        setCurrentIndex(prev => (prev + 1) % images.length);
      } else {
        setCurrentIndex(prev => (prev - 1 + images.length) % images.length);
      }
    },
    [images.length]
  );

  const handleMouseMove = e => {
    if (!isHovered) return;

    const bounds = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - bounds.left) / bounds.width;
    const y = (e.clientY - bounds.top) / bounds.height;

    setMousePosition({ x, y });
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    setZoomLevel(2);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setZoomLevel(1);
  };

  // Function to toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Close fullscreen when pressing Escape
  useEffect(() => {
    const handleKeyDown = e => {
      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen]);

  // Update thumbnails visibility when current image changes
  useEffect(() => {
    // If the current image is not in the visible range, adjust the thumbnails
    if (currentIndex < thumbnailsStartIndex) {
      setThumbnailsStartIndex(currentIndex);
    } else if (currentIndex >= thumbnailsStartIndex + maxVisibleThumbnails) {
      setThumbnailsStartIndex(currentIndex - maxVisibleThumbnails + 1);
    }
  }, [currentIndex, thumbnailsStartIndex]);

  // Handle thumbnail scroll window
  const navigateThumbnails = direction => {
    if (direction === 'up' && hasMoreThumbnailsUp) {
      setThumbnailsStartIndex(prev => Math.max(0, prev - 1));
    } else if (direction === 'down' && hasMoreThumbnailsDown) {
      setThumbnailsStartIndex(prev =>
        Math.min(uniqueImages.length - maxVisibleThumbnails, prev + 1)
      );
    }
  };

  return (
    <>
      <div className="flex gap-6">
        {/* Thumbnails with navigation controls when needed */}
        <div className="hidden md:flex flex-col gap-3 w-[100px] relative py-12">
          {uniqueImages.length > maxVisibleThumbnails && (
            <button
              onClick={() => setCurrentIndex(prev => Math.max(prev - 1, 0))}
              className={`absolute top-2 left-1/2 transform -translate-x-1/2 bg-primary w-10 h-10 rounded-full flex items-center justify-center shadow-lg z-10 ${
                currentIndex === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-primary/90'
              }`}
              disabled={currentIndex === 0}
              aria-label="Previous thumbnail"
            >
              <FiArrowUp className="w-5 h-5 text-white" />
            </button>
          )}

          <div
            className="overflow-hidden"
            style={{
              height:
                uniqueImages.length > maxVisibleThumbnails ? `${visibleThumbnailsHeight}px` : 'auto'
            }}
          >
            <div
              className="flex flex-col gap-3 transition-transform duration-300"
              style={{
                transform: `translateY(-${thumbnailsStartIndex * thumbnailScrollAmount}px)`
              }}
            >
              {uniqueImages.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`relative w-[100px] h-[100px] border rounded-md overflow-hidden transition-all flex-shrink-0
                    ${currentIndex === index ? 'border-primary' : 'border-gray-200 hover:border-gray-300'}`}
                >
                  {!imageLoaded[index] && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                  <img
                    src={image}
                    alt={`${productName} - миниатюра ${index + 1}`}
                    className={`w-full h-full object-contain p-1 transition-opacity duration-200 ${
                      imageLoaded[index] ? 'opacity-100' : 'opacity-0'
                    }`}
                    onLoad={() => handleImageLoad(index)}
                    onError={handleImageError}
                    loading={index === 0 ? 'eager' : 'lazy'}
                  />
                </button>
              ))}
            </div>
          </div>

          {uniqueImages.length > maxVisibleThumbnails && (
            <button
              onClick={() => setCurrentIndex(prev => Math.min(prev + 1, uniqueImages.length - 1))}
              className={`absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-primary w-10 h-10 rounded-full flex items-center justify-center shadow-lg z-10 ${
                currentIndex === uniqueImages.length - 1
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-primary/90'
              }`}
              disabled={currentIndex === uniqueImages.length - 1}
              aria-label="Next thumbnail"
            >
              <FiArrowDown className="w-5 h-5 text-white" />
            </button>
          )}
        </div>

        {/* Main Image */}
        <div className="flex-1 relative">
          <div
            className="relative bg-white rounded-lg overflow-hidden"
            style={{ height: '600px' }}
            onMouseMove={handleMouseMove}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            {...swipeHandlers}
          >
            {/* Action buttons */}
            <div className="absolute top-4 right-4 z-20 flex flex-col gap-2">
              <button
                onClick={toggleFullscreen}
                className="bg-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all hover:bg-gray-50"
                aria-label="View fullscreen"
              >
                <Maximize2 size={20} className="text-gray-600" />
              </button>
              <button
                onClick={handleWishlistClick}
                className="bg-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all hover:bg-gray-50"
                aria-label="Toggle wishlist"
              >
                <Heart
                  size={20}
                  className={productInWishlist ? 'text-red-500 fill-current' : 'text-gray-600'}
                />
              </button>
              <button
                onClick={handleCompareClick}
                className="bg-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg transition-all hover:bg-gray-50"
                aria-label="Toggle compare"
              >
                <BarChart2
                  size={20}
                  className={productInCompare ? 'text-blue-500' : 'text-gray-600'}
                />
              </button>
            </div>

            {/* Navigation Buttons */}
            {uniqueImages.length > 1 && (
              <>
                <button
                  onClick={() => handleNavigate('prev')}
                  onMouseEnter={() => setIsHovered(false)}
                  onMouseLeave={() => setIsHovered(true)}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-primary/80 w-12 h-12 rounded-full flex items-center justify-center shadow-lg transition-all z-10 hover:bg-primary"
                  aria-label="Previous image"
                >
                  <FiChevronLeft className="w-7 h-7 text-white" />
                </button>
                <button
                  onClick={() => handleNavigate('next')}
                  onMouseEnter={() => setIsHovered(false)}
                  onMouseLeave={() => setIsHovered(true)}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-primary/80 w-12 h-12 rounded-full flex items-center justify-center shadow-lg transition-all z-10 hover:bg-primary"
                  aria-label="Next image"
                >
                  <FiChevronRight className="w-7 h-7 text-white" />
                </button>
              </>
            )}

            {/* Main Image */}
            <div
              className="w-full h-full relative cursor-pointer"
              style={{
                overflow: 'hidden'
              }}
              onClick={toggleFullscreen}
            >
              {loading && !imageLoaded[currentIndex] && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                  <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
              {error ? (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                  <span className="text-gray-500">Изображение недоступно</span>
                </div>
              ) : (
                <div className="w-full h-full relative">
                  <img
                    src={uniqueImages[currentIndex]}
                    alt={`${productName} - ${currentIndex + 1}`}
                    className={`w-full h-full object-contain transition-opacity duration-300 ${
                      imageLoaded[currentIndex] ? 'opacity-100' : 'opacity-0'
                    }`}
                    style={{
                      transform: isHovered ? `scale(${zoomLevel})` : 'scale(1)',
                      transformOrigin: `${mousePosition.x * 100}% ${mousePosition.y * 100}%`,
                      transition: isHovered ? 'none' : 'transform 0.3s ease-out'
                    }}
                    onLoad={() => handleImageLoad(currentIndex)}
                    onError={handleImageError}
                    loading="eager"
                  />
                  {isHovered && (
                    <div className="absolute top-4 left-4 bg-white/90 px-3 py-1 rounded-full text-sm">
                      {t('hover_to_zoom', 'Наведите для увеличения')}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Mobile Thumbnails */}
          <div className="flex md:hidden justify-center gap-2 mt-4">
            {uniqueImages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentIndex ? 'bg-primary w-4' : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Switch to image ${index + 1}`}
              ></button>
            ))}
          </div>
        </div>
      </div>

      {/* Fullscreen image viewer */}
      {isFullscreen && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
          <div className="w-full h-full relative">
            <div className="absolute top-4 right-4 z-20 flex flex-col gap-2">
              <button
                onClick={toggleFullscreen}
                className="bg-white/20 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                aria-label="Close fullscreen"
              >
                <FiX size={24} className="text-white" />
              </button>
            </div>

            {uniqueImages.length > 1 && (
              <>
                <button
                  onClick={() => handleNavigate('prev')}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-primary/70 backdrop-blur-sm w-14 h-14 rounded-full flex items-center justify-center hover:bg-primary/90 transition-colors shadow-lg z-30"
                  aria-label="Previous image"
                >
                  <FiChevronLeft size={30} className="text-white" />
                </button>
                <button
                  onClick={() => handleNavigate('next')}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-primary/70 backdrop-blur-sm w-14 h-14 rounded-full flex items-center justify-center hover:bg-primary/90 transition-colors shadow-lg z-30"
                  aria-label="Next image"
                >
                  <FiChevronRight size={30} className="text-white" />
                </button>
              </>
            )}

            <TransformWrapper
              initialScale={1}
              initialPositionX={0}
              initialPositionY={0}
              wheel={{
                disabled: true
              }}
              doubleClick={{
                disabled: true
              }}
            >
              {({ zoomIn, zoomOut, resetTransform }) => (
                <>
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
                    <button
                      onClick={() => zoomIn()}
                      className="bg-white/20 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                      aria-label="Zoom in"
                    >
                      <FiZoomIn size={24} className="text-white" />
                    </button>
                    <button
                      onClick={() => zoomOut()}
                      className="bg-white/20 backdrop-blur-sm w-12 h-12 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                      aria-label="Zoom out"
                    >
                      <FiZoomIn size={24} className="text-white rotate-180" />
                    </button>
                    <button
                      onClick={() => resetTransform()}
                      className="bg-white/20 backdrop-blur-sm px-4 h-12 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                      aria-label="Reset zoom"
                    >
                      <span className="text-white">{t('reset', 'Сбросить')}</span>
                    </button>
                  </div>

                  <TransformComponent
                    wrapperStyle={{
                      width: '100%',
                      height: 'calc(100% - 100px)',
                      marginTop: '20px'
                    }}
                  >
                    <img
                      src={uniqueImages[currentIndex]}
                      alt={`${productName} - ${currentIndex + 1} (fullscreen)`}
                      className="max-w-full max-h-full object-contain mx-auto"
                    />
                  </TransformComponent>
                </>
              )}
            </TransformWrapper>

            {/* Thumbnails in fullscreen mode */}
            {uniqueImages.length > 1 && (
              <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex gap-3 z-30 py-3 px-5 bg-black/50 backdrop-blur-sm rounded-lg shadow-lg">
                {uniqueImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`relative w-14 h-14 rounded-md overflow-hidden border-2 ${
                      currentIndex === index
                        ? 'border-white shadow-glow'
                        : 'border-transparent hover:border-gray-300'
                    }`}
                    style={{
                      boxShadow:
                        currentIndex === index ? '0 0 10px rgba(255, 255, 255, 0.5)' : 'none'
                    }}
                  >
                    <img
                      src={image}
                      alt={`${productName} - миниатюра ${index + 1}`}
                      className="w-full h-full object-contain"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default ProductGallery;
