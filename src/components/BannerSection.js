import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { supabase } from '../supabaseClient';
import { getSafeImageUrl } from '../utils/imageHelpers';
import { useAuth } from '../context/AuthContext';

// Импортируем специальные стили для баннеров
import '../styles/banner.css';

const BannerSection = ({ position = 'top' }) => {
  const [banner, setBanner] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageError, setImageError] = useState(false);
  const bannerRef = useRef(null);
  const imageRef = useRef(null);
  const location = useLocation();
  const { user } = useAuth();

  const isHomePage = location.pathname === '/';

  const defaultBannerImage = {
    top: '/images/banners/default-top-banner.jpg',
    bottom: '/images/banners/default-bottom-banner.jpg'
  };

  const fetchBanner = useCallback(async () => {
    if (!isHomePage) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('banners').select('*');

      if (position === 'top') {
        query = query.lte('position', 5);
      } else {
        query = query.gt('position', 5);
      }

      const { data, error } = await query.order('position');

      if (error) {
        setError(error.message);
        return;
      }

      const activeBanners = data.filter(b => {
        return b.is_active === true || b.is_active === undefined;
      });

      if (activeBanners && activeBanners.length > 0) {
        const activeBanner = {
          ...activeBanners[0],
          image: activeBanners[0].image_url || activeBanners[0].image
        };
        setBanner(activeBanner);
      } else {
        setBanner(null);
      }
    } catch (err) {
      setError('Failed to load banner');
    } finally {
      setLoading(false);
    }
  }, [position, isHomePage]);

  useEffect(() => {
    fetchBanner();
  }, [fetchBanner]);

  // Специальная логика обработки скролла с учетом положения баннера
  useEffect(() => {
    const handleScroll = () => {
      if (!imageRef.current || !bannerRef.current) return;

      // Вычисляем позицию баннера относительно окна
      const bannerRect = bannerRef.current.getBoundingClientRect();

      // Определяем, видим ли баннер сейчас на экране или близко к нему
      const isBannerNearView =
        bannerRect.bottom > -500 && bannerRect.top < window.innerHeight + 500;

      if (isBannerNearView) {
        // Разная логика для верхнего и нижнего баннеров
        if (position === 'top') {
          // Для верхнего баннера - стандартный параллакс
          const offset = bannerRect.top * 0.5;
          imageRef.current.style.transform = `translateY(${offset}px)`;
        } else {
          // Для нижнего баннера - расширенная логика с учетом всего пути прокрутки

          // Получаем высоту окна и документа
          const windowHeight = window.innerHeight;
          const docHeight = document.documentElement.scrollHeight;
          const scrollTop = window.pageYOffset;

          // Вычисляем, насколько мы прокрутили относительно общей высоты документа (0-1)
          const scrollProgress = scrollTop / (docHeight - windowHeight);

          // Рассчитываем расстояние от верха экрана до баннера
          const distanceFromTop = bannerRect.top;

          // Начальное смещение для нижнего баннера
          const initialOffset = -150;

          // Ограничиваем максимальное смещение, чтобы избежать появления черного фона снизу
          const maxOffset = 250;

          // Параллакс смещение, зависящее от прокрутки и позиции
          // При приближении к баннеру - меньше смещения, при удалении - больше
          const scrollOffset =
            distanceFromTop < 0
              ? Math.min(-distanceFromTop * 0.2, maxOffset) // Баннер уже прокручен
              : Math.min(distanceFromTop * 0.1, maxOffset); // Баннер еще впереди

          // Итоговое смещение с учетом начальной позиции
          const totalOffset = initialOffset - scrollOffset;

          // Применяем трансформацию
          imageRef.current.style.transform = `translateY(${totalOffset}px)`;
        }
      }
    };

    // Добавляем слушатель события прокрутки с оптимизацией
    let ticking = false;
    const scrollListener = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', scrollListener);
    // Вызываем обработчик сразу для инициализации
    handleScroll();

    // Удаляем слушатель при размонтировании
    return () => {
      window.removeEventListener('scroll', scrollListener);
    };
  }, [position]);

  if (!isHomePage || location.pathname.startsWith('/account')) {
    return null;
  }

  const getDefaultBanner = () => {
    if (position === 'top') {
      return {
        title: 'Quality Furniture Fittings',
        subtitle: 'Discover our range of premium hardware for your furniture projects',
        button_text: 'Shop Now',
        button_link: '/products',
        image: defaultBannerImage.top
      };
    }
    return {
      title: 'Special Offers',
      subtitle: 'Limited time discounts on selected products',
      button_text: 'View Offers',
      button_link: '/sale',
      image: defaultBannerImage.bottom
    };
  };

  const activeBanner = banner || getDefaultBanner();

  const getImageUrl = bannerData => {
    const imageUrl =
      bannerData?.image_url || bannerData?.image || bannerData?.img || bannerData?.imageUrl;
    return imageUrl || defaultBannerImage[position] || defaultBannerImage.top;
  };

  const getButtonText = bannerData => {
    return bannerData?.button_text || bannerData?.buttonText || bannerData?.btn_text || 'Подробнее';
  };

  const getButtonLink = bannerData => {
    return (
      bannerData?.button_link ||
      bannerData?.link ||
      bannerData?.link_url ||
      bannerData?.url ||
      '/products'
    );
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const bannerImageUrl = imageError
    ? defaultBannerImage[position]
    : getSafeImageUrl(getImageUrl(activeBanner), 'banner');

  if (loading) {
    return (
      <div
        className={`${position}-banner-loading w-full h-40 bg-gray-200 flex items-center justify-center`}
      >
        <p className="text-gray-500">Loading banner...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`${position}-banner-error w-full h-40 bg-gray-100 flex items-center justify-center`}
      >
        <p className="text-gray-500">Error loading banner. Please try again.</p>
      </div>
    );
  }

  return (
    <div
      className={`banner-section-wrapper w-full ${position === 'top' ? 'mt-0' : 'mb-8'}`}
      style={{ marginTop: '-1px' }}
    >
      <div ref={bannerRef} className={`full-width-banner ${position}-banner`}>
        {/* Используем контейнер для изображения с параллакс-эффектом */}
        <div className="banner-image-container">
          <img
            ref={imageRef}
            src={bannerImageUrl}
            alt={activeBanner.title || ''}
            className={`banner-image ${position}-banner-image`}
            onError={handleImageError}
            style={{
              transition: 'transform 0.05s ease-out',
              // Добавляем специальные стили для нижнего баннера
              ...(position === 'bottom'
                ? {
                    objectPosition: 'center top', // Фокусируем на верхней части изображения
                    transform: 'translateY(-150px)' // Увеличенное начальное смещение
                  }
                : {})
            }}
          />
        </div>

        {/* Затемняющий оверлей для лучшего контраста текста */}
        <div className="banner-overlay"></div>

        {/* Контент баннера: заголовок, подзаголовок и кнопка */}
        <div className="banner-content">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="banner-title">{activeBanner.title}</h2>
              <p className="banner-subtitle">{activeBanner.subtitle}</p>
              <Link
                to={getButtonLink(activeBanner)}
                className="banner-button hover:bg-primary-dark"
              >
                {getButtonText(activeBanner)}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerSection;
