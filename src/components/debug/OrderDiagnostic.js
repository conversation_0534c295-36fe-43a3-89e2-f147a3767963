import React, { useState } from 'react';
import { supabase } from '../../supabaseClient';
import { OrderCreationService } from '../../utils/orderHelpers';
import { toast } from 'react-toastify';

const OrderDiagnostic = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState([]);

  const addResult = (test, status, message, details = null) => {
    setResults(prev => [
      ...prev,
      {
        test,
        status, // 'success', 'warning', 'error'
        message,
        details,
        timestamp: new Date().toLocaleTimeString()
      }
    ]);
  };

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults([]);

    try {
      // Test 1: Supabase Connection
      addResult('Connection', 'info', 'Testing Supabase connection...');
      try {
        const { data, error } = await supabase.from('orders').select('count').limit(1);
        if (error) {
          addResult('Connection', 'error', 'Connection failed', error.message);
        } else {
          addResult('Connection', 'success', 'Supabase connection successful');
        }
      } catch (error) {
        addResult('Connection', 'error', 'Connection exception', error.message);
      }

      // Test 2: Authentication
      addResult('Authentication', 'info', 'Checking authentication status...');
      try {
        const {
          data: { session },
          error
        } = await supabase.auth.getSession();
        if (error) {
          addResult('Authentication', 'warning', 'Auth error', error.message);
        } else if (session) {
          addResult('Authentication', 'success', `Authenticated as: ${session.user.email}`);
        } else {
          addResult('Authentication', 'warning', 'Not authenticated (anonymous mode)');
        }
      } catch (error) {
        addResult('Authentication', 'error', 'Auth exception', error.message);
      }

      // Test 3: Table Access
      addResult('Tables', 'info', 'Testing table access permissions...');
      try {
        // Test orders table
        const { data: ordersData, error: ordersError } = await supabase
          .from('orders')
          .select('id')
          .limit(1);

        if (ordersError) {
          addResult('Tables', 'error', 'Orders table access failed', ordersError.message);
        } else {
          addResult('Tables', 'success', 'Orders table accessible');
        }

        // Test order_items table
        const { data: itemsData, error: itemsError } = await supabase
          .from('order_items')
          .select('id')
          .limit(1);

        if (itemsError) {
          addResult('Tables', 'error', 'Order items table access failed', itemsError.message);
        } else {
          addResult('Tables', 'success', 'Order items table accessible');
        }
      } catch (error) {
        addResult('Tables', 'error', 'Table access exception', error.message);
      }

      // Test 4: RLS Policies
      addResult('RLS', 'info', 'Testing Row Level Security policies...');
      try {
        const testOrder = {
          customer_name: 'Test Customer',
          customer_phone: '+1234567890',
          total_amount: 99.99,
          status: 'test'
        };

        // Try to insert a test order
        const { data: insertData, error: insertError } = await supabase
          .from('orders')
          .insert([testOrder])
          .select()
          .single();

        if (insertError) {
          if (
            insertError.message.includes('permission') ||
            insertError.message.includes('policy')
          ) {
            addResult('RLS', 'error', 'RLS policies are too restrictive', insertError.message);
          } else {
            addResult('RLS', 'warning', 'Insert failed (non-RLS issue)', insertError.message);
          }
        } else {
          addResult('RLS', 'success', 'RLS policies allow inserts');

          // Clean up test order
          await supabase.from('orders').delete().eq('id', insertData.id);
          addResult('RLS', 'success', 'Test cleanup successful');
        }
      } catch (error) {
        addResult('RLS', 'error', 'RLS test exception', error.message);
      }

      // Test 5: Order Creation Service
      addResult('Service', 'info', 'Testing OrderCreationService...');
      try {
        const testOrderData = {
          customer_name: 'Test Service Customer',
          customer_phone: '+1234567890',
          total_amount: 99.99
        };

        const testOrderItems = [
          {
            product_id: '550e8400-e29b-41d4-a716-446655440000',
            product_name: 'Test Product',
            quantity: 1,
            price: 99.99
          }
        ];

        // Validate order data
        OrderCreationService.validateOrderData(testOrderData);
        addResult('Service', 'success', 'Order data validation passed');

        // Try to create order (this might fail, but we'll catch it)
        try {
          const order = await OrderCreationService.createOrder(testOrderData, testOrderItems);
          if (order) {
            addResult('Service', 'success', 'Order creation service works');
            // Clean up
            await supabase.from('order_items').delete().eq('order_id', order.id);
            await supabase.from('orders').delete().eq('id', order.id);
          }
        } catch (serviceError) {
          addResult('Service', 'warning', 'Order creation service failed', serviceError.message);
        }
      } catch (error) {
        addResult('Service', 'error', 'Service test exception', error.message);
      }

      // Test 6: Multiple Instance Check
      addResult('Instances', 'info', 'Checking for multiple GoTrueClient instances...');
      try {
        // This is a basic check - in reality, multiple instances are harder to detect
        if (window._supabaseInstances && window._supabaseInstances.length > 1) {
          addResult(
            'Instances',
            'warning',
            `Multiple instances detected: ${window._supabaseInstances.length}`
          );
        } else {
          addResult('Instances', 'success', 'No multiple instance issues detected');
        }
      } catch (error) {
        addResult('Instances', 'success', 'Instance check completed (no issues found)');
      }

      addResult('Complete', 'success', 'Diagnostic test completed');
    } catch (error) {
      addResult('Error', 'error', 'Diagnostic failed', error.message);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = status => {
    switch (status) {
      case 'success':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'error':
        return 'text-red-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = status => {
    switch (status) {
      case 'success':
        return '✅';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      case 'info':
        return 'ℹ️';
      default:
        return '•';
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4">Order System Diagnostics</h2>

      <div className="mb-6">
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className={`px-4 py-2 rounded ${
            isRunning
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
        </button>
      </div>

      {results.length > 0 && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {results.map((result, index) => (
            <div key={index} className="border-l-4 border-gray-200 pl-4 py-2">
              <div className="flex items-center space-x-2">
                <span>{getStatusIcon(result.status)}</span>
                <span className="font-medium">{result.test}</span>
                <span className="text-gray-500 text-sm">{result.timestamp}</span>
              </div>
              <div className={`ml-6 ${getStatusColor(result.status)}`}>{result.message}</div>
              {result.details && (
                <div className="ml-6 text-sm text-gray-600 bg-gray-100 p-2 rounded mt-1">
                  <pre className="whitespace-pre-wrap">{result.details}</pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {results.length === 0 && (
        <div className="text-gray-500 text-center py-8">
          Click "Run Diagnostics" to test the order system
        </div>
      )}
    </div>
  );
};

export default OrderDiagnostic;
