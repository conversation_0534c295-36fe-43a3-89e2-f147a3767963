import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../context/AuthContext';
import { Fa<PERSON>ser, FaSignOutAlt, FaShoppingBag, FaHeart, FaCog } from 'react-icons/fa';

const ProfileDropdown = () => {
  const { t } = useTranslation();
  const { user, signOut } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = async () => {
    await signOut();
    setIsOpen(false);
  };

  // Get user initials for avatar
  const getInitials = () => {
    if (!user) return '?';

    if (user.user_metadata?.full_name) {
      const nameParts = user.user_metadata.full_name.split(' ');
      if (nameParts.length >= 2) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return nameParts[0][0].toUpperCase();
    }

    if (user.email) {
      return user.email[0].toUpperCase();
    }

    return '?';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button onClick={() => setIsOpen(!isOpen)} className="flex items-center space-x-1">
        <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
          {user?.user_metadata?.avatar_url ? (
            <img
              src={user.user_metadata.avatar_url}
              alt={t('user_avatar', 'User Avatar')}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <span className="font-medium text-sm">{getInitials()}</span>
          )}
        </div>
      </button>

      {isOpen && (
        <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="px-4 py-2 border-b">
            <p className="text-sm font-medium">{user?.email}</p>
          </div>

          <Link
            to="/account"
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsOpen(false)}
          >
            <FaUser className="mr-2" />
            {t('my_account', 'Мой аккаунт')}
          </Link>

          <Link
            to="/orders"
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsOpen(false)}
          >
            <FaShoppingBag className="mr-2" />
            {t('my_orders', 'Мои заказы')}
          </Link>

          <Link
            to="/wishlist"
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsOpen(false)}
          >
            <FaHeart className="mr-2" />
            {t('wishlist', 'Избранное')}
          </Link>

          {user?.app_metadata?.claims_admin && (
            <Link
              to="/admin"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              onClick={() => setIsOpen(false)}
            >
              <FaCog className="mr-2" />
              {t('admin_panel', 'Админ панель')}
            </Link>
          )}

          <button
            onClick={handleSignOut}
            className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <FaSignOutAlt className="mr-2" />
            {t('sign_out', 'Выйти')}
          </button>
        </div>
      )}
    </div>
  );
};

export default ProfileDropdown;
