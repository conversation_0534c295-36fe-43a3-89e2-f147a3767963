import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

/**
 * Улучшенный компонент баннера с поддержкой различных стилей и эффектов
 * @param {object} props - Свойства компонента
 * @param {object} props.banner - Данные баннера (title, subtitle, image_url, link_url)
 * @param {string} props.position - Позиция баннера ('top' или 'bottom')
 * @param {string} props.textColor - Цвет текста баннера
 * @param {boolean} props.darkOverlay - Добавлять ли темный оверлей для улучшения читаемости
 * @param {string} props.overlayOpacity - Непрозрачность оверлея (0.3, 0.5, 0.7)
 * @param {string} props.height - Высота баннера ('small', 'medium', 'large')
 */
const Banner = ({
  banner,
  position = 'top',
  textColor = 'white', // Will now be used in text classes
  darkOverlay = true,
  overlayOpacity = '0.5',
  height = 'medium'
}) => {
  if (!banner) return null;

  // Определяем высоту на основе параметра
  const heightClass =
    {
      small: 'h-48 md:h-64',
      medium: 'h-64 md:h-80',
      large: 'h-80 md:h-96'
    }[height] || 'h-64 md:h-80';

  // Анимация для баннера
  const bannerAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
  };

  // Извлекаем данные из баннера с проверкой на существование
  const { title, subtitle, image_url, link_url } = banner;

  // Содержимое баннера
  const bannerContent = (
    <div className="absolute-full-width-container">
      <div
        className={`relative ${heightClass} full-width-banner ${position}-banner`}
        style={{
          width: '100vw',
          left: '50%',
          right: '50%',
          marginLeft: '-50vw',
          marginRight: '-50vw'
        }}
      >
        {/* Фоновое изображение */}
        <div
          className="absolute inset-0 bg-cover bg-center w-full h-full transform hover:scale-105 transition-transform duration-500"
          style={{ backgroundImage: `url(${image_url})` }}
        />

        {/* Затемняющий оверлей для улучшения читаемости текста */}
        {darkOverlay && (
          <div className="absolute inset-0 bg-black" style={{ opacity: overlayOpacity }} />
        )}

        {/* Содержимое баннера */}
        <div className="absolute inset-0 flex flex-col items-center justify-center p-6 text-center z-10">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={bannerAnimation}
            className="max-w-3xl"
          >
            {title && (
              <h2
                className={`text-${textColor} font-bold text-3xl md:text-4xl lg:text-5xl mb-4 font-heading leading-tight`}
                style={{ textShadow: '2px 2px 8px rgba(0,0,0,0.8)' }}
              >
                {title}
              </h2>
            )}

            {subtitle && (
              <p
                className={`text-${textColor} text-lg md:text-xl lg:text-2xl mb-6 max-w-2xl mx-auto font-body`}
                style={{ textShadow: '1px 1px 5px rgba(0,0,0,0.7)' }}
              >
                {subtitle}
              </p>
            )}

            {link_url && link_url !== '#' && (
              <button className="mt-4 px-6 py-3 bg-primary hover:bg-primary-dark text-white font-semibold rounded-md transition-colors duration-300 transform hover:scale-105 hover:shadow-lg">
                Подробнее
              </button>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );

  // Если есть ссылка, оборачиваем содержимое в Link
  return link_url && link_url !== '#' ? (
    <Link to={link_url} className="block w-full">
      {bannerContent}
    </Link>
  ) : (
    bannerContent
  );
};

export default Banner;
