import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

const PartnersSlider = ({
  partners = [],
  autoPlay = true,
  autoPlayInterval = 3000,
  showNavigation = true,
  showDots = true,
  className = '',
  slidesPerView = { mobile: 2, tablet: 3, desktop: 4, large: 5 }
}) => {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);
  const [currentSlidesPerView, setCurrentSlidesPerView] = useState(4);
  const sliderRef = useRef(null);
  const autoPlayRef = useRef(null);

  // Responsive slides per view
  useEffect(() => {
    const updateSlidesPerView = () => {
      if (typeof window === 'undefined') return;
      
      const width = window.innerWidth;
      if (width < 640) {
        setCurrentSlidesPerView(slidesPerView.mobile);
      } else if (width < 768) {
        setCurrentSlidesPerView(slidesPerView.tablet);
      } else if (width < 1024) {
        setCurrentSlidesPerView(slidesPerView.desktop);
      } else {
        setCurrentSlidesPerView(slidesPerView.large);
      }
    };

    updateSlidesPerView();
    window.addEventListener('resize', updateSlidesPerView);
    return () => window.removeEventListener('resize', updateSlidesPerView);
  }, [slidesPerView]);

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying && partners.length > currentSlidesPerView) {
      autoPlayRef.current = setInterval(() => {
        setCurrentSlide(prev => {
          const maxSlides = Math.ceil(partners.length / currentSlidesPerView);
          return (prev + 1) % maxSlides;
        });
      }, autoPlayInterval);
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, partners.length, currentSlidesPerView, autoPlayInterval]);

  if (!partners.length) return null;

  const totalSlides = Math.ceil(partners.length / currentSlidesPerView);

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + totalSlides) % totalSlides);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const handleMouseEnter = () => {
    if (autoPlay) setIsAutoPlaying(false);
  };

  const handleMouseLeave = () => {
    if (autoPlay) setIsAutoPlaying(true);
  };

  return (
    <div className={`partners-slider pb-4 ${className}`}>
      {/* Container with navigation */}
      <div
        className="relative flex items-center"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Left Navigation Arrow */}
        {showNavigation && totalSlides > 1 && (
          <button
            onClick={prevSlide}
            className="nav-button prev hidden sm:flex flex-shrink-0 mr-3 md:mr-4 bg-white hover:bg-gray-50 text-gray-600 hover:text-primary rounded-full p-2 md:p-3 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-110 z-10"
            aria-label={t('previous_slide', 'Previous slide')}
          >
            <svg className="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}

        {/* Slider Container */}
        <div className="slider-container flex-1 overflow-hidden rounded-xl pb-2">
          <div
            ref={sliderRef}
            className="slider-track flex transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(-${currentSlide * (100 / totalSlides)}%)`,
              width: `${totalSlides * 100}%`
            }}
          >
            {Array.from({ length: totalSlides }).map((_, slideIndex) => (
              <div
                key={slideIndex}
                className="flex"
                style={{ width: `${100 / totalSlides}%` }}
              >
                {partners
                  .slice(slideIndex * currentSlidesPerView, (slideIndex + 1) * currentSlidesPerView)
                  .map((partner) => (
                    <div
                      key={partner.id}
                      className="partner-slide flex-1 px-2"
                    >
                      <div className="group">
                        <a
                          href={partner.website_url || partner.link || '#'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="partner-card block bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 p-4 h-24 flex items-center justify-center group-hover:scale-105 border border-gray-100"
                        >
                          <img
                            src={partner.logo_url || partner.image}
                            alt={partner.name}
                            className="partner-logo max-h-12 max-w-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                            onError={e => {
                              e.target.onerror = null;
                              e.target.src = `https://placehold.co/150x60/f3f4f6/6b7280?text=${encodeURIComponent(partner.name)}`;
                            }}
                          />
                        </a>
                      </div>
                    </div>
                  ))}
              </div>
            ))}
          </div>
        </div>

        {/* Right Navigation Arrow */}
        {showNavigation && totalSlides > 1 && (
          <button
            onClick={nextSlide}
            className="nav-button next hidden sm:flex flex-shrink-0 ml-3 md:ml-4 bg-white hover:bg-gray-50 text-gray-600 hover:text-primary rounded-full p-2 md:p-3 shadow-md hover:shadow-lg transition-all duration-300 hover:scale-110 z-10"
            aria-label={t('next_slide', 'Next slide')}
          >
            <svg className="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        )}
      </div>

      {/* Dots Indicator */}
      {showDots && totalSlides > 1 && (
        <div className="dots-container flex justify-center mt-6 space-x-2">
          {Array.from({ length: totalSlides }).map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`dot w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                index === currentSlide
                  ? 'bg-primary scale-125'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
              aria-label={t('go_to_slide', 'Go to slide {{number}}', { number: index + 1 })}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default PartnersSlider;
