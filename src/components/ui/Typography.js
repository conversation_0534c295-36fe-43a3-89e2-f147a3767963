import React from 'react';

// Компонент для заголовков
export const Heading = ({ as: Component = 'h2', variant, children, className = '', ...props }) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'h1':
        return 'text-h1';
      case 'h2':
        return 'text-h2';
      case 'h3':
        return 'text-h3';
      case 'h4':
        return 'text-xl font-semibold';
      case 'h5':
        return 'text-lg font-semibold';
      case 'h6':
        return 'text-base font-semibold';
      default:
        // Если variant не указан, определяем класс по Component
        switch (Component) {
          case 'h1':
            return 'text-h1';
          case 'h2':
            return 'text-h2';
          case 'h3':
            return 'text-h3';
          case 'h4':
            return 'text-xl font-semibold';
          case 'h5':
            return 'text-lg font-semibold';
          case 'h6':
            return 'text-base font-semibold';
          default:
            return 'text-h2';
        }
    }
  };

  return (
    <Component
      className={`font-heading text-heading ${getVariantClasses()} ${className}`}
      {...props}
    >
      {children}
    </Component>
  );
};

// Компонент для текста
export const Text = ({
  as: Component = 'p',
  variant = 'body',
  children,
  className = '',
  ...props
}) => {
  const variants = {
    body: 'text-base text-body',
    lead: 'text-lg text-body',
    small: 'text-sm text-muted',
    tiny: 'text-xs text-muted',
    muted: 'text-base text-muted',
    error: 'text-base text-red-600',
    success: 'text-base text-green-600'
  };

  return (
    <Component className={`font-sans ${variants[variant]} ${className}`} {...props}>
      {children}
    </Component>
  );
};

// Компонент для ссылок
export const Link = ({ href, children, className = '', variant = 'default', ...props }) => {
  const variants = {
    default: 'text-primary hover:text-primary-dark hover:underline',
    muted: 'text-muted hover:text-primary hover:underline',
    button:
      'inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-primary hover:bg-primary-dark text-white'
  };

  return (
    <a href={href} className={`${variants[variant]} ${className}`} {...props}>
      {children}
    </a>
  );
};

// Компонент для маркированных списков
export const List = ({ children, className = '', ...props }) => {
  return (
    <ul className={`list-disc pl-5 space-y-1 ${className}`} {...props}>
      {children}
    </ul>
  );
};

export const ListItem = ({ children, className = '', ...props }) => {
  return (
    <li className={`text-body ${className}`} {...props}>
      {children}
    </li>
  );
};
