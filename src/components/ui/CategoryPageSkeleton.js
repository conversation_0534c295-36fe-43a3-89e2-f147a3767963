import React from 'react';

const CategoryPageSkeleton = () => {
  return (
    <div className="animate-pulse">
      {/* Breadcrumbs */}
      <div className="bg-gray-100 dark:bg-gray-900">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center">
            <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <span className="mx-2">/</span>
            <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Category heading */}
        <div className="h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded mb-8"></div>

        {/* Subcategories */}
        <div className="mb-12">
          <div className="h-6 w-36 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Array(4)
              .fill(0)
              .map((_, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-gray-800 border rounded-lg overflow-hidden"
                >
                  <div className="w-full h-32 bg-gray-200 dark:bg-gray-700"></div>
                  <div className="p-4">
                    <div className="h-5 w-3/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Products count */}
        <div className="h-6 w-36 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>

        {/* Filters and sorting */}
        <div className="mb-6">
          <div className="sticky top-16 z-10 bg-white dark:bg-gray-800 shadow-sm py-3 px-4 rounded-lg mb-4 flex flex-wrap gap-4 justify-between items-center">
            <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-8 w-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="w-full md:w-auto h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            {/* Filters sidebar */}
            <div className="hidden lg:block lg:w-1/4">
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>

                {/* Filter groups */}
                {Array(3)
                  .fill(0)
                  .map((_, index) => (
                    <div key={index} className="mb-6">
                      <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded mb-3"></div>
                      <div className="space-y-2">
                        {Array(4)
                          .fill(0)
                          .map((_, idx) => (
                            <div key={idx} className="flex items-center">
                              <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded mr-3"></div>
                              <div className="h-4 flex-grow bg-gray-200 dark:bg-gray-700 rounded"></div>
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}

                <div className="h-8 w-full bg-gray-200 dark:bg-gray-700 rounded mt-6"></div>
              </div>
            </div>

            {/* Products grid */}
            <div className="lg:w-3/4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {Array(9)
                  .fill(0)
                  .map((_, index) => (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 border rounded-lg overflow-hidden shadow-sm h-full flex flex-col"
                    >
                      <div className="relative">
                        <div className="w-full h-48 bg-gray-200 dark:bg-gray-700"></div>
                      </div>
                      <div className="p-4 flex-grow flex flex-col">
                        <div className="h-6 w-3/4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                        <div className="h-4 w-1/2 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                        <div className="mt-auto">
                          <div className="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>

              {/* Load more button */}
              <div className="text-center mt-8">
                <div className="inline-block h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryPageSkeleton;
