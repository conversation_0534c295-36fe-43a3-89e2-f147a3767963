import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const BackButton = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  // Проверяем, находимся ли мы на главной странице админки
  const isAdminRoot = location.pathname === '/admin' || location.pathname === '/admin/';

  // Если мы на главной странице админки, не показываем кнопку назад
  if (isAdminRoot) {
    return null;
  }

  return (
    <button
      onClick={() => navigate(-1)}
      className="mb-4 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
      aria-label={t('back', 'Назад')}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 mr-1"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M10 19l-7-7m0 0l7-7m-7 7h18"
        />
      </svg>
      {t('back', 'Назад')}
    </button>
  );
};

export default BackButton;
