import React from 'react';

// Скелетон баннера
const BannerSkeleton = () => {
  return (
    <div className="banner-skeleton w-full h-64 sm:h-80 md:h-96 bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse mb-8">
      <div className="flex items-center justify-center w-full h-full">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-8 w-48 bg-gray-300 dark:bg-gray-700 rounded"></div>
          <div className="h-4 w-64 bg-gray-300 dark:bg-gray-700 rounded"></div>
          <div className="h-10 w-32 bg-gray-300 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    </div>
  );
};

// Скелетон карточки популярной категории
const CategoryCardSkeleton = () => {
  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-800 animate-pulse">
      <div className="aspect-square bg-gray-200 dark:bg-gray-800"></div>
      <div className="p-4">
        <div className="h-5 w-2/3 bg-gray-200 dark:bg-gray-800 rounded mb-2"></div>
        <div className="h-4 w-1/2 bg-gray-200 dark:bg-gray-800 rounded"></div>
      </div>
    </div>
  );
};

// Скелетон для секции популярных категорий
const PopularCategoriesSkeleton = () => {
  return (
    <div className="mb-12">
      <div className="h-8 w-48 bg-gray-200 dark:bg-gray-800 rounded mb-6"></div>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <CategoryCardSkeleton key={index} />
          ))}
      </div>
    </div>
  );
};

// Скелетон для промо-блока
const PromoBlockSkeleton = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
      <div className="bg-gray-200 dark:bg-gray-800 rounded-lg h-48 animate-pulse"></div>
      <div className="bg-gray-200 dark:bg-gray-800 rounded-lg h-48 animate-pulse"></div>
    </div>
  );
};

// Основной скелетон главной страницы
const HomePageSkeleton = () => {
  return (
    <div className="container mx-auto px-4">
      <BannerSkeleton />

      <PopularCategoriesSkeleton />

      <div className="mb-12">
        <div className="h-8 w-48 bg-gray-200 dark:bg-gray-800 rounded mb-6"></div>
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array(8)
            .fill(0)
            .map((_, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-900 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-800 animate-pulse"
              >
                <div className="aspect-square bg-gray-200 dark:bg-gray-800"></div>
                <div className="p-4">
                  <div className="h-5 w-3/4 bg-gray-200 dark:bg-gray-800 rounded mb-2"></div>
                  <div className="h-6 w-1/3 bg-gray-200 dark:bg-gray-800 rounded mb-2"></div>
                  <div className="h-8 w-full bg-gray-200 dark:bg-gray-800 rounded mt-4"></div>
                </div>
              </div>
            ))}
        </div>
      </div>

      <PromoBlockSkeleton />

      <BannerSkeleton />
    </div>
  );
};

export default HomePageSkeleton;
