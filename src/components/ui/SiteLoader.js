import React from 'react';
import { useLoading } from '../../context/LoadingContext';

const SiteLoader = () => {
  const { loadingMessage } = useLoading();

  return (
    <div className="fixed inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center flex-col">
      <div className="flex items-center justify-center">
        <div className="animate-bounce mx-2 h-4 w-4 rounded-full bg-primary delay-75"></div>
        <div className="animate-bounce mx-2 h-4 w-4 rounded-full bg-primary delay-150"></div>
        <div className="animate-bounce mx-2 h-4 w-4 rounded-full bg-primary delay-300"></div>
      </div>
      {loadingMessage && <p className="text-gray-600 mt-4 font-medium">{loadingMessage}</p>}
    </div>
  );
};

export default SiteLoader;
