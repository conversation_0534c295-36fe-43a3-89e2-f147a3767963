import React from 'react';

const ProductPageSkeleton = () => {
  return (
    <div className="container mx-auto px-4 py-6 max-w-[1400px] animate-pulse">
      {/* Breadcrumbs */}
      <div className="flex mb-6">
        <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="mx-2">/</div>
        <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="mx-2">/</div>
        <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>

      {/* Product Card */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-0">
          {/* Gallery */}
          <div className="lg:col-span-3 p-4 md:p-8">
            {/* Main image */}
            <div className="mb-6 aspect-square md:aspect-[4/3] w-full bg-gray-200 dark:bg-gray-700 rounded-lg"></div>

            {/* Thumbnails */}
            <div className="grid grid-cols-5 md:grid-cols-6 gap-2">
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-md"
                  ></div>
                ))}
            </div>
          </div>

          {/* Product info */}
          <div className="lg:col-span-2 p-4 md:p-8 border-t md:border-t-0 md:border-l border-gray-100 dark:border-gray-700">
            {/* Title */}
            <div className="mb-6">
              <div className="h-8 w-3/4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-4 w-1/2 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-3 mb-6">
              <div className="flex">
                {Array(5)
                  .fill(0)
                  .map((_, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 bg-gray-200 dark:bg-gray-700 rounded-full mr-1"
                    ></div>
                  ))}
              </div>
              <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>

            {/* Price */}
            <div className="mb-6">
              <div className="h-8 w-1/3 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-4 w-1/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>

            {/* Parameters */}
            <div className="mb-6 space-y-3">
              <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                {Array(4)
                  .fill(0)
                  .map((_, index) => (
                    <div key={index} className="col-span-1">
                      <div className="h-3 w-1/2 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                      <div className="h-5 w-3/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  ))}
              </div>
            </div>

            {/* Quantity */}
            <div className="mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="w-24 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                <div className="h-10 w-full md:w-48 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm border-t border-gray-100 dark:border-gray-700 pt-6">
              {Array(4)
                .fill(0)
                .map((_, index) => (
                  <div key={index} className="flex items-start">
                    <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                    <div className="ml-3 flex-1">
                      <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                      <div className="h-3 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-t border-gray-200 dark:border-gray-700">
          <div className="flex overflow-x-auto">
            {Array(3)
              .fill(0)
              .map((_, index) => (
                <div key={index} className="px-6 py-3 mr-4">
                  <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              ))}
          </div>

          {/* Tab content */}
          <div className="p-6">
            <div className="space-y-3">
              <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 w-5/6 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Similar products */}
      <div className="mt-12">
        <div className="h-7 w-48 bg-gray-200 dark:bg-gray-700 rounded mb-6"></div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700"
              >
                <div className="aspect-square bg-gray-200 dark:bg-gray-700"></div>
                <div className="p-3">
                  <div className="h-5 w-3/4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                  <div className="h-6 w-1/2 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ProductPageSkeleton;
