import React from 'react';

// Скелетон карточки товара для отображения во время загрузки
const ProductCardSkeleton = () => {
  return (
    <div className="relative bg-white dark:bg-gray-900 rounded-lg shadow-md p-4 overflow-hidden">
      {/* Анимация пульсации для скелетона */}
      <div className="animate-pulse">
        {/* Заглушка изображения товара */}
        <div className="aspect-square bg-gray-200 dark:bg-gray-800 rounded-md mb-4"></div>

        {/* Заглушка названия товара */}
        <div className="h-5 bg-gray-200 dark:bg-gray-800 rounded mb-2 w-3/4"></div>

        {/* Заглушка цены товара (два элемента рядом) */}
        <div className="flex items-center mt-2">
          <div className="h-6 bg-gray-200 dark:bg-gray-800 rounded w-1/3 mr-2"></div>
          <div className="h-6 bg-gray-200 dark:bg-gray-800 rounded w-1/4 opacity-70"></div>
        </div>

        {/* Заглушка дополнительной информации */}
        <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-1/2 mt-2"></div>

        {/* Заглушка кнопки */}
        <div className="h-10 bg-gray-200 dark:bg-gray-800 rounded w-full mt-4"></div>
      </div>
    </div>
  );
};

// Компонент для отображения сетки скелетонов товаров
export const ProductGridSkeleton = ({ count = 8 }) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <ProductCardSkeleton key={index} />
        ))}
    </div>
  );
};

export default ProductCardSkeleton;
