import React from 'react';
import { useTheme } from '../context/ThemeContext';

export const ProductCardSkeleton = () => {
  const { theme } = useTheme();
  const bgColor = theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200';

  return (
    <div className="border rounded-lg p-4 space-y-3 animate-pulse">
      <div className={`w-full h-48 ${bgColor} rounded`}></div>
      <div className={`h-4 ${bgColor} rounded w-3/4`}></div>
      <div className={`h-4 ${bgColor} rounded w-1/2`}></div>
      <div className="space-y-2">
        <div className={`h-8 ${bgColor} rounded`}></div>
        <div className={`h-8 ${bgColor} rounded`}></div>
      </div>
    </div>
  );
};

export const ProductListSkeleton = ({ count = 4 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array(count)
        .fill()
        .map((_, i) => (
          <ProductCardSkeleton key={i} />
        ))}
    </div>
  );
};

export const TableRowSkeleton = () => {
  const { theme } = useTheme();
  const bgColor = theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200';

  return (
    <div className="flex space-x-4 p-4">
      <div className={`${bgColor} rounded-full w-10 h-10`}></div>
      <div className="flex-1 space-y-2">
        <div className={`${bgColor} h-4 rounded w-3/4`}></div>
        <div className={`${bgColor} h-4 rounded w-1/2`}></div>
      </div>
    </div>
  );
};

export const ProductDetailsSkeleton = () => {
  const { theme } = useTheme();
  const bgColor = theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200';

  return (
    <div className="container mx-auto px-4 py-8 animate-pulse">
      <div className="grid md:grid-cols-2 gap-8">
        <div className={`w-full h-96 ${bgColor} rounded-lg`}></div>
        <div className="space-y-4">
          <div className={`h-8 ${bgColor} rounded w-3/4`}></div>
          <div className={`h-4 ${bgColor} rounded w-full`}></div>
          <div className={`h-4 ${bgColor} rounded w-full`}></div>
          <div className={`h-4 ${bgColor} rounded w-2/3`}></div>
          <div className="flex space-x-4">
            <div className={`h-10 ${bgColor} rounded w-32`}></div>
            <div className={`h-10 ${bgColor} rounded w-32`}></div>
          </div>
        </div>
      </div>
    </div>
  );
};
