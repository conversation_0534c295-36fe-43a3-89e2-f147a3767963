import { banners } from '../assets/images/data';
import { useTranslation } from 'react-i18next';

const Carousel = () => {
  const { t } = useTranslation();

  return (
    <div className="mb-8">
      {banners.map(banner => (
        <div key={banner.id} className="relative">
          <img
            src={banner.image}
            alt={banner.alt}
            className="w-full h-96 object-cover rounded"
            onError={e => {
              e.target.onerror = null;
              e.target.src = 'https://placehold.co/1200x400/EEE/31343C?text=Banner+Not+Found';
            }}
          />
          <div className="absolute bottom-4 left-4 text-white">
            <h2 className="text-2xl font-semibold">{banner.alt}</h2>
            <a href="/categories" className="text-blue-300 hover:text-blue-100">
              {t('shop_now', 'Shop Now')}
            </a>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Carousel;
