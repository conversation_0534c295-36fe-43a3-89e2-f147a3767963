import { supabase } from '../supabaseClient';
require('dotenv').config();

const checkTableExists = async tableName => {
  try {
    // Guard against undefined supabase client
    if (!supabase || !supabase.from) {
      console.error('❌ Supabase client not available in checkTableExists');
      return false;
    }

    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', tableName)
      .single();

    if (error) throw error;
    return !!data;
  } catch (error) {
    console.error('Error checking if table exists:', error);
    return false;
  }
};

const executeSql = async (query, params = {}) => {
  try {
    // Guard against undefined supabase client
    if (!supabase || !supabase.rpc) {
      console.error('❌ Supabase client not available in executeSql');
      return null;
    }

    const { data, error } = await supabase.rpc('exec_sql', {
      query,
      params: JSON.stringify(params)
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error executing SQL:', error);
    return null;
  }
};

/**
 * Execute multiple SQL queries in a single transaction
 * @param {string[]} queries - Array of SQL queries to execute
 * @param {Object} params - Optional parameters for the queries
 * @returns {Promise<Object>} - Result of the batch execution
 */
const executeSqlBatch = async (queries, params = {}) => {
  if (!queries || !Array.isArray(queries) || queries.length === 0) {
    return null;
  }

  // Combine queries into a single transaction
  const combinedQuery = `
    BEGIN;
    ${queries.join(';\n    ')}${queries[queries.length - 1].endsWith(';') ? '' : ';'}
    COMMIT;
  `;

  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      query: combinedQuery,
      params: JSON.stringify(params)
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error executing SQL batch:', error);
    return null;
  }
};

export { supabase };
