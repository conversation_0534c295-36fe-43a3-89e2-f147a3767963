/**
 * Emergency admin access utility
 *
 * This can be run from the browser console to make the current logged-in user an admin.
 * Copy-paste the entire function into the browser console and run it when logged in.
 */

async function makeCurrentUserAdmin() {
  try {
    // Check if we have the supabase client available
    if (!window.supabase) {
      console.error('Error: Supabase client not found in window object');
      return;
    }

    // Get the current user
    const {
      data: { user },
      error: userError
    } = await window.supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error: No user is currently logged in', userError);
      return;
    }

    console.log(`Making user ${user.email} an admin...`);

    // First try to create or update the profiles table with is_admin column
    const { error: sqlError } = await window.supabase.rpc('exec_sql', {
      query: `
        DO $profile_setup$
        BEGIN
          /* Create profiles table if it doesn't exist */
          IF NOT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name = 'profiles'
          ) THEN
            CREATE TABLE public.profiles (
              id UUID PRIMARY KEY REFERENCES auth.users(id),
              email TEXT,
              first_name TEXT,
              last_name TEXT,
              avatar_url TEXT,
              is_admin BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMPTZ DEFAULT NOW(),
              updated_at TIMESTAMPTZ DEFAULT NOW()
            );
          END IF;
          
          -- Add is_admin column if it doesn't exist
          IF NOT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public'
            AND table_name = 'profiles'
            AND column_name = 'is_admin'
          ) THEN
            ALTER TABLE public.profiles ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
          END IF;
        END $profile_setup$;
      `
    });

    if (sqlError) {
      console.warn('Could not set up profiles table, attempting direct profile update...');
    }

    // Then try to update the user profile with is_admin=true
    const { error: updateError } = await window.supabase.from('profiles').upsert({
      id: user.id,
      email: user.email,
      is_admin: true,
      updated_at: new Date().toISOString()
    });

    if (updateError) {
      // Try direct SQL if standard upsert fails
      const { error: directSqlError } = await window.supabase.rpc('exec_sql', {
        query: `
          INSERT INTO public.profiles (id, email, is_admin, updated_at)
          VALUES ('${user.id}', '${user.email}', TRUE, NOW())
          ON CONFLICT (id) 
          DO UPDATE SET 
            email = EXCLUDED.email,
            is_admin = TRUE,
            updated_at = NOW();
        `
      });

      if (directSqlError) {
        throw new Error(`Failed to update profile: ${directSqlError.message}`);
      }
    }

    console.log(`✅ Successfully made ${user.email} an admin!`);

    // Also modify the admin users array for extra security

    return true;
  } catch (error) {
    console.error('Error making current user admin:', error.message);
    return false;
  }
}

// Export for potential use as a module
export { makeCurrentUserAdmin };
