import { supabase } from '../supabaseClient';

/**
 * Removes any database entries that use placeholder image services that cause errors
 */
export const cleanupPlaceholderImages = async () => {
  try {
    // Guard against undefined supabase client
    if (!supabase || !supabase.from) {
      console.error('❌ Supabase client not available in cleanupPlaceholderImages');
      return false;
    }

    // Create a function to check if an image URL is a placeholder service
    const isPlaceholderServiceUrl = url => {
      if (!url) return false;
      return (
        url.includes('via.placeholder.com') ||
        url.includes('placeholder.com') ||
        url.includes('placehold.it')
      );
    };

    // First, update brands with placeholder URLs to use null instead
    const { data: brands, error: brandsError } = await supabase.from('brands').select('id, logo');

    if (brandsError) throw brandsError;

    for (const brand of brands) {
      if (isPlaceholderServiceUrl(brand.logo)) {
        const { error } = await supabase.from('brands').update({ logo: null }).eq('id', brand.id);

        if (error) console.error('Error updating brand:', error);
      }
    }

    // Then update products with placeholder URLs
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, image');

    if (productsError) throw productsError;

    for (const product of products) {
      if (isPlaceholderServiceUrl(product.image)) {
        const { error } = await supabase
          .from('products')
          .update({ image: null })
          .eq('id', product.id);

        if (error) console.error('Error updating product:', error);
      }
    }

    // Update categories with placeholder URLs
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, image');

    if (categoriesError) throw categoriesError;

    for (const category of categories) {
      if (isPlaceholderServiceUrl(category.image)) {
        const { error } = await supabase
          .from('categories')
          .update({ image: null })
          .eq('id', category.id);

        if (error) console.error('Error updating category:', error);
      }
    }
    return true;
  } catch (error) {
    console.error('Error cleaning up placeholder URLs:', error);
    return false;
  }
};
