const testCategories = [
  {
    name: 'Living Room',
    image: 'https://source.unsplash.com/800x600/?living-room',
    description: 'Comfortable and stylish living room furniture'
  },
  {
    name: 'Bedroom',
    image: 'https://source.unsplash.com/800x600/?bedroom',
    description: 'Peaceful bedroom furniture'
  },
  {
    name: 'Kitchen',
    image: 'https://source.unsplash.com/800x600/?kitchen',
    description: 'Modern kitchen furniture and accessories'
  }
];

const testProducts = [
  {
    name: 'Modern Sofa',
    price: 899.99,
    description: 'Comfortable modern sofa with premium fabric',
    images: ['https://source.unsplash.com/800x600/?sofa'],
    category: 'living-room',
    stock: 10,
    rating: 4.5,
    specs: {
      material: 'Fabric',
      dimensions: '220x85x70 cm',
      color: 'Gray'
    }
  }
  // Add more test products...
];

export const seedDatabase = async () => {
  try {
    // Add categories
    for (const category of testCategories) {
    }

    // Add products
    for (const product of testProducts) {
    }

    return true;
  } catch (error) {
    console.error('Error seeding database:', error);
    return false;
  }
};

export const seedProductImages = async () => {
  const uploadPromises = testProducts.map(async product => {
    try {
      // Simulate image upload

      return {
        ...product,
        images: [product.images[0]]
      };
    } catch (error) {
      console.error(`Failed to upload image for product ${product.name}:`, error);
      return product;
    }
  });

  return Promise.all(uploadPromises);
};
