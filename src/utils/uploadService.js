import axios from 'axios';

// Assuming you set up a server endpoint for handling uploads
export const uploadImageToServer = async file => {
  const formData = new FormData();
  formData.append('image', file);

  try {
    const response = await axios.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: progressEvent => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        // Update progress state here
      }
    });

    return response.data.imageUrl; // URL to the uploaded image
  } catch (error) {
    console.error('Error uploading to server:', error);
    throw error;
  }
};
