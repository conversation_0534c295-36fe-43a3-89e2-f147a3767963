import { supabase } from '../supabaseClient';

export const checkDatabaseTables = async () => {
  try {
    const requiredTables = ['categories', 'products'];
    const tableChecks = await Promise.all(
      requiredTables.map(async table => {
        const { data, error } = await supabase.from(table).select('*').limit(1);

        if (error) {
          console.error(`${table} table error:`, error);
          return {
            table,
            exists: false,
            error: error.message
          };
        }

        return {
          table,
          exists: true,
          columns: data && data[0] ? Object.keys(data[0]) : [],
          hasData: data && data.length > 0
        };
      })
    );

    const hasErrors = tableChecks.some(check => !check.exists);

    return {
      success: !hasErrors,
      timestamp: new Date().toISOString(),
      tables: tableChecks.reduce(
        (acc, check) => ({
          ...acc,
          [check.table]: {
            exists: check.exists,
            columns: check.columns,
            hasData: check.hasData
          }
        }),
        {}
      )
    };
  } catch (error) {
    console.error('Database check failed:', error);
    return {
      success: false,
      error: 'Failed to check database structure',
      details: error,
      timestamp: new Date().toISOString()
    };
  }
};
