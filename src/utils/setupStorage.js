import { supabase } from '../supabaseClient';

export const initializeStorage = async () => {
  try {
    // Check if the bucket exists
    const { data: buckets } = await supabase.storage.listBuckets();
    const imagesBucketExists = buckets.some(bucket => bucket.name === 'images');

    // Create the bucket if it doesn't exist
    if (!imagesBucketExists) {
      const { error } = await supabase.storage.createBucket('images', {
        public: true,
        fileSizeLimit: 5242880 // 5MB
      });

      if (error) console.error('Error creating bucket:', error);
    }

    // Update bucket policies to allow public access
    await supabase.storage.from('images').setPublic();

    return { success: true };
  } catch (error) {
    console.error('Error initializing storage:', error);
    return { success: false, error };
  }
};
