// Simplified order creation helpers to prevent multiple client instances
import { supabase } from '../supabaseClient';

/**
 * Simplified order creation service
 */
export class OrderCreationService {
  /**
   * Create order with basic error handling
   */
  static async createOrder(orderData, orderItems) {
    try {
      console.log('Creating order with data:', orderData);

      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in createOrder');
        throw new Error('Database connection not available');
      }

      // Validate order data
      this.validateOrderData(orderData);

      // Create the order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert([orderData])
        .select()
        .single();

      if (orderError) {
        console.error('Order creation error:', orderError);
        throw new Error(`Failed to create order: ${orderError.message}`);
      }

      console.log('Order created successfully:', order);

      // Create order items if provided
      if (order && orderItems && orderItems.length > 0) {
        const items = orderItems.map(item => ({
          ...item,
          order_id: order.id
        }));

        const { error: itemsError } = await supabase.from('order_items').insert(items);

        if (itemsError) {
          console.error('Error creating order items:', itemsError);
          // Log warning but don't fail the order
          console.warn('Order created but some items failed to save');
        } else {
          console.log('Order items created successfully');
        }
      }

      return order;
    } catch (error) {
      console.error('Error in createOrder:', error);
      throw error;
    }
  }

  /**
   * Validate order data before creation
   */
  static validateOrderData(orderData) {
    const required = ['customer_name', 'customer_phone', 'total_amount'];
    const missing = required.filter(field => !orderData[field]);

    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }

    if (orderData.total_amount <= 0) {
      throw new Error('Order total must be greater than 0');
    }

    return true;
  }

  /**
   * Get order by ID
   */
  static async getOrderById(orderId) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in getOrderById');
        throw new Error('Database connection not available');
      }

      const { data: order, error } = await supabase
        .from('orders')
        .select(
          `
          *,
          order_items (*)
        `
        )
        .eq('id', orderId)
        .single();

      if (error) {
        console.error('Error fetching order:', error);
        throw new Error(`Failed to fetch order: ${error.message}`);
      }

      return order;
    } catch (error) {
      console.error('Error in getOrderById:', error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(orderId, status) {
    try {
      // Guard against undefined supabase client
      if (!supabase || !supabase.from) {
        console.error('❌ Supabase client not available in updateOrderStatus');
        throw new Error('Database connection not available');
      }

      const { data: order, error } = await supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('Error updating order status:', error);
        throw new Error(`Failed to update order status: ${error.message}`);
      }

      return order;
    } catch (error) {
      console.error('Error in updateOrderStatus:', error);
      throw error;
    }
  }
}

/**
 * Helper functions for order processing
 */
export const OrderHelpers = {
  /**
   * Format order data for database insertion
   */
  formatOrderData: (checkoutData, userId = null) => {
    return {
      customer_name: checkoutData.name,
      customer_email: checkoutData.email,
      customer_phone: checkoutData.phone,
      shipping_address: {
        address: checkoutData.address,
        city: checkoutData.city,
        state: checkoutData.state,
        zip: checkoutData.zip
      },
      total_amount: parseFloat(checkoutData.total),
      payment_method: checkoutData.paymentMethod || 'cash_on_delivery',
      payment_status: 'pending',
      status: 'pending',
      notes: checkoutData.notes || '',
      user_id: userId
    };
  },

  /**
   * Format order items for database insertion
   */
  formatOrderItems: cartItems => {
    return cartItems.map(item => ({
      product_id: item.id,
      product_name: item.name,
      quantity: parseInt(item.quantity),
      price: parseFloat(item.price)
    }));
  },

  /**
   * Calculate order total
   */
  calculateTotal: cartItems => {
    return cartItems.reduce((total, item) => {
      return total + parseFloat(item.price) * parseInt(item.quantity);
    }, 0);
  },

  /**
   * Generate order confirmation message
   */
  generateConfirmationMessage: order => {
    return `Order #${order.id.substring(0, 8)} created successfully! 
           Customer: ${order.customer_name}
           Total: $${order.total_amount}
           Status: ${order.status}`;
  }
};

export default OrderCreationService;
