import { supabase } from '../supabaseClient';

/**
 * Safely execute a Supabase query with fallback to SQL if ORM methods fail
 * @param {string} tableName - The table to query
 * @param {Object} options - Query options
 * @returns {Promise<{data: any, error: any}>} - Query result
 */
export const safeQuery = async (tableName, options = {}) => {
  const {
    select = '*',
    where = {},
    limit = null,
    offset = null,
    orderBy = null,
    orderDirection = null
  } = options;

  try {
    // First try using the ORM methods
    let query = supabase.from(tableName).select(select);

    // Apply filters
    for (const [key, value] of Object.entries(where)) {
      try {
        query = query.eq(key, value);
      } catch (err) {
        return directSqlQuery(tableName, options);
      }
    }

    // Apply ordering
    if (orderBy) {
      try {
        query = query.order(orderBy, { ascending: orderDirection !== 'desc' });
      } catch (err) {
        return directSqlQuery(tableName, options);
      }
    }

    // Apply pagination
    if (limit !== null) {
      try {
        query = query.limit(limit);
      } catch (err) {
        return directSqlQuery(tableName, options);
      }
    }

    if (offset !== null) {
      try {
        query = query.offset(offset);
      } catch (err) {
        return directSqlQuery(tableName, options);
      }
    }

    const { data, error } = await query;

    if (error) {
      return directSqlQuery(tableName, options);
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error in safeQuery ORM attempt:', error);
    return directSqlQuery(tableName, options);
  }
};

/**
 * Execute a query using direct SQL as a fallback
 * @param {string} tableName - The table to query
 * @param {Object} options - Query options
 * @returns {Promise<{data: any, error: any}>} - Query result
 */
const directSqlQuery = async (tableName, options = {}) => {
  const {
    select = '*',
    where = {},
    limit = null,
    offset = null,
    orderBy = null,
    orderDirection = null
  } = options;

  try {
    // Build the SQL query
    let sql = `SELECT ${select} FROM ${tableName}`;

    // Add WHERE clause
    if (Object.keys(where).length > 0) {
      const conditions = Object.entries(where)
        .map(([key, value]) => {
          // Handle different value types
          if (value === null) {
            return `${key} IS NULL`;
          } else if (typeof value === 'string') {
            return `${key} = '${value.replace(/'/g, "''")}'`;
          } else {
            return `${key} = ${value}`;
          }
        })
        .join(' AND ');

      sql += ` WHERE ${conditions}`;
    }

    // Add ORDER BY
    if (orderBy) {
      sql += ` ORDER BY ${orderBy} ${orderDirection === 'desc' ? 'DESC' : 'ASC'}`;
    }

    // Add LIMIT and OFFSET
    if (limit !== null) {
      sql += ` LIMIT ${limit}`;
    }

    if (offset !== null) {
      sql += ` OFFSET ${offset}`;
    }

    // Execute the SQL query
    const { data, error } = await supabase.rpc('exec_sql', {
      query: sql
    });

    if (error) {
      console.error('SQL query failed:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error in directSqlQuery:', error);
    return { data: null, error };
  }
};

/**
 * Safely insert data into a table with fallback
 * @param {string} tableName - The table to insert into
 * @param {Object|Array} data - Data to insert
 * @returns {Promise<{data: any, error: any}>} - Insert result
 */
export const safeInsert = async (tableName, data) => {
  try {
    // Try using the ORM insert
    const { data: result, error } = await supabase.from(tableName).insert(data).select();

    if (error) {
      return directSqlInsert(tableName, data);
    }

    return { data: result, error: null };
  } catch (error) {
    console.error('Error in safeInsert ORM attempt:', error);
    return directSqlInsert(tableName, data);
  }
};

/**
 * Insert data using direct SQL as a fallback
 * @param {string} tableName - The table to insert into
 * @param {Object|Array} data - Data to insert
 * @returns {Promise<{data: any, error: any}>} - Insert result
 */
const directSqlInsert = async (tableName, data) => {
  try {
    // Convert to array if it's not already
    const dataArray = Array.isArray(data) ? data : [data];

    // For each item, create an SQL insert statement
    const sqlPromises = dataArray.map(item => {
      const columns = Object.keys(item).join(', ');
      const values = Object.values(item)
        .map(value => {
          if (value === null) {
            return 'NULL';
          } else if (typeof value === 'string') {
            return `'${value.replace(/'/g, "''")}'`;
          } else if (typeof value === 'boolean') {
            return value ? 'TRUE' : 'FALSE';
          } else {
            return value;
          }
        })
        .join(', ');

      const sql = `
        INSERT INTO ${tableName} (${columns})
        VALUES (${values})
        RETURNING *;
      `;

      return supabase.rpc('exec_sql', { query: sql });
    });

    const results = await Promise.all(sqlPromises);

    // Check for errors
    const errors = results.filter(r => r.error).map(r => r.error);
    if (errors.length > 0) {
      console.error('SQL insert errors:', errors);
      return { data: null, error: errors[0] };
    }

    // Combine all data
    const allData = results.map(r => r.data).flat();
    return { data: allData, error: null };
  } catch (error) {
    console.error('Error in directSqlInsert:', error);
    return { data: null, error };
  }
};

/**
 * Safely update data in a table with fallback
 * @param {string} tableName - The table to update
 * @param {Object} data - Update data
 * @param {Object} where - Where conditions
 * @returns {Promise<{data: any, error: any}>} - Update result
 */
export const safeUpdate = async (tableName, data, where) => {
  try {
    // Try using the ORM update
    let query = supabase.from(tableName).update(data);

    // Apply filters
    for (const [key, value] of Object.entries(where)) {
      try {
        query = query.eq(key, value);
      } catch (err) {
        return directSqlUpdate(tableName, data, where);
      }
    }

    const { data: result, error } = await query.select();

    if (error) {
      return directSqlUpdate(tableName, data, where);
    }

    return { data: result, error: null };
  } catch (error) {
    console.error('Error in safeUpdate ORM attempt:', error);
    return directSqlUpdate(tableName, data, where);
  }
};

/**
 * Update data using direct SQL as a fallback
 * @param {string} tableName - The table to update
 * @param {Object} data - Update data
 * @param {Object} where - Where conditions
 * @returns {Promise<{data: any, error: any}>} - Update result
 */
const directSqlUpdate = async (tableName, data, where) => {
  try {
    // Build the SET clause
    const setClause = Object.entries(data)
      .map(([key, value]) => {
        if (value === null) {
          return `${key} = NULL`;
        } else if (typeof value === 'string') {
          return `${key} = '${value.replace(/'/g, "''")}'`;
        } else if (typeof value === 'boolean') {
          return `${key} = ${value ? 'TRUE' : 'FALSE'}`;
        } else {
          return `${key} = ${value}`;
        }
      })
      .join(', ');

    // Build the WHERE clause
    const whereClause = Object.entries(where)
      .map(([key, value]) => {
        if (value === null) {
          return `${key} IS NULL`;
        } else if (typeof value === 'string') {
          return `${key} = '${value.replace(/'/g, "''")}'`;
        } else {
          return `${key} = ${value}`;
        }
      })
      .join(' AND ');

    const sql = `
      UPDATE ${tableName}
      SET ${setClause}
      WHERE ${whereClause}
      RETURNING *;
    `;

    const { data: result, error } = await supabase.rpc('exec_sql', { query: sql });

    if (error) {
      console.error('SQL update failed:', error);
      return { data: null, error };
    }

    return { data: result, error: null };
  } catch (error) {
    console.error('Error in directSqlUpdate:', error);
    return { data: null, error };
  }
};
