/**
 * Utility functions for CSV file operations
 */

/**
 * Parse CSV content to an array of objects
 * @param {string} csvContent - The CSV content as string
 * @returns {Array<Object>} Array of objects with headers as keys
 */
export const parseCSV = csvContent => {
  if (!csvContent || typeof csvContent !== 'string') {
    throw new Error('Invalid CSV content');
  }

  const lines = csvContent.split(/\r?\n/);
  if (lines.length === 0) {
    return [];
  }

  const headers = lines[0].split(',').map(header => header.trim().toLowerCase());
  const result = [];

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    // Handle quoted values with commas inside
    const values = [];
    let inQuote = false;
    let currentValue = '';

    for (let j = 0; j < line.length; j++) {
      const char = line[j];

      if (char === '"' && (j === 0 || line[j - 1] !== '\\')) {
        inQuote = !inQuote;
      } else if (char === ',' && !inQuote) {
        values.push(currentValue);
        currentValue = '';
      } else {
        currentValue += char;
      }
    }

    values.push(currentValue); // Add the last value

    // Create object using headers as keys
    const obj = {};
    headers.forEach((header, index) => {
      if (header && index < values.length) {
        // Remove quotes from quoted values
        let value = values[index];
        if (value.startsWith('"') && value.endsWith('"')) {
          value = value.substring(1, value.length - 1);
        }
        // Try to parse numbers and booleans
        if (value.toLowerCase() === 'true') {
          obj[header] = true;
        } else if (value.toLowerCase() === 'false') {
          obj[header] = false;
        } else if (!isNaN(Number(value)) && value.trim() !== '') {
          obj[header] = Number(value);
        } else {
          obj[header] = value;
        }
      }
    });

    result.push(obj);
  }

  return result;
};

/**
 * Convert array of objects to CSV string
 * @param {Array<Object>} data - Array of objects to convert
 * @returns {string} CSV formatted string
 */
export const objectsToCSV = data => {
  if (!Array.isArray(data) || data.length === 0) {
    return '';
  }

  // Get all unique keys from all objects
  const headers = [...new Set(data.flatMap(obj => Object.keys(obj)))];

  // Create CSV header row
  const csvRows = [headers.join(',')];

  // Create data rows
  for (const obj of data) {
    const values = headers.map(header => {
      const value = obj[header];

      // Handle different data types
      if (value === null || value === undefined) {
        return '';
      } else if (typeof value === 'string') {
        // Escape quotes and wrap in quotes if it contains commas or quotes
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      } else {
        return String(value);
      }
    });

    csvRows.push(values.join(','));
  }

  return csvRows.join('\n');
};

/**
 * Download data as CSV file
 * @param {Array<Object>} data - Data to convert to CSV
 * @param {string} filename - Name of the file to download
 */
export const downloadCSV = (data, filename = 'download.csv') => {
  const csvContent = objectsToCSV(data);
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up URL object
  setTimeout(() => URL.revokeObjectURL(url), 100);
};
