/**
 * Форматирует цену в соответствии с локалью
 * @param {number} price - Цена для форматирования
 * @param {string} locale - Локаль для форматирования (по умолчанию ru-RU)
 * @param {string} currency - Валюта для форматирования (по умолчанию UAH)
 * @returns {string} - Отформатированная цена
 */
export const formatPrice = (price, locale = 'ru-RU', currency = 'UAH') => {
  if (price === undefined || price === null) return '';

  try {
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });

    return formatter.format(price);
  } catch (error) {
    console.error('Error formatting price:', error);
    return `${price} ${currency}`;
  }
};

/**
 * Форматирует дату в соответствии с локалью
 * @param {string|Date} date - Дата для форматирования
 * @param {string} locale - Локаль для форматирования (по умолчанию ru-RU)
 * @returns {string} - Отформатированная дата
 */
export const formatDate = (date, locale = 'ru-RU') => {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    return dateObj.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return String(date);
  }
};
