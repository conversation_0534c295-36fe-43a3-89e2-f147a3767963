/**
 * Сервис кеширования для хранения и управления данными
 * Поддерживает время жизни для автоматического обновления данных
 */

/**
 * Получить данные из кеша
 * @param {string} key - Ключ для кеша
 * @param {number} maxAge - Максимальное время жизни кеша в миллисекундах (по умолчанию: 1 час)
 * @returns {any|null} - Данные из кеша или null если кеш недействителен
 */
export const getCache = (key, maxAge = 60 * 60 * 1000) => {
  try {
    const cachedData = localStorage.getItem(key);
    if (!cachedData) return null;

    const { data, timestamp } = JSON.parse(cachedData);
    const isExpired = Date.now() - timestamp > maxAge;

    if (isExpired) {
      localStorage.removeItem(key);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Ошибка при получении кеша для ${key}:`, error);
    return null;
  }
};

/**
 * Сохранить данные в кеш
 * @param {string} key - Ключ для кеша
 * @param {any} data - Данные для сохранения
 * @returns {boolean} - Успешность операции
 */
export const setCache = (key, data) => {
  try {
    const cacheObj = {
      data,
      timestamp: Date.now()
    };
    localStorage.setItem(key, JSON.stringify(cacheObj));
    return true;
  } catch (error) {
    console.error(`Ошибка при сохранении кеша для ${key}:`, error);
    return false;
  }
};

/**
 * Очистить определенный кеш
 * @param {string} key - Ключ для кеша
 */
export const clearCache = key => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Ошибка при очистке кеша для ${key}:`, error);
  }
};

/**
 * Очистить весь кеш приложения
 * @param {string} prefix - Префикс для ограничения очистки (опционально)
 */
export const clearAllCache = prefix => {
  try {
    if (prefix) {
      // Удаляем только кеш с указанным префиксом
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(prefix)) {
          localStorage.removeItem(key);
        }
      });
    } else {
      // Удаляем весь кеш (за исключением пользовательских данных)
      const keysToPreserve = ['cart', 'wishlist', 'compare', 'user', 'auth'];
      Object.keys(localStorage).forEach(key => {
        if (!keysToPreserve.some(preserve => key.includes(preserve))) {
          localStorage.removeItem(key);
        }
      });
    }
  } catch (error) {
    console.error(`Ошибка при очистке всего кеша:`, error);
  }
};
