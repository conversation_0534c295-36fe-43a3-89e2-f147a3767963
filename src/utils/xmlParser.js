import { supabase, convertUUIDToBigInt, __convertBigIntToUUID } from '../supabaseClient';
import { v4 as uuidv4 } from 'uuid'; // Убедитесь, что пакет uuid установлен

// Define a custom event emitter for progress tracking
let progressCallback = null;
export const setProgressCallback = callback => {
  progressCallback = callback;
};

export const updateProgress = (status, percentage) => {
  if (progressCallback) {
    progressCallback({ status, percentage });
  }
};

export const parseXML = async xmlUrl => {
  try {
    updateProgress('Загрузка XML файла...', 10);
    const response = await fetch(xmlUrl, { mode: 'no-cors' });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    updateProgress('Чтение XML данных...', 20);
    const xmlText = await response.text();

    updateProgress('Разбор XML структуры...', 30);
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

    // Validate parsed XML
    const parserError = xmlDoc.querySelector('parsererror');
    if (parserError) {
      throw new Error('Parser error: ' + parserError.textContent);
    }

    // Convert XML to JSON-like structure
    updateProgress('Извлечение данных о товарах...', 40);
    const offers = Array.from(xmlDoc.querySelectorAll('offer')).map(offer => {
      // Собираем параметры товара
      const params = Array.from(offer.querySelectorAll('param')).map(param => ({
        name: param.getAttribute('name'),
        value: param.textContent.trim()
      }));

      return {
        id: offer.getAttribute('id'),
        name: offer.querySelector('name')?.textContent.trim() || '', // Plain UTF-8 text
        price: offer.querySelector('price')?.textContent.trim() || '',
        image: offer.querySelector('picture')?.textContent.trim() || '',
        description: offer.querySelector('description')?.textContent.trim() || '', // Plain UTF-8 text
        category_id: offer.querySelector('categoryId')?.textContent.trim() || '',
        vendor: offer.querySelector('vendor')?.textContent.trim() || '', // Plain UTF-8 text
        url: offer.querySelector('url')?.textContent.trim() || '',
        params: params // Добавляем параметры товара
      };
    });
    updateProgress('Анализ завершен', 50);

    return { offers, xmlDoc };
  } catch (error) {
    console.error('Error fetching or parsing XML:', error);
    throw error;
  }
};

// Function to clear all products and categories from the database
export const clearStore = async () => {
  try {
    updateProgress('Удаление товаров...', 60);
    // Delete all products first (due to foreign key constraints)
    const { error: productsError } = await supabase.from('products').delete().not('id', 'is', null);

    if (productsError) throw productsError;

    updateProgress('Удаление категорий...', 70);
    // Then delete all categories
    const { error: categoriesError } = await supabase
      .from('categories')
      .delete()
      .not('id', 'is', null);

    if (categoriesError) throw categoriesError;

    updateProgress('Магазин очищен', 80);
    return true;
  } catch (error) {
    console.error('Error clearing store:', error);
    throw error;
  }
};

// Исправляем проблему с категориями после импорта товаров

export const parseAndPopulateStore = async (xmlUrl = '/data/products.xml') => {
  try {
    const { offers, xmlDoc } = await parseXML(xmlUrl);
    updateProgress('Обработка категорий...', 60);
    const categoryElements = Array.from(xmlDoc.querySelectorAll('category'));
    const categoryMap = new Map();

    // First pass: Create categories
    for (const category of categoryElements) {
      const categoryId = category.getAttribute('id');
      const name = category.textContent;

      if (categoryId && name) {
        const newCategory = {
          id: uuidv4(),
          name: name.trim(),
          image: null,
          parent_id: null
        };

        categoryMap.set(categoryId, {
          ...newCategory,
          originalParentId: category.getAttribute('parentId')
        });

        // Проверяем существование категории
        const { data: existingCategory, error: checkError } = await supabase
          .from('categories')
          .select('id')
          .eq('name', name.trim())
          .maybeSingle();

        if (checkError) {
          console.error('Error checking category:', checkError);
        }

        if (!existingCategory) {
          // Добавляем только если категории не существует
          const { error } = await supabase.from('categories').insert([newCategory]);
          if (error && !error.message.includes('duplicate')) {
            console.error('Error inserting category:', error);
            throw error;
          }
        } else {
          // Обновляем соответствие в карте для существующей категории
          categoryMap.set(categoryId, {
            ...newCategory,
            id: existingCategory.id,
            originalParentId: category.getAttribute('parentId')
          });
        }
      }
    }

    updateProgress('Обновление иерархии категорий...', 70);
    // Second pass: Update parent relationships
    for (const [, category] of categoryMap.entries()) {
      if (category.originalParentId) {
        const parentCategory = categoryMap.get(category.originalParentId);
        if (parentCategory) {
          const { error } = await supabase
            .from('categories')
            .update({ parent_id: parentCategory.id })
            .eq('id', category.id);
          if (error) throw error;
        }
      }
    }

    updateProgress('Подготовка товаров к импорту...', 75);
    // Transform and insert products
    const products = offers
      .map(offer => ({
        id: uuidv4(),
        name: offer.name,
        price: parseFloat(offer.price.replace(/[^\d.-]/g, '')),
        category_id: categoryMap.get(offer.category_id)?.id || null,
        image: offer.image || null,
        description: offer.description || '',
        url: offer.url || null,
        vendor: offer.vendor || null
      }))
      .filter(product => !isNaN(product.price) && product.price > 0);

    // Insert products in batches of 100
    const productIdMap = new Map(); // Map для отслеживания новых ID товаров
    updateProgress('Импорт товаров...', 80);

    for (let i = 0; i < products.length; i += 100) {
      const batch = products.slice(i, i + 100);
      const { data: insertedProducts, error } = await supabase
        .from('products')
        .insert(batch)
        .select('id');

      if (error) throw error;

      // Сохраняем соответствия между индексами и ID товаров
      insertedProducts.forEach((product, _) => {
        productIdMap.set(i + _, product.id);
      });

      updateProgress(
        `Импортировано ${i + batch.length} товаров из ${products.length}...`,
        80 + Math.floor((i / products.length) * 10)
      );
    }

    // Создаём таблицу для параметров, если она не существует
    await supabase.rpc('exec_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS product_params (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          product_id UUID REFERENCES products(id) ON DELETE CASCADE,
          name TEXT NOT NULL,
          value TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_product_params_product_id ON product_params(product_id);
        CREATE INDEX IF NOT EXISTS idx_product_params_name ON product_params(name);
        CREATE INDEX IF NOT EXISTS idx_product_params_value ON product_params(value);
      `
    });

    // Вставляем параметры товаров
    updateProgress('Импорт параметров товаров...', 90);
    let paramCount = 0;

    for (let i = 0; i < offers.length; i++) {
      const productId = productIdMap.get(i);
      const params = offers[i].params;

      if (productId && params && params.length > 0) {
        // Convert UUID to BigInt for product_id in parameters
        const paramBatch = params.map(param => ({
          product_id: convertUUIDToBigInt(productId),
          name: param.name,
          value: param.value
        }));

        const { error: paramError } = await supabase.from('product_params').insert(paramBatch);
        if (paramError) {
          console.error(`Error inserting params for product ${i}:`, paramError);
        } else {
          paramCount += params.length;
        }
      }

      if (i % 100 === 0) {
        updateProgress(
          `Импортировано ${i} товаров с параметрами...`,
          90 + Math.floor((i / offers.length) * 5)
        );
      }
    }

    // После импорта категорий и товаров добавляем обновление идентификаторов категорий
    updateProgress('Обновление связей категорий и товаров...', 95);

    // Обновляем иерархию категорий заново для уверенности
    for (const [, category] of categoryMap.entries()) {
      if (category.originalParentId) {
        const parentCategory = categoryMap.get(category.originalParentId);
        if (parentCategory) {
          const { error } = await supabase
            .from('categories')
            .update({ parent_id: parentCategory.id })
            .eq('id', category.id);
          if (error) console.error('Error updating category parent:', error);
        }
      }
    }

    updateProgress('Импорт завершен!', 100);
    return { categories: categoryElements.length, products: products.length, params: paramCount };
  } catch (error) {
    console.error('Error populating store:', error);
    throw error;
  }
};

const checkRequiredColumns = async () => {
  try {
    const { error } = await supabase
      .from('products')
      .select('id, name, image, description, url, vendor, category_id')
      .limit(1);

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Required columns are missing in the products table');
      }
      throw error;
    }
    return true;
  } catch (error) {
    console.error('Schema check failed:', error);
    throw error;
  }
};

export const resetAndPopulateStore = async xmlUrl => {
  try {
    await checkRequiredColumns();
    await clearStore();
    return await parseAndPopulateStore(xmlUrl);
  } catch (error) {
    console.error('Failed to reset and populate store:', error);
    throw error;
  }
};
