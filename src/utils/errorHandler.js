import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';

export const handleApiError = (error, t) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const status = error.response.status;
    if (status === 401) {
      return t('error_unauthorized', 'Please login to continue.');
    } else if (status === 403) {
      return t('error_forbidden', 'You do not have permission to perform this action.');
    } else if (status === 404) {
      return t('error_not_found', 'The requested resource was not found.');
    } else if (status >= 500) {
      return t('error_server', 'Server error. Please try again later.');
    }
  } else if (error.request) {
    // The request was made but no response was received
    return t('error_network', 'Network error. Please check your connection.');
  }

  // Something happened in setting up the request that triggered an Error
  return t('error_unknown', 'An unknown error occurred.');
};

export const withErrorHandling = WrappedComponent => {
  return function WithErrorHandling(props) {
    return (
      <ErrorBoundary>
        <WrappedComponent {...props} />
      </ErrorBoundary>
    );
  };
};
