import axios from 'axios';
import { supabase } from '../supabaseClient';

/**
 * Fetches XML feed from the specified URL
 */
const FEED_PROXY_PATH = '/feed-proxy';

const createParser = () =>
  typeof window !== 'undefined' && window.DOMParser
    ? new window.DOMParser()
    : new (require('@xmldom/xmldom').DOMParser)();

async function fetchXmlFeed(url) {
  try {
    let requestUrl = url;

    // В development используем прокси, в production - напрямую
    if (process.env.NODE_ENV === 'development') {
      const { pathname, search } = new URL(url);
      requestUrl = `${FEED_PROXY_PATH}${pathname}${search}`;
    } else {
      // В продакшене используем CORS прокси сервис или напрямую
      // Можно использовать публичные CORS прокси:
      // requestUrl = `https://cors-anywhere.herokuapp.com/${url}`;
      // Или свой прокси сервис
      requestUrl = url; // Пробуем напрямую
    }

    const response = await axios.get(requestUrl, {
      responseType: 'text',
      headers: {
        Accept: 'application/xml, text/xml',
        'User-Agent': 'OnlineStore-FeedProcessor/1.0'
      }
    });

    const parser = createParser();
    const xmlDoc = parser.parseFromString(response.data, 'text/xml');

    return xmlDoc;
  } catch (error) {
    console.error(`Error fetching or parsing XML feed from ${url}:`, error.message);

    // В случае CORS ошибки в продакшене, пробуем через публичный прокси
    if (process.env.NODE_ENV === 'production' && error.message.includes('CORS')) {
      try {
        console.log('Trying with CORS proxy...');
        const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
        const proxyResponse = await axios.get(proxyUrl);

        const parser = createParser();
        const xmlDoc = parser.parseFromString(proxyResponse.data.contents, 'text/xml');

        return xmlDoc;
      } catch (proxyError) {
        console.error('CORS proxy also failed:', proxyError.message);
        throw new Error(`Не удалось загрузить фид. CORS ошибка: ${error.message}`);
      }
    }

    throw error;
  }
}

/**
 * Extract products from XML document
 */
function extractProductsFromXml(xmlDoc, language = 'ru') {
  const products = [];
  let isOfferFormat = false;
  // Find item nodes
  let productNodes = Array.from(xmlDoc.getElementsByTagName('*')).filter(
    n => n.localName === 'item'
  );
  if (!productNodes.length) {
    const fallbackItems = Array.from(xmlDoc.getElementsByTagName('item'));
    if (fallbackItems.length) {
      productNodes = fallbackItems;
    }
  }
  // Fallback to <offer> elements
  if (!productNodes.length) {
    const offers = Array.from(xmlDoc.getElementsByTagName('offer'));
    if (offers.length) {
      productNodes = offers;
      isOfferFormat = true;
    }
  }

  for (const item of productNodes) {
    try {
      const getTag = tagName => {
        const nodes = Array.from(item.getElementsByTagName(tagName));
        return nodes[0]?.textContent || '';
      };
      const externalId = isOfferFormat ? item.getAttribute('id') : getTag('id');
      const rawTitle = isOfferFormat ? getTag('name') : getTag('title');
      const rawImage = isOfferFormat ? getTag('picture') : getTag('image_link');
      const rawCategory = isOfferFormat ? getTag('categoryId') : getTag('product_type');
      const availability = isOfferFormat
        ? item.getAttribute('available') === 'true'
        : getTag('availability') === 'in stock';
      const product = {
        external_id: externalId,
        name: rawTitle,
        description: getTag('description'),
        price: parseFloat(getTag('price').replace(/[^0-9.]/g, '')),
        image: rawImage,
        category: rawCategory,
        availability,
        brand: isOfferFormat ? getTag('vendor') : getTag('brand'),
        condition: getTag('condition'),
        language,
        additional_images: isOfferFormat ? [] : extractAdditionalImages(item),
        attributes: isOfferFormat ? {} : extractAttributes(item)
      };
      products.push(product);
    } catch (err) {
      console.error('Error processing product node:', err.message);
    }
  }
  return products;
}

/**
 * Helper functions for XML parsing
 */
function getNodeTextContent(parentNode, tagName) {
  const node = parentNode.getElementsByTagName(tagName)[0];
  return node ? node.textContent : '';
}

function extractAdditionalImages(itemNode) {
  const images = [];
  const additionalImageNodes = itemNode.getElementsByTagName('g:additional_image_link');

  for (let i = 0; i < additionalImageNodes.length; i++) {
    const url = additionalImageNodes[i].textContent;
    if (url) images.push(url);
  }

  return images;
}

function extractAttributes(itemNode) {
  const attributes = {};
  const customLabelNodes = itemNode.getElementsByTagName('g:custom_label_0');
  const customLabelNodes1 = itemNode.getElementsByTagName('g:custom_label_1');
  const customLabelNodes2 = itemNode.getElementsByTagName('g:custom_label_2');

  // Process basic attributes if available
  if (customLabelNodes.length > 0) {
    const attrString = customLabelNodes[0].textContent;
    if (attrString) {
      try {
        const parsed = JSON.parse(attrString);
        Object.assign(attributes, parsed);
      } catch (e) {
        attributes.custom_label_0 = attrString;
      }
    }
  }

  // Additional custom labels
  if (customLabelNodes1.length > 0) {
    attributes.custom_label_1 = customLabelNodes1[0].textContent;
  }

  if (customLabelNodes2.length > 0) {
    attributes.custom_label_2 = customLabelNodes2[0].textContent;
  }

  return attributes;
}

/**
 * Process a feed by ID
 */
export async function processFeed(feedId) {
  try {
    // Получаем admin client для операций с базой данных
    const { getAdminClient } = await import('../supabaseClient');
    const adminClient = getAdminClient();

    // Get feed information
    const { data: feed, error } = await adminClient
      .from('feeds')
      .select('*')
      .eq('id', feedId)
      .single();

    if (error) throw error;

    if (!feed) {
      throw new Error(`Feed with ID ${feedId} not found`);
    }

    console.log(`Processing feed: ${feed.name} (${feed.language})`);

    // Create a job entry to track processing
    const { data: jobData, error: jobError } = await adminClient
      .from('feed_jobs')
      .insert([
        {
          feed_id: feedId,
          status: 'pending'
        }
      ])
      .select();

    if (jobError) {
      throw jobError;
    }

    const jobId = jobData[0].id;
    console.log(`Created job with ID: ${jobId}`);

    // Fetch and process the XML feed
    const xmlDoc = await fetchXmlFeed(feed.url);
    const products = extractProductsFromXml(xmlDoc, feed.language);

    console.log(`Extracted ${products.length} products from feed`);

    // Save products to the database
    const stats = await saveProductsToDatabase(products, feedId, jobId, adminClient);

    return {
      feedId,
      jobId,
      stats
    };
  } catch (error) {
    console.error('Error processing feed:', error);

    // Try to update the job status if we have a job ID
    try {
      const { getAdminClient } = await import('../supabaseClient');
      const adminClient = getAdminClient();

      const { data: failedJobs } = await adminClient
        .from('feed_jobs')
        .select('id')
        .eq('feed_id', feedId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })
        .limit(1);

      if (failedJobs && failedJobs.length > 0) {
        await adminClient
          .from('feed_jobs')
          .update({
            status: 'failed',
            error_message: error.message || 'Unknown error',
            finished_at: new Date().toISOString()
          })
          .eq('id', failedJobs[0].id);
      }
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }

    throw error;
  }
}

/**
 * Save products to database
 */
async function saveProductsToDatabase(products, feedId, jobId, adminClient) {
  const stats = {
    processed: products.length,
    created: 0,
    updated: 0,
    failed: 0
  };

  // Update job status to processing
  await adminClient
    .from('feed_jobs')
    .update({
      status: 'processing',
      started_at: new Date().toISOString(),
      items_processed: products.length
    })
    .eq('id', jobId);

  // Process products in batches to avoid overwhelming the database
  const batchSize = 50;
  const batches = Math.ceil(products.length / batchSize);

  for (let i = 0; i < batches; i++) {
    const start = i * batchSize;
    const end = Math.min(start + batchSize, products.length);
    const batch = products.slice(start, end);
    // Processing batch info: ${i + 1} of ${batches}, products ${start + 1} to ${end}

    for (const product of batch) {
      try {
        // Check if product exists by external_id
        const { data: existingProduct } = await adminClient
          .from('products')
          .select('id')
          .eq('external_id', product.external_id)
          .single();

        if (existingProduct) {
          // Update existing product
          await adminClient
            .from('products')
            .update({
              name: product.name,
              description: product.description,
              price: product.price,
              old_price: product.old_price,
              image: product.image,
              is_available: product.availability,
              brand: product.brand,
              attributes: product.attributes,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingProduct.id);

          stats.updated++;
        } else {
          // Create a new product
          const { error } = await adminClient.from('products').insert([
            {
              external_id: product.external_id,
              name: product.name,
              description: product.description,
              price: product.price,
              old_price: product.old_price,
              image: product.image,
              is_available: product.availability,
              brand: product.brand,
              attributes: product.attributes,
              language: product.language
            }
          ]);

          if (error) throw error;
          stats.created++;
        }
      } catch (error) {
        console.error(`Error processing product ${product.external_id}:`, error.message);
        stats.failed++;
      }
    }
  }

  // Update feed with last fetched timestamp
  await adminClient
    .from('feeds')
    .update({ last_fetched: new Date().toISOString() })
    .eq('id', feedId);

  // Update job completion status
  await adminClient
    .from('feed_jobs')
    .update({
      status: 'completed',
      finished_at: new Date().toISOString(),
      items_created: stats.created,
      items_updated: stats.updated,
      items_failed: stats.failed
    })
    .eq('id', jobId);

  return stats;
}
