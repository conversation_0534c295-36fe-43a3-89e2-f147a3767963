import { supabase } from '../supabaseClient'; // Исправлен импорт

/**
 * Helper functions for image handling and placeholders
 */

// Use Base64 encoded images instead of external services
const productPlaceholder =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgMzAwIDMwMCI+PHJlY3Qgd2lkdGg9IjMwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9IiNlZWUiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRzZSIgYWxpZ25tLWJhc2VsaW5lPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJzYW5zLXNlcmlmIiBmaWxsPSIjYWFhIj7Qn9GA0L7QtNGD0LrRgjwvdGV4dD48L3N2Zz4=';
const categoryPlaceholder =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiB2aWV3Qm94PSIwIDAgMzAwIDMwMCI+PHJlY3Qgd2lkdGg9IjMwMCIgaGVpZ2h0PSIzMDAiIGZpbGw9IiNlZWUiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxOCIgdGV4dC1hbmNob3I9Im1pZGRzZSIgYWxpZ25tLWJhc2VsaW5lPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJzYW5zLXNlcmlmIiBmaWxsPSIjYWFhIj7QmtCw0YLQtdCz0L7RgNC40Y88L3RleHQ+PC9zdmc+';
const brandPlaceholder =
  'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMjAwIDEwMCI+PHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9IiNmMGYwZjAiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1zaXplPSIxNiIgdGV4dC1hbmNob3I9Im1pZGRzZSIgYWxpZ25tLWJhc2VsaW5lPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJzYW5zLXNlcmlmIiBmaWxsPSIjOTk5Ij5CcmFuZDwvdGV4dD48L3N2Zz4=';

/**
 * Get placeholder image for product
 * @returns {string} - Data URL for the placeholder image
 */
export const getProductPlaceholder = () => {
  return productPlaceholder;
};

/**
 * Get placeholder image for category
 * @returns {string} - Data URL for the placeholder image
 */
export const getCategoryPlaceholder = () => {
  return categoryPlaceholder;
};

/**
 * Get placeholder image for brand
 * @returns {string} - Data URL for the placeholder image
 */
export const getBrandPlaceholder = () => {
  return brandPlaceholder;
};

/**
 * Format image path consistently across the application
 * @param {string} imagePath - Original image path
 * @param {string} type - Type of image ('product', 'category', 'brand')
 * @returns {string} - Formatted image path or appropriate placeholder
 */
export const getFormattedImagePath = (imagePath, type = 'product') => {
  // If no image path provided or it's not a string, return placeholder
  if (!imagePath || typeof imagePath !== 'string') {
    if (type === 'category') return getCategoryPlaceholder();
    if (type === 'brand') return getBrandPlaceholder();
    return getProductPlaceholder();
  }

  // If path already starts with http or slash, it's already formatted
  if (imagePath.startsWith('http') || imagePath.startsWith('/')) {
    return imagePath;
  }

  // Add the correct prefix based on image type
  if (type === 'product') {
    return `/images/products/${imagePath}`;
  } else if (type === 'category') {
    return `/images/categories/${imagePath}`;
  } else if (type === 'brand') {
    return `/images/brands/${imagePath}`;
  }

  // Default case
  return `/images/${imagePath}`;
};

/**
 * Get a safe image URL, falling back to a placeholder if the original is invalid
 * @param {string} imageUrl - Original image URL
 * @param {string} type - Type of image ('product', 'category', 'brand')
 * @returns {string} - Safe image URL
 */
export const getSafeImageUrl = (imageUrl, type = 'product') => {
  if (imageUrl && typeof imageUrl === 'string') {
    // Format the image path first
    const formattedPath = getFormattedImagePath(imageUrl, type);

    // Don't use external placeholder services that cause errors
    if (
      formattedPath.includes('via.placeholder.com') ||
      formattedPath.includes('placeholder.com') ||
      formattedPath.includes('placehold.it')
    ) {
      return type === 'category'
        ? getCategoryPlaceholder()
        : type === 'brand'
          ? getBrandPlaceholder()
          : getProductPlaceholder();
    }
    return formattedPath;
  }

  // Return appropriate placeholder based on type
  if (type === 'category') return getCategoryPlaceholder();
  if (type === 'brand') return getBrandPlaceholder();
  return getProductPlaceholder();
};

/**
 * Create an image error handler function
 * @param {string} type - Type of image ('product', 'category', 'brand')
 * @returns {function} - Function to handle image loading errors
 */
export const createImageErrorHandler = (type = 'product') => {
  return e => {
    if (type === 'category') {
      e.target.src = getCategoryPlaceholder();
    } else if (type === 'brand') {
      e.target.src = getBrandPlaceholder();
    } else {
      e.target.src = getProductPlaceholder();
    }
  };
};

export const uploadImage = async (file, path, bucket = 'media') => {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}.${fileExt}`;
    const filePath = `${path}/${fileName}`;

    const { error: uploadError } = await supabase.storage.from(bucket).upload(filePath, file);

    if (uploadError) throw uploadError;

    const {
      data: { publicUrl }
    } = supabase.storage.from(bucket).getPublicUrl(filePath);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading image:', error);
    return null;
  }
};
