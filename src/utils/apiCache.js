/**
 * Простой кэш в памяти
 */
const memoryCache = new Map();

/**
 * Время жизни кэша по умолчанию (5 минут)
 */
const DEFAULT_TTL = 5 * 60 * 1000;

/**
 * Проверяет, не устарел ли кэш
 */
const isCacheValid = (timestamp, ttl) => {
  return Date.now() - timestamp < ttl;
};

/**
 * Генерирует ключ кэша на основе параметров запроса
 */
const generateCacheKey = (table, query = {}) => {
  return `${table}:${JSON.stringify(query)}`;
};

/**
 * Оборачивает Supabase запрос в кэширующую логику
 */
export const withCache = async (supabaseQuery, options = {}) => {
  const { ttl = DEFAULT_TTL, table = '', query = {}, bypassCache = false } = options;

  const cacheKey = generateCacheKey(table, query);

  // Проверяем кэш, если не требуется его обход
  if (!bypassCache) {
    const cached = memoryCache.get(cacheKey);
    if (cached && isCacheValid(cached.timestamp, ttl)) {
      return cached.data;
    }
  }

  // Выполняем запрос
  const result = await supabaseQuery;

  // Если запрос успешен, кэшируем результат
  if (!result.error) {
    memoryCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });
  }

  return result;
};

/**
 * Очищает кэш для определенной таблицы или всего кэша
 */
export const clearCache = (table = '') => {
  if (table) {
    // Очищаем кэш только для указанной таблицы
    for (const key of memoryCache.keys()) {
      if (key.startsWith(`${table}:`)) {
        memoryCache.delete(key);
      }
    }
  } else {
    // Очищаем весь кэш
    memoryCache.clear();
  }
};

/**
 * Инвалидирует кэш для конкретного запроса
 */
export const invalidateCache = (table, query = {}) => {
  const cacheKey = generateCacheKey(table, query);
  memoryCache.delete(cacheKey);
};

/**
 * Предварительно загружает данные в кэш
 */
export const prefetchData = async (supabaseQuery, options = {}) => {
  return withCache(supabaseQuery, { ...options, bypassCache: true });
};
