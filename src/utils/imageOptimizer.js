/**
 * Утилиты для оптимизации изображений
 */

// Постоянные размеры изображений для разных типов контента
const IMAGE_SIZES = {
  thumbnail: { width: 150, height: 150 },
  product: { width: 500, height: 500 },
  banner: { width: 1200, height: 400 },
  category: { width: 300, height: 300 },
  avatar: { width: 100, height: 100 }
};

// Форматы изображений для разных браузеров
const IMAGE_FORMATS = {
  webp: ['image/webp'],
  avif: ['image/avif'],
  jpeg: ['image/jpeg', 'image/jpg'],
  png: ['image/png']
};

/**
 * Оптимизирует URL изображения, добавляя параметры размера, качества и формата
 *
 * @param {string} imageUrl - Исходный URL изображения
 * @param {string} type - Тип изображения (thumbnail, product, banner, category, avatar)
 * @param {number} quality - Качество изображения от 1 до 100
 * @returns {string} - Оптимизированный URL изображения
 */
export const optimizeImageUrl = (imageUrl, type = 'product', quality = 80) => {
  if (!imageUrl) return '';

  // Проверяем, является ли URL внешним Supabase
  const isSupabaseStorage = imageUrl.includes('supabase');

  // Если это внешний CDN с поддержкой трансформации изображений
  if (isSupabaseStorage) {
    // Получаем размеры для запрошенного типа
    const { width, height } = IMAGE_SIZES[type] || IMAGE_SIZES.product;

    // Добавляем параметры трансформации для Supabase Storage
    // Документация: https://supabase.com/docs/guides/storage/image-transformations
    const separator = imageUrl.includes('?') ? '&' : '?';

    // Добавляем поддержку WebP для Supabase Storage
    return `${imageUrl}${separator}width=${width}&height=${height}&quality=${quality}&format=webp&resize=contain`;
  }

  // Для локальных и других изображений возвращаем исходный URL
  return imageUrl;
};

/**
 * Предварительно загружает изображения для улучшения производительности
 *
 * @param {string[]} imageUrls - Массив URL изображений для предзагрузки
 */
export const preloadImages = (imageUrls = [], priority = 'low') => {
  if (!imageUrls.length) return;

  // Создаем элементы link для предзагрузки изображений
  imageUrls.forEach(url => {
    if (!url) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    link.fetchPriority = priority;
    document.head.appendChild(link);
  });
};

/**
 * Генерирует srcset для адаптивных изображений
 *
 * @param {string} baseUrl - Базовый URL изображения
 * @param {number[]} widths - Массив ширин для разных разрешений
 * @param {number} quality - Качество изображения
 * @returns {string} - Строка srcset для использования в теге img
 */
export const generateSrcSet = (baseUrl, widths = [320, 640, 768, 1024, 1280], quality = 80) => {
  if (!baseUrl) return '';

  // Проверяем, является ли URL внешним Supabase
  const isSupabaseStorage = baseUrl.includes('supabase');

  if (!isSupabaseStorage) return '';

  const separator = baseUrl.includes('?') ? '&' : '?';

  return widths
    .map(width => {
      const url = `${baseUrl}${separator}width=${width}&quality=${quality}&format=webp`;
      return `${url} ${width}w`;
    })
    .join(', ');
};

/**
 * Генерирует sizes атрибут для адаптивных изображений
 */
export const generateSizes = (type = 'product') => {
  switch (type) {
    case 'banner':
      return '100vw';
    case 'product':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw';
    case 'category':
      return '(max-width: 640px) 100vw, (max-width: 1024px) 33vw, 25vw';
    case 'thumbnail':
      return '150px';
    default:
      return '100vw';
  }
};

/**
 * Определяет поддерживаемые форматы изображений
 */
export const getSupportedImageFormats = async () => {
  const formats = [];

  for (const [format, mimeTypes] of Object.entries(IMAGE_FORMATS)) {
    const supported = await Promise.all(
      mimeTypes.map(type =>
        createImageBitmap(new Blob([], { type })).then(
          () => true,
          () => false
        )
      )
    );
    if (supported.some(Boolean)) {
      formats.push(format);
    }
  }

  return formats;
};

/**
 * Создает URL для изображения с оптимальным форматом
 */
export const createOptimizedImageUrl = async (imageUrl, type = 'product', quality = 80) => {
  const supportedFormats = await getSupportedImageFormats();
  const bestFormat = supportedFormats[0] || 'jpeg'; // Используем первый поддерживаемый формат или JPEG

  const optimizedUrl = optimizeImageUrl(imageUrl, type, quality);
  if (!optimizedUrl.includes('supabase')) return optimizedUrl;

  const separator = optimizedUrl.includes('?') ? '&' : '?';
  return `${optimizedUrl}${separator}format=${bestFormat}`;
};

// Создаем именованный экспорт для исправления предупреждения import/no-anonymous-default-export
const imageOptimizer = {
  optimizeImageUrl,
  preloadImages,
  generateSrcSet,
  generateSizes,
  getSupportedImageFormats,
  createOptimizedImageUrl,
  IMAGE_SIZES
};

export default imageOptimizer;
