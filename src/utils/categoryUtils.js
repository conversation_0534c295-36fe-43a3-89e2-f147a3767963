import { supabase } from '../supabaseClient';

export const getFeaturedCategories = async (limit = 6) => {
  try {
    // First get all featured categories
    const { data: featuredData, error: featuredError } = await supabase
      .from('categories')
      .select('*')
      .eq('is_featured', true)
      .order('display_order', { ascending: true })
      .limit(limit);

    // Filter to get only categories with products
    const featuredWithProducts = [];

    if (!featuredError && featuredData && featuredData.length > 0) {
      for (const category of featuredData) {
        // Check if category has products
        const { count, error: countError } = await supabase
          .from('products')
          .select('id', { count: 'exact', head: true })
          .eq('category_id', category.id)
          .eq('is_active', true);

        if (!countError && count > 0) {
          featuredWithProducts.push(category);
        }
      }

      if (featuredWithProducts.length > 0) {
        return featuredWithProducts;
      }
    }

    // If no featured categories with products, get root categories with products
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .is('parent_id', null)
      .order('display_order', { ascending: true })
      .limit(limit);

    if (error) throw error;

    // Filter root categories to only include those with products
    const rootWithProducts = [];

    if (data && data.length > 0) {
      for (const category of data) {
        // Check if category has products
        const { count, error: countError } = await supabase
          .from('products')
          .select('id', { count: 'exact', head: true })
          .eq('category_id', category.id)
          .eq('is_active', true);

        if (!countError && count > 0) {
          rootWithProducts.push(category);
        }
      }
    }

    return rootWithProducts;
  } catch (error) {
    console.error('Error in getFeaturedCategories:', error);
    return [];
  }
};

export const updateCategoryOrder = async (categoryId, newOrder) => {
  const { error } = await supabase
    .from('categories')
    .update({ display_order: newOrder })
    .eq('id', categoryId);

  if (error) throw error;
  return true;
};

export const getCategoryHierarchy = async categoryId => {
  try {
    // Получаем текущую категорию
    const { data: category, error } = await supabase
      .from('categories')
      .select('*, parent:parent_id(*)')
      .eq('id', categoryId)
      .single();

    if (error) throw error;

    const hierarchy = [category];
    let currentCategory = category;

    // Рекурсивно получаем родительские категории
    while (currentCategory.parent) {
      hierarchy.unshift(currentCategory.parent);

      const { data: parent, error: parentError } = await supabase
        .from('categories')
        .select('*, parent:parent_id(*)')
        .eq('id', currentCategory.parent.id)
        .single();

      if (parentError) break;

      currentCategory = parent;
    }

    return hierarchy;
  } catch (error) {
    console.error('Error getting category hierarchy:', error);
    return [];
  }
};
