/**
 * Утилита для сбора аналитики использования фильтров
 */

// Инициализация хранилища для аналитики
const initFilterAnalytics = () => {
  const analytics = localStorage.getItem('filter_analytics');
  if (!analytics) {
    localStorage.setItem(
      'filter_analytics',
      JSON.stringify({
        filterUses: 0,
        mostUsedFilters: {},
        mostFilteredCategories: {},
        lastUsed: new Date().toISOString()
      })
    );
  }
};

// Запись использования фильтра
export const trackFilterUse = (categoryId, filters) => {
  try {
    initFilterAnalytics();
    const analytics = JSON.parse(localStorage.getItem('filter_analytics'));

    // Увеличиваем счетчик использования
    analytics.filterUses += 1;

    // Отслеживаем категорию
    analytics.mostFilteredCategories[categoryId] =
      (analytics.mostFilteredCategories[categoryId] || 0) + 1;

    // Отслеживаем использованные параметры фильтров
    if (filters.params) {
      Object.keys(filters.params).forEach(param => {
        analytics.mostUsedFilters[param] = (analytics.mostUsedFilters[param] || 0) + 1;
      });
    }

    // Отслеживаем фильтры по цене
    if (filters.price?.min || filters.price?.max) {
      analytics.mostUsedFilters['price'] = (analytics.mostUsedFilters['price'] || 0) + 1;
    }

    // Отслеживаем фильтры по производителю
    if (filters.vendors && filters.vendors.length > 0) {
      analytics.mostUsedFilters['vendor'] = (analytics.mostUsedFilters['vendor'] || 0) + 1;
    }

    analytics.lastUsed = new Date().toISOString();

    localStorage.setItem('filter_analytics', JSON.stringify(analytics));
  } catch (err) {
    console.error('Ошибка при сохранении аналитики фильтров:', err);
  }
};

// Получение аналитики по фильтрам
export const getFilterAnalytics = () => {
  try {
    initFilterAnalytics();
    return JSON.parse(localStorage.getItem('filter_analytics'));
  } catch (err) {
    console.error('Ошибка при получении аналитики фильтров:', err);
    return null;
  }
};
