# Products API Documentation

API для получения товаров из интернет-магазина. Идеально подходит для создания страниц-заглушек и внешних интеграций.

## 🚀 Быстрый старт

### Запуск API сервера

```bash
# Установка зависимостей (если еще не установлены)
npm install

# Запуск API сервера
npm run api

# Запуск в продакшн режиме
npm run api:prod
```

API будет доступен по адресу: `http://localhost:3001`

### Переменные окружения

Убедитесь, что в файле `.env` указаны:
```
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
API_PORT=3001
```

## 📋 API Endpoints

### Базовая информация
- **Base URL**: `http://localhost:3001/api`
- **Format**: JSON
- **CORS**: Включен для всех доменов

### 1. Получить все товары
```
GET /api/products
```

**Параметры запроса:**
- `limit` (number, default: 50) - Количество товаров
- `offset` (number, default: 0) - Смещение для пагинации
- `page` (number, default: 1) - Номер страницы
- `category` (string) - ID категории
- `search` (string) - Поисковый запрос
- `sort` (string, default: 'created_at') - Поле для сортировки
- `order` (string, default: 'desc') - Порядок сортировки (asc/desc)

**Пример запроса:**
```
GET /api/products?limit=20&page=1&sort=price&order=asc
```

**Пример ответа:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Название товара",
      "description": "Описание товара",
      "price": 1500.00,
      "original_price": 2000.00,
      "image": "https://example.com/image.jpg",
      "vendor": "Производитель",
      "brand": "Бренд",
      "is_on_sale": true,
      "is_new": false,
      "is_bestseller": false,
      "created_at": "2024-01-01T00:00:00Z",
      "categories": {
        "id": "uuid",
        "name": "Категория"
      }
    }
  ],
  "pagination": {
    "limit": 20,
    "offset": 0,
    "total": 150,
    "pages": 8
  }
}
```

### 2. Получить новые товары
```
GET /api/products/new
```

**Параметры:**
- `limit` (number, default: 20) - Количество товаров

### 3. Получить товары со скидкой
```
GET /api/products/sale
```

**Параметры:**
- `limit` (number, default: 20) - Количество товаров

### 4. Получить хиты продаж
```
GET /api/products/bestsellers
```

**Параметры:**
- `limit` (number, default: 20) - Количество товаров

### 5. Поиск товаров
```
GET /api/products/search
```

**Параметры:**
- `q` (string, required) - Поисковый запрос
- `limit` (number, default: 20) - Количество товаров

**Пример:**
```
GET /api/products/search?q=телефон&limit=10
```

### 6. Статистика товаров
```
GET /api/products/stats
```

**Пример ответа:**
```json
{
  "success": true,
  "data": {
    "total": 1500,
    "active": 1450,
    "new": 120,
    "onSale": 200,
    "bestsellers": 50,
    "lastUpdated": "2024-01-01T00:00:00Z"
  }
}
```

### 7. Получить категории
```
GET /api/categories
```

**Пример ответа:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Электроника",
      "slug": "electronics",
      "display_order": 1,
      "is_featured": true
    }
  ]
}
```

## 🌐 Использование на странице-заглушке

### HTML пример

```html
<!DOCTYPE html>
<html>
<head>
    <title>Наши товары</title>
</head>
<body>
    <div id="products"></div>
    
    <script>
        async function loadProducts() {
            try {
                const response = await fetch('http://your-api-domain.com/api/products?limit=20');
                const result = await response.json();
                
                if (result.success) {
                    const productsHtml = result.data.map(product => `
                        <div class="product">
                            <img src="${product.image}" alt="${product.name}">
                            <h3>${product.name}</h3>
                            <p>${product.price} ₴</p>
                        </div>
                    `).join('');
                    
                    document.getElementById('products').innerHTML = productsHtml;
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }
        
        loadProducts();
    </script>
</body>
</html>
```

### JavaScript/React пример

```javascript
// Функция для получения товаров
const fetchProducts = async (options = {}) => {
    const params = new URLSearchParams(options);
    const response = await fetch(`http://your-api-domain.com/api/products?${params}`);
    return await response.json();
};

// Использование
const products = await fetchProducts({ limit: 20, category: 'electronics' });
```

## 🔧 Настройка CORS

Для использования API на другом домене, обновите настройки CORS в `api-server.js`:

```javascript
app.use(cors({
  origin: ['http://localhost:3000', 'https://yourdomain.com', 'https://www.yourdomain.com'],
  credentials: true
}));
```

## 📝 Тестирование

1. Запустите API сервер: `npm run api`
2. Откройте `api-example.html` в браузере
3. Протестируйте различные эндпоинты

## 🚀 Деплой

### На VPS/сервере

1. Скопируйте файлы `api-server.js` и `.env` на сервер
2. Установите зависимости: `npm install express cors @supabase/supabase-js dotenv`
3. Запустите: `npm run api:prod`
4. Настройте reverse proxy (nginx) для домена

### Пример nginx конфигурации

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔒 Безопасность

- API использует только публичные ключи Supabase
- Нет аутентификации (только чтение данных)
- Включены базовые CORS настройки
- Рекомендуется добавить rate limiting для продакшн

## 📞 Поддержка

При возникновении проблем проверьте:
1. Правильность переменных окружения
2. Доступность базы данных Supabase
3. Настройки CORS
4. Логи сервера в консоли
