#!/usr/bin/env node

/**
 * Fix Order Creation Error - Removes problematic email triggers
 * 
 * This script fixes the "schema 'net' does not exist" error by removing
 * email triggers that use net.http_post() function which requires
 * PostgreSQL net extension that doesn't exist in Supabase.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY
);

async function fixOrderCreationError() {
  console.log('🔧 Исправление ошибки создания заказов...\n');

  try {
    // Step 1: Remove problematic email triggers
    console.log('1️⃣ Удаление проблематичных триггеров электронной почты...');
    
    const removeTriggersSQL = `
      -- Remove email triggers that use net.http_post
      DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders;
      DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders;
      
      -- Remove email trigger functions
      DROP FUNCTION IF EXISTS send_order_confirmation_email();
      DROP FUNCTION IF EXISTS send_status_update_email();
    `;

    const { error: triggerError } = await supabase.rpc('exec_sql', {
      query_text: removeTriggersSQL
    });

    if (triggerError) {
      console.log('⚠️ Не удалось удалить триггеры через exec_sql, пробуем альтернативный метод...');
      
      // Try individual statements
      const statements = [
        'DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders',
        'DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders',
        'DROP FUNCTION IF EXISTS send_order_confirmation_email()',
        'DROP FUNCTION IF EXISTS send_status_update_email()'
      ];

      for (const statement of statements) {
        try {
          const { error } = await supabase.rpc('exec_sql', {
            query_text: statement
          });
          if (!error) {
            console.log(`✅ Выполнено: ${statement}`);
          } else {
            console.log(`⚠️ Предупреждение для "${statement}": ${error.message}`);
          }
        } catch (err) {
          console.log(`⚠️ Ошибка для "${statement}": ${err.message}`);
        }
      }
    } else {
      console.log('✅ Триггеры электронной почты удалены');
    }

    // Step 2: Test order creation
    console.log('\n2️⃣ Тестирование создания заказа...');
    
    const testOrderData = {
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+1234567890',
      shipping_address: {
        city: 'Test City',
        nova_poshta_office: 'Office 1'
      },
      total_amount: 100.00,
      status: 'pending',
      payment_method: 'cash_on_delivery',
      payment_status: 'pending',
      notes: 'Test order for fixing',
      user_id: null
    };

    const { data: testOrder, error: orderError } = await supabase
      .from('orders')
      .insert([testOrderData])
      .select()
      .single();

    if (orderError) {
      console.error('❌ Ошибка создания тестового заказа:', orderError);
      throw orderError;
    }

    console.log('✅ Тестовый заказ создан успешно:', testOrder.id);

    // Step 3: Test order items
    console.log('\n3️⃣ Тестирование создания товаров заказа...');
    
    const testOrderItems = [{
      order_id: testOrder.id,
      product_id: null,
      product_name: 'Test Product',
      quantity: 1,
      price: 100.00
    }];

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(testOrderItems);

    if (itemsError) {
      console.error('❌ Ошибка создания товаров заказа:', itemsError);
      throw itemsError;
    }

    console.log('✅ Товары заказа созданы успешно');

    // Step 4: Clean up test order
    console.log('\n4️⃣ Очистка тестовых данных...');
    
    await supabase.from('order_items').delete().eq('order_id', testOrder.id);
    await supabase.from('orders').delete().eq('id', testOrder.id);
    
    console.log('✅ Тестовые данные очищены');

    // Step 5: Refresh schema
    console.log('\n5️⃣ Обновление схемы базы данных...');
    
    try {
      await supabase.rpc('exec_sql', {
        query_text: "NOTIFY pgrst, 'reload schema';"
      });
      console.log('✅ Схема обновлена');
    } catch (err) {
      console.log('⚠️ Не удалось обновить схему:', err.message);
    }

    console.log('\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО УСПЕШНО!');
    console.log('✅ Заказы теперь можно создавать без ошибок');
    console.log('📧 Примечание: Электронные уведомления отключены (будут работать через фронтенд)');
    
  } catch (error) {
    console.error('\n❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    console.error('Стек ошибки:', error.stack);
    process.exit(1);
  }
}

// Run the fix
if (require.main === module) {
  fixOrderCreationError()
    .then(() => {
      console.log('\n✨ Скрипт завершен!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Скрипт завершился с ошибкой:', error.message);
      process.exit(1);
    });
}

module.exports = { fixOrderCreationError };
