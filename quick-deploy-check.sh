#!/bin/bash

# Quick Email Notifications Deployment Guide
echo "🚀 Email Notifications Manual Deployment"
echo "========================================"
echo ""

echo "📋 DEPLOYMENT CHECKLIST:"
echo "========================"
echo ""

echo "☐ 1. Get Resend API Key"
echo "   → Go to https://resend.com"
echo "   → Sign up and create API key"
echo ""

echo "☐ 2. Deploy Edge Function"
echo "   → Go to Supabase Dashboard: https://supabase.com/dashboard/project/dmdijuuwnbwngerkbfak"
echo "   → Navigate to Edge Functions"
echo "   → Create new function named 'send-email'"
echo "   → Copy content from supabase/functions/send-email/index.ts"
echo ""

echo "☐ 3. Set Environment Variables"
echo "   → In Supabase Dashboard: Settings > Edge Functions > Environment Variables"
echo "   → Add RESEND_API_KEY=your_api_key_here"
echo "   → Add FROM_EMAIL=<EMAIL> (optional)"
echo ""

echo "☐ 4. Create Database Tables"
echo "   → Go to SQL Editor in Supabase Dashboard"
echo "   → Run SQL from MANUAL_DEPLOYMENT_GUIDE.md"
echo ""

echo "☐ 5. Test the System"
echo "   → Start React app: npm start"
echo "   → Go to Admin > Settings > Email Notifications"
echo "   → Send test email"
echo ""

echo "🔗 QUICK LINKS:"
echo "==============="
echo "🌐 Supabase Dashboard: https://supabase.com/dashboard/project/dmdijuuwnbwngerkbfak"
echo "📧 Resend: https://resend.com"
echo "📖 Full Guide: ./MANUAL_DEPLOYMENT_GUIDE.md"
echo ""

echo "⏱️  Estimated time: 15-20 minutes"
echo ""

# Check if files exist
echo "🔍 FILE VERIFICATION:"
echo "===================="

if [ -f "supabase/functions/send-email/index.ts" ]; then
    echo "✅ Edge Function code ready"
else
    echo "❌ Edge Function code missing"
fi

if [ -f "supabase/migrations/001_create_email_logs.sql" ]; then
    echo "✅ Email logs migration ready"
else
    echo "❌ Email logs migration missing"
fi

if [ -f "supabase/migrations/002_create_email_triggers.sql" ]; then
    echo "✅ Email triggers migration ready"
else
    echo "❌ Email triggers migration missing"
fi

if [ -f "src/services/emailService.js" ]; then
    echo "✅ Email service ready"
else
    echo "❌ Email service missing"
fi

if [ -f "src/components/admin/EmailSystemManager.js" ]; then
    echo "✅ Admin interface ready"
else
    echo "❌ Admin interface missing"
fi

echo ""
echo "🚀 Ready to deploy! Follow MANUAL_DEPLOYMENT_GUIDE.md"
