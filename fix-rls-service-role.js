const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8';

async function fixProductParamsRLS() {
  console.log('🔧 Fixing product_params RLS policies for service role access...');
  
  const adminClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
    auth: { autoRefreshToken: false, persistSession: false }
  });

  const sqlCommands = [
    // Drop all existing policies to start fresh
    `DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "dev_product_params_policy" ON product_params;`,
    `DROP POLICY IF EXISTS "dev_all_access" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_read_all" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_admin_all" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_service_role_access" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_authenticated_write" ON product_params;`,
    `DROP POLICY IF EXISTS "product_params_public_read" ON product_params;`,

    // Create permissive policies that explicitly allow service role operations

    // Allow everyone to read product parameters (for public product display)
    `CREATE POLICY "product_params_public_read" ON product_params
     FOR SELECT TO public USING (true);`,

    // Allow service role full access (this is what the admin client uses)
    `CREATE POLICY "product_params_service_role_all" ON product_params
     FOR ALL TO service_role USING (true) WITH CHECK (true);`,

    // Allow authenticated users (for admin panel)
    `CREATE POLICY "product_params_authenticated_all" ON product_params
     FOR ALL TO authenticated USING (true) WITH CHECK (true);`,

    // Grant permissions
    `GRANT ALL ON product_params TO authenticated;`,
    `GRANT ALL ON product_params TO service_role;`,
    `GRANT SELECT ON product_params TO anon;`
  ];

  let successCount = 0;
  
  for (const sql of sqlCommands) {
    try {
      const { error } = await adminClient.rpc('exec_sql', { query: sql });
      if (error) {
        console.log('⚠️  SQL command failed:', sql.substring(0, 60) + '...', error.message);
      } else {
        console.log('✅ Success:', sql.substring(0, 60) + '...');
        successCount++;
      }
    } catch (err) {
      console.log('❌ Exception:', sql.substring(0, 60) + '...', err.message);
    }
  }

  console.log(`\n📊 Results: ${successCount}/${sqlCommands.length} commands succeeded`);

  // Test the fix
  console.log('\n🧪 Testing product_params access...');
  
  try {
    // Test 1: Check if we can read from product_params
    const { data: readData, error: readError } = await adminClient
      .from('product_params')
      .select('*')
      .limit(1);
      
    if (readError) {
      console.log('❌ Read test failed:', readError.message);
    } else {
      console.log('✅ Read test successful');
    }

    // Test 2: Try to insert a test record
    const testParam = {
      product_id: 999999,
      name: 'test_fix_param',
      value: 'test_value'
    };

    const { data: insertData, error: insertError } = await adminClient
      .from('product_params')
      .insert(testParam)
      .select();

    if (insertError) {
      console.log('❌ Insert test failed:', insertError.message);
      console.log('   Error code:', insertError.code);
      console.log('   This means 403 errors will continue');
    } else {
      console.log('✅ Insert test successful!');
      console.log('🎯 403 errors should now be fixed!');
      
      // Clean up test data
      if (insertData && insertData[0]) {
        await adminClient.from('product_params').delete().eq('id', insertData[0].id);
        console.log('🧹 Test data cleaned up');
      }
    }
  } catch (error) {
    console.error('❌ Test error:', error);
  }

  console.log('\n🎯 SUMMARY');
  console.log('==========');
  if (successCount >= sqlCommands.length - 2) { // Allow for some expected failures
    console.log('✅ RLS policies updated successfully');
    console.log('✅ Service role should now have proper access');
    console.log('✅ Product parameter saving errors should be resolved');
  } else {
    console.log('⚠️  Some commands failed, but this may be expected');
    console.log('   Please check the test results above');
  }
}

fixProductParamsRLS().catch(console.error);
