const { createClient } = require('@supabase/supabase-js');

async function fixDatabaseTrigger() {
  const supabaseUrl = 'https://dmdijuuwnbwngerkbfak.supabase.co';
  // IMPORTANT: Replace with your actual service role key.
  // Consider loading this from an environment variable for security.
  const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8';

  console.log('🔧 Attempting to remove database trigger and function...');

  try {
    const dropTriggerSQL = `
      DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
      DROP FUNCTION IF EXISTS public.handle_new_user();
    `;

    console.log('Executing SQL:\n', dropTriggerSQL);

    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceRoleKey}`,
        'apikey': serviceRoleKey // For Supabase REST API, apikey is often the anon key or service key
      },
      body: JSON.stringify({
        sql_query: dropTriggerSQL // Ensure this matches the parameter name in your exec_sql function
      })
    });

    const responseBody = await response.text(); // Read body for logging regardless of status

    if (!response.ok) {
      console.error(`❌ Failed to execute SQL. Status: ${response.status}`);
      console.error('Response Body:', responseBody);
      if (response.status === 404) {
        console.error('Hint: The rpc/exec_sql endpoint was not found. Ensure the exec_sql function is created in your database and accessible.');
      } else if (response.status === 401 || response.status === 403) {
        console.error('Hint: Authorization error. Check if the service_role_key is correct and has permissions.');
      }
    } else {
      // A 204 No Content is a common success response for RPCs that don\'t return data.
      // A 200 OK might also occur.
      console.log(`✅ SQL execution command sent. Status: ${response.status}`);
      console.log('Response Body:', responseBody); // Log body even on success, might contain info
      console.log('If the exec_sql function executed correctly and without internal SQL errors, the trigger and function should now be removed.');
      console.log('Please verify in your Supabase dashboard (Database -> Triggers and Database -> Functions) if they are gone.');
    }

  } catch (error) {
    console.error('❌ Error in remove-trigger-robust.js script:', error.message);
    console.error(error.stack);
  }
}

fixDatabaseTrigger();
