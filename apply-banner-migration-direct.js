const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Ошибка: Не найдены переменные окружения Supabase');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyBannerSliderMigrationDirect() {
  try {
    console.log('🚀 Начинаем миграцию баннеров для слайдера (прямой способ)...');

    // Сначала проверим текущую структуру таблицы
    console.log('🔍 Проверяем текущую структуру таблицы banners...');
    
    const { data: existingBanners, error: fetchError } = await supabase
      .from('banners')
      .select('*')
      .limit(1);

    if (fetchError) {
      console.error('❌ Ошибка при проверке таблицы banners:', fetchError);
      return;
    }

    console.log('✅ Таблица banners доступна');

    // Обновим существующие баннеры, добавив новые поля напрямую
    console.log('🔄 Обновляем баннеры с новыми полями...');

    const { data: allBanners, error: getAllError } = await supabase
      .from('banners')
      .select('*')
      .order('position');

    if (getAllError) {
      console.error('❌ Ошибка при получении всех баннеров:', getAllError);
      return;
    }

    console.log(`📋 Найдено ${allBanners.length} баннеров для обновления`);

    // Обновляем каждый баннер
    for (let i = 0; i < allBanners.length; i++) {
      const banner = allBanners[i];
      
      const updateData = {
        subtitle: banner.subtitle || `Откройте для себя лучшие предложения`,
        button_text: banner.button_text || (i === 0 ? 'Смотреть коллекцию' : i === 1 ? 'Купить сейчас' : 'Узнать больше'),
        button_link: banner.button_link || banner.link_url || (i === 0 ? '/products' : i === 1 ? '/sale' : '/about'),
        updated_at: new Date().toISOString()
      };

      try {
        const { error: updateError } = await supabase
          .from('banners')
          .update(updateData)
          .eq('id', banner.id);

        if (updateError) {
          console.error(`❌ Ошибка при обновлении баннера ${banner.id}:`, updateError);
        } else {
          console.log(`✅ Обновлен баннер ${banner.id}: "${banner.title}"`);
        }
      } catch (error) {
        console.error(`❌ Ошибка при обновлении баннера ${banner.id}:`, error.message);
      }
    }

    // Проверяем результат
    console.log('🔍 Проверяем обновленные баннеры...');
    
    const { data: updatedBanners, error: checkError } = await supabase
      .from('banners')
      .select('id, title, subtitle, button_text, button_link, position, is_active')
      .order('position');

    if (checkError) {
      console.error('❌ Ошибка при проверке обновленных баннеров:', checkError);
      return;
    }

    console.log('📋 Обновленные баннеры:');
    updatedBanners.forEach(banner => {
      console.log(`  - ID: ${banner.id}, Заголовок: "${banner.title}", Подзаголовок: "${banner.subtitle}", Кнопка: "${banner.button_text}"`);
    });

    console.log('🎉 Миграция завершена успешно!');
    console.log('🔧 Теперь можно использовать новый HeroBannerSlider');

  } catch (error) {
    console.error('❌ Неожиданная ошибка:', error);
  }
}

// Запускаем миграцию
applyBannerSliderMigrationDirect()
  .then(() => {
    console.log('\n✨ Процесс миграции завершен');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 Критическая ошибка:', error);
    process.exit(1);
  });
