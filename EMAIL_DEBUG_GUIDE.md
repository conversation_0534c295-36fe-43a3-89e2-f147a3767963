# 🚨 Диагностика и исправление ошибок Email System

## Текущие ошибки:
- Edge Function возвращает статус 400
- Тестовое письмо не отправляется

## ✅ Пошаговое исправление:

### Шаг 1: Проверьте Edge Function
1. Откройте Supabase Dashboard → Edge Functions → send-email
2. Посмотрите **Logs** - там будет точная ошибка
3. Если функции нет - создайте её (скопируйте код из `supabase/functions/send-email/index.ts`)

### Шаг 2: Проверьте переменные окружения
В Dashboard → Settings → Edge Functions → Environment Variables должны быть:
```
RESEND_API_KEY=re_ваш_ключ_от_resend
SUPABASE_URL=https://dmdijuuwnbwngerkbfak.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8
```

### Шаг 3: Получите Resend API Key (если ещё нет)
1. Идите на https://resend.com
2. Зарегистрируйтесь (бесплатно)
3. Создайте API ключ
4. Добавьте его в переменные окружения

### Шаг 4: Исправьте SQL триггеры
В SQL Editor выполните исправленные команды из обновлённого `MANUAL_DEPLOYMENT_GUIDE.md`:

1. Сначала включите HTTP расширение:
```sql
CREATE EXTENSION IF NOT EXISTS http;
```

2. Затем пересоздайте функции с исправленным кодом

### Шаг 5: Проверьте таблицу email_logs
```sql
-- Проверьте, что таблица создана
SELECT * FROM email_logs LIMIT 1;
```

### Шаг 6: Тестируйте пошагово

1. **Тест Edge Function напрямую** в Dashboard:
   - Идите в Edge Functions → send-email
   - Нажмите "Invoke"
   - Используйте тестовые данные:
   ```json
   {
     "type": "order_confirmation",
     "orderData": {
       "id": "test-123",
       "customer_name": "Тест Клиент",
       "customer_email": "<EMAIL>",
       "created_at": "2025-06-10T10:00:00Z",
       "total_amount": 100,
       "order_items": []
     }
   }
   ```

2. **Тест через админ панель** (после исправления функции)

## 🔍 Наиболее вероятные причины ошибок:

1. **Нет RESEND_API_KEY** - самая частая причина
2. **Неправильный формат API ключа** 
3. **HTTP расширение не включено в базе данных**
4. **Ошибка в коде Edge Function**

## 📞 Нужна помощь?
Пошлите скриншот из **Edge Functions → Logs** - там будет точная ошибка!
