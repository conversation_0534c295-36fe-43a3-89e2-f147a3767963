# DNS & Deployment Setup Guide

## Current Status
- ✅ Main domain: `roomchic.shop` → `*************`
- ❌ Test subdomain: `test.roomchic.shop` → **DNS NOT FOUND**
- ✅ Local development: `localhost:3001` → Working

## Options to Fix DNS Issue

### Option 1: Add DNS Record for test.roomchic.shop

#### If using Cloudflare:
```
1. Go to Cloudflare Dashboard → DNS → Records
2. Add new record:
   - Type: A or CNAME
   - Name: test
   - Content: Your hosting server IP or domain
   - Proxy: Orange cloud (proxied) or Gray cloud (DNS only)
```

#### If using other DNS provider:
```
1. Login to your domain registrar/DNS provider
2. Add DNS record:
   - Type: A (if pointing to IP) or CNAME (if pointing to domain)
   - Host: test
   - Value: Your hosting server IP or domain
   - TTL: 300 (5 minutes) for testing
```

### Option 2: Deploy to Main Domain

#### Deploy to roomchic.shop directly:
```
- Use main domain for production
- Set up staging environment elsewhere
- Or use roomchic.shop/staging path
```

### Option 3: Use Alternative Hosting

#### Quick deployment options:
- **Vercel**: Connect GitHub → Auto deploy
- **Netlify**: Drag & drop build folder
- **Firebase Hosting**: CLI deployment
- **GitHub Pages**: Push to gh-pages branch

## Recommended Deployment Steps

### Step 1: Choose Hosting Platform

#### For Vercel (Recommended):
```bash
# Install Vercel CLI
npm install -g vercel

# Login and deploy
cd /Users/<USER>/e-com_new/online-store
vercel

# Follow prompts:
# - Project name: online-store or roomchic-shop
# - Deploy to: test.roomchic.shop (if DNS configured) or use Vercel domain
```

#### For Netlify:
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login and deploy
cd /Users/<USER>/e-com_new/online-store
netlify login
netlify deploy --prod --dir=build

# Or use drag & drop: https://app.netlify.com/drop
```

### Step 2: Configure DNS

#### If you want test.roomchic.shop:
```
Add CNAME record:
- Name: test
- Value: your-vercel-domain.vercel.app (or your hosting URL)
```

#### Alternative subdomains:
- `staging.roomchic.shop`
- `preview.roomchic.shop`
- `beta.roomchic.shop`

### Step 3: Environment Variables

#### Set up environment variables on hosting platform:
```
REACT_APP_SUPABASE_URL=https://dmdijuuwnbwngerkbfak.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Quick Fix Options

### Option A: Use Existing Infrastructure
Since `roomchic.shop` resolves to `*************`, check if you have hosting there:

```bash
# Test if your app is already deployed to main domain
curl -I http://roomchic.shop
curl -I https://roomchic.shop
```

### Option B: Deploy to Free Hosting

#### Vercel (Recommended - Free tier):
```bash
# Quick deploy from build folder
cd /Users/<USER>/e-com_new/online-store
npx vercel --prod
```

#### Netlify (Alternative - Free tier):
```bash
# Quick deploy
cd /Users/<USER>/e-com_new/online-store
npx netlify deploy --prod --dir=build
```

## Testing Your Current Build

### Local Testing (Current):
```bash
# Your app is already running at:
http://localhost:3001

# Test suite available at:
http://localhost:3001/test-suite.html

# Debug tools at:
http://localhost:3001/clear-sw-cache.html
```

### Production Testing (After deployment):
```bash
# Test main functionality
curl -I https://your-deployed-domain.com

# Test service worker
curl -I https://your-deployed-domain.com/serviceWorker.js

# Test static assets
curl -I https://your-deployed-domain.com/manifest.json
```

## Next Steps

1. **Immediate**: Continue testing on `localhost:3001`
2. **Deploy**: Choose hosting platform and deploy build folder
3. **DNS**: Configure subdomain if needed
4. **SSL**: Ensure HTTPS is enabled
5. **Email**: Verify domain in Resend for production emails

## Common DNS Providers

### Cloudflare:
- Dashboard: https://dash.cloudflare.com
- DNS section → Add record

### GoDaddy:
- DNS Management → Add record

### Namecheap:
- Domain List → Manage → Advanced DNS

### Google Domains:
- DNS → Custom records

Would you like me to help you with a specific deployment option?
