#!/usr/bin/env node

// Простая проверка работы модерации
console.log('=== Testing Moderation System ===\n');

console.log('Step 1: Check current status');
console.log('Run: node check-pending-products.js');
console.log('Expected: Should show 20 products pending moderation\n');

console.log('Step 2: Test web interface');
console.log('1. Open: http://localhost:3001/admin/moderation');
console.log('2. You should see a table with pending products');
console.log('3. Try clicking the green checkmark (approve) for one product');
console.log('4. Try clicking the red X (reject) for another product');
console.log('5. Check browser console for debug messages\n');

console.log('Step 3: Verify changes');
console.log('Run: node check-pending-products.js again');
console.log('Expected: Should show 18 products pending (2 less than before)\n');

console.log('Step 4: Check approved products');
console.log('Approved products should:');
console.log('- Have moderation_status = "approved"');
console.log('- Have is_active = true');
console.log('- Appear on the main product catalog\n');

console.log('Step 5: Check rejected products');
console.log('Rejected products should:');
console.log('- Have moderation_status = "rejected"');
console.log('- Have is_active = false');
console.log('- NOT appear on the main product catalog\n');

console.log('=== Quick Test Instructions ===');
console.log('1. Make sure the dev server is running on port 3001');
console.log('2. Open the moderation panel in your browser');
console.log('3. Test approve/reject buttons');
console.log('4. Check that the count decreases');
console.log('5. Verify approved products appear on the frontend\n');

console.log('If everything works correctly, the moderation system is fully functional!');
