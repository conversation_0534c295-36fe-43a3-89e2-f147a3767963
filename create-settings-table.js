const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Не найдены переменные окружения Supabase');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createSettingsTable() {
  try {
    console.log('🚀 Создаем таблицу настроек для баннеров...');

    // Проверяем, существует ли таблица settings
    const { data: existingTables, error: checkError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'settings');

    if (checkError) {
      console.log('ℹ️ Не удалось проверить существование таблицы, создаем новую...');
    }

    // Попробуем создать таблицу (если не существует)
    try {
      // Попробуем вставить тестовую запись, чтобы проверить существование таблицы
      const { error: testError } = await supabase
        .from('settings')
        .select('id')
        .limit(1);

      if (testError && testError.code === '42P01') {
        console.log('📋 Таблица settings не существует, создаем...');
        
        // Создаем таблицу напрямую через Supabase API, если доступно
        // Альтернативно - создаем вручную в Supabase Dashboard
        console.log('⚠️ Необходимо создать таблицу settings в Supabase Dashboard со следующей структурой:');
        console.log(`
CREATE TABLE settings (
  id SERIAL PRIMARY KEY,
  category VARCHAR(100) NOT NULL,
  key VARCHAR(100) NOT NULL,
  value TEXT,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(category, key)
);

-- RLS политики
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Политика для чтения (публичная)
CREATE POLICY "settings_select_policy" ON settings
  FOR SELECT USING (true);

-- Политика для записи (только для аутентифицированных пользователей)
CREATE POLICY "settings_insert_policy" ON settings
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "settings_update_policy" ON settings
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Индексы
CREATE INDEX idx_settings_category_key ON settings (category, key);
        `);
        
        return false;
      } else {
        console.log('✅ Таблица settings уже существует');
      }
    } catch (createError) {
      console.error('❌ Ошибка при работе с таблицей settings:', createError);
      return false;
    }

    // Добавляем настройки эффектов по умолчанию
    console.log('🔧 Добавляем настройки эффектов по умолчанию...');

    const defaultSettings = {
      category: 'banner_effects',
      key: 'effects_config',
      value: JSON.stringify({
        sticky_enabled: true,
        sticky_threshold: 100,
        parallax_enabled: true,
        parallax_speed: 0.3,
        auto_play_default: true,
        auto_play_interval_default: 5000,
        transition_duration: 300,
        show_nav_arrows: true,
        show_indicators: true,
        show_play_pause: true,
        fade_in_enabled: true,
        slide_animation_enabled: true,
        hover_effects_enabled: true,
        mobile_auto_play: false,
        mobile_nav_always_visible: true,
        reduced_motion_support: true
      }),
      description: 'Настройки эффектов для баннеров и слайдеров',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('settings')
      .upsert(defaultSettings, {
        onConflict: 'category,key'
      });

    if (insertError) {
      console.error('❌ Ошибка при добавлении настроек:', insertError);
      return false;
    }

    console.log('✅ Настройки эффектов по умолчанию добавлены');

    // Проверяем результат
    const { data: settings, error: selectError } = await supabase
      .from('settings')
      .select('*')
      .eq('category', 'banner_effects');

    if (selectError) {
      console.error('❌ Ошибка при проверке настроек:', selectError);
      return false;
    }

    console.log('📋 Текущие настройки:');
    settings.forEach(setting => {
      console.log(`  - ${setting.category}.${setting.key}: ${setting.description}`);
    });

    return true;

  } catch (error) {
    console.error('❌ Неожиданная ошибка:', error);
    return false;
  }
}

// Запускаем создание таблицы настроек
createSettingsTable()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Таблица настроек успешно инициализирована!');
    } else {
      console.log('\n⚠️ Необходимо вручную создать таблицу settings в Supabase Dashboard');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 Критическая ошибка:', error);
    process.exit(1);
  });
