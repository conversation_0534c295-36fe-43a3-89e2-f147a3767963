# 🚀 Быстрая настройка автоматических фидов в продакшене

## ⚡ GitHub Actions (5 минут)

### 1. Настроить GitHub Secrets
```
Repository → Settings → Secrets and variables → Actions → New repository secret
```

**Добавить 3 секрета:**
- `REACT_APP_SUPABASE_URL` = `https://dmdijuuwnbwngerkbfak.supabase.co`
- `REACT_APP_SUPABASE_ANON_KEY` = `ваш_anon_key`
- `SUPABASE_SERVICE_ROLE_KEY` = `ваш_service_role_key`

### 2. Активировать workflow
```bash
git add .
git commit -m "Add automatic feed processing"
git push origin main
```

### 3. Проверить запуск
- Перейти в `Actions` → `Automatic Feed Processing`
- Workflow запустится автоматически в 00:00, 06:00, 12:00, 18:00 UTC

## ✅ Проверочный чеклист

- [ ] GitHub secrets настроены
- [ ] Workflow файл `.github/workflows/feed-processing.yml` в репозитории
- [ ] Фиды активны в админ панели (`/admin/feed-management`)
- [ ] Первый автоматический запуск прошел успешно

## 🔧 Ручное тестирование

```bash
# Локально
node scripts/process-feeds.js --dry-run

# В GitHub Actions
Actions → Automatic Feed Processing → Run workflow
```

## 📊 Мониторинг

- **GitHub Actions:** Repository → Actions
- **Админ панель:** `/admin/feed-management`
- **Частота:** Каждые 6 часов автоматически

---
**🎯 Результат:** Фиды будут обновляться автоматически каждые 6 часов без необходимости в backend сервере!
