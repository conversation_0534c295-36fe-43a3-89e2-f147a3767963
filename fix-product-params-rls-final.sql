-- Fix product_params RLS policies to allow admin operations
-- This will resolve the 403 errors when saving product parameters

-- First, let's see what policies currently exist
-- (This will help us understand the current state)

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;
DROP POLICY IF EXISTS "dev_product_params_policy" ON product_params;
DROP POLICY IF EXISTS "dev_all_access" ON product_params;

-- For development: Create permissive policies that allow all operations
-- This will fix the 403 errors immediately

-- Allow all users to read product parameters (for public product display)
CREATE POLICY "product_params_read_all" ON product_params
  FOR SELECT USING (true);

-- Allow all operations for service role and authenticated users
-- This covers admin operations in development mode
CREATE POLICY "product_params_admin_all" ON product_params
  FOR ALL USING (
    -- Allow service role (admin operations)
    auth.role() = 'service_role'
    OR
    -- Allow authenticated users (for admin panel in development)
    auth.uid() IS NOT NULL
  )
  WITH CHECK (
    -- Same conditions for inserts/updates
    auth.role() = 'service_role'
    OR
    auth.uid() IS NOT NULL
  );

-- Ensure proper permissions are granted
GRANT ALL ON product_params TO authenticated;
GRANT ALL ON product_params TO service_role;
GRANT SELECT ON product_params TO anon;
