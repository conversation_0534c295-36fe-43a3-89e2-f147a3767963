-- Fix product_params RLS policies for service role access
-- This will resolve the 403 errors when saving product parameters using admin client

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;
DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;
DROP POLICY IF EXISTS "dev_product_params_policy" ON product_params;
DROP POLICY IF EXISTS "dev_all_access" ON product_params;
DROP POLICY IF EXISTS "product_params_read_all" ON product_params;
DROP POLICY IF EXISTS "product_params_admin_all" ON product_params;
DROP POLICY IF EXISTS "product_params_service_role_access" ON product_params;
DROP POLICY IF EXISTS "product_params_authenticated_write" ON product_params;
DROP POLICY IF EXISTS "product_params_public_read" ON product_params;

-- Create permissive policies that explicitly allow service role operations

-- Allow everyone to read product parameters (for public product display)
CREATE POLICY "product_params_public_read" ON product_params
  FOR SELECT 
  TO public
  USING (true);

-- Allow service role full access (this is what the admin client uses)
CREATE POLICY "product_params_service_role_all" ON product_params
  FOR ALL 
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Allow authenticated users (for admin panel)
CREATE POLICY "product_params_authenticated_all" ON product_params
  FOR ALL 
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Ensure proper permissions are granted
GRANT ALL ON product_params TO authenticated;
GRANT ALL ON product_params TO service_role;
GRANT SELECT ON product_params TO anon;

-- Force schema refresh
NOTIFY pgrst, 'reload schema';
