#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

async function testModerationWorkflow() {
  console.log('=== Testing Product Moderation Workflow ===\n');
  
  try {
    // 1. Проверяем количество продуктов в очереди модерации
    console.log('1. Checking current pending products...');
    const checkResult = execSync('node check-pending-products.js', { encoding: 'utf8' });
    const lines = checkResult.split('\n');
    const countLine = lines.find(line => line.includes('Found'));
    if (countLine) {
      console.log(`   ✓ ${countLine.trim()}`);
    }
    
    // 2. Ждем действий пользователя через веб-интерфейс
    console.log('\n2. Please perform the following actions in the web interface:');
    console.log('   • Open http://localhost:3001/admin/moderation');
    console.log('   • Try approving one product (green checkmark button)');
    console.log('   • Try rejecting one product (red X button)');
    console.log('   • Watch console logs for debug information');
    console.log('\n   Press ENTER when you have tested the buttons...');
    
    // Ждем ввода пользователя
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', () => {
      process.stdin.setRawMode(false);
      process.stdin.pause();
      continueTest();
    });
    
  } catch (error) {
    console.error('Error during test:', error.message);
  }
}

function continueTest() {
  console.log('\n3. Checking results after manual testing...');
  
  try {
    // Проверяем изменения
    const finalResult = execSync('node check-pending-products.js', { encoding: 'utf8' });
    const finalLines = finalResult.split('\n');
    const finalCountLine = finalLines.find(line => line.includes('Found'));
    
    if (finalCountLine) {
      console.log(`   ✓ ${finalCountLine.trim()}`);
    }
    
    console.log('\n4. Summary:');
    console.log('   • If the count decreased, moderation functions are working');
    console.log('   • Check browser console for any error messages');
    console.log('   • Check if products appear on the main site after approval');
    
    console.log('\n=== Manual Test Complete ===');
    
  } catch (error) {
    console.error('Error checking final results:', error.message);
  }
}

// Проверяем, что сервер запущен
console.log('Checking if development server is running...');
try {
  const http = require('http');
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/',
    method: 'GET',
    timeout: 2000
  };
  
  const req = http.request(options, (res) => {
    console.log('✓ Development server is running on port 3001');
    testModerationWorkflow();
  });
  
  req.on('error', (e) => {
    console.log('✗ Development server is not running on port 3001');
    console.log('Please start the server with: npm start');
    process.exit(1);
  });
  
  req.on('timeout', () => {
    console.log('✗ Development server is not responding');
    process.exit(1);
  });
  
  req.end();
  
} catch (error) {
  console.error('Error checking server:', error.message);
}
