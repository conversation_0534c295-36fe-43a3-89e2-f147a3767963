-- Add new columns to products table if they don't exist
DO $$
BEGIN
    -- Check and add status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'status') THEN
        ALTER TABLE products ADD COLUMN status text DEFAULT 'active';
    END IF;

    -- Check and add weight column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'weight') THEN
        ALTER TABLE products ADD COLUMN weight text;
    END IF;

    -- Check and add dimensions column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'dimensions') THEN
        ALTER TABLE products ADD COLUMN dimensions text;
    END IF;

    -- Check and add meta_title column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'meta_title') THEN
        ALTER TABLE products ADD COLUMN meta_title text;
    END IF;

    -- Check and add meta_description column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'meta_description') THEN
        ALTER TABLE products ADD COLUMN meta_description text;
    END IF;

    -- Check and add tags column (as JSONB array)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'tags') THEN
        ALTER TABLE products ADD COLUMN tags jsonb DEFAULT '[]'::jsonb;
    END IF;

    -- Check and add image_gallery column (as JSONB array)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'image_gallery') THEN
        ALTER TABLE products ADD COLUMN image_gallery jsonb DEFAULT '[]'::jsonb;
    END IF;

    -- Create indexes for commonly searched fields
    CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
    CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING gin(tags);
END $$;
