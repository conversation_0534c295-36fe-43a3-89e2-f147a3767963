{"name": "online-store", "version": "0.1.0", "private": true, "homepage": "/", "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@heroicons/react": "^2.1.1", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@reduxjs/toolkit": "^2.2.1", "@supabase/supabase-js": "^2.39.7", "@svgr/webpack": "^8.1.0", "@tinymce/tinymce-react": "^6.2.0", "@xmldom/xmldom": "^0.9.8", "axios": "^1.6.7", "cors": "^2.8.5", "cross-fetch": "^4.0.0", "css-select": "^5.1.0", "dotenv": "^16.4.5", "envalid": "^8.0.0", "express": "^4.18.2", "file-saver": "^2.0.5", "framer-motion": "^11.0.5", "http-proxy-middleware": "^2.0.6", "i18next": "^23.8.2", "i18next-browser-languagedetector": "^7.2.0", "keen-slider": "^6.8.6", "nth-check": "^2.1.1", "quill": "^2.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.12", "react-feather": "^2.0.10", "react-helmet-async": "^2.0.4", "react-hotkeys-hook": "^4.5.0", "react-i18next": "^14.0.5", "react-icons": "^5.0.1", "react-intersection-observer": "^9.8.0", "react-loading-skeleton": "^3.4.0", "react-medium-image-zoom": "^5.1.10", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "react-scripts": "^5.0.1", "react-slick": "^0.30.2", "react-swipeable": "^7.0.1", "react-toastify": "^10.0.4", "react-transition-group": "^4.4.5", "react-zoom-pan-pinch": "^3.3.0", "recharts": "^2.12.0", "resolve-url-loader": "^5.0.0", "slick-carousel": "^1.8.1", "svgo": "^3.3.2", "swiper": "^11.0.5", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.23.3", "@babel/plugin-transform-private-property-in-object": "^7.23.4", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/register": "^7.27.1", "@babel/runtime": "^7.23.9", "@craco/craco": "^7.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "autoprefixer": "^10.4.17", "compression-webpack-plugin": "^11.0.0", "core-js": "^3.36.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.1.7", "ignore-styles": "^5.0.1", "lint-staged": "^15.5.2", "postcss": "^8.5.4", "postcss-import": "^16.1.0", "postcss-preset-env": "^9.3.0", "prettier": "^3.2.5", "react-router-dom": "^6.22.1", "tailwindcss": "^3.4.1", "typescript": "^5.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "prepare": "husky install", "proxy-server": "node src/proxy.js", "ssr": "node server.js", "build:ssr": "react-scripts build && node server.js", "generate-sitemap": "node src/scripts/generate-sitemap.js", "build:full": "npm run generate-sitemap && react-scripts build && node server.js", "tsc": "tsc", "format": "node src/scripts/utils/format.js", "format-check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,css,scss,json}\"", "update-store": "node src/scripts/database/update-store.js", "setup-images": "node src/scripts/utils/setup-images.js", "setup-store": "node src/scripts/database/setup-store.js", "create-categories": "node src/scripts/database/create-categories.js", "link-products": "node src/scripts/database/link-products.js", "fix-products": "node src/scripts/database/fix-products.js", "import-params": "node src/scripts/database/import-params.js", "run-migration": "node src/scripts/database/execute-migration.js", "setup-filters": "node src/scripts/database/setup-filters.js", "fix-format": "node src/scripts/utils/fix-format.js", "fix-filters": "node src/scripts/database/fix-filters.js", "create-filters": "node src/scripts/database/create-filters.js", "create-indexes": "node src/scripts/database/create-indexes.js", "setup-db": "node src/scripts/database/setup.js", "fix-banners-table": "node src/scripts/database/fix-banners-table.js", "create-demo-data": "node src/scripts/database/create-demo-data.js", "create-banners-table": "node src/scripts/database/create-banners-table.js", "update-banners": "node src/scripts/database/update-banners.js", "fix-banners-structure": "node src/scripts/database/fix-banners-structure.js", "fix-banners": "node src/scripts/database/fix-banners.js", "setup": "node src/scripts/setup.js", "setup:db": "node src/scripts/database/init.js", "setup:banners": "node src/scripts/database/banners.js", "setup:products": "node src/scripts/database/products.js", "migrate": "node src/scripts/utils/migrate.js", "dev": "react-scripts start", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss,json}\"", "format:all": "npm run lint:fix && npm run prettier", "start-server": "node scripts/server.js", "api": "node api-server.js", "api:prod": "NODE_ENV=production node api-server.js"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "alias": {"@": "./src", "@components": "./src/components", "@utils": "./src/utils", "@services": "./src/services", "@hooks": "./src/hooks", "@config": "./src/config"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss,json,md}": "prettier --write"}}