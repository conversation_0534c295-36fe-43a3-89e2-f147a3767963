# Remaining Tasks Checklist

Ниже приведён список задач для дальнейшего завершения проекта «Online Store», без учёта лицензии (будет добавлена позже).

## Документация
- [x] Архитектурное описание (диаграммы, схема модулей)
- [x] API-документация (REST-эндпойнты, примеры запросов)
- [x] Руководство по деплою (Docker, Kubernetes, cloud)
- [x] CONTRIBUTING.md (процесс PR, код-стайл, release-план)

## E2E-тестирование
- [ ] Настроить E2E-тесты (Cypress или Playwright) для ключевых сценариев: регистрация, каталог → заказ

## Покрытие тестами (Jest)
- [ ] Дополнить тестами страницы, админ-панель и сервисные функции
- [ ] Настроить отчёты покрытия и блокировку merge при падении порогов

## Код-качество
- [ ] Исправить оставшиеся ESLint-предупреждения (no-unused-vars, React Hooks)
- [ ] Удалить неиспользуемые зависимости (TS-пакеты и др.)

## Производительность и PWA
- [ ] Анализ бандла и динамический импорт (React.lazy/Suspense)
- [ ] Настройка Service Worker, офлайн-режим, кеширование
- [ ] Интеграция Lighthouse CI для автоматических аудитов

## Доступность (a11y)
- [ ] Провести axe-аудит и исправить ошибки (ARIA-атрибуты, фокус, контраст)

## SEO и аналитика
- [ ] Проверить/сгенерировать robots.txt, sitemap.xml
- [ ] Добавить структурированные данные (JSON-LD) для карточек товаров
- [ ] Интеграция Google Analytics / Tag Manager

## Безопасность
- [ ] Настроить CSP, HSTS, X-Frame-Options на SSR-сервере
- [ ] Проверить CORS-политику, защиту от XSS/CSRF
- [ ] Ротация ключей Supabase и хранение секретов (GitHub Secrets)
- [ ] CI: npm audit, Snyk или GitHub Advanced Security

## DevOps и деплой
- [ ] Написать Dockerfile и docker-compose для локальной разработки
- [ ] Infrastructure as Code (Terraform / Ansible)
- [ ] Автоматический деплой (GitHub Actions → staging/production)

## Миграции базы данных
- [ ] Перенести ad-hoc SQL-скрипты в систему миграций (Supabase Migrations)
- [ ] CI-валидатор схемы, тестовая БД для автоматических тестов

## Интернационализация
- [ ] Проверить полноту переводов, настроить fallback и UI-переключатель языков

## Чистка кода
- [ ] Удалить ручные тестовые скрипты в корне (test-*.js)
- [ ] Архивировать или задокументировать оставшиеся утилиты и скрипты 