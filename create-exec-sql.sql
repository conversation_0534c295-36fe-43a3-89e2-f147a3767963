-- Drop existing functions first
DROP FUNCTION IF EXISTS exec_sql(text);
DROP FUNCTION IF EXISTS exec_sql(text, json);

-- Create the SQL execution function
CREATE OR REPLACE FUNCTION exec_sql(query text)
RETURNS void AS $$
BEGIN
  EXECUTE query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission
GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;

-- Alternative function signature that accepts params
CREATE OR REPLACE FUNCTION exec_sql(
  query text,
  params json DEFAULT '{}'::json
)
RETURNS void AS $$
BEGIN
  EXECUTE query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION exec_sql(text, json) TO authenticated, anon; 