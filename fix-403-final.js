const { createClient } = require('@supabase/supabase-js');

// Use service role key for admin operations
const SUPABASE_URL = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTk3ODc4MiwiZXhwIjoyMDQ3NTU0NzgyfQ.Y1Q9l2NwayOcN_p8PN7GNvVfm5Ag_4nXMl8QkXvvgcE';

async function fixProductParamsRLS() {
  console.log('🔧 Fixing product_params RLS policies to eliminate 403 errors...');
  
  const adminClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  try {
    console.log('1️⃣  Checking current policies...');
    
    // First, let's see what policies exist
    const { data: policies, error: policiesError } = await adminClient
      .rpc('exec_sql', {
        query: `
          SELECT policyname, permissive, roles, cmd, qual, with_check
          FROM pg_policies 
          WHERE tablename = 'product_params';
        `
      });
    
    if (policiesError) {
      console.log('⚠️  Could not check existing policies:', policiesError.message);
    } else {
      console.log('📋 Current policies:', policies);
    }
    
    console.log('2️⃣  Dropping existing policies...');
    
    // Drop all existing policies to start fresh
    const dropPolicies = [
      'DROP POLICY IF EXISTS "product_params_select_policy" ON product_params;',
      'DROP POLICY IF EXISTS "product_params_insert_policy" ON product_params;',
      'DROP POLICY IF EXISTS "product_params_update_policy" ON product_params;',
      'DROP POLICY IF EXISTS "product_params_delete_policy" ON product_params;',
      'DROP POLICY IF EXISTS "dev_product_params_policy" ON product_params;',
      'DROP POLICY IF EXISTS "dev_all_access" ON product_params;',
      'DROP POLICY IF EXISTS "product_params_read_all" ON product_params;',
      'DROP POLICY IF EXISTS "product_params_admin_all" ON product_params;',
      'DROP POLICY IF EXISTS "product_params_dev_bypass" ON product_params;'
    ];
    
    for (const sql of dropPolicies) {
      const { error } = await adminClient.rpc('exec_sql', { query: sql });
      if (error && !error.message.includes('does not exist')) {
        console.log('⚠️  Drop policy warning:', error.message);
      }
    }
    
    console.log('3️⃣  Creating new permissive policies...');
    
    // Create new policies that allow authenticated users to manage product_params
    const newPolicies = [
      // Allow everyone to read product parameters (for public product display)
      `CREATE POLICY "product_params_public_read" ON product_params 
       FOR SELECT USING (true);`,
      
      // Allow authenticated users to insert, update, delete product parameters
      `CREATE POLICY "product_params_authenticated_write" ON product_params 
       FOR ALL TO authenticated 
       USING (true) 
       WITH CHECK (true);`,
       
      // Allow service role (admin) full access
      `CREATE POLICY "product_params_service_role_access" ON product_params 
       FOR ALL TO service_role 
       USING (true) 
       WITH CHECK (true);`
    ];
    
    for (const sql of newPolicies) {
      const { error } = await adminClient.rpc('exec_sql', { query: sql });
      if (error) {
        console.log('❌ Failed to create policy:', error.message);
      } else {
        console.log('✅ Created policy:', sql.split('ON')[0].trim());
      }
    }
    
    console.log('4️⃣  Granting permissions...');
    
    // Ensure proper permissions are granted
    const grantPermissions = [
      'GRANT ALL ON product_params TO authenticated;',
      'GRANT ALL ON product_params TO service_role;',
      'GRANT SELECT ON product_params TO anon;'
    ];
    
    for (const sql of grantPermissions) {
      const { error } = await adminClient.rpc('exec_sql', { query: sql });
      if (error) {
        console.log('⚠️  Grant permission warning:', error.message);
      } else {
        console.log('✅ Granted:', sql);
      }
    }
    
    console.log('5️⃣  Testing the fix...');
    
    // Test with the authenticated user context
    const regularClient = createClient(SUPABASE_URL, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU');
    
    // Simulate the user session (this would normally be done by the browser)
    await regularClient.auth.setSession({
      access_token: 'dummy_token',
      refresh_token: 'dummy_refresh',
      user: { id: '6cc2c928-b7eb-4f60-98dc-3b9e89784928' }
    });
    
    // Test insert operation
    const testParam = {
      product_id: 999999,
      name: 'test_param_fix',
      value: 'test_value'
    };
    
    const { data: insertData, error: insertError } = await regularClient
      .from('product_params')
      .insert(testParam)
      .select();
    
    if (insertError) {
      console.log('❌ Test insert still failing:', insertError.message);
      console.log('   403 errors will continue to occur');
      
      // Try with admin client as backup
      const { data: adminInsertData, error: adminInsertError } = await adminClient
        .from('product_params')
        .insert(testParam)
        .select();
        
      if (adminInsertError) {
        console.log('❌ Even admin client failing:', adminInsertError.message);
      } else {
        console.log('✅ Admin client works, cleaning up test data...');
        if (adminInsertData?.[0]) {
          await adminClient.from('product_params').delete().eq('id', adminInsertData[0].id);
        }
      }
    } else {
      console.log('✅ Test insert SUCCESSFUL! 403 errors should be fixed!');
      
      // Clean up test data
      if (insertData?.[0]) {
        await regularClient.from('product_params').delete().eq('id', insertData[0].id);
        console.log('🧹 Test data cleaned up');
      }
    }
    
    console.log('\n🎯 RLS FIX SUMMARY');
    console.log('==================');
    console.log('✅ Policies updated');
    console.log('✅ Permissions granted');
    
    if (!insertError) {
      console.log('🎉 403 ERRORS SHOULD NOW BE RESOLVED!');
      console.log('   Users can now save product parameters without issues');
    } else {
      console.log('⚠️  Some issues remain - may need manual database intervention');
    }
    
  } catch (error) {
    console.log('❌ Error during RLS fix:', error.message);
  }
}

fixProductParamsRLS().catch(console.error);
