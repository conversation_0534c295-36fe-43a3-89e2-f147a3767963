#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://fijuwtwzugtmbggdmxjb.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE';

async function testApproval() {
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Возьмем первый тестовый продукт
    const testProductId = '25210029-f373-42f5-b120-4588f27d7ca2'; // Тестовый товар 2
    
    console.log('Testing product approval...');
    console.log('Product ID:', testProductId);
    
    // Одобряем продукт
    const { data, error } = await supabase
      .from('products')
      .update({
        moderation_status: 'approved',
        is_active: true
      })
      .eq('id', testProductId)
      .select();
    
    if (error) {
      console.error('Error:', error);
    } else {
      console.log('Success! Updated product:', data);
    }
  } catch (err) {
    console.error('Exception:', err);
  }
}

testApproval();
