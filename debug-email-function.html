<!DOCTYPE html>
<html>
<head>
    <title>Edge Function Debug Test</title>
</head>
<body>
    <h1>Debug Edge Function</h1>
    <button onclick="testFunction()">Test Edge Function</button>
    <div id="result"></div>

    <script>
        async function testFunction() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('https://dmdijuuwnbwngerkbfak.supabase.co/functions/v1/send-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU'
                    },
                    body: JSON.stringify({
                        type: 'order_confirmation',
                        orderData: {
                            id: 'test-debug-123',
                            customer_name: 'Debug Test',
                            customer_email: '<EMAIL>',
                            total_amount: 100,
                            created_at: new Date().toISOString(),
                            order_items: []
                        }
                    })
                });
                
                const responseText = await response.text();
                let responseJson;
                try {
                    responseJson = JSON.parse(responseText);
                } catch (e) {
                    responseJson = responseText;
                }
                
                resultDiv.innerHTML = `
                    <h3>Response Details:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Status Text:</strong> ${response.statusText}</p>
                    <p><strong>Headers:</strong></p>
                    <pre>${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}</pre>
                    <p><strong>Response Body:</strong></p>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; white-space: pre-wrap;">${typeof responseJson === 'object' ? JSON.stringify(responseJson, null, 2) : responseJson}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <pre style="background: #ffebee; padding: 10px; border-radius: 4px; color: red;">${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
