-- This script will force a refresh of the PostgREST schema cache

-- Option 1: Notify PostgREST to refresh its schema cache
NOTIFY pgrst, 'reload schema';

-- Option 2: Temporarily change the schema and change it back
-- This often forces a cache refresh as well
ALTER TABLE orders RENAME TO orders_temp;
ALTER TABLE orders_temp RENAME TO orders;

-- Option 3: Re-apply column definitions to ensure they're in the schema cache
ALTER TABLE orders 
  ALTER COLUMN customer_name TYPE TEXT,
  ALTER COLUMN customer_email TYPE TEXT,
  ALTER COLUMN customer_phone TYPE TEXT,
  ALTER COLUMN shipping_address TYPE JSONB,
  ALTER COLUMN total_amount TYPE DECIMAL(10,2),
  ALTER COLUMN status TYPE TEXT,
  ALTER COLUMN payment_method TYPE TEXT, 
  ALTER COLUMN payment_status TYPE TEXT,
  ALTER COLUMN notes TYPE TEXT,
  ALTER COLUMN user_id TYPE UUID; 