# Online Store

## Описание

Онлайн-магазин, построенный на React с поддержкой SSR и интеграцией с Supabase для аутентификации и хранения данных. Позволяет пользователям регистрироваться, просматривать товары, оформлять заказы, а администраторам — управлять каталогом.

## Ключевые особенности

- React (Create React App) + Tailwind CSS
- Управление состоянием через Redux Toolkit
- CMS-функции через Supabase (Auth, Database, Storage)
- Сервисы SSR на Express (скрипты `ssr`, `build:ssr`, `build:full`)
- Генерация sitemap для SEO
- Утилиты и миграции БД (`src/scripts/database/`)
- Ad-hoc и автоматические тесты (в планах миграция на Jest)

## Установка и запуск

1. Клонировать репозиторий:
   ```bash
   git clone https://github.com/your-org/online-store.git
   cd online-store
   ```
2. Установить зависимости:
   ```bash
   npm install
   ```
3. Создать файл окружения `.env` на основе `.env.example` и указать:
   ```env
   REACT_APP_SUPABASE_URL=<your-supabase-url>
   REACT_APP_SUPABASE_ANON_KEY=<your-anon-key>
   REACT_APP_SUPABASE_SERVICE_ROLE_KEY=<your-service-role-key>
   PORT=<порт для сервера>
   API_PORT=<порт для прокси/API>
   ```
4. Запустить в режиме разработки:
   ```bash
   npm run dev
   ```
5. Сборка и запуск SSR:
   ```bash
   npm run build
   npm run ssr
   ```

## Структура проекта

```
/online-store
├── src/
│   ├── pages/             # React-страницы
│   ├── components/        # Переиспользуемые UI-компоненты
│   ├── hooks/             # Кастомные React-хуки
│   ├── store/             # Redux Toolkit store и слайсы
│   ├── scripts/           # Скрипты генерации sitemap, миграции БД
│   ├── utils/             # Вспомогательные функции
│   └── supabaseClient.js  # Конфигурация Supabase
├── server.js              # Express-сервер для SSR
├── router.js              # Серверная маршрутизация
├── scripts/               # Скрипты БД, тесты, утилиты
├── public/                # Статические файлы и конфиги
├── package.json
└── README.md
```

## Полезные скрипты (package.json)

- `npm run start` / `npm run dev` — запустить dev-сервер CRA
- `npm run build` — собрать production-бандл
- `npm run ssr` — запустить SSR-сервер
- `npm run build:ssr` — собрать и сразу запустить SSR
- `npm run build:full` — генерация sitemap + сборка + запуск SSR
- `npm run lint` / `npm run lint:fix` — проверка/исправление ESLint
- `npm run format` / `npm run format:check` — форматирование Prettier

## Тестирование

В проекте есть набор ad-hoc скриптов для проверки функциональности (в корне и в директории `scripts`). В дальнейшем планируется миграция на Jest и React Testing Library.

## Контрибьюция

PRs и issue-природения приветствуются. Пожалуйста, перед PR выполните `npm run lint:fix` и `npm run format`.

---

*Автоматически сгенерировано.*
