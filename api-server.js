const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const app = express();
const PORT = process.env.API_PORT || 3001;

// Инициализация Supabase
const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.REACT_APP_SUPABASE_ANON_KEY
);

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'https://yourdomain.com', 'https://www.yourdomain.com'],
  credentials: true
}));
app.use(express.json());

// Middleware для логирования
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Базовая функция для получения товаров
const getProductsFromDB = async (options = {}) => {
  try {
    const {
      limit = 50,
      offset = 0,
      category = null,
      search = null,
      isActive = true,
      isAvailable = true,
      sortBy = 'created_at',
      sortOrder = 'desc',
      includeCategories = false,
      type = null // 'new', 'sale', 'bestseller'
    } = options;

    // Базовый запрос
    let query = supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        price,
        original_price,
        image,
        vendor,
        brand,
        external_id,
        is_on_sale,
        is_new,
        is_bestseller,
        is_available,
        is_active,
        created_at,
        updated_at,
        url,
        category_id,
        stock,
        ${includeCategories ? 'categories(id, name)' : ''}
      `, { count: 'exact' });

    // Применяем фильтры
    if (isActive !== null) {
      query = query.eq('is_active', isActive);
    }

    if (isAvailable !== null) {
      query = query.eq('is_available', isAvailable);
    }

    if (category) {
      query = query.eq('category_id', category);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,vendor.ilike.%${search}%`);
    }

    // Фильтр по типу
    if (type === 'new') {
      query = query.eq('is_new', true);
    } else if (type === 'sale') {
      query = query.eq('is_on_sale', true);
    } else if (type === 'bestseller') {
      query = query.eq('is_bestseller', true);
    }

    // Сортировка
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Пагинация
    if (limit) {
      query = query.range(offset, offset + limit - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      success: true,
      data: data || [],
      pagination: {
        limit,
        offset,
        total: count,
        pages: Math.ceil(count / limit)
      }
    };

  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
};

// API Routes

// Главная страница API
app.get('/', (req, res) => {
  res.json({
    message: 'Products API Server',
    version: '1.0.0',
    endpoints: {
      '/api/products': 'GET - Получить все товары',
      '/api/products/new': 'GET - Получить новые товары',
      '/api/products/sale': 'GET - Получить товары со скидкой',
      '/api/products/bestsellers': 'GET - Получить хиты продаж',
      '/api/products/search': 'GET - Поиск товаров',
      '/api/products/stats': 'GET - Статистика товаров',
      '/api/categories': 'GET - Получить категории'
    }
  });
});

// Получить все товары
app.get('/api/products', async (req, res) => {
  try {
    const {
      limit = 50,
      offset = 0,
      page = 1,
      category,
      search,
      sort = 'created_at',
      order = 'desc'
    } = req.query;

    const actualOffset = page > 1 ? (page - 1) * limit : offset;

    const result = await getProductsFromDB({
      limit: parseInt(limit),
      offset: parseInt(actualOffset),
      category,
      search,
      sortBy: sort,
      sortOrder: order,
      includeCategories: true
    });

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Получить новые товары
app.get('/api/products/new', async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const result = await getProductsFromDB({
      limit: parseInt(limit),
      type: 'new',
      includeCategories: true,
      sortBy: 'created_at',
      sortOrder: 'desc'
    });

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Получить товары со скидкой
app.get('/api/products/sale', async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const result = await getProductsFromDB({
      limit: parseInt(limit),
      type: 'sale',
      includeCategories: true,
      sortBy: 'created_at',
      sortOrder: 'desc'
    });

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Получить хиты продаж
app.get('/api/products/bestsellers', async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const result = await getProductsFromDB({
      limit: parseInt(limit),
      type: 'bestseller',
      includeCategories: true,
      sortBy: 'created_at',
      sortOrder: 'desc'
    });

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Поиск товаров
app.get('/api/products/search', async (req, res) => {
  try {
    const { q, limit = 20 } = req.query;

    if (!q) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    const result = await getProductsFromDB({
      search: q,
      limit: parseInt(limit),
      includeCategories: true
    });

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Получить статистику товаров
app.get('/api/products/stats', async (req, res) => {
  try {
    const [totalResult, activeResult, newResult, saleResult, bestsellerResult] = await Promise.all([
      supabase.from('products').select('id', { count: 'exact', head: true }),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_new', true),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_on_sale', true),
      supabase.from('products').select('id', { count: 'exact', head: true }).eq('is_bestseller', true)
    ]);

    res.json({
      success: true,
      data: {
        total: totalResult.count || 0,
        active: activeResult.count || 0,
        new: newResult.count || 0,
        onSale: saleResult.count || 0,
        bestsellers: bestsellerResult.count || 0,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Получить категории
app.get('/api/categories', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('id, name, slug, display_order, is_featured')
      .order('display_order', { ascending: true });

    if (error) throw error;

    res.json({
      success: true,
      data: data || []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Обработка ошибок 404
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Запуск сервера
app.listen(PORT, () => {
  console.log(`🚀 Products API Server running on port ${PORT}`);
  console.log(`📖 API Documentation: http://localhost:${PORT}`);
  console.log(`🔗 Products endpoint: http://localhost:${PORT}/api/products`);
});
