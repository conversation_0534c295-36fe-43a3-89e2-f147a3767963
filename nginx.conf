server {
    listen 80;
    server_name localhost;
    root /Users/<USER>/e-com_new/online-store/build;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    location /static/ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }

    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, no-transform";
    }
} 