const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Import supabase config (using actual credentials from supabaseClient.js)
const supabaseUrl = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyDatabaseFixes() {
  console.log('🚀 Starting database structure fixes...');
  
  try {
    // Read the SQL file
    const sqlContent = fs.readFileSync(path.join(__dirname, 'fix-database-structure.sql'), 'utf8');
    
    // Split into individual statements (simple approach)
    const statements = sqlContent
      .split(/(?:^|\n)(?:--.*(?:\n|$))?/)
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'))
      .join('\n')
      .split(/;\s*(?=\n|$)/)
      .map(s => s.trim())
      .filter(s => s.length > 0);
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute the complete SQL as one statement using exec_sql
    console.log('🔧 Applying database fixes...');
    
    const { data, error } = await supabase.rpc('exec_sql', {
      query: sqlContent
    });
    
    if (error) {
      console.error('❌ Error applying fixes:', error);
      
      // Try executing individual critical statements
      console.log('🔄 Trying to execute critical statements individually...');
      
      const criticalStatements = [
        // Add foreign key constraint
        `
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE constraint_name = 'orders_user_id_fkey' 
                AND table_name = 'orders'
            ) THEN
                UPDATE orders SET user_id = NULL WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM profiles);
                ALTER TABLE orders ADD CONSTRAINT orders_user_id_fkey 
                FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL;
                RAISE NOTICE 'Added foreign key constraint orders.user_id -> profiles.id';
            END IF;
        END $$;
        `,
        
        // Create improved exec_sql function
        `
        CREATE OR REPLACE FUNCTION exec_sql(query_text text)
        RETURNS void AS $$
        BEGIN
            EXECUTE query_text;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
        `,
        
        // Create helper function for diagnostics
        `
        CREATE OR REPLACE FUNCTION get_foreign_keys(table_name_param text)
        RETURNS TABLE(
            constraint_name text,
            column_name text,
            foreign_table_name text,
            foreign_column_name text
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT
                tc.constraint_name::text,
                kcu.column_name::text,
                ccu.table_name::text,
                ccu.column_name::text
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_name = table_name_param
            ORDER BY tc.constraint_name;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        GRANT EXECUTE ON FUNCTION get_foreign_keys(text) TO authenticated, anon;
        `
      ];
      
      for (let i = 0; i < criticalStatements.length; i++) {
        try {
          console.log(`⚙️  Executing critical statement ${i + 1}/${criticalStatements.length}...`);
          await supabase.rpc('exec_sql', { query: criticalStatements[i] });
          console.log(`✅ Statement ${i + 1} executed successfully`);
        } catch (statementError) {
          console.error(`❌ Error in statement ${i + 1}:`, statementError);
        }
      }
    } else {
      console.log('✅ Database structure fixes applied successfully!');
    }
    
    // Test the fixes
    console.log('🧪 Testing applied fixes...');
    
    // Test foreign key constraint
    try {
      const { data: fkTest } = await supabase.rpc('get_foreign_keys', { table_name_param: 'orders' });
      console.log('🔗 Foreign key constraints for orders table:', fkTest);
    } catch (error) {
      console.log('⚠️  Could not test foreign keys (function may not exist yet)');
    }
    
    // Test exec_sql function
    try {
      await supabase.rpc('exec_sql', { query: 'SELECT 1' });
      console.log('✅ exec_sql function is working');
    } catch (error) {
      console.log('❌ exec_sql function test failed:', error.message);
    }
    
    console.log('🎉 Database structure fixes completed!');
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the fixes
applyDatabaseFixes().then(() => {
  console.log('✨ Script completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
