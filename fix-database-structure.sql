-- Fix database structure issues identified by diagnostic system
-- This script addresses foreign key constraints, RLS policies, and function issues

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. FIX FOREIGN KEY CONSTRAINT: orders.user_id -> profiles.id
-- First, ensure profiles table exists with proper structure
DO $$
BEGIN
    -- Check if profiles table exists
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') THEN
        -- Create profiles table if it doesn't exist
        CREATE TABLE profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            email TEXT,
            first_name TEXT,
            last_name TEXT,
            avatar_url TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        RAISE NOTICE 'Created profiles table';
    END IF;
    
    -- Add foreign key constraint for orders.user_id -> profiles.id
    -- First check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'orders_user_id_fkey' 
        AND table_name = 'orders'
    ) THEN
        -- Clean up any invalid user_id values first
        UPDATE orders SET user_id = NULL WHERE user_id IS NOT NULL AND user_id NOT IN (SELECT id FROM profiles);
        
        -- Add the foreign key constraint
        ALTER TABLE orders ADD CONSTRAINT orders_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL;
        
        RAISE NOTICE 'Added foreign key constraint orders.user_id -> profiles.id';
    ELSE
        RAISE NOTICE 'Foreign key constraint orders.user_id -> profiles.id already exists';
    END IF;
END $$;

-- 2. OPTIMIZE RLS POLICIES
-- Update RLS policies for better security and performance

-- Profiles table RLS policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS profiles_select_policy ON profiles;
DROP POLICY IF EXISTS profiles_insert_policy ON profiles;
DROP POLICY IF EXISTS profiles_update_policy ON profiles;

-- Create optimized policies for profiles
CREATE POLICY profiles_select_policy ON profiles
    FOR SELECT
    USING (auth.uid() = id OR auth.jwt() ->> 'role' = 'admin');

CREATE POLICY profiles_insert_policy ON profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY profiles_update_policy ON profiles
    FOR UPDATE
    USING (auth.uid() = id OR auth.jwt() ->> 'role' = 'admin')
    WITH CHECK (auth.uid() = id OR auth.jwt() ->> 'role' = 'admin');

-- Products table RLS policies
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS products_select_policy ON products;
DROP POLICY IF EXISTS products_insert_policy ON products;
DROP POLICY IF EXISTS products_update_policy ON products;
DROP POLICY IF EXISTS products_delete_policy ON products;

-- Create optimized policies for products
CREATE POLICY products_select_policy ON products
    FOR SELECT
    USING (true); -- Allow public read access

CREATE POLICY products_insert_policy ON products
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY products_update_policy ON products
    FOR UPDATE
    TO authenticated
    USING (auth.jwt() ->> 'role' = 'admin')
    WITH CHECK (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY products_delete_policy ON products
    FOR DELETE
    TO authenticated
    USING (auth.jwt() ->> 'role' = 'admin');

-- Orders table RLS policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS orders_select_policy ON orders;
DROP POLICY IF EXISTS orders_insert_policy ON orders;
DROP POLICY IF EXISTS orders_update_policy ON orders;

-- Create optimized policies for orders
CREATE POLICY orders_select_policy ON orders
    FOR SELECT
    USING (
        user_id = auth.uid() OR 
        auth.jwt() ->> 'role' = 'admin' OR
        user_id IS NULL -- Allow access to guest orders
    );

CREATE POLICY orders_insert_policy ON orders
    FOR INSERT
    WITH CHECK (
        user_id = auth.uid() OR 
        auth.jwt() ->> 'role' = 'admin' OR
        user_id IS NULL -- Allow guest orders
    );

CREATE POLICY orders_update_policy ON orders
    FOR UPDATE
    USING (
        user_id = auth.uid() OR 
        auth.jwt() ->> 'role' = 'admin'
    )
    WITH CHECK (
        user_id = auth.uid() OR 
        auth.jwt() ->> 'role' = 'admin'
    );

-- Order items table RLS policies
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS order_items_select_policy ON order_items;
DROP POLICY IF EXISTS order_items_insert_policy ON order_items;

-- Create policies for order_items (inherit from orders)
CREATE POLICY order_items_select_policy ON order_items
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND (
                orders.user_id = auth.uid() OR 
                auth.jwt() ->> 'role' = 'admin' OR
                orders.user_id IS NULL
            )
        )
    );

CREATE POLICY order_items_insert_policy ON order_items
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND (
                orders.user_id = auth.uid() OR 
                auth.jwt() ->> 'role' = 'admin' OR
                orders.user_id IS NULL
            )
        )
    );

-- 3. CREATE IMPROVED exec_sql FUNCTION
-- Create a single, robust exec_sql function that handles multiple signatures
CREATE OR REPLACE FUNCTION exec_sql(query_text text)
RETURNS void AS $$
BEGIN
    EXECUTE query_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create overloaded version for parameterized queries
CREATE OR REPLACE FUNCTION exec_sql(query_text text, params jsonb DEFAULT '{}'::jsonb)
RETURNS void AS $$
DECLARE
    param_key text;
    param_value text;
    processed_query text;
BEGIN
    processed_query := query_text;
    
    -- Replace parameters in query (simple implementation)
    FOR param_key, param_value IN SELECT key, value FROM jsonb_each_text(params)
    LOOP
        processed_query := replace(processed_query, '$' || param_key, param_value);
    END LOOP;
    
    EXECUTE processed_query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION exec_sql(text, jsonb) TO authenticated, anon;

-- 4. CREATE ADDITIONAL HELPER FUNCTIONS FOR DIAGNOSTICS
CREATE OR REPLACE FUNCTION get_table_info(table_name_param text)
RETURNS TABLE(
    column_name text,
    data_type text,
    is_nullable text,
    column_default text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.column_name::text,
        c.data_type::text,
        c.is_nullable::text,
        c.column_default::text
    FROM information_schema.columns c
    WHERE c.table_name = table_name_param
    ORDER BY c.ordinal_position;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_foreign_keys(table_name_param text)
RETURNS TABLE(
    constraint_name text,
    column_name text,
    foreign_table_name text,
    foreign_column_name text
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        tc.constraint_name::text,
        kcu.column_name::text,
        ccu.table_name::text,
        ccu.column_name::text
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = table_name_param
    ORDER BY tc.constraint_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions to helper functions
GRANT EXECUTE ON FUNCTION get_table_info(text) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_foreign_keys(text) TO authenticated, anon;

-- 5. CREATE INDEXES FOR BETTER PERFORMANCE
-- Add missing indexes
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- 6. FORCE SCHEMA REFRESH
NOTIFY pgrst, 'reload schema';

-- Report completion
DO $$
BEGIN
    RAISE NOTICE 'Database structure fixes completed successfully!';
    RAISE NOTICE '✅ Added foreign key constraint: orders.user_id -> profiles.id';
    RAISE NOTICE '✅ Optimized RLS policies for profiles, products, orders, order_items';
    RAISE NOTICE '✅ Created robust exec_sql function with multiple signatures';
    RAISE NOTICE '✅ Added helper functions for diagnostics';
    RAISE NOTICE '✅ Created performance indexes';
    RAISE NOTICE '✅ Refreshed PostgREST schema cache';
END $$;
