const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Функция для очистки отладочного кода из файла
function cleanupFile(filePath) {
  console.log(`Cleaning ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;

  // Remove debug state variables
  content = content.replace(/\s*const \[debug, setDebug\] = useState\({}\);/g, '');
  content = content.replace(/\s*const \[debugInfo, setDebugInfo\] = useState\({}\);/g, '');

  // Remove debugInfo initialization
  content = content.replace(/\s*const debugInfo = {};/g, '');

  // Remove all debugInfo assignments
  content = content.replace(/\s*debugInfo\.[^;]+;/g, '');

  // Remove console.log statements
  content = content.replace(/\s*console\.log\([^)]*\);/g, '');

  // Remove console.error statements (keep only those that don't contain debug info)
  content = content.replace(/\s*console\.error\([^)]*debug[^)]*\);/g, '');
  content = content.replace(/\s*console\.error\('Error fetching[^)]*\);/g, '');

  // Remove console.warn statements
  content = content.replace(/\s*console\.warn\([^)]*\);/g, '');

  // Remove setDebug calls
  content = content.replace(/\s*setDebug\([^)]*\);/g, '');
  content = content.replace(/\s*setDebugInfo\([^)]*\);/g, '');

  // Remove renderDebugInfo function
  content = content.replace(/\s*\/\/ Добавляем отображение информации для отладки[^}]+}\);/gs, '');
  content = content.replace(/\s*const renderDebugInfo = \(\) => \{[^}]+\{[^}]+\}[^}]+\};/gs, '');

  // Remove renderDebugInfo calls
  content = content.replace(/\s*\{\/\* Отладочная информация \*\/\}\s*\{renderDebugInfo\(\)\}/g, '');

  // Remove debug comments
  content = content.replace(/\s*\/\/ Fetch total products count in the database for debugging[^}]+}/gs, '');
  content = content.replace(/\s*\/\/ Debug info[^}]*}/gs, '');
  content = content.replace(/\s*\/\/ DEBUG:[^}]*}/gs, '');

  // Remove specific debug error handlers
  content = content.replace(/\s*setDebug\(prev => \(\{ \.\.\.prev, error: error\.message \}\)\);/g, '');

  // Remove specific debug log patterns
  content = content.replace(/\s*console\.log\('Category debug info:'[^)]*\);/g, '');
  content = content.replace(/\s*console\.log\('Root categories debug info:'[^)]*\);/g, '');
  content = content.replace(/\s*console\.log\('Optimized \/categories debug info:'[^)]*\);/g, '');
  content = content.replace(/\s*console\.log\('Debug info:'[^)]*\);/g, '');

  // Remove development-only console statements
  content = content.replace(/\s*console\.log\('Route changed to:'[^)]*\);/g, '');
  content = content.replace(/\s*console\.log\('Opening cache:'[^)]*\);/g, '');

  // Remove unused imports if they're now unused (basic check)
  if (!content.includes('useState({})') && content.includes('useState,')) {
    // Remove useState from imports if debug state was the only usage
    content = content.replace(/(import[^}]*\{[^}]*), useState([^}]*\})/g, '$1$2');
    content = content.replace(/(import[^}]*\{) useState,([^}]*\})/g, '$1$2');
    content = content.replace(/\{ useState \}/g, '{}');
  }

  // Clean up empty lines (remove multiple consecutive empty lines)
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

  // Only write if content changed
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Cleaned ${filePath}`);
    return true;
  } else {
    console.log(`⏭️  No changes needed for ${filePath}`);
    return false;
  }
}

// Основная функция
async function cleanupProject() {
  console.log('🧹 Starting project-wide debug cleanup...\n');

  try {
    // Find all JS/JSX/TS/TSX files in src directory
    const pattern = 'src/**/*.{js,jsx,ts,tsx}';
    const files = glob.sync(pattern, { 
      cwd: process.cwd(),
      ignore: ['**/node_modules/**', '**/build/**', '**/.history/**']
    });

    console.log(`Found ${files.length} files to process\n`);

    let cleanedCount = 0;

    // Process each file
    for (const file of files) {
      const filePath = path.resolve(file);
      if (fs.existsSync(filePath)) {
        const wasCleaned = cleanupFile(filePath);
        if (wasCleaned) {
          cleanedCount++;
        }
      }
    }

    console.log(`\n🎉 Cleanup completed!`);
    console.log(`📊 Processed: ${files.length} files`);
    console.log(`✨ Cleaned: ${cleanedCount} files`);
    console.log(`⏭️  Skipped: ${files.length - cleanedCount} files (no changes needed)`);

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

// Запуск очистки
cleanupProject();
