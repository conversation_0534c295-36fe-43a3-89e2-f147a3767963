#!/usr/bin/env node

// Quick test of the complete email system
console.log('🚀 Starting email system test...');

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('📦 Loaded dependencies');

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

console.log('🔑 Environment variables:');
console.log('   URL present:', !!supabaseUrl);
console.log('   Key present:', !!supabaseKey);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testEmailSystem() {
  console.log('📧 Testing email system functionality...\n');

  // Test 1: Create a test order
  console.log('1️⃣  Creating test order...');
  const testOrder = {
    customer_name: 'Test Customer',
    customer_email: '<EMAIL>',
    customer_phone: '+1234567890',
    shipping_address: { city: 'Test City', nova_poshta_office: '1' },
    total_amount: 100,
    status: 'pending',
    payment_method: 'cash_on_delivery',
    payment_status: 'pending'
  };

  try {
    const { data: order, error } = await supabase
      .from('orders')
      .insert([testOrder])
      .select('*')
      .single();

    if (error) {
      console.log('   ❌ Order creation failed:', error.message);
      return false;
    }

    console.log('   ✅ Order created:', order.id);

    // Test 2: Check if email logs exist
    console.log('\n2️⃣  Checking email logs...');
    const { data: logs, error: logsError } = await supabase
      .from('email_logs')
      .select('*')
      .eq('order_id', order.id);

    if (logsError) {
      console.log('   ⚠️  Could not check email logs:', logsError.message);
    } else {
      console.log('   ✅ Email logs accessible, found', logs?.length || 0, 'entries');
    }

    // Cleanup
    await supabase.from('orders').delete().eq('id', order.id);
    console.log('   🧹 Test order cleaned up');

    return true;

  } catch (error) {
    console.log('   ❌ Test failed:', error.message);
    return false;
  }
}

async function testEdgeFunction() {
  console.log('\n3️⃣  Testing Supabase Edge Function...');
  
  try {
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        type: 'order_confirmation',
        orderId: 'test-123',
        orderData: {
          id: 'test-123',
          customer_name: 'Test Customer',
          customer_email: '<EMAIL>',
          total_amount: 100
        }
      }
    });

    if (error) {
      console.log('   ⚠️  Edge Function failed:', error.message);
      console.log('   💡 This is expected if RESEND_API_KEY is not configured');
      return false;
    } else {
      console.log('   ✅ Edge Function working:', data);
      return true;
    }
  } catch (error) {
    console.log('   ⚠️  Edge Function error:', error.message);
    return false;
  }
}

async function main() {
  console.log('='.repeat(60));
  console.log('🧪 COMPREHENSIVE EMAIL SYSTEM TEST');
  console.log('='.repeat(60));

  const orderTest = await testEmailSystem();
  const edgeTest = await testEdgeFunction();

  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY:');
  console.log('='.repeat(60));

  console.log('Order Creation & DB:', orderTest ? '✅ PASS' : '❌ FAIL');
  console.log('Edge Function:', edgeTest ? '✅ PASS' : '⚠️  NEEDS SETUP');

  if (orderTest && !edgeTest) {
    console.log('\n✅ Email system is working in MOCK MODE');
    console.log('💡 To enable real emails, configure RESEND_API_KEY in Supabase');
    console.log('📋 Instructions: EMAIL_SETUP_INSTRUCTIONS.md');
  } else if (orderTest && edgeTest) {
    console.log('\n🎉 Email system is FULLY OPERATIONAL!');
    console.log('✅ Real emails will be sent');
  } else {
    console.log('\n❌ Email system has issues');
    console.log('🔧 Check database connectivity and permissions');
  }

  console.log('\n🌐 Test your system:');
  console.log('   • Admin Panel: http://localhost:3001/admin');
  console.log('   • Create Order: http://localhost:3001');
  console.log('   • Email Diagnostic: Available in Admin Panel');
}

main().catch(console.error);
