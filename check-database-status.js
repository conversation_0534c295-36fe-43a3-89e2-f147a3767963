#!/usr/bin/env node

// Check database status after applying the fix
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('🔧 Starting database status check...');

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

console.log('URL present:', !!supabaseUrl);
console.log('Key present:', !!supabaseKey);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('REACT_APP_SUPABASE_URL:', supabaseUrl ? 'SET' : 'NOT SET');
  console.log('REACT_APP_SUPABASE_ANON_KEY:', supabaseKey ? 'SET' : 'NOT SET');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabaseStatus() {
  console.log('🔍 Checking database status after fix...');
  console.log('='.repeat(50));
  
  try {
    // Check if problematic triggers still exist
    console.log('1️⃣  Checking for email triggers...');
    const { data: triggers, error: triggerError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT trigger_name, event_object_table 
        FROM information_schema.triggers 
        WHERE trigger_name IN (
          'trigger_send_order_confirmation_email',
          'trigger_send_status_update_email'
        );
      `
    });

    if (triggerError) {
      console.log('   ⚠️  Could not check triggers:', triggerError.message);
    } else if (!triggers || triggers.length === 0) {
      console.log('   ✅ No problematic email triggers found');
    } else {
      console.log('   ⚠️  Found remaining triggers:', triggers);
    }

    // Check if problematic functions still exist
    console.log('\n2️⃣  Checking for email functions...');
    const { data: functions, error: functionError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT routine_name, routine_type 
        FROM information_schema.routines 
        WHERE routine_name IN (
          'send_order_confirmation_email',
          'send_status_update_email'
        );
      `
    });

    if (functionError) {
      console.log('   ⚠️  Could not check functions:', functionError.message);
    } else if (!functions || functions.length === 0) {
      console.log('   ✅ No problematic email functions found');
    } else {
      console.log('   ⚠️  Found remaining functions:', functions);
    }

    // Test basic order table access
    console.log('\n3️⃣  Testing orders table access...');
    const { data: orderCount, error: countError } = await supabase
      .from('orders')
      .select('id', { count: 'exact', head: true });

    if (countError) {
      console.log('   ❌ Orders table access failed:', countError.message);
    } else {
      console.log('   ✅ Orders table accessible (count:', orderCount?.count || 0, ')');
    }

    // Test simple order insertion (without committing)
    console.log('\n4️⃣  Testing order insertion capability...');
    const testData = {
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+1234567890',
      shipping_address: { city: 'Test City', nova_poshta_office: '1' },
      total_amount: 100,
      status: 'pending',
      payment_method: 'cash_on_delivery',
      payment_status: 'pending'
    };

    const { data: testOrder, error: insertError } = await supabase
      .from('orders')
      .insert([testData])
      .select('*')
      .single();

    if (insertError) {
      console.log('   ❌ Order insertion failed:', insertError.message);
      if (insertError.code === '3F000') {
        console.log('   🚨 Still getting "schema does not exist" error!');
        console.log('   💡 The fix may not have been applied yet.');
      }
    } else {
      console.log('   ✅ Order insertion successful, ID:', testOrder.id);
      
      // Clean up test order
      await supabase.from('orders').delete().eq('id', testOrder.id);
      console.log('   🧹 Test order cleaned up');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }

  console.log('='.repeat(50));
  console.log('✅ Database status check complete');
}

checkDatabaseStatus().catch(console.error);
