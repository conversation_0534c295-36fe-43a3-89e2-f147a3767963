const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://fijuwtwzugtmbggdmxjb.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE';

console.log('Creating supabase client...');
const supabase = createClient(supabaseUrl, supabaseKey);
console.log('Client created successfully');

async function testModerationUpdate() {
  try {
    console.log('Starting moderation test...');
    
    // Берем тестовый продукт
    const testProductId = '25210029-f373-42f5-b120-4588f27d7ca2';
    
    console.log('Approving product:', testProductId);
    
    const { data, error } = await supabase
      .from('products')
      .update({
        moderation_status: 'approved',
        is_active: true
      })
      .eq('id', testProductId)
      .select();
    
    if (error) {
      console.error('Update error:', JSON.stringify(error, null, 2));
    } else {
      console.log('Update successful:', JSON.stringify(data, null, 2));
    }
    
    // Проверим результат
    console.log('Checking updated product...');
    const { data: checkData, error: checkError } = await supabase
      .from('products')
      .select('id, name, moderation_status, is_active')
      .eq('id', testProductId)
      .single();
    
    if (checkError) {
      console.error('Check error:', JSON.stringify(checkError, null, 2));
    } else {
      console.log('Product status after update:', JSON.stringify(checkData, null, 2));
    }
    
  } catch (err) {
    console.error('Exception:', err.message);
    console.error('Stack:', err.stack);
  }
}

testModerationUpdate()
  .then(() => console.log('Test completed'))
  .catch(err => console.error('Test failed:', err));
