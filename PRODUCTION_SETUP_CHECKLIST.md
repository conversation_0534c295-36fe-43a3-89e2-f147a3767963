# ✅ Чек-лист настройки автоматического обновления фидов в продакшене

## 🚀 GitHub Actions (Рекомендуемый способ)

### 1. Настройка секретов:
- [ ] `Settings` → `Secrets and variables` → `Actions`
- [ ] Добавить `REACT_APP_SUPABASE_URL`
- [ ] Добавить `REACT_APP_SUPABASE_ANON_KEY`  
- [ ] Добавить `SUPABASE_SERVICE_ROLE_KEY`

### 2. Активация:
- [ ] Сделать коммит с файлом `.github/workflows/feed-processing.yml`
- [ ] Проверить в `Actions` что workflow появился
- [ ] Запустить тестовый запуск: `Actions` → `Automatic Feed Processing` → `Run workflow`

### 3. Мониторинг:
- [ ] Проверить логи в `Actions`
- [ ] Проверить обновление фидов в админ панели
- [ ] Настроить уведомления о сбоях (по желанию)

## 🔄 Альтернативный способ: Supabase Edge Functions

### 1. Установка Supabase CLI:
- [ ] `npm install -g supabase`
- [ ] `supabase login`

### 2. Деплой функции:
- [ ] `supabase functions deploy process-feed`
- [ ] Настроить переменные окружения в Supabase Dashboard

### 3. Настройка pg_cron:
- [ ] Выполнить SQL из `scripts/migrations/setup-automatic-feed-processing.sql`
- [ ] Проверить что cron задание создано: `SELECT * FROM cron.job;`

## 📊 Проверка работы

### Через SQL:
```sql
-- Проверить последние обновления фидов
SELECT name, last_fetched, 
       EXTRACT(EPOCH FROM (NOW() - last_fetched))/3600 as hours_ago
FROM feeds WHERE is_active = true;

-- Проверить задачи обработки
SELECT f.name, fj.status, fj.created_at, fj.items_created
FROM feed_jobs fj
JOIN feeds f ON fj.feed_id = f.id
ORDER BY fj.created_at DESC LIMIT 10;
```

### Через админ панель:
- [ ] Открыть `/admin/feed-management`
- [ ] Проверить колонку "Last Processed"
- [ ] Проверить колонку "Last Job"

## ⚙️ Настройка частоты (по желанию)

### GitHub Actions:
Изменить cron в `.github/workflows/feed-processing.yml`:
- `'0 */6 * * *'` - каждые 6 часов
- `'0 */4 * * *'` - каждые 4 часа  
- `'0 */12 * * *'` - каждые 12 часов

### pg_cron:
```sql
-- Удалить старое задание
SELECT cron.unschedule('process-feeds-every-6-hours');

-- Создать новое
SELECT cron.schedule('process-feeds-every-4-hours', '0 */4 * * *', 'SELECT process_all_active_feeds();');
```

## 🆘 Устранение проблем

### Если фиды не обновляются:
1. Проверить логи в GitHub Actions
2. Проверить секреты в GitHub
3. Проверить активность фидов в базе данных
4. Проверить доступность URL фидов

### Если есть ошибки 403:
1. Проверить SUPABASE_SERVICE_ROLE_KEY
2. Проверить RLS политики в Supabase
3. Убедиться что admin client работает корректно

✅ **Готово!** Фиды будут обновляться автоматически каждые 6 часов.
