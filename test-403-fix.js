/**
 * Test Admin Product Creation - Verify 403 errors are fixed
 */

const { getAdminClient, convertUUIDToBigInt } = require('./src/supabaseClient');

async function testProductCreation() {
  console.log('🧪 Testing Admin Product Creation (403 Error Fix)');
  console.log('====================================================');

  const adminClient = getAdminClient();
  
  if (!adminClient) {
    console.log('❌ Admin client not available');
    return;
  }

  console.log('✅ Admin client available');

  // Test 1: Basic product creation
  const testProduct = {
    name: 'Test Product - 403 Fix',
    description: 'Testing product creation after 403 fix',
    price: 99.99,
    is_active: true,
    category_id: null,
    vendor: 'Test Vendor',
    brand: 'Test Brand'
  };

  try {
    console.log('\n1️⃣  Testing product creation...');
    
    const { data: productData, error: productError } = await adminClient
      .from('products')
      .insert([testProduct])
      .select()
      .single();

    if (productError) {
      console.log('❌ Product creation failed:', productError.message);
      return;
    }

    console.log('✅ Product created successfully:', productData.id);

    // Test 2: Product parameters creation (this was causing 403 errors)
    console.log('\n2️⃣  Testing product parameters creation...');
    
    const bigIntProductId = convertUUIDToBigInt(productData.id);
    
    const testParams = [
      { product_id: bigIntProductId, name: 'Color', value: 'Red' },
      { product_id: bigIntProductId, name: 'Size', value: 'Large' },
      { product_id: bigIntProductId, name: 'Material', value: 'Cotton' }
    ];

    const { data: paramsData, error: paramsError } = await adminClient
      .from('product_params')
      .insert(testParams)
      .select();

    if (paramsError) {
      console.log('❌ Product parameters creation failed:', paramsError.message);
      console.log('   This indicates 403 errors are NOT fixed yet');
    } else {
      console.log('✅ Product parameters created successfully:', paramsData.length, 'params');
      console.log('   🎉 403 ERRORS ARE FIXED!');
    }

    // Test 3: Update operation
    console.log('\n3️⃣  Testing product update...');
    
    const { data: updateData, error: updateError } = await adminClient
      .from('products')
      .update({ name: 'Test Product - 403 Fix (Updated)' })
      .eq('id', productData.id)
      .select();

    if (updateError) {
      console.log('❌ Product update failed:', updateError.message);
    } else {
      console.log('✅ Product update successful');
    }

    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    
    if (paramsData && paramsData.length > 0) {
      await adminClient
        .from('product_params')
        .delete()
        .eq('product_id', bigIntProductId);
      console.log('   Removed test parameters');
    }

    await adminClient
      .from('products')
      .delete()
      .eq('id', productData.id);
    console.log('   Removed test product');

    console.log('\n🎯 SUMMARY');
    console.log('============');
    console.log('Product Creation:', productError ? '❌ FAILED' : '✅ SUCCESS');
    console.log('Product Parameters:', paramsError ? '❌ FAILED (403 error)' : '✅ SUCCESS');
    console.log('Product Update:', updateError ? '❌ FAILED' : '✅ SUCCESS');
    
    if (!paramsError) {
      console.log('\n🎉 ALL TESTS PASSED - 403 ERRORS ARE FIXED!');
      console.log('   Admin panel should now work correctly for saving product parameters');
    } else {
      console.log('\n⚠️  403 errors still present - additional fixes needed');
    }

  } catch (error) {
    console.log('❌ Test failed with exception:', error.message);
  }
}

testProductCreation().catch(console.error);
