#!/bin/bash

# Quick GitHub Secrets Setup Helper
# This script provides the exact values to copy-paste into GitHub Secrets

echo "🔐 GitHub Secrets Configuration Helper"
echo "======================================"
echo ""
echo "Copy these values to GitHub Repository Settings → Secrets and variables → Actions:"
echo ""

echo "1️⃣ First Secret:"
echo "   Name: REACT_APP_SUPABASE_URL"
echo "   Value: https://dmdijuuwnbwngerkbfak.supabase.co"
echo ""

echo "2️⃣ Second Secret:"
echo "   Name: REACT_APP_SUPABASE_ANON_KEY"
echo "   Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU"
echo ""

echo "3️⃣ Third Secret:"
echo "   Name: SUPABASE_SERVICE_ROLE_KEY"
echo "   Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8"
echo ""

echo "📋 Instructions:"
echo "1. Go to GitHub repository → Settings → Secrets and variables → Actions"
echo "2. Click 'New repository secret' for each one"
echo "3. Copy name and value exactly as shown above"
echo "4. After all 3 secrets are added, commit and push to activate workflow"
echo ""

echo "🚀 After setup:"
echo "   - Feeds will update every 6 hours automatically"
echo "   - Check GitHub Actions tab to monitor"
echo "   - Check /admin/feed-management for updates"
echo ""

echo "✅ System is ready for production deployment!"

# Also save values to a file for easy reference
cat > "github-secrets-values.txt" << 'EOF'
GitHub Secrets Configuration
===========================

Secret 1:
Name: REACT_APP_SUPABASE_URL
Value: https://dmdijuuwnbwngerkbfak.supabase.co

Secret 2:
Name: REACT_APP_SUPABASE_ANON_KEY
Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU

Secret 3:
Name: SUPABASE_SERVICE_ROLE_KEY
Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8

Instructions:
1. Go to GitHub repository → Settings → Secrets and variables → Actions
2. Click 'New repository secret' for each one
3. Copy name and value exactly as shown above
4. After all 3 secrets are added, commit and push to activate workflow

The system will then automatically update feeds every 6 hours.
EOF

echo ""
echo "📄 Values also saved to: github-secrets-values.txt"
