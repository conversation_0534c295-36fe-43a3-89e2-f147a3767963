#!/usr/bin/env node

/**
 * Test script to verify that the feeds table column name fix is working
 * This script tests both column names to see which one exists in the actual table
 */

const { createClient } = require('@supabase/supabase-js');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env.local') });

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://smkkodldxjnthospnapv.supabase.co';
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNta2tvZGxkeGpudGhvc3BuYXB2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODYxNDAsImV4cCI6MjA1OTE2MjE0MH0.ny6fy6SGVftwTHE0L-E1wBDdcUHs0skKcd3ZWOQ7b4E';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testFeedColumns() {
  console.log('🔍 Testing feeds table column names...\n');

  try {
    // Test 1: Get all feeds to see the structure
    console.log('1️⃣ Testing basic feeds table access...');
    const { data: allFeeds, error: allError } = await supabase
      .from('feeds')
      .select('*')
      .limit(5);

    if (allError) {
      console.log('❌ Error accessing feeds table:', allError.message);
      return;
    }

    console.log(`✅ Successfully accessed feeds table. Found ${allFeeds.length} feeds.`);
    
    if (allFeeds.length > 0) {
      console.log('📋 Sample feed structure:');
      const sampleFeed = allFeeds[0];
      console.log('   Available columns:', Object.keys(sampleFeed).join(', '));
      
      // Check which active column exists
      const hasIsActive = 'is_active' in sampleFeed;
      const hasActive = 'active' in sampleFeed;
      
      console.log(`   Has 'is_active' column: ${hasIsActive ? '✅ YES' : '❌ NO'}`);
      console.log(`   Has 'active' column: ${hasActive ? '✅ YES' : '❌ NO'}`);
    }

    // Test 2: Try filtering by is_active
    console.log('\n2️⃣ Testing filter by is_active=true...');
    const { data: isActiveFeeds, error: isActiveError } = await supabase
      .from('feeds')
      .select('id, name')
      .eq('is_active', true);

    if (isActiveError) {
      console.log('❌ Error filtering by is_active:', isActiveError.message);
    } else {
      console.log(`✅ Successfully filtered by is_active. Found ${isActiveFeeds.length} active feeds.`);
    }

    // Test 3: Try filtering by active (should fail if column doesn't exist)
    console.log('\n3️⃣ Testing filter by active=true...');
    const { data: activeFeeds, error: activeError } = await supabase
      .from('feeds')
      .select('id, name')
      .eq('active', true);

    if (activeError) {
      console.log('❌ Error filtering by active:', activeError.message);
      console.log('   This is expected if the column name is correctly set to "is_active"');
    } else {
      console.log(`⚠️ Successfully filtered by active. Found ${activeFeeds.length} active feeds.`);
      console.log('   This suggests the table might still have an "active" column');
    }

    // Test 4: Show the process-feeds.js logic will work
    console.log('\n4️⃣ Testing process-feeds.js logic...');
    let query = supabase.from('feeds').select('*');
    query = query.eq('active', true);

    const { data: processFeeds, error: processError } = await query;

    if (processError) {
      console.log('❌ Error with process-feeds logic:', processError.message);
    } else {
      console.log(`✅ Process-feeds logic works. Found ${processFeeds.length} feeds to process.`);
      if (processFeeds.length > 0) {
        console.log('   Feeds that would be processed:');
        processFeeds.forEach(feed => {
          console.log(`   - ${feed.name} (ID: ${feed.id})`);
        });
      }
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

async function main() {
  console.log('🚀 Starting feed column name test...\n');
  
  console.log('Environment check:');
  console.log('  SUPABASE_URL:', !!supabaseUrl);
  console.log('  SUPABASE_KEY:', !!supabaseKey);
  console.log('  URL:', supabaseUrl);
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing environment variables');
    return;
  }
  
  await testFeedColumns();
  console.log('\n✅ Test completed!');
}

main().catch(console.error);
