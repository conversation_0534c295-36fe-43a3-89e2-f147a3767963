const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Не найдены переменные окружения Supabase');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createSliderBanners() {
  try {
    console.log('🎨 Создаем красивые баннеры для демонстрации слайдера...');

    // Удаляем существующие тестовые баннеры
    const { error: deleteError } = await supabase
      .from('banners')
      .delete()
      .ilike('title', '%демо%');

    if (deleteError) {
      console.log('ℹ️ Не удалось удалить старые демо баннеры:', deleteError.message);
    }

    // Создаем новые красивые баннеры для слайдера
    const newBanners = [
      {
        title: 'Эксклюзивная коллекция 2025',
        subtitle: 'Откройте для себя новые горизонты стиля и качества',
        button_text: 'Смотреть коллекцию',
        button_link: '/products',
        image_url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
        position: 1,
        is_active: true,
        slider_enabled: true,
        auto_play: true,
        auto_play_interval: 6000
      },
      {
        title: 'Летняя распродажа',
        subtitle: 'Скидки до 70% на лучшие товары сезона',
        button_text: 'Купить со скидкой',
        button_link: '/sale',
        image_url: 'https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
        position: 2,
        is_active: true,
        slider_enabled: true,
        auto_play: true,
        auto_play_interval: 6000
      },
      {
        title: 'Премиум качество',
        subtitle: 'Товары от ведущих мировых брендов с гарантией качества',
        button_text: 'Узнать больше',
        button_link: '/brands',
        image_url: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
        position: 3,
        is_active: true,
        slider_enabled: true,
        auto_play: true,
        auto_play_interval: 6000
      },
      {
        title: 'Быстрая доставка',
        subtitle: 'Получите свой заказ уже завтра с нашей экспресс-доставкой',
        button_text: 'Заказать сейчас',
        button_link: '/products',
        image_url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
        position: 4,
        is_active: true,
        slider_enabled: true,
        auto_play: true,
        auto_play_interval: 6000
      },
      // Нижние баннеры
      {
        title: 'Новые поступления',
        subtitle: 'Первыми узнавайте о новых товарах и эксклюзивных предложениях',
        button_text: 'Смотреть новинки',
        button_link: '/new-arrivals',
        image_url: 'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
        position: 6,
        is_active: true,
        slider_enabled: true,
        auto_play: true,
        auto_play_interval: 8000
      },
      {
        title: 'Хиты продаж',
        subtitle: 'Самые популярные товары по мнению наших покупателей',
        button_text: 'Смотреть хиты',
        button_link: '/bestsellers',
        image_url: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=2126&q=80',
        position: 7,
        is_active: true,
        slider_enabled: true,
        auto_play: true,
        auto_play_interval: 8000
      }
    ];

    console.log(`📋 Добавляем ${newBanners.length} новых баннеров...`);

    for (let i = 0; i < newBanners.length; i++) {
      const banner = newBanners[i];
      
      const { data, error } = await supabase
        .from('banners')
        .insert([{
          ...banner,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select();

      if (error) {
        console.error(`❌ Ошибка при создании баннера "${banner.title}":`, error);
      } else {
        console.log(`✅ Создан баннер: "${banner.title}" (позиция ${banner.position})`);
      }
    }

    // Проверяем результат
    console.log('🔍 Проверяем созданные баннеры...');
    
    const { data: allBanners, error: checkError } = await supabase
      .from('banners')
      .select('*')
      .order('position');

    if (checkError) {
      console.error('❌ Ошибка при проверке баннеров:', checkError);
      return;
    }

    console.log('\n📊 Статистика баннеров:');
    const topBanners = allBanners.filter(b => b.position <= 5);
    const bottomBanners = allBanners.filter(b => b.position > 5);
    const sliderBanners = allBanners.filter(b => b.slider_enabled);

    console.log(`  📍 Верхние баннеры: ${topBanners.length}`);
    console.log(`  📍 Нижние баннеры: ${bottomBanners.length}`);
    console.log(`  🎠 Включенные в слайдер: ${sliderBanners.length}`);
    console.log(`  ✅ Активные: ${allBanners.filter(b => b.is_active).length}`);

    console.log('\n📋 Детализация баннеров:');
    allBanners.forEach(banner => {
      const status = banner.is_active ? '🟢' : '🔴';
      const slider = banner.slider_enabled ? '🎠' : '📄';
      console.log(`  ${status} ${slider} Поз.${banner.position}: "${banner.title}"`);
    });

    console.log('\n🎉 Баннеры для слайдера созданы успешно!');
    console.log('🌐 Откройте главную страницу для просмотра нового слайдера');

  } catch (error) {
    console.error('❌ Неожиданная ошибка:', error);
  }
}

// Запускаем создание баннеров для слайдера
createSliderBanners()
  .then(() => {
    console.log('\n✨ Процесс создания баннеров завершен');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 Критическая ошибка:', error);
    process.exit(1);
  });
