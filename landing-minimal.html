<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roomchik - Товары для дома | Сайт в разработке</title>
    <meta name="description" content="Интернет-магазин товаров для дома и интерьера. Товары уже доступны для заказа!">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏠</text></svg>">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
            line-height: 1.6; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        
        .header { 
            background: rgba(255,255,255,0.95); 
            padding: 1rem 0; 
            position: fixed; 
            width: 100%; 
            top: 0; 
            z-index: 1000;
            backdrop-filter: blur(10px);
        }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: 700; color: #667eea; text-decoration: none; }
        .status { background: #ff6b6b; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; }
        
        .hero { 
            padding: 120px 0 60px; 
            text-align: center; 
            color: white; 
        }
        .hero h1 { font-size: 2.5rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
        .btn { 
            display: inline-block; 
            background: #ff6b6b; 
            color: white; 
            padding: 1rem 2rem; 
            border-radius: 30px; 
            text-decoration: none; 
            font-weight: 600;
            transition: transform 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover { transform: translateY(-2px); }
        
        .products { padding: 60px 0; background: white; }
        .products h2 { text-align: center; font-size: 2rem; margin-bottom: 2rem; }
        .products-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); 
            gap: 1.5rem; 
            margin-bottom: 2rem;
        }
        .product-card { 
            background: #f8f9fa; 
            border-radius: 10px; 
            overflow: hidden; 
            transition: transform 0.3s;
        }
        .product-card:hover { transform: translateY(-3px); }
        .product-image { width: 100%; height: 180px; object-fit: cover; background: #e9ecef; }
        .product-info { padding: 1rem; }
        .product-name { font-weight: 600; margin-bottom: 0.5rem; }
        .product-price { color: #667eea; font-weight: 700; font-size: 1.1rem; }
        .product-vendor { color: #666; font-size: 0.9rem; margin-bottom: 0.5rem; }
        .badge { 
            display: inline-block; 
            padding: 0.2rem 0.5rem; 
            border-radius: 10px; 
            font-size: 0.7rem; 
            color: white; 
            margin-right: 0.3rem;
        }
        .badge-new { background: #28a745; }
        .badge-sale { background: #dc3545; }
        .badge-bestseller { background: #ffc107; color: #000; }
        
        .loading { text-align: center; padding: 2rem; color: #666; }
        .footer { 
            background: #2c3e50; 
            color: white; 
            padding: 2rem 0; 
            text-align: center; 
        }
        .footer a { color: #667eea; text-decoration: none; }
        
        @media (max-width: 768px) {
            .hero h1 { font-size: 2rem; }
            .hero p { font-size: 1rem; }
            .products-grid { grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">🏠 Roomchik</a>
                <div class="status">В разработке</div>
            </div>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>Товары для дома и интерьера</h1>
            <p>Сайт находится в разработке. Товары уже доступны для заказа!</p>
            <button class="btn" onclick="document.getElementById('products').scrollIntoView({behavior:'smooth'})">
                Посмотреть товары
            </button>
        </div>
    </section>

    <section class="products" id="products">
        <div class="container">
            <h2>Наши товары</h2>
            <div id="products-container">
                <div class="loading">Загружаем товары...</div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <button class="btn" onclick="loadMore()">Загрузить еще</button>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <p><strong>🏠 Roomchik</strong> - Товары для дома и интерьера</p>
            <p>📧 <a href="mailto:<EMAIL>"><EMAIL></a> | 📱 <a href="tel:+380123456789">+38 (012) 345-67-89</a></p>
            <p>&copy; 2024 Roomchik. Все права защищены.</p>
        </div>
    </footer>

    <script>
        const API_URL = 'https://api.roomchik.com/api'; // Замените на ваш API URL
        let page = 1;

        async function loadProducts(pageNum = 1, append = false) {
            const container = document.getElementById('products-container');
            
            if (!append) {
                container.innerHTML = '<div class="loading">Загружаем товары...</div>';
            }

            try {
                const response = await fetch(`${API_URL}/products?limit=12&page=${pageNum}`);
                const result = await response.json();

                if (result.success && result.data.length > 0) {
                    const html = result.data.map(product => `
                        <div class="product-card">
                            <img src="${product.image || 'https://via.placeholder.com/250x180?text=Товар'}" 
                                 alt="${product.name}" 
                                 class="product-image"
                                 onerror="this.src='https://via.placeholder.com/250x180?text=Товар'">
                            <div class="product-info">
                                <div style="margin-bottom: 0.5rem;">
                                    ${product.is_new ? '<span class="badge badge-new">Новинка</span>' : ''}
                                    ${product.is_on_sale ? '<span class="badge badge-sale">Скидка</span>' : ''}
                                    ${product.is_bestseller ? '<span class="badge badge-bestseller">Хит</span>' : ''}
                                </div>
                                <div class="product-name">${product.name}</div>
                                <div class="product-vendor">${product.vendor || product.brand || ''}</div>
                                <div class="product-price">
                                    ${product.price} ₴
                                    ${product.original_price && product.original_price > product.price ? 
                                      ` <span style="text-decoration: line-through; color: #999; font-size: 0.9rem;">${product.original_price} ₴</span>` : ''}
                                </div>
                            </div>
                        </div>
                    `).join('');

                    if (append) {
                        const grid = container.querySelector('.products-grid');
                        if (grid) {
                            grid.innerHTML += html;
                        }
                    } else {
                        container.innerHTML = `<div class="products-grid">${html}</div>`;
                    }
                } else if (!append) {
                    container.innerHTML = '<div class="loading">Товары скоро появятся! 🛍️</div>';
                }
            } catch (error) {
                console.error('Ошибка:', error);
                container.innerHTML = '<div class="loading">Временные технические работы 🔧</div>';
            }
        }

        function loadMore() {
            page++;
            loadProducts(page, true);
        }

        // Загрузка при старте
        document.addEventListener('DOMContentLoaded', () => loadProducts());
    </script>
</body>
</html>
