const { createClient } = require('@supabase/supabase-js');

// Используем правильные настройки Supabase
const supabase = createClient(
  'https://dmdijuuwnbwngerkbfak.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU'
);

async function testCorrectDatabase() {
  try {
    console.log('=== Тестирование с правильной базой данных ===\n');
    
    // Проверяем подключение
    const { data: testConnection, error: connError } = await supabase
      .from('products')
      .select('id, name, moderation_status')
      .limit(1);
    
    if (connError) {
      console.error('❌ Ошибка подключения:', connError.message);
      return;
    }
    
    console.log('✅ Подключение к базе данных успешно');
    
    // Получаем продукты в очереди модерации
    const { data: pendingProducts, error: pendingError } = await supabase
      .from('products')
      .select('id, name, external_id, moderation_status, is_active')
      .eq('moderation_status', 'pending_approval')
      .limit(5);
    
    if (pendingError) {
      console.error('❌ Ошибка при получении продуктов:', pendingError.message);
      return;
    }
    
    console.log(`📋 Найдено ${pendingProducts.length} продуктов в очереди модерации:`);
    pendingProducts.forEach((p, i) => {
      console.log(`  ${i+1}. ${p.name} (ID: ${p.id})`);
    });
    
    // Тестируем одобрение первого продукта
    if (pendingProducts.length > 0) {
      const productToTest = pendingProducts[0];
      console.log(`\n🧪 Тестируем одобрение: ${productToTest.name}`);
      
      const { error: updateError } = await supabase
        .from('products')
        .update({
          moderation_status: 'approved',
          is_active: true
        })
        .eq('id', productToTest.id);
      
      if (updateError) {
        console.error('❌ Ошибка при одобрении:', updateError.message);
      } else {
        console.log('✅ Продукт успешно одобрен!');
        
        // Проверяем результат
        const { data: updatedProduct } = await supabase
          .from('products')
          .select('moderation_status, is_active')
          .eq('id', productToTest.id)
          .single();
        
        console.log(`📊 Новый статус: ${updatedProduct.moderation_status}, Активен: ${updatedProduct.is_active}`);
      }
    }
    
    // Получаем общую статистику
    const { data: allProducts } = await supabase
      .from('products')
      .select('moderation_status, is_active');
    
    const stats = allProducts.reduce((acc, p) => {
      const key = `${p.moderation_status}_${p.is_active}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\n📊 Статистика всех продуктов:');
    Object.entries(stats).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });
    
  } catch (error) {
    console.error('❌ Общая ошибка:', error.message);
  }
}

testCorrectDatabase();
