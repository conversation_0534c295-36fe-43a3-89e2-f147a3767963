-- Add status column to products table if it doesn't exist
DO $$
BEGIN
  -- Check if status column exists, if not add it
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'products' AND column_name = 'status'
  ) THEN
    ALTER TABLE products ADD COLUMN status VARCHAR(20) DEFAULT 'active';
    RAISE NOTICE 'Added status column to products table';
  ELSE
    RAISE NOTICE 'Status column already exists';
  END IF;
  
  -- Update all NULL values to 'active'
  UPDATE products SET status = 'active' WHERE status IS NULL;
END
$$;
