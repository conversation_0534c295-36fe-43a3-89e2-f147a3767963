{"version": 2, "builds": [{"src": "build/**", "use": "@vercel/static"}], "routes": [{"src": "/static/(.*)", "dest": "/build/static/$1", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "dest": "/build/$1", "headers": {"cache-control": "public, max-age=31536000, immutable"}}, {"src": "/(.*)", "dest": "/build/index.html"}], "env": {"REACT_APP_SUPABASE_URL": "https://dmdijuuwnbwngerkbfak.supabase.co", "REACT_APP_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE5Nzg3ODIsImV4cCI6MjA0NzU1NDc4Mn0.6nM6DX8gYXxdSF4EGNHwFSUoYO7PtGuNg1vXC9_cT0g"}, "cleanUrls": true, "trailingSlash": false}