console.log('Тест запускается...');

async function simpleTest() {
  console.log('Функция запущена');
  
  try {
    const { createClient } = require('@supabase/supabase-js');
    console.log('Supabase модуль загружен');
    
    const supabase = createClient(
      'https://fijuwtwzugtmbggdmxjb.supabase.co',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE'
    );
    console.log('Supabase клиент создан');
    
    // Тестируем одобрение тестового продукта
    const testProductId = '25210029-f373-42f5-b120-4588f27d7ca2';
    console.log('Тестируем продукт:', testProductId);
    
    const result = await supabase
      .from('products')
      .update({
        moderation_status: 'approved',
        is_active: true
      })
      .eq('id', testProductId);
    
    console.log('Результат обновления:', result);
    
    if (result.error) {
      console.log('Ошибка:', result.error);
    } else {
      console.log('✅ Успешно!');
    }
    
  } catch (error) {
    console.error('Ошибка в try-catch:', error);
  }
}

simpleTest().then(() => {
  console.log('Тест завершен');
}).catch(error => {
  console.error('Ошибка в промисе:', error);
});
