const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.REACT_APP_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createTestProducts() {
  try {
    console.log('=== Creating test products for moderation ===');
    
    // Создадим несколько тестовых товаров с pending_approval статусом
    const testProducts = [
      {
        name: 'Тестовый товар 1 для модерации',
        external_id: 'test_mod_1',
        description: 'Описание тестового товара 1',
        price: 100.00,
        moderation_status: 'pending_approval',
        is_active: false,
        vendor: 'Test Vendor',
        language: 'ru'
      },
      {
        name: 'Тестовый товар 2 для модерации',
        external_id: 'test_mod_2', 
        description: 'Описание тестового товара 2',
        price: 200.00,
        moderation_status: 'pending_approval',
        is_active: false,
        vendor: 'Test Vendor',
        language: 'ru'
      },
      {
        name: 'Тестовый товар 3 для модерации',
        external_id: 'test_mod_3',
        description: 'Описание тестового товара 3', 
        price: 300.00,
        moderation_status: 'pending_approval',
        is_active: false,
        vendor: 'Test Vendor',
        language: 'ru'
      }
    ];
    
    for (const product of testProducts) {
      try {
        // Проверяем, существует ли уже товар с таким external_id
        const { data: existing } = await supabase
          .from('products')
          .select('id')
          .eq('external_id', product.external_id)
          .single();
        
        if (existing) {
          console.log(`Updating existing product ${product.external_id} to pending_approval`);
          const { error } = await supabase
            .from('products')
            .update({
              moderation_status: 'pending_approval',
              is_active: false
            })
            .eq('id', existing.id);
          
          if (error) throw error;
          console.log(`✅ Updated ${product.external_id}`);
        } else {
          console.log(`Creating new test product: ${product.name}`);
          const { data, error } = await supabase
            .from('products')
            .insert(product)
            .select('id');
          
          if (error) throw error;
          console.log(`✅ Created ${product.external_id} with ID: ${data[0].id}`);
        }
      } catch (error) {
        console.error(`❌ Error with product ${product.external_id}:`, error.message);
      }
    }
    
    // Проверяем результат
    const { data: pendingProducts, error } = await supabase
      .from('products')
      .select('id, name, moderation_status, is_active, external_id')
      .eq('moderation_status', 'pending_approval')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('Error fetching pending products:', error);
    } else {
      console.log(`\n=== Found ${pendingProducts.length} products pending approval ===`);
      pendingProducts.forEach(p => {
        console.log(`- ${p.name} (id: ${p.id}, external_id: ${p.external_id}, active: ${p.is_active})`);
      });
    }
    
    console.log('\n✅ Test products created successfully!');
    console.log('Now you can go to /admin/product_moderation to see them in the moderation panel.');
    
  } catch (error) {
    console.error('❌ Error creating test products:', error);
  }
}

createTestProducts();
