const { createClient } = require('@supabase/supabase-js');

async function fixDatabaseTrigger() {
  const supabaseUrl = 'https://dmdijuuwnbwngerkbfak.supabase.co';
  const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8';

  const supabase = createClient(supabaseUrl, serviceRoleKey);

  console.log('🔧 Fixing database trigger...');

  try {
    // First, let's check what triggers exist
    console.log('Checking existing triggers...');
    const { data: existingTriggers, error: triggerError } = await supabase
      .from('information_schema.triggers')
      .select('*')
      .eq('event_object_table', 'users');
    
    if (triggerError) {
      console.log('Could not check triggers:', triggerError);
    } else {
      console.log('Existing triggers:', existingTriggers);
    }

    // Drop the problematic trigger
    console.log('Dropping existing trigger...');
    const dropTriggerSQL = `
      DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
      DROP FUNCTION IF EXISTS public.handle_new_user();
    `;

    // Use direct fetch to Supabase REST API for SQL execution
    const dropResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceRoleKey}`,
        'apikey': serviceRoleKey
      },
      body: JSON.stringify({
        sql_query: dropTriggerSQL
      })
    });

    if (!dropResponse.ok) {
      const errorText = await dropResponse.text();
      console.log('Drop trigger response:', errorText);
    } else {
      console.log('✅ Trigger dropped successfully');
    }

    // Now test registration without any trigger
    console.log('✅ Database trigger removed - registration should work now');
    console.log('You can now test registration in the browser');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixDatabaseTrigger();
