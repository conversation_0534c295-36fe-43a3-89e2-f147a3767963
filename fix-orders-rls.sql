-- Fix RLS policies and database structure for orders
-- This script ensures proper table structure and permissive policies

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables to start fresh (optional - comment out if you want to preserve data)
-- DROP TABLE IF EXISTS order_items CASCADE;
-- DROP TABLE IF EXISTS orders CASCADE;

-- Create orders table with proper structure
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_name TEXT NOT NULL,
  customer_email TEXT,
  customer_phone TEXT NOT NULL,
  shipping_address JSONB,
  total_amount DECIMAL(10, 2) NOT NULL,
  status TEXT DEFAULT 'pending',
  payment_method TEXT,
  payment_status TEXT DEFAULT 'pending',
  notes TEXT,
  user_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create order_items table with proper structure
CREATE TABLE IF NOT EXISTS order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL,
  product_id UUID,
  quantity INTEGER NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  product_name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- Create order_status_history table for tracking
CREATE TABLE IF NOT EXISTS order_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL,
  status TEXT NOT NULL,
  note TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  created_by TEXT,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- Enable RLS on all tables
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS orders_all_policy ON orders;
DROP POLICY IF EXISTS orders_insert_policy ON orders;
DROP POLICY IF EXISTS orders_select_policy ON orders;
DROP POLICY IF EXISTS orders_update_policy ON orders;

DROP POLICY IF EXISTS order_items_all_policy ON order_items;
DROP POLICY IF EXISTS order_items_insert_policy ON order_items;
DROP POLICY IF EXISTS order_items_select_policy ON order_items;

DROP POLICY IF EXISTS order_status_history_all_policy ON order_status_history;
DROP POLICY IF EXISTS order_status_history_insert_policy ON order_status_history;
DROP POLICY IF EXISTS order_status_history_select_policy ON order_status_history;

-- Create permissive policies for orders table
CREATE POLICY orders_insert_policy ON orders
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY orders_select_policy ON orders
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY orders_update_policy ON orders
  FOR UPDATE
  TO public
  USING (true)
  WITH CHECK (true);

CREATE POLICY orders_delete_policy ON orders
  FOR DELETE
  TO public
  USING (true);

-- Create permissive policies for order_items table
CREATE POLICY order_items_insert_policy ON order_items
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY order_items_select_policy ON order_items
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY order_items_update_policy ON order_items
  FOR UPDATE
  TO public
  USING (true)
  WITH CHECK (true);

CREATE POLICY order_items_delete_policy ON order_items
  FOR DELETE
  TO public
  USING (true);

-- Create permissive policies for order_status_history table
CREATE POLICY order_status_history_insert_policy ON order_status_history
  FOR INSERT
  TO public
  WITH CHECK (true);

CREATE POLICY order_status_history_select_policy ON order_status_history
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY order_status_history_update_policy ON order_status_history
  FOR UPDATE
  TO public
  USING (true)
  WITH CHECK (true);

CREATE POLICY order_status_history_delete_policy ON order_status_history
  FOR DELETE
  TO public
  USING (true);

-- Grant permissions to all roles
GRANT ALL ON orders TO anon, authenticated, service_role;
GRANT ALL ON order_items TO anon, authenticated, service_role;
GRANT ALL ON order_status_history TO anon, authenticated, service_role;

-- Grant usage on sequences (for id generation)
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated, service_role;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON orders(customer_email);

CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id);
CREATE INDEX IF NOT EXISTS idx_order_status_history_created_at ON order_status_history(created_at DESC);

-- Force schema refresh
NOTIFY pgrst, 'reload schema';

-- Insert a test order to verify everything works (will be cleaned up)
DO $$
BEGIN
  -- Test insert
  INSERT INTO orders (
    customer_name, 
    customer_email, 
    customer_phone, 
    shipping_address, 
    total_amount, 
    status, 
    payment_method, 
    payment_status,
    notes
  ) VALUES (
    'Test Customer',
    '<EMAIL>',
    '+1234567890',
    '{"city": "Test City", "address": "Test Address"}',
    99.99,
    'test',
    'test',
    'test',
    'Test order for verification'
  );
  
  -- Clean up test data
  DELETE FROM orders WHERE customer_name = 'Test Customer' AND status = 'test';
  
  RAISE NOTICE 'Orders table test successful - schema is working correctly';
EXCEPTION
  WHEN others THEN
    RAISE NOTICE 'Orders table test failed: %', SQLERRM;
END $$;
