import { createClient } from '@supabase/supabase-js';

// Используем переменные окружения
const SUPABASE_URL = process.env.REACT_APP_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.REACT_APP_SUPABASE_ANON_KEY;

// Логирование в режиме разработки для отладки
if (process.env.NODE_ENV === 'development') {
  console.log('Initializing Supabase client with URL:', SUPABASE_URL);
  console.log('API Key format check:', SUPABASE_ANON_KEY?.slice(0, 10) + '...');
}

// Проверка конфигурации
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error(
    'Missing Supabase configuration! Check your environment variables.'
  );
}

// Создаем и экспортируем клиент Supabase
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  headers: {
    'Content-Type': 'application/json',
    Prefer: 'return=minimal'
  },
  persistSession: true,
  autoRefreshToken: true
});

// Экспортируем по умолчанию для совместимости
export default supabase;

// Re-export из корневого файла для обратной совместимости
export { supabase, default } from '../supabaseClient';