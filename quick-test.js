const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://dmdijuuwnbwngerkbfak.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU'
);

async function quickTest() {
  console.log('🔍 Quick database test...');
  
  try {
    // Test exec_sql function
    console.log('Testing exec_sql...');
    const { error } = await supabase.rpc('exec_sql', { query_text: 'SELECT 1' });
    if (error) {
      console.log('exec_sql error:', error.code, error.message);
    } else {
      console.log('✅ exec_sql works');
    }
    
    // Test foreign keys
    console.log('Testing foreign keys...');
    const { data: fkData, error: fkError } = await supabase.rpc('get_foreign_keys', { table_name_param: 'orders' });
    if (fkError) {
      console.log('get_foreign_keys error:', fkError.message);
    } else {
      console.log('✅ get_foreign_keys works, found:', fkData?.length || 0, 'constraints');
    }
    
    // Test tables
    console.log('Testing orders table...');
    const { error: ordersError } = await supabase.from('orders').select('id').limit(1);
    if (ordersError) {
      console.log('orders error:', ordersError.message);
    } else {
      console.log('✅ orders table accessible');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
  
  console.log('✨ Quick test completed');
}

quickTest();
