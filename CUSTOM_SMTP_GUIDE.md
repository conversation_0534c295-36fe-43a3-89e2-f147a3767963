# Supabase Custom SMTP Setup with Resend

## Overview
This guide shows how to configure Supabase to use <PERSON>send as your Custom SMTP provider for authentication emails (password reset, email confirmation, etc.) while keeping your Edge Function email system for order notifications.

## Why Use Custom SMTP?
- **Branded emails**: Send from your own domain (`<EMAIL>`)
- **Better delivery**: Higher success rates than default Supabase SMTP
- **Unified provider**: Use Resend for both auth emails and order notifications
- **Professional appearance**: Consistent branding across all emails

## Prerequisites
1. ✅ Resend account with API key
2. ✅ Domain verification in Resend (for production)
3. ✅ Supabase project access

## Step-by-Step Setup

### 1. Verify Your Domain in Resend

#### In Resend Dashboard:
```
1. Go to: https://resend.com/domains
2. Click "Add Domain"
3. Enter your domain: roomchic.shop
4. Copy the DNS records provided
```

#### DNS Records to Add:
```dns
# Example records (yours will be different)
Type: TXT
Name: @
Value: v=spf1 include:_spf.resend.com ~all

Type: TXT  
Name: resend._domainkey
Value: p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKB...

Type: CNAME
Name: resend
Value: resend.com
```

### 2. Configure Supabase Custom SMTP

#### In Supabase Dashboard:
```
1. Go to: Project Settings → Authentication → SMTP Settings
2. Enable "Enable custom SMTP"
3. Fill in the following settings:
```

#### SMTP Configuration:
```
Host: smtp.resend.com
Port: 587
Username: resend
Password: [Your Resend API Key - starts with re_]
Sender name: Room Chic Shop
Sender email: <EMAIL>
```

### 3. Email Templates Configuration

#### Authentication Email Templates:
```
In Supabase Dashboard → Authentication → Email Templates

Confirm signup:
Subject: Welcome to Room Chic Shop - Confirm your email
Body: Custom HTML template with your branding

Reset password:  
Subject: Reset your Room Chic Shop password
Body: Custom HTML template with reset link

Magic link:
Subject: Your Room Chic Shop login link
Body: Custom HTML template with magic link
```

### 4. Test the Configuration

#### Test Authentication Emails:
```javascript
// Test signup confirmation
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'testpassword123'
})

// Test password reset
const { data, error } = await supabase.auth.resetPasswordForEmail(
  '<EMAIL>',
  { redirectTo: 'https://roomchic.shop/reset-password' }
)
```

## Email System Architecture

### After Custom SMTP Setup:
```
Authentication Emails (Supabase Auth):
├── Provider: Resend Custom SMTP
├── Sender: <EMAIL>
├── Types: Signup, Password Reset, Magic Links
└── Templates: Supabase Auth templates

Order Notification Emails (Edge Functions):
├── Provider: Resend API (Direct)
├── Sender: <EMAIL>  
├── Types: Order confirmation, Status updates
└── Templates: Custom HTML in Edge Function
```

## Production Checklist

### Before Going Live:
- [ ] Domain verified in Resend
- [ ] DNS records propagated (24-48 hours)
- [ ] SMTP settings tested in Supabase
- [ ] Authentication email templates updated
- [ ] Test emails sent successfully
- [ ] Edge Function using verified domain sender

### Environment Variables Update:
```bash
# In Supabase Edge Function secrets
RESEND_API_KEY=re_your_api_key_here

# Update sender email in Edge Function
# Change from: <EMAIL>
# Change to: <EMAIL>
```

## Common Issues & Solutions

### Issue: Domain Not Verified
```
Error: Domain not verified
Solution: 
1. Check DNS records are correctly added
2. Wait 24-48 hours for propagation
3. Verify in Resend dashboard shows "Verified"
```

### Issue: Authentication Failed
```
Error: SMTP authentication failed
Solution:
1. Double-check API key (starts with re_)
2. Ensure API key has sending permissions
3. Verify username is exactly "resend"
```

### Issue: Emails Going to Spam
```
Solution:
1. Complete domain verification
2. Add SPF, DKIM, DMARC records
3. Use consistent sender addresses
4. Avoid spam-trigger words
```

## Testing Commands

### Test Custom SMTP:
```bash
# Test in Supabase SQL Editor
SELECT auth.email();

# Test with API
curl -X POST 'https://your-project.supabase.co/auth/v1/recover' \
  -H 'apikey: your-anon-key' \
  -H 'Content-Type: application/json' \
  -d '{"email": "<EMAIL>"}'
```

### Monitor Email Logs:
```sql
-- Check Supabase auth logs
SELECT * FROM auth.audit_log_entries 
WHERE type = 'email' 
ORDER BY created_at DESC;

-- Check your custom email logs
SELECT * FROM email_logs 
ORDER BY created_at DESC;
```

## Benefits After Setup

### For Users:
- Professional emails from your domain
- Consistent branding experience
- Better email deliverability
- Reduced spam likelihood

### For You:
- Unified email provider (Resend)
- Better delivery statistics
- Professional brand image
- Easier email management

## Cost Considerations

### Resend Pricing:
- Free tier: 3,000 emails/month
- Pro: $20/month for 50,000 emails
- Scale: $85/month for 250,000 emails

### Recommendation:
Start with free tier, upgrade as your customer base grows.

## Support & Documentation

- **Resend Docs**: https://resend.com/docs
- **Supabase SMTP**: https://supabase.com/docs/guides/auth/auth-smtp
- **DNS Help**: Your domain registrar's support

---

**Next Step**: Verify your domain in Resend, then configure Supabase Custom SMTP with the settings above.
