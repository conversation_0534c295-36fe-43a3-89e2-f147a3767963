const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://fijuwtwzugtmbggdmxjb.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE'
);

(async () => {
  try {
    console.log('Проверка подключения к Supabase...');
    
    const { data, error } = await supabase
      .from('products')
      .select('moderation_status, is_active')
      .limit(1);
    
    if (error) {
      console.error('Ошибка подключения:', error);
      return;
    }
    
    console.log('✅ Подключение к базе данных работает');
    console.log('Пример данных:', data[0]);
    
    // Проверяем количество продуктов по статусам
    const { data: allProducts } = await supabase
      .from('products')
      .select('moderation_status, is_active');
    
    const stats = allProducts.reduce((acc, product) => {
      const key = `${product.moderation_status || 'null'}_${product.is_active}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\nСтатистика продуктов по статусам:');
    Object.entries(stats).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });
    
  } catch (error) {
    console.error('Общая ошибка:', error);
  }
})();
