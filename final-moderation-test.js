#!/usr/bin/env node

// Простой тест функциональности модерации
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://fijuwtwzugtmbggdmxjb.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE';

async function testModerationWorkflow() {
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  console.log('🔄 Начинаем тестирование системы модерации...\n');
  
  try {
    // 1. Получаем статистику до тестирования
    console.log('📊 Статистика ДО тестирования:');
    await printStats(supabase);
    
    // 2. Берем первый тестовый продукт для одобрения
    const { data: pendingProducts } = await supabase
      .from('products')
      .select('*')
      .eq('moderation_status', 'pending_approval')
      .eq('external_id', 'test_mod_2')
      .limit(1);
    
    if (!pendingProducts || pendingProducts.length === 0) {
      console.log('❌ Не найден тестовый продукт для одобрения');
      return;
    }
    
    const productToApprove = pendingProducts[0];
    console.log(`\n✅ ТЕСТ ОДОБРЕНИЯ: ${productToApprove.name}`);
    console.log(`   ID: ${productToApprove.id}`);
    
    // 3. Одобряем продукт
    const { error: approveError } = await supabase
      .from('products')
      .update({
        moderation_status: 'approved',
        is_active: true
      })
      .eq('id', productToApprove.id);
    
    if (approveError) {
      console.log('❌ Ошибка при одобрении:', approveError.message);
    } else {
      console.log('✅ Продукт успешно одобрен!');
    }
    
    // 4. Берем второй тестовый продукт для отклонения
    const { data: pendingProducts2 } = await supabase
      .from('products')
      .select('*')
      .eq('moderation_status', 'pending_approval')
      .eq('external_id', 'test_mod_3')
      .limit(1);
    
    if (pendingProducts2 && pendingProducts2.length > 0) {
      const productToReject = pendingProducts2[0];
      console.log(`\n❌ ТЕСТ ОТКЛОНЕНИЯ: ${productToReject.name}`);
      console.log(`   ID: ${productToReject.id}`);
      
      // 5. Отклоняем продукт
      const { error: rejectError } = await supabase
        .from('products')
        .update({
          moderation_status: 'rejected',
          is_active: false
        })
        .eq('id', productToReject.id);
      
      if (rejectError) {
        console.log('❌ Ошибка при отклонении:', rejectError.message);
      } else {
        console.log('✅ Продукт успешно отклонен!');
      }
    }
    
    // 6. Показываем статистику после тестирования
    console.log('\n📊 Статистика ПОСЛЕ тестирования:');
    await printStats(supabase);
    
    console.log('\n🎉 Тестирование завершено успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка при тестировании:', error.message);
  }
}

async function printStats(supabase) {
  const { data: stats } = await supabase
    .from('products')
    .select('moderation_status, is_active');
  
  const counts = {
    pending: 0,
    approved_active: 0,
    approved_inactive: 0,
    rejected: 0,
    total: 0
  };
  
  stats.forEach(product => {
    counts.total++;
    if (product.moderation_status === 'pending_approval') {
      counts.pending++;
    } else if (product.moderation_status === 'approved' && product.is_active) {
      counts.approved_active++;
    } else if (product.moderation_status === 'approved' && !product.is_active) {
      counts.approved_inactive++;
    } else if (product.moderation_status === 'rejected') {
      counts.rejected++;
    }
  });
  
  console.log(`   📋 Ожидают модерации: ${counts.pending}`);
  console.log(`   ✅ Одобрены и активны: ${counts.approved_active}`);
  console.log(`   ⚠️  Одобрены но неактивны: ${counts.approved_inactive}`);
  console.log(`   ❌ Отклонены: ${counts.rejected}`);
  console.log(`   📦 Всего продуктов: ${counts.total}`);
}

// Запускаем тест
testModerationWorkflow();
