# 🔑 GitHub Secrets Quick Reference

## Copy these values exactly into GitHub Secrets:

### Repository: https://github.com/rackovchen/roomchik
### Path: Settings → Secrets and variables → Actions

---

**Secret 1:**
```
Name: REACT_APP_SUPABASE_URL
Value: https://dmdijuuwnbwngerkbfak.supabase.co
```

**Secret 2:**
```
Name: REACT_APP_SUPABASE_ANON_KEY
Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU
```

**Secret 3:**
```
Name: SUPABASE_SERVICE_ROLE_KEY
Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8
```

---

## After adding secrets:

✅ **Test**: Go to Actions → Automatic Feed Processing → Run workflow  
✅ **Monitor**: Check Actions tab for green checkmarks every 6 hours  
✅ **Verify**: Admin panel shows updated feed times  

**Schedule**: 00:00, 06:00, 12:00, 18:00 UTC (every 6 hours)
