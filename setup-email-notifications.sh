#!/bin/bash

# Quick setup script for Supabase Email Notifications
# This script helps setup the email notification system

echo "🚀 Supabase Email Notifications Setup"
echo "====================================="

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed"
    echo "📦 Installing Supabase CLI..."
    npm install -g supabase
fi

echo "✅ Supabase CLI found"

# Check if user is logged in
if ! supabase projects list &> /dev/null; then
    echo "🔐 Please login to Supabase first:"
    supabase login
fi

echo "✅ Supabase authentication confirmed"

# Get project reference
echo "🔗 Enter your Supabase project reference ID:"
read -p "Project ID: " PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Project ID is required"
    exit 1
fi

# Link to project
echo "🔗 Linking to project..."
supabase link --project-ref $PROJECT_ID

# Create migrations directory if it doesn't exist
mkdir -p supabase/migrations

# Apply database migrations
echo "📊 Applying database migrations..."

echo "📋 Creating email_logs table..."
supabase db push --file supabase/migrations/001_create_email_logs.sql

echo "⚡ Creating email triggers and functions..."
supabase db push --file supabase/migrations/002_create_email_triggers.sql

# Deploy Edge Function
echo "🚀 Deploying send-email Edge Function..."
supabase functions deploy send-email

# Setup environment variables
echo ""
echo "🔧 Environment Variables Setup"
echo "=============================="
echo ""
echo "Please set up the following environment variables in your Supabase dashboard:"
echo "Settings → Edge Functions → Environment Variables"
echo ""
echo "Required variables:"
echo "- RESEND_API_KEY: Your Resend API key"
echo "- FROM_EMAIL: Email address to send from (optional)"
echo ""

# Get Resend API key
echo "🔑 Do you have a Resend API key? (y/n)"
read -p "Answer: " HAS_RESEND

if [ "$HAS_RESEND" = "n" ] || [ "$HAS_RESEND" = "N" ]; then
    echo ""
    echo "📧 To get a Resend API key:"
    echo "1. Go to https://resend.com"
    echo "2. Sign up for an account"
    echo "3. Create an API key"
    echo "4. Add it to your Supabase environment variables"
    echo ""
fi

# Test the setup
echo "🧪 Testing the setup..."

# Check if tables exist
echo "📋 Checking if email_logs table exists..."
if supabase db diff --schema public --table email_logs &> /dev/null; then
    echo "✅ email_logs table created"
else
    echo "⚠️  email_logs table might not be created properly"
fi

# Check if functions exist
echo "⚡ Checking Edge Functions..."
if supabase functions list | grep -q "send-email"; then
    echo "✅ send-email function deployed"
else
    echo "⚠️  send-email function might not be deployed properly"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Set environment variables in Supabase dashboard"
echo "2. Test email sending in your application"
echo "3. Monitor email logs in the admin panel"
echo ""
echo "📖 Read EMAIL_SETUP_SUPABASE.md for detailed instructions"
echo ""

# Optional: Run a quick test
echo "🧪 Would you like to run a test? (y/n)"
read -p "Answer: " RUN_TEST

if [ "$RUN_TEST" = "y" ] || [ "$RUN_TEST" = "Y" ]; then
    echo "🧪 Test will be available in the admin panel once you set up the API key"
    echo "Go to: Admin → Settings → Email Notifications → Test Email"
fi

echo "✅ All done! Happy emailing! 📧"
