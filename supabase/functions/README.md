# Supabase Edge Functions configuration

# This directory contains the Edge Functions for the e-commerce platform
# Functions are deployed to Supabase and handle server-side operations

## send-email function
- <PERSON>les sending email notifications for orders
- Supports order confirmation emails and status update emails
- Uses Resend API for email delivery
- Logs all email activities to the database

### Required environment variables:
- RESEND_API_KEY: API key for Resend email service
- FROM_EMAIL: Email address to send emails from (optional, <NAME_EMAIL>)
- SUPABASE_URL: Your Supabase project URL
- SUPABASE_SERVICE_ROLE_KEY: Service role key for database access

### Usage:
The function is called automatically via database triggers when:
1. A new order is created (sends order confirmation)
2. An order status is updated (sends status update notification)

Manual calls can also be made through the EmailService class in the frontend.
