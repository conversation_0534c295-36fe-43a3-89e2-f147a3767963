import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Email templates
const getOrderConfirmationTemplate = (order: any) => {
  return {
    subject: `Подтверждение заказа №${order.id.substring(0, 8)}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Подтверждение заказа</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { padding: 20px; }
          .order-details { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
          .total { font-weight: bold; font-size: 18px; color: #2563eb; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Спасибо за ваш заказ!</h1>
          </div>
          
          <div class="content">
            <p>Здравствуйте, <strong>${order.customer_name}</strong>!</p>
            
            <p>Ваш заказ успешно оформлен и принят в обработку.</p>
            
            <div class="order-details">
              <h3>Детали заказа</h3>
              <p><strong>Номер заказа:</strong> #${order.id.substring(0, 8)}</p>
              <p><strong>Дата заказа:</strong> ${new Date(order.created_at).toLocaleDateString('ru-RU')}</p>
              <p><strong>Статус:</strong> В обработке</p>
              ${order.customer_phone ? `<p><strong>Телефон:</strong> ${order.customer_phone}</p>` : ''}
              ${order.shipping_address?.city ? `<p><strong>Город доставки:</strong> ${order.shipping_address.city}</p>` : ''}
              ${order.shipping_address?.nova_poshta_office ? `<p><strong>Отделение Новой Почты:</strong> ${order.shipping_address.nova_poshta_office}</p>` : ''}
            </div>
            
            <div class="order-details">
              <h3>Товары в заказе</h3>
              ${order.order_items?.map((item: any) => `
                <div class="item">
                  <span>${item.product_name} × ${item.quantity}</span>
                  <span>${(item.price * item.quantity).toFixed(2)} грн</span>
                </div>
              `).join('') || '<p>Информация о товарах загружается...</p>'}
              
              <div class="item total">
                <span>Итого:</span>
                <span>${order.total_amount?.toFixed(2) || '0.00'} грн</span>
              </div>
            </div>
            
            <div class="order-details">
              <h3>Оплата и доставка</h3>
              <p><strong>Способ оплаты:</strong> ${order.payment_method === 'cash_on_delivery' ? 'Наложенный платёж' : order.payment_method || 'Не указан'}</p>
              <p><strong>Статус оплаты:</strong> ${order.payment_status === 'pending' ? 'Ожидает оплаты' : order.payment_status || 'Не указан'}</p>
            </div>
            
            ${order.notes ? `
              <div class="order-details">
                <h3>Примечания к заказу</h3>
                <p>${order.notes}</p>
              </div>
            ` : ''}
            
            <p>Мы свяжемся с вами в ближайшее время для подтверждения деталей доставки.</p>
            
            <p>С уважением,<br>Команда интернет-магазина</p>
          </div>
          
          <div class="footer">
            <p>Если у вас есть вопросы, свяжитесь с нами по телефону или email.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Подтверждение заказа №${order.id.substring(0, 8)}
      
      Здравствуйте, ${order.customer_name}!
      
      Ваш заказ успешно оформлен и принят в обработку.
      
      Номер заказа: #${order.id.substring(0, 8)}
      Дата заказа: ${new Date(order.created_at).toLocaleDateString('ru-RU')}
      Статус: В обработке
      Итого: ${order.total_amount?.toFixed(2) || '0.00'} грн
      
      Мы свяжемся с вами в ближайшее время для подтверждения деталей доставки.
      
      С уважением,
      Команда интернет-магазина
    `
  }
}

const getStatusUpdateTemplate = (order: any, oldStatus: string) => {
  const statusLabels: Record<string, string> = {
    pending: 'В ожидании',
    processing: 'В обработке',
    packed: 'Упакован',
    shipped: 'Отправлен',
    delivered: 'Доставлен',
    cancelled: 'Отменен',
    refunded: 'Возвращен'
  }

  const statusIcons: Record<string, string> = {
    pending: '⏳',
    processing: '🔄',
    packed: '📦',
    shipped: '🚚',
    delivered: '✅',
    cancelled: '❌',
    refunded: '💰'
  }

  const currentStatusLabel = statusLabels[order.status] || order.status
  const currentStatusIcon = statusIcons[order.status] || '📋'

  return {
    subject: `${currentStatusIcon} Статус заказа №${order.id.substring(0, 8)} изменен`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Обновление статуса заказа</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 0; background-color: #f4f4f4; }
          .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { padding: 20px; }
          .status-update { background: #ecfdf5; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; border-left: 4px solid #059669; }
          .order-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
          .status-icon { font-size: 48px; margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Статус заказа обновлен</h1>
          </div>
          
          <div class="content">
            <p>Здравствуйте, <strong>${order.customer_name}</strong>!</p>
            
            <div class="status-update">
              <div class="status-icon">${currentStatusIcon}</div>
              <h2>Статус вашего заказа изменен</h2>
              <p><strong>Новый статус:</strong> ${currentStatusLabel}</p>
            </div>
            
            <div class="order-info">
              <h3>Информация о заказе</h3>
              <p><strong>Номер заказа:</strong> #${order.id.substring(0, 8)}</p>
              <p><strong>Дата заказа:</strong> ${new Date(order.created_at).toLocaleDateString('ru-RU')}</p>
              <p><strong>Сумма заказа:</strong> ${order.total_amount?.toFixed(2) || '0.00'} грн</p>
              <p><strong>Обновлено:</strong> ${new Date().toLocaleDateString('ru-RU')} в ${new Date().toLocaleTimeString('ru-RU')}</p>
            </div>
            
            ${order.status === 'shipped' ? `
              <div class="order-info">
                <h3>🚚 Информация о доставке</h3>
                <p>Ваш заказ передан в службу доставки!</p>
                ${order.shipping_address?.city ? `<p><strong>Город доставки:</strong> ${order.shipping_address.city}</p>` : ''}
                ${order.shipping_address?.nova_poshta_office ? `<p><strong>Отделение Новой Почты:</strong> ${order.shipping_address.nova_poshta_office}</p>` : ''}
                <p>Ожидайте SMS с трек-номером для отслеживания посылки.</p>
              </div>
            ` : ''}
            
            ${order.status === 'delivered' ? `
              <div class="order-info">
                <h3>🎉 Заказ доставлен!</h3>
                <p>Ваш заказ успешно доставлен. Спасибо за покупку!</p>
                <p>Будем рады видеть вас снова в нашем магазине.</p>
              </div>
            ` : ''}
            
            ${order.status === 'cancelled' ? `
              <div class="order-info">
                <h3>❌ Заказ отменен</h3>
                <p>К сожалению, ваш заказ был отменен.</p>
                <p>Если у вас есть вопросы, пожалуйста, свяжитесь с нами.</p>
              </div>
            ` : ''}
            
            <p>Если у вас есть вопросы о вашем заказе, пожалуйста, свяжитесь с нами.</p>
            
            <p>С уважением,<br>Команда интернет-магазина</p>
          </div>
          
          <div class="footer">
            <p>Это автоматическое уведомление. Пожалуйста, не отвечайте на это письмо.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Статус заказа обновлен
      
      Здравствуйте, ${order.customer_name}!
      
      Статус вашего заказа №${order.id.substring(0, 8)} изменен на: ${currentStatusLabel}
      
      Номер заказа: #${order.id.substring(0, 8)}
      Дата заказа: ${new Date(order.created_at).toLocaleDateString('ru-RU')}
      Сумма заказа: ${order.total_amount?.toFixed(2) || '0.00'} грн
      Обновлено: ${new Date().toLocaleDateString('ru-RU')} в ${new Date().toLocaleTimeString('ru-RU')}
      
      С уважением,
      Команда интернет-магазина
    `
  }
}

// Send email using Resend API
async function sendEmail(to: string, subject: string, html: string, text: string) {
  const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
  
  if (!RESEND_API_KEY) {
    console.error('RESEND_API_KEY not found in environment variables')
    throw new Error('Email service not configured')
  }

  const emailData = {
    from: Deno.env.get('FROM_EMAIL') || 'Интернет-магазин <<EMAIL>>',
    to: [to],
    subject,
    html,
    text
  }

  console.log('Sending email to:', to)
  console.log('Subject:', subject)

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(emailData),
  })

  if (!response.ok) {
    const errorData = await response.text()
    console.error('Email sending failed:', errorData)
    throw new Error(`Failed to send email: ${response.status} ${errorData}`)
  }

  const result = await response.json()
  console.log('Email sent successfully:', result)
  return result
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { type, orderId, orderData, oldStatus } = await req.json()

    if (!type) {
      throw new Error('Email type is required')
    }

    if (!orderId && !orderData) {
      throw new Error('Order ID or order data is required')
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    let order = orderData

    // If only orderId provided, fetch order data
    if (!order && orderId) {
      const { data: orderResult, error: orderError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (
            id,
            product_name,
            quantity,
            price
          )
        `)
        .eq('id', orderId)
        .single()

      if (orderError) {
        throw new Error(`Failed to fetch order: ${orderError.message}`)
      }

      order = orderResult
    }

    if (!order?.customer_email) {
      throw new Error('Customer email not found')
    }

    let emailTemplate
    
    switch (type) {
      case 'order_confirmation':
        emailTemplate = getOrderConfirmationTemplate(order)
        break
      case 'status_update':
        emailTemplate = getStatusUpdateTemplate(order, oldStatus)
        break
      default:
        throw new Error(`Unknown email type: ${type}`)
    }

    // Send email
    const result = await sendEmail(
      order.customer_email,
      emailTemplate.subject,
      emailTemplate.html,
      emailTemplate.text
    )

    // Log email sending to database
    try {
      await supabase
        .from('email_logs')
        .insert({
          order_id: order.id,
          recipient: order.customer_email,
          email_type: type,
          subject: emailTemplate.subject,
          status: 'sent',
          sent_at: new Date().toISOString(),
          external_id: result.id || null
        })
    } catch (logError) {
      console.error('Failed to log email sending:', logError)
      // Don't fail the whole operation if logging fails
    }

    return new Response(
      JSON.stringify({ success: true, emailId: result.id }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error in send-email function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})
