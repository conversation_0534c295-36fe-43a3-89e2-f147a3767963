// Supabase Edge Function для обработки фидов
// Файл: supabase/functions/process-feed/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface FeedProcessRequest {
  feedId: string;
}

interface Product {
  external_id: string;
  name: string;
  description: string;
  price: number;
  old_price?: number;
  image: string;
  is_available: boolean;
  brand?: string;
  attributes?: any;
  language: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Создаем Supabase клиент с service role ключом
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    const { feedId }: FeedProcessRequest = await req.json()

    if (!feedId) {
      throw new Error('Feed ID is required')
    }

    // Получаем информацию о фиде
    const { data: feed, error: feedError } = await supabase
      .from('feeds')
      .select('*')
      .eq('id', feedId)
      .single()

    if (feedError || !feed) {
      throw new Error(`Feed not found: ${feedError?.message}`)
    }

    console.log(`Processing feed: ${feed.name} (${feed.language})`)

    // Создаем запись о задаче
    const { data: jobData, error: jobError } = await supabase
      .from('feed_jobs')
      .insert([{
        feed_id: feedId,
        status: 'pending'
      }])
      .select()

    if (jobError) {
      throw new Error(`Failed to create job: ${jobError.message}`)
    }

    const jobId = jobData[0].id

    try {
      // Обновляем статус на processing
      await supabase
        .from('feed_jobs')
        .update({
          status: 'processing',
          started_at: new Date().toISOString()
        })
        .eq('id', jobId)

      // Загружаем и парсим XML фид
      console.log(`Fetching XML from: ${feed.url}`)
      const xmlResponse = await fetch(feed.url, {
        headers: {
          'Accept': 'application/xml, text/xml',
          'User-Agent': 'OnlineStore-FeedProcessor/1.0'
        }
      })

      if (!xmlResponse.ok) {
        throw new Error(`Failed to fetch feed: ${xmlResponse.status} ${xmlResponse.statusText}`)
      }

      const xmlText = await xmlResponse.text()
      console.log(`XML downloaded, size: ${xmlText.length} characters`)

      // Парсим XML используя DOMParser
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(xmlText, 'text/xml')

      // Извлекаем продукты
      const products = await extractProductsFromXml(xmlDoc, feed.language)
      console.log(`Extracted ${products.length} products`)

      // Сохраняем продукты в базу данных
      const stats = await saveProductsToDatabase(supabase, products, feedId, jobId)

      // Обновляем фид с датой последнего обновления
      await supabase
        .from('feeds')
        .update({ last_fetched: new Date().toISOString() })
        .eq('id', feedId)

      // Завершаем задачу
      await supabase
        .from('feed_jobs')
        .update({
          status: 'completed',
          finished_at: new Date().toISOString(),
          items_created: stats.created,
          items_updated: stats.updated,
          items_failed: stats.failed,
          items_processed: stats.processed
        })
        .eq('id', jobId)

      return new Response(
        JSON.stringify({
          success: true,
          feedId,
          jobId,
          stats
        }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )

    } catch (processError) {
      // Обновляем статус задачи на failed
      await supabase
        .from('feed_jobs')
        .update({
          status: 'failed',
          finished_at: new Date().toISOString(),
          error_message: processError.message
        })
        .eq('id', jobId)

      throw processError
    }

  } catch (error) {
    console.error('Error processing feed:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

// Функция для извлечения продуктов из XML
async function extractProductsFromXml(xmlDoc: Document, language: string = 'ru'): Promise<Product[]> {
  const products: Product[] = []
  let isOfferFormat = false

  // Ищем узлы товаров
  let productNodes = Array.from(xmlDoc.getElementsByTagName('*')).filter(
    n => n.localName === 'item'
  )

  if (!productNodes.length) {
    const fallbackItems = Array.from(xmlDoc.getElementsByTagName('item'))
    if (fallbackItems.length) {
      productNodes = fallbackItems
    }
  }

  // Пробуем формат offer (YML)
  if (!productNodes.length) {
    const offers = Array.from(xmlDoc.getElementsByTagName('offer'))
    if (offers.length) {
      productNodes = offers
      isOfferFormat = true
    }
  }

  for (const item of productNodes) {
    try {
      const getTag = (tagName: string): string => {
        const nodes = Array.from(item.getElementsByTagName(tagName))
        return nodes[0]?.textContent || ''
      }

      const externalId = isOfferFormat ? item.getAttribute('id') : getTag('id')
      const rawTitle = isOfferFormat ? getTag('name') : getTag('title')
      const rawImage = isOfferFormat ? getTag('picture') : getTag('image_link')
      const availability = isOfferFormat
        ? item.getAttribute('available') === 'true'
        : getTag('availability') === 'in stock'

      if (!externalId || !rawTitle) {
        continue
      }

      const priceText = getTag('price').replace(/[^0-9.]/g, '')
      const price = parseFloat(priceText) || 0

      const product: Product = {
        external_id: externalId,
        name: rawTitle,
        description: getTag('description'),
        price: price,
        image: rawImage,
        is_available: availability,
        brand: isOfferFormat ? getTag('vendor') : getTag('brand'),
        language: language,
        attributes: {}
      }

      products.push(product)
    } catch (err) {
      console.error('Error processing product node:', err)
    }
  }

  return products
}

// Функция для сохранения продуктов в базу данных
async function saveProductsToDatabase(
  supabase: any,
  products: Product[],
  feedId: string,
  jobId: string
) {
  const stats = {
    processed: products.length,
    created: 0,
    updated: 0,
    failed: 0
  }

  // Обрабатываем продукты батчами
  const batchSize = 50
  const batches = Math.ceil(products.length / batchSize)

  for (let i = 0; i < batches; i++) {
    const start = i * batchSize
    const end = Math.min(start + batchSize, products.length)
    const batch = products.slice(start, end)

    console.log(`Processing batch ${i + 1}/${batches} (products ${start + 1}-${end})`)

    for (const product of batch) {
      try {
        // Проверяем существует ли продукт
        const { data: existingProduct } = await supabase
          .from('products')
          .select('id')
          .eq('external_id', product.external_id)
          .single()

        if (existingProduct) {
          // Обновляем существующий продукт
          await supabase
            .from('products')
            .update({
              name: product.name,
              description: product.description,
              price: product.price,
              image: product.image,
              is_available: product.is_available,
              brand: product.brand,
              attributes: product.attributes,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingProduct.id)

          stats.updated++
        } else {
          // Создаем новый продукт
          const { error } = await supabase
            .from('products')
            .insert([{
              external_id: product.external_id,
              name: product.name,
              description: product.description,
              price: product.price,
              image: product.image,
              is_available: product.is_available,
              brand: product.brand,
              attributes: product.attributes,
              language: product.language
            }])

          if (error) throw error
          stats.created++
        }
      } catch (error) {
        console.error(`Error processing product ${product.external_id}:`, error)
        stats.failed++
      }
    }
  }

  return stats
}
