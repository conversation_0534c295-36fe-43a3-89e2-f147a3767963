-- Create email logs table for tracking sent emails
CREATE TABLE IF NOT EXISTS email_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  recipient TEXT NOT NULL,
  email_type TEXT NOT NULL, -- 'order_confirmation', 'status_update'
  subject TEXT NOT NULL,
  status TEXT DEFAULT 'pending', -- 'pending', 'sent', 'failed'
  sent_at TIMESTAMPTZ,
  error_message TEXT,
  external_id TEXT, -- ID from email service provider (e.g., Resend)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_email_logs_order_id ON email_logs(order_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_recipient ON email_logs(recipient);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);

-- Enable RLS (Row Level Security)
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for email_logs table
CREATE POLICY email_logs_insert_policy ON email_logs
    FOR INSERT
    TO authenticated, anon, service_role
    WITH CHECK (true);

CREATE POLICY email_logs_select_policy ON email_logs
    FOR SELECT
    TO authenticated, anon, service_role
    USING (true);

CREATE POLICY email_logs_update_policy ON email_logs
    FOR UPDATE
    TO authenticated, anon, service_role
    USING (true);

-- Grant permissions
GRANT ALL ON email_logs TO authenticated;
GRANT ALL ON email_logs TO anon;
GRANT ALL ON email_logs TO service_role;

-- Force schema refresh
NOTIFY pgrst, 'reload schema';
