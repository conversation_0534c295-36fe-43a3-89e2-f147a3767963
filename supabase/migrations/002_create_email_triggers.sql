-- Create function to send order confirmation email
CREATE OR REPLACE FUNCTION send_order_confirmation_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Only send confirmation email for new orders
  IF TG_OP = 'INSERT' THEN
    -- Call the Edge Function to send email
    PERFORM
      net.http_post(
        url := current_setting('app.settings.supabase_url') || '/functions/v1/send-email',
        headers := jsonb_build_object(
          'Content-Type', 'application/json',
          'Authorization', 'Bearer ' || current_setting('app.settings.supabase_anon_key')
        ),
        body := jsonb_build_object(
          'type', 'order_confirmation',
          'orderId', NEW.id::text
        )
      );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to send status update email
CREATE OR REPLACE FUNCTION send_status_update_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Only send status update email when status actually changes
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    -- Call the Edge Function to send email
    PERFORM
      net.http_post(
        url := current_setting('app.settings.supabase_url') || '/functions/v1/send-email',
        headers := jsonb_build_object(
          'Content-Type', 'application/json',
          'Authorization', 'Bearer ' || current_setting('app.settings.supabase_anon_key')
        ),
        body := jsonb_build_object(
          'type', 'status_update',
          'orderId', NEW.id::text,
          'oldStatus', OLD.status
        )
      );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders;
CREATE TRIGGER trigger_send_order_confirmation_email
  AFTER INSERT ON orders
  FOR EACH ROW
  EXECUTE FUNCTION send_order_confirmation_email();

DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders;  
CREATE TRIGGER trigger_send_status_update_email
  AFTER UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION send_status_update_email();
