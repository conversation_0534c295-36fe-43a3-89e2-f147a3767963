# 🕐 Автоматическое обновление фидов каждые 6 часов - Продакшен

Этот документ описывает настройку автоматического обновления фидов в продакшене.

## 🎯 Реализованные решения

### 1. **GitHub Actions (Рекомендуемый для продакшена)**
- **Файл:** `.github/workflows/feed-processing.yml`
- **Частота:** Каждые 6 часов (00:00, 06:00, 12:00, 18:00 UTC)
- **Преимущества:** 
  - ✅ Работает независимо от хостинга
  - ✅ Бесплатно для публичных репозиториев
  - ✅ Логи и мониторинг встроены
  - ✅ Можно запускать вручную

### 2. **Supabase Edge Functions + pg_cron**
- **Файл:** `supabase/functions/process-feed/index.ts`
- **Миграция:** `scripts/migrations/setup-automatic-feed-processing.sql`
- **Преимущества:**
  - ✅ Работает на уровне базы данных
  - ✅ Высокая надежность
  - ✅ Нет зависимости от внешних сервисов

## 🚀 Быстрая настройка для продакшена

### Вариант 1: GitHub Actions (Проще всего)

1. **Добавить переменные в GitHub Secrets:**
   ```
   Repository Settings → Secrets and variables → Actions → New repository secret
   ```
   
   Добавить:
   - `REACT_APP_SUPABASE_URL` - URL вашего Supabase проекта
   - `REACT_APP_SUPABASE_ANON_KEY` - Anon key
   - `SUPABASE_SERVICE_ROLE_KEY` - Service role key

2. **Активировать workflow:**
   - Workflow автоматически активируется после коммита файла
   - Можно запустить вручную: `Actions → Automatic Feed Processing → Run workflow`

3. **Мониторинг:**
   - Логи: `Actions → Automatic Feed Processing`
   - Статус: зеленая галочка ✅ или красный крестик ❌

### Вариант 2: Supabase Edge Functions

1. **Установить Supabase CLI:**
   ```bash
   npm install -g supabase
   ```

2. **Логин и деплой функции:**
   ```bash
   supabase login
   supabase functions deploy process-feed
   ```

3. **Настроить pg_cron:**
   ```sql
   -- Выполнить в Supabase SQL Editor
   -- Содержимое файла: scripts/migrations/setup-automatic-feed-processing.sql
   ```

## 📊 Мониторинг и проверка

### Проверить работу автоматического обновления:

1. **Проверить статус фидов:**
   ```sql
   SELECT 
     name,
     last_fetched,
     EXTRACT(EPOCH FROM (NOW() - last_fetched))/3600 as hours_since_update
   FROM feeds 
   WHERE is_active = true;
   ```

2. **Проверить задачи обработки:**
   ```sql
   SELECT 
     f.name,
     fj.status,
     fj.created_at,
     fj.items_created,
     fj.items_updated,
     fj.error_message
   FROM feed_jobs fj
   JOIN feeds f ON fj.feed_id = f.id
   ORDER BY fj.created_at DESC
   LIMIT 10;
   ```

3. **Логи GitHub Actions:**
   - Перейти в `Actions` на GitHub
   - Выбрать `Automatic Feed Processing`
   - Посмотреть последние запуски

## ⚙️ Настройка частоты обновления

### Изменить частоту в GitHub Actions:
```yaml
# .github/workflows/feed-processing.yml
on:
  schedule:
    - cron: '0 */4 * * *'  # Каждые 4 часа
    # или
    - cron: '0 */12 * * *' # Каждые 12 часов
```

### Изменить частоту в pg_cron:
```sql
-- Удалить существующее задание
SELECT cron.unschedule('process-feeds-every-6-hours');

-- Создать новое с другой частотой
SELECT cron.schedule(
    'process-feeds-every-4-hours',
    '0 */4 * * *',  -- Каждые 4 часа
    'SELECT process_all_active_feeds();'
);
```

## 🔧 Ручной запуск

### GitHub Actions:
1. Перейти в `Actions` → `Automatic Feed Processing`
2. Нажать `Run workflow`
3. Выбрать ветку и нажать `Run workflow`

### Локально для тестирования:
```bash
cd /Users/<USER>/e-com_new/online-store
node scripts/process-feeds.js
```

### Через админ панель:
- Перейти в админ панель → Feed Management
- Нажать кнопку "🔄" рядом с нужным фидом

## 📈 Оптимизация производительности

1. **Настроить интервал обновления для каждого фида:**
   ```sql
   UPDATE feeds 
   SET fetch_interval_hours = 12 
   WHERE name = 'Медленно обновляемый фид';
   ```

2. **Мониторинг размера фидов:**
   ```sql
   SELECT 
     f.name,
     COUNT(p.id) as product_count,
     f.last_fetched
   FROM feeds f
   LEFT JOIN products p ON p.language = f.language
   GROUP BY f.id, f.name, f.last_fetched;
   ```

## 🚨 Устранение неполадок

### Если фиды не обновляются:

1. **Проверить GitHub Actions:**
   - Есть ли ошибки в логах?
   - Правильно ли настроены секреты?

2. **Проверить Supabase:**
   - Есть ли доступ к базе данных?
   - Работает ли pg_cron?

3. **Проверить фиды:**
   ```sql
   SELECT name, url, is_active, last_fetched 
   FROM feeds 
   WHERE is_active = true;
   ```

### Если есть ошибки 403:
- Проверить службу role key в секретах GitHub
- Убедиться, что RLS политики настроены правильно

## ✅ Готово!

После настройки одного из вариантов ваши фиды будут автоматически обновляться каждые 6 часов без необходимости запуска сервера или ручного вмешательства.
