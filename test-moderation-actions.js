#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://fijuwtwzugtmbggdmxjb.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpanV3dHd6dWd0bWJnZ2RteGpiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMxNTIyMDQsImV4cCI6MjA0ODcyODIwNH0.5Tn4EfWF7tAJ31JowNz8Gi6lAFe5CqDONwP46CvCaTE';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testModerationActions() {
  console.log('=== Testing Moderation Actions ===\n');
  
  // 1. Получим первый продукт в очереди модерации
  const { data: pendingProducts, error: fetchError } = await supabase
    .from('products')
    .select('*')
    .eq('moderation_status', 'pending_approval')
    .limit(2)
    .order('created_at', { ascending: true });

  if (fetchError) {
    console.error('Error fetching pending products:', fetchError);
    return;
  }

  if (!pendingProducts || pendingProducts.length === 0) {
    console.log('No products pending moderation');
    return;
  }

  console.log(`Found ${pendingProducts.length} products pending moderation\n`);
  
  // 2. Одобрим первый продукт
  const productToApprove = pendingProducts[0];
  console.log(`Testing APPROVE for product: ${productToApprove.name}`);
  console.log(`Product ID: ${productToApprove.id}`);
  console.log(`Current status: ${productToApprove.moderation_status}, Active: ${productToApprove.is_active}\n`);

  const { error: approveError } = await supabase
    .from('products')
    .update({
      moderation_status: 'approved',
      is_active: true
    })
    .eq('id', productToApprove.id);

  if (approveError) {
    console.error('Error approving product:', approveError);
  } else {
    console.log('✅ Product approved successfully!');
    
    // Проверим изменения
    const { data: updatedProduct } = await supabase
      .from('products')
      .select('*')
      .eq('id', productToApprove.id)
      .single();
    
    console.log(`New status: ${updatedProduct.moderation_status}, Active: ${updatedProduct.is_active}\n`);
  }

  // 3. Отклоним второй продукт (если есть)
  if (pendingProducts.length > 1) {
    const productToReject = pendingProducts[1];
    console.log(`Testing REJECT for product: ${productToReject.name}`);
    console.log(`Product ID: ${productToReject.id}`);
    console.log(`Current status: ${productToReject.moderation_status}, Active: ${productToReject.is_active}\n`);

    const { error: rejectError } = await supabase
      .from('products')
      .update({
        moderation_status: 'rejected',
        is_active: false
      })
      .eq('id', productToReject.id);

    if (rejectError) {
      console.error('Error rejecting product:', rejectError);
    } else {
      console.log('✅ Product rejected successfully!');
      
      // Проверим изменения
      const { data: updatedProduct } = await supabase
        .from('products')
        .select('*')
        .eq('id', productToReject.id)
        .single();
      
      console.log(`New status: ${updatedProduct.moderation_status}, Active: ${updatedProduct.is_active}\n`);
    }
  }

  // 4. Проверим общую статистику
  console.log('=== Final Statistics ===');
  
  const { data: stats } = await supabase
    .from('products')
    .select('moderation_status, is_active')
    .in('moderation_status', ['pending_approval', 'approved', 'rejected']);

  const statCounts = stats.reduce((acc, product) => {
    const key = `${product.moderation_status}_${product.is_active ? 'active' : 'inactive'}`;
    acc[key] = (acc[key] || 0) + 1;
    return acc;
  }, {});

  console.log('Product counts by status:');
  Object.entries(statCounts).forEach(([key, count]) => {
    console.log(`  ${key}: ${count}`);
  });
}

testModerationActions().catch(console.error);
