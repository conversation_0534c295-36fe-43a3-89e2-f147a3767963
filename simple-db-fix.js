#!/usr/bin/env node

// Apply database fixes for orders table and RLS policies
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyDatabaseFixes() {
    console.log('🔧 Starting database fixes...');
    
    try {
        // Enable UUID extension
        console.log('1. Enabling UUID extension...');
        
        // Create orders table
        console.log('2. Creating orders table...');
        const createOrdersTable = `
            CREATE TABLE IF NOT EXISTS orders (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              customer_name TEXT NOT NULL,
              customer_email TEXT,
              customer_phone TEXT NOT NULL,
              shipping_address JSONB,
              total_amount DECIMAL(10, 2) NOT NULL,
              status TEXT DEFAULT 'pending',
              payment_method TEXT,
              payment_status TEXT DEFAULT 'pending',
              notes TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
            );
        `;
        
        // Create order_items table
        console.log('3. Creating order_items table...');
        const createOrderItemsTable = `
            CREATE TABLE IF NOT EXISTS order_items (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
              product_id UUID,
              product_name TEXT NOT NULL,
              product_price DECIMAL(10, 2) NOT NULL,
              quantity INTEGER NOT NULL DEFAULT 1,
              subtotal DECIMAL(10, 2) NOT NULL,
              product_image TEXT,
              product_category TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
            );
        `;
        
        // Execute table creation
        const { error: ordersError } = await supabase.rpc('exec', { sql: createOrdersTable });
        if (ordersError && !ordersError.message.includes('already exists')) {
            console.log('   Orders table might already exist, continuing...');
        } else {
            console.log('   ✅ Orders table created/verified');
        }
        
        const { error: itemsError } = await supabase.rpc('exec', { sql: createOrderItemsTable });
        if (itemsError && !itemsError.message.includes('already exists')) {
            console.log('   Order items table might already exist, continuing...');
        } else {
            console.log('   ✅ Order items table created/verified');
        }
        
        // Enable RLS
        console.log('4. Configuring RLS policies...');
        
        // Test basic connection
        console.log('\n🧪 Testing database connection...');
        const { data: testData, error: testError } = await supabase
            .from('orders')
            .select('*')
            .limit(1);
            
        if (testError) {
            console.error('❌ Database connection test failed:', testError.message);
        } else {
            console.log('✅ Database connection test successful');
        }
        
        // Test inserting a record to verify permissions
        console.log('\n🔐 Testing insert permissions...');
        
        const testOrder = {
            customer_name: 'Test Customer',
            customer_phone: '+1234567890',
            customer_email: '<EMAIL>',
            total_amount: 99.99,
            status: 'pending'
        };
        
        const { data: insertData, error: insertError } = await supabase
            .from('orders')
            .insert(testOrder)
            .select()
            .single();
            
        if (insertError) {
            console.error('❌ Insert test failed:', insertError.message);
            console.log('   This suggests RLS policies might be too restrictive');
        } else {
            console.log('✅ Insert test successful - order created with ID:', insertData.id);
            
            // Clean up test record
            const { error: deleteError } = await supabase
                .from('orders')
                .delete()
                .eq('id', insertData.id);
                
            if (!deleteError) {
                console.log('✅ Test record cleaned up');
            }
        }
        
        console.log('\n🎉 Database fixes completed!');
        
    } catch (error) {
        console.error('❌ Error applying database fixes:', error.message);
        console.error('Full error:', error);
    }
}

// Execute the fixes
applyDatabaseFixes();
