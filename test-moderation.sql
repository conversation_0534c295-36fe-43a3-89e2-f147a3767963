-- Manual SQL commands to test moderation functionality

-- 1. Check current status of test product
SELECT id, name, moderation_status, is_active, external_id 
FROM products 
WHERE external_id = 'test_mod_2';

-- 2. Approve the test product
UPDATE products 
SET moderation_status = 'approved', is_active = true
WHERE external_id = 'test_mod_2';

-- 3. Check status after approval
SELECT id, name, moderation_status, is_active, external_id 
FROM products 
WHERE external_id = 'test_mod_2';

-- 4. Check another test product
SELECT id, name, moderation_status, is_active, external_id 
FROM products 
WHERE external_id = 'test_mod_3';

-- 5. Reject the second test product
UPDATE products 
SET moderation_status = 'rejected', is_active = false
WHERE external_id = 'test_mod_3';

-- 6. Check status after rejection
SELECT id, name, moderation_status, is_active, external_id 
FROM products 
WHERE external_id = 'test_mod_3';

-- 7. Get summary of all moderation statuses
SELECT 
    moderation_status,
    is_active,
    COUNT(*) as count
FROM products 
WHERE moderation_status IN ('pending_approval', 'approved', 'rejected')
GROUP BY moderation_status, is_active
ORDER BY moderation_status, is_active;
