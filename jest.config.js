module.exports = {
  // Автоматически очищать моки между каждым тестом
  clearMocks: true,
  
  // Директория с файлами тестов
  testEnvironment: 'jsdom',
  
  // Пути к модулям, которые не требуют расширений при импорте
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json', 'node'],
  
  // Преобразователи для конкретных типов файлов
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },
  
  // Модули, которые должны быть замокированы
  moduleNameMapper: {
    // Мок для framer-motion
    '^framer-motion$': '<rootDir>/src/__mocks__/framer-motion.js',
    
    // Мок для стилей
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    
    // Мок для медиафайлов
    '\\.(gif|ttf|eot|svg|png|jpg|jpeg)$': '<rootDir>/src/__mocks__/fileMock.js',
  },
  
  // Настройки для покрытия кода тестами
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/index.js',
    '!src/reportWebVitals.js',
    '!src/setupTests.js'
  ],

  // Пути, которые следует игнорировать
  testPathIgnorePatterns: [
    '/node_modules/',
    '/build/',
    '/.git/',
  ],

  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],

  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],

  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};