#!/bin/bash

echo "🚀 Альтернативный запуск обработки фидов"
echo "======================================="
echo ""

# Запуск локального процессинга для проверки
echo "🔄 Запускаем обработку фидов локально..."
cd /Users/<USER>/e-com_new/online-store

# Устанавливаем переменные окружения
export REACT_APP_SUPABASE_URL="https://dmdijuuwnbwngerkbfak.supabase.co"
export REACT_APP_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU3NTA1NzUsImV4cCI6MjA2MTMyNjU3NX0._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU"
export SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8"

# Запуск обработки
echo "📋 Запуск scripts/process-feeds.js..."
node scripts/process-feeds.js

echo ""
echo "✅ Готово! Если это сработало - значит система настроена правильно."
echo "⚠️  GitHub Actions появится через несколько минут после последнего коммита."
echo ""
echo "📱 Попробуйте обновить страницу https://github.com/rackovchen/roomchik/actions"
