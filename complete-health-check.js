#!/usr/bin/env node

// Complete email system health check
console.log('🏥 Starting complete health check...');

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('📦 Dependencies loaded');

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

console.log('🔑 Environment variables checked');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing environment variables');
  console.log('URL present:', !!supabaseUrl);
  console.log('Key present:', !!supabaseKey);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testCompleteEmailFlow() {
  console.log('='.repeat(70));
  console.log('🔬 COMPLETE EMAIL SYSTEM HEALTH CHECK');
  console.log('='.repeat(70));

  const results = {
    database: false,
    orderCreation: false,
    emailLogs: false,
    edgeFunction: false,
    overallHealth: 'unknown'
  };

  // Test 1: Database connectivity
  console.log('\n1️⃣  Testing database connectivity...');
  try {
    const { data, error } = await supabase.from('orders').select('count', { count: 'exact', head: true });
    if (error) throw error;
    console.log('   ✅ Database connected successfully');
    results.database = true;
  } catch (error) {
    console.log('   ❌ Database connection failed:', error.message);
  }

  // Test 2: Order creation capability
  console.log('\n2️⃣  Testing order creation...');
  try {
    const testOrder = {
      customer_name: 'Health Check Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+**********',
      shipping_address: { city: 'Test City', nova_poshta_office: '1' },
      total_amount: 99.99,
      status: 'pending',
      payment_method: 'cash_on_delivery',
      payment_status: 'pending'
    };

    const { data: order, error } = await supabase
      .from('orders')
      .insert([testOrder])
      .select('*')
      .single();

    if (error) throw error;

    console.log('   ✅ Order created successfully:', order.id);
    results.orderCreation = true;

    // Cleanup
    await supabase.from('orders').delete().eq('id', order.id);
    console.log('   🧹 Test order cleaned up');

  } catch (error) {
    console.log('   ❌ Order creation failed:', error.message);
  }

  // Test 3: Email logs table
  console.log('\n3️⃣  Testing email logs table...');
  try {
    const { data, error } = await supabase
      .from('email_logs')
      .select('count', { count: 'exact', head: true });

    if (error) throw error;
    console.log('   ✅ Email logs table accessible');
    results.emailLogs = true;
  } catch (error) {
    console.log('   ❌ Email logs table issue:', error.message);
  }

  // Test 4: Edge Function
  console.log('\n4️⃣  Testing Supabase Edge Function...');
  try {
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        type: 'order_confirmation',
        orderId: 'health-check-123',
        orderData: {
          id: 'health-check-123',
          customer_name: 'Health Check',
          customer_email: '<EMAIL>',
          total_amount: 100,
          created_at: new Date().toISOString(),
          order_items: []
        }
      }
    });

    if (error) {
      if (error.message.includes('non-2xx status')) {
        console.log('   ⚠️  Edge Function deployed but needs RESEND_API_KEY');
        console.log('   📋 This is expected - configure Resend API for real emails');
      } else {
        throw error;
      }
    } else {
      console.log('   ✅ Edge Function working perfectly!');
      results.edgeFunction = true;
    }

  } catch (error) {
    console.log('   ❌ Edge Function error:', error.message);
  }

  // Overall health assessment
  console.log('\n' + '='.repeat(70));
  console.log('📊 HEALTH CHECK RESULTS:');
  console.log('='.repeat(70));

  console.log('Database Connectivity:    ', results.database ? '✅ HEALTHY' : '❌ FAILED');
  console.log('Order Creation:          ', results.orderCreation ? '✅ HEALTHY' : '❌ FAILED');
  console.log('Email Logs Table:        ', results.emailLogs ? '✅ HEALTHY' : '❌ FAILED');
  console.log('Edge Function:           ', results.edgeFunction ? '✅ HEALTHY' : '⚠️  NEEDS CONFIG');

  // Determine overall health
  const criticalIssues = !results.database || !results.orderCreation;
  const configIssues = !results.emailLogs || !results.edgeFunction;

  if (criticalIssues) {
    results.overallHealth = 'critical';
    console.log('\n🚨 OVERALL HEALTH: CRITICAL ISSUES');
    console.log('❌ Core functionality is not working');
    console.log('🔧 Fix database and order creation issues first');
  } else if (configIssues) {
    results.overallHealth = 'needs_config';
    console.log('\n⚠️  OVERALL HEALTH: NEEDS CONFIGURATION');
    console.log('✅ Core functionality working');
    console.log('🔧 Configure Resend API for real emails');
  } else {
    results.overallHealth = 'excellent';
    console.log('\n🎉 OVERALL HEALTH: EXCELLENT!');
    console.log('✅ All systems operational');
    console.log('📧 Real emails being sent');
  }

  // Recommendations
  console.log('\n' + '='.repeat(70));
  console.log('💡 RECOMMENDATIONS:');
  console.log('='.repeat(70));

  if (results.overallHealth === 'critical') {
    console.log('🚨 URGENT: Fix database connectivity and order creation');
    console.log('📋 Check Supabase project settings and RLS policies');
  } else if (results.overallHealth === 'needs_config') {
    console.log('🔧 NEXT STEP: Configure Resend API for real emails');
    console.log('📋 Follow instructions in: QUICK_EMAIL_SETUP.md');
    console.log('⏰ CURRENT: MockEmailService provides fallback functionality');
  } else {
    console.log('🎉 PERFECT: Your email system is fully operational!');
    console.log('📧 Monitor email delivery and logs as needed');
  }

  console.log('\n🌐 Test URLs:');
  console.log('   • Main App: http://localhost:3001');
  console.log('   • Admin Panel: http://localhost:3001/admin');
  console.log('   • Email Diagnostic: Available in Admin Panel');

  return results;
}

testCompleteEmailFlow().catch(console.error);
