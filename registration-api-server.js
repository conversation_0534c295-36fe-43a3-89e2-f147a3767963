const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const port = 3002;

// Middleware
app.use(cors());
app.use(express.json());

const supabaseUrl = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRtZGlqdXV3bmJ3bmdlcmtiZmFrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTc1MDU3NSwiZXhwIjoyMDYxMzI2NTc1fQ.3tnauUvsWU6kIQWQ3FVMN0xUM10aBAud2I5_LeGXsY8';

// Create admin client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Custom registration endpoint
app.post('/api/register-user', async (req, res) => {
  const { email, password, firstName, lastName } = req.body;

  console.log('Registration request received:', { email, firstName, lastName });

  if (!email || !password) {
    return res.status(400).json({ error: 'Email and password are required' });
  }

  try {
    console.log('Attempting user creation with admin client...');

    // First, ensure profiles table exists
    const { error: tableError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .limit(1);

    if (tableError && tableError.code === 'PGRST106') {
      console.log('Profiles table does not exist, creating it...');
      
      // Try to create the table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.profiles (
          id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
          email TEXT,
          first_name TEXT,
          last_name TEXT,
          avatar_url TEXT,
          is_admin BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
        
        CREATE POLICY IF NOT EXISTS "Public profiles are viewable by everyone"
          ON public.profiles FOR SELECT
          USING (true);
        
        CREATE POLICY IF NOT EXISTS "Users can insert their own profile"
          ON public.profiles FOR INSERT
          WITH CHECK (auth.uid() = id);
        
        CREATE POLICY IF NOT EXISTS "Users can update their own profile"
          ON public.profiles FOR UPDATE
          USING (auth.uid() = id);
        
        GRANT ALL ON public.profiles TO authenticated;
        GRANT ALL ON public.profiles TO anon;
        GRANT ALL ON public.profiles TO service_role;
      `;

      // Execute table creation (this might fail, but we'll continue)
      try {
        await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'apikey': supabaseServiceKey
          },
          body: JSON.stringify({ sql: createTableSQL })
        });
        console.log('Table creation attempted');
      } catch (e) {
        console.log('Table creation failed:', e.message);
      }
    }

    // Create user using admin client
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        first_name: firstName || '',
        last_name: lastName || ''
      }
    });

    if (userError) {
      console.error('User creation failed:', userError.message);
      return res.status(400).json({ error: userError.message });
    }

    console.log('User created successfully:', userData.user.id);

    // Try to create profile manually
    try {
      const { error: profileError } = await supabaseAdmin
        .from('profiles')
        .insert({
          id: userData.user.id,
          email: email,
          first_name: firstName || '',
          last_name: lastName || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.warn('Profile creation failed:', profileError.message);
      } else {
        console.log('Profile created successfully');
      }
    } catch (profileErr) {
      console.warn('Profile creation error:', profileErr.message);
    }

    return res.status(200).json({
      success: true,
      user: {
        id: userData.user.id,
        email: userData.user.email
      },
      message: 'User registered successfully'
    });

  } catch (error) {
    console.error('Registration error:', error.message);
    return res.status(500).json({ error: 'Internal server error: ' + error.message });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(`🚀 Registration API server running on http://localhost:${port}`);
  console.log(`📋 Endpoints:
  - POST /api/register-user
  - GET /health`);
});

module.exports = app;
