const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Ошибка: Не найдены переменные окружения Supabase');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyBannerSliderMigration() {
  try {
    console.log('🚀 Начинаем миграцию баннеров для слайдера...');

    // Читаем SQL файл
    const sqlFile = path.join(__dirname, 'add-banner-slider-fields.sql');
    const sqlQuery = fs.readFileSync(sqlFile, 'utf8');

    // Выполняем миграцию
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: sqlQuery
    });

    if (error) {
      console.error('❌ Ошибка при выполнении миграции:', error);
      return;
    }

    console.log('✅ Миграция успешно применена');

    // Проверяем структуру таблицы
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('exec_sql', {
        sql_query: `
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = 'banners' 
          ORDER BY ordinal_position;
        `
      });

    if (tableError) {
      console.error('❌ Ошибка при проверке структуры таблицы:', tableError);
      return;
    }

    console.log('📋 Текущая структура таблицы banners:');
    if (tableInfo && Array.isArray(tableInfo)) {
      tableInfo.forEach(column => {
        console.log(`  - ${column.column_name}: ${column.data_type} ${column.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
      });
    }

    // Обновляем несколько баннеров для тестирования слайдера
    console.log('🔄 Обновляем тестовые баннеры...');

    const { data: banners, error: fetchError } = await supabase
      .from('banners')
      .select('*')
      .order('position')
      .limit(3);

    if (fetchError) {
      console.error('❌ Ошибка при получении баннеров:', fetchError);
      return;
    }

    if (banners && banners.length > 0) {
      // Обновляем первые несколько баннеров для демонстрации слайдера
      for (let i = 0; i < Math.min(banners.length, 3); i++) {
        const banner = banners[i];
        const updateData = {
          subtitle: `Подзаголовок для баннера ${i + 1}`,
          button_text: i === 0 ? 'Смотреть коллекцию' : i === 1 ? 'Купить сейчас' : 'Узнать больше',
          button_link: i === 0 ? '/products' : i === 1 ? '/sale' : '/about',
          slider_enabled: true,
          auto_play: true,
          auto_play_interval: 5000 + (i * 1000), // Разные интервалы для разнообразия
          updated_at: new Date().toISOString()
        };

        const { error: updateError } = await supabase
          .from('banners')
          .update(updateData)
          .eq('id', banner.id);

        if (updateError) {
          console.error(`❌ Ошибка при обновлении баннера ${banner.id}:`, updateError);
        } else {
          console.log(`✅ Обновлен баннер ${banner.id}: "${banner.title}"`);
        }
      }
    }

    console.log('🎉 Миграция завершена успешно!');
    console.log('🔧 Теперь можно использовать новый HeroBannerSlider');

  } catch (error) {
    console.error('❌ Неожиданная ошибка:', error);
  }
}

// Запускаем миграцию
applyBannerSliderMigration()
  .then(() => {
    console.log('\n✨ Процесс миграции завершен');
  })
  .catch(error => {
    console.error('\n💥 Критическая ошибка:', error);
    process.exit(1);
  });
