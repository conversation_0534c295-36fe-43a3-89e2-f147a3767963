# Email Notifications Setup Guide

Эта система email уведомлений использует Supabase Edge Functions и Database Triggers для автоматической отправки email при создании заказов и изменении их статуса.

## Архитектура

1. **Supabase Edge Functions** - обрабатывают отправку email
2. **Database Triggers** - автоматически вызывают Edge Functions при изменениях в БД
3. **Email Service Provider** - Resend API для доставки email
4. **Email Logs** - таблица для отслеживания всех отправленных писем

## Настройка

### 1. Установка Supabase CLI

```bash
npm install -g supabase
```

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON> в Supabase

```bash
supabase login
```

### 3. Инициализация проекта

```bash
supabase init
```

### 4. Связывание с вашим проектом

```bash
supabase link --project-ref YOUR_PROJECT_ID
```

### 5. Применение миграций

```bash
# Создание таблицы email_logs
supabase db push --file supabase/migrations/001_create_email_logs.sql

# Создание функций и триггеров
supabase db push --file supabase/migrations/002_create_email_triggers.sql
```

### 6. Настройка переменных окружения в Supabase

В панели Supabase идите в Settings > Edge Functions и добавьте переменные:

```
RESEND_API_KEY=your_resend_api_key_here
FROM_EMAIL=<EMAIL>
```

### 7. Деплой Edge Function

```bash
supabase functions deploy send-email
```

### 8. Получение Resend API ключа

1. Зарегистрируйтесь на [resend.com](https://reаролsend.com)
2. Создайте API ключ
3. Добавьте его в переменные окружения Supabase

### 9. Настройка домена (опционально)

Для лучшей доставляемости email настройте собственный домен в Resend:
1. Добавьте домен в Resend
2. Настройте DNS записи
3. Обновите переменную FROM_EMAIL

## Использование

### Автоматические уведомления

После настройки email будут отправляться автоматически:

- **При создании заказа** - отправляется письмо подтверждения
- **При изменении статуса** - отправляется уведомление об обновлении

### Ручная отправка

Используйте EmailService в коде:

```javascript
import { EmailService } from '../services/emailService';

// Отправка подтверждения заказа
await EmailService.sendOrderConfirmation(orderId);

// Отправка обновления статуса
await EmailService.sendStatusUpdate(orderId, oldStatus);

// Тестовое письмо
await EmailService.sendTestEmail('<EMAIL>');
```

### Админ панель

В разделе "Настройки магазина" → "Email Уведомления" доступны:

- Проверка статуса системы
- Отправка тестовых писем
- Просмотр логов отправок
- Управление настройками

## Мониторинг

### Проверка логов Edge Functions

```bash
supabase functions logs send-email
```

### Просмотр email логов

В админ панели или через SQL:

```sql
SELECT * FROM email_logs 
ORDER BY created_at DESC 
LIMIT 50;
```

### Проверка статуса системы

```javascript
const status = await EmailService.initializeEmailSystem();
console.log(status);
```

## Troubleshooting

### Письма не отправляются

1. Проверьте переменные окружения в Supabase
2. Убедитесь что Edge Function развернута
3. Проверьте логи функций
4. Проверьте квоты в Resend

### Ошибки доступа к БД

1. Проверьте RLS политики для таблиц
2. Убедитесь что SERVICE_ROLE_KEY правильный
3. Проверьте права доступа к таблицам

### Триггеры не срабатывают

1. Проверьте что триггеры созданы:
```sql
SELECT * FROM information_schema.triggers 
WHERE trigger_name LIKE '%email%';
```

2. Проверьте что функции существуют:
```sql
SELECT * FROM information_schema.routines 
WHERE routine_name LIKE '%email%';
```

### Проблемы с доставкой

1. Проверьте Resend dashboard на ошибки
2. Убедитесь что FROM_EMAIL настроен правильно
3. Проверьте SPF/DKIM записи для домена

## Кастомизация

### Изменение шаблонов писем

Отредактируйте функции в `supabase/functions/send-email/index.ts`:
- `getOrderConfirmationTemplate()` - для подтверждения заказа
- `getStatusUpdateTemplate()` - для обновления статуса

### Добавление новых типов писем

1. Добавьте новый case в Edge Function
2. Создайте шаблон письма
3. Добавьте метод в EmailService
4. При необходимости создайте новый триггер

### Интеграция с другими email провайдерами

Замените вызовы Resend API в Edge Function на API вашего провайдера (SendGrid, Mailgun, и т.д.)

## Безопасность

- Используйте HTTPS для всех запросов
- Храните API ключи в переменных окружения
- Регулярно обновляйте ключи доступа
- Ограничьте права доступа к минимально необходимым

## Масштабирование

- Supabase Edge Functions автоматически масштабируются
- Resend поддерживает высокие объемы отправки
- Для enterprise нагрузок рассмотрите dedicated IP
- Настройте мониторинг и алерты

## Поддержка

Если возникли вопросы:
1. Проверьте логи и документацию
2. Убедитесь что все настройки корректны
3. Проверьте статус Supabase и Resend сервисов
