<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products API Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            transition: transform 0.2s;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .product-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .product-price {
            color: #e74c3c;
            font-size: 18px;
            font-weight: bold;
        }
        .product-vendor {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .badges {
            margin-bottom: 10px;
        }
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
            margin-right: 5px;
        }
        .badge-new { background-color: #27ae60; }
        .badge-sale { background-color: #e74c3c; }
        .badge-bestseller { background-color: #f39c12; }
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background-color: #3498db;
            color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .search-box {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ Products API Example</h1>
        <p>Пример использования API товаров для страницы-заглушки</p>
        
        <!-- Статистика -->
        <div id="stats" class="stats"></div>
        
        <!-- Управление -->
        <div class="controls">
            <input type="text" id="searchInput" class="search-box" placeholder="Поиск товаров...">
            <button onclick="searchProducts()">🔍 Поиск</button>
            <button onclick="loadAllProducts()">📦 Все товары</button>
            <button onclick="loadNewProducts()">🆕 Новинки</button>
            <button onclick="loadSaleProducts()">🔥 Скидки</button>
            <button onclick="loadBestsellerProducts()">⭐ Хиты</button>
            <button onclick="loadStats()">📊 Статистика</button>
        </div>
        
        <!-- Результаты -->
        <div id="results">
            <div class="loading">Нажмите на кнопку для загрузки товаров</div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3001/api';
        
        // Загрузка статистики
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE_URL}/products/stats`);
                const result = await response.json();
                
                if (result.success) {
                    const statsHtml = `
                        <div class="stat-card">
                            <div class="stat-number">${result.data.total}</div>
                            <div>Всего товаров</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${result.data.active}</div>
                            <div>Активных</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${result.data.new}</div>
                            <div>Новинок</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${result.data.onSale}</div>
                            <div>Со скидкой</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${result.data.bestsellers}</div>
                            <div>Хитов продаж</div>
                        </div>
                    `;
                    document.getElementById('stats').innerHTML = statsHtml;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        // Загрузка всех товаров
        async function loadAllProducts() {
            await loadProducts(`${API_BASE_URL}/products?limit=20`);
        }
        
        // Загрузка новинок
        async function loadNewProducts() {
            await loadProducts(`${API_BASE_URL}/products/new?limit=20`);
        }
        
        // Загрузка товаров со скидкой
        async function loadSaleProducts() {
            await loadProducts(`${API_BASE_URL}/products/sale?limit=20`);
        }
        
        // Загрузка хитов продаж
        async function loadBestsellerProducts() {
            await loadProducts(`${API_BASE_URL}/products/bestsellers?limit=20`);
        }
        
        // Поиск товаров
        async function searchProducts() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (!searchTerm) {
                alert('Введите поисковый запрос');
                return;
            }
            await loadProducts(`${API_BASE_URL}/products/search?q=${encodeURIComponent(searchTerm)}&limit=20`);
        }
        
        // Обработка Enter в поиске
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchProducts();
            }
        });
        
        // Базовая функция загрузки товаров
        async function loadProducts(url) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">Загрузка товаров...</div>';
            
            try {
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    const productsHtml = result.data.map(product => `
                        <div class="product-card">
                            <img src="${product.image || '/placeholder.png'}" 
                                 alt="${product.name}" 
                                 class="product-image"
                                 onerror="this.src='/placeholder.png'">
                            <div class="badges">
                                ${product.is_new ? '<span class="badge badge-new">Новинка</span>' : ''}
                                ${product.is_on_sale ? '<span class="badge badge-sale">Скидка</span>' : ''}
                                ${product.is_bestseller ? '<span class="badge badge-bestseller">Хит</span>' : ''}
                            </div>
                            <div class="product-name">${product.name}</div>
                            <div class="product-vendor">${product.vendor || product.brand || ''}</div>
                            <div class="product-price">
                                ${product.price} ₴
                                ${product.original_price && product.original_price > product.price ? 
                                  `<span style="text-decoration: line-through; color: #999; margin-left: 10px;">${product.original_price} ₴</span>` : ''}
                            </div>
                        </div>
                    `).join('');
                    
                    resultsDiv.innerHTML = `
                        <h3>Найдено товаров: ${result.data.length}</h3>
                        <div class="product-grid">${productsHtml}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = '<div class="loading">Товары не найдены</div>';
                }
            } catch (error) {
                console.error('Error loading products:', error);
                resultsDiv.innerHTML = '<div class="loading">Ошибка загрузки товаров</div>';
            }
        }
        
        // Загружаем статистику при загрузке страницы
        window.addEventListener('load', loadStats);
    </script>
</body>
</html>
