// Примеры конфигурации API для разных сценариев размещения

// 1. API на том же домене, но другом порту
const CONFIG_SAME_DOMAIN_DIFFERENT_PORT = {
    baseUrl: 'https://yourdomain.com:3001/api'
};

// 2. API на поддомене
const CONFIG_SUBDOMAIN = {
    baseUrl: 'https://api.yourdomain.com/api'
};

// 3. API на том же сервере, относительный путь
const CONFIG_RELATIVE_PATH = {
    baseUrl: '/api'
};

// 4. API на отдельном сервере
const CONFIG_SEPARATE_SERVER = {
    baseUrl: 'https://api-server.yourdomain.com/api'
};

// 5. Локальное тестирование
const CONFIG_LOCAL_TESTING = {
    baseUrl: 'http://localhost:3001/api'
};

// 6. API через nginx proxy
const CONFIG_NGINX_PROXY = {
    baseUrl: 'https://yourdomain.com/api'
};

// 7. API с базовой аутентификацией (если потребуется)
const CONFIG_WITH_AUTH = {
    baseUrl: 'https://api.yourdomain.com/api',
    headers: {
        'Authorization': 'Bearer your-token-here'
    }
};

// Функция для автоматического определения API URL
function getApiUrl() {
    const hostname = window.location.hostname;
    
    // Локальная разработка
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'http://localhost:3001/api';
    }
    
    // Продакшн
    if (hostname.includes('yourdomain.com')) {
        return 'https://api.yourdomain.com/api';
    }
    
    // Fallback
    return '/api';
}

// Пример использования в landing-page.html:
/*
const API_CONFIG = {
    baseUrl: getApiUrl()
};
*/

// Расширенная конфигурация с обработкой ошибок
const ADVANCED_CONFIG = {
    baseUrl: 'https://api.yourdomain.com/api',
    timeout: 10000, // 10 секунд
    retries: 3,
    headers: {
        'Content-Type': 'application/json'
    }
};

// Функция для запросов с обработкой ошибок
async function apiRequest(endpoint, options = {}) {
    const config = ADVANCED_CONFIG;
    const url = `${config.baseUrl}${endpoint}`;
    
    for (let i = 0; i < config.retries; i++) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), config.timeout);
            
            const response = await fetch(url, {
                ...options,
                headers: { ...config.headers, ...options.headers },
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.warn(`Попытка ${i + 1} не удалась:`, error.message);
            
            if (i === config.retries - 1) {
                throw error;
            }
            
            // Задержка перед повторной попыткой
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}

// Пример использования расширенной функции:
/*
async function loadProducts() {
    try {
        const result = await apiRequest('/products?limit=12');
        if (result.success) {
            displayProducts(result.data);
        }
    } catch (error) {
        console.error('Не удалось загрузить товары:', error);
        showErrorMessage('Временные технические работы');
    }
}
*/
