const fs = require('fs');
const path = require('path');

// Simple function to find all JS files in src directory
function findJSFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      files.push(...findJSFiles(fullPath));
    } else if (item.endsWith('.js') || item.endsWith('.jsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Clean console.log statements from file
function cleanFile(filePath) {
  console.log(`Cleaning ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Remove console.log statements
  content = content.replace(/\s*console\.log\([^)]*\);?/g, '');
  
  // Remove console.warn statements  
  content = content.replace(/\s*console\.warn\([^)]*\);?/g, '');
  
  // Remove debug-related console.error statements
  content = content.replace(/\s*console\.error\([^)]*debug[^)]*\);?/g, '');
  
  // Remove multi-line console.log statements
  content = content.replace(/\s*console\.log\(\s*[^)]*\s*\);?/gs, '');
  
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✓ Cleaned ${filePath}`);
  } else {
    console.log(`- No changes needed for ${filePath}`);
  }
}

// Main execution
const srcDir = path.join(__dirname, 'src');
console.log('Starting cleanup of console.log statements...');

const jsFiles = findJSFiles(srcDir);
console.log(`Found ${jsFiles.length} JavaScript files to clean`);

jsFiles.forEach(cleanFile);

console.log('Cleanup completed!');
