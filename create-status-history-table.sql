-- Create order status history table
CREATE TABLE IF NOT EXISTS order_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  status TEXT NOT NULL,
  note TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  created_by TEXT
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_order_status_history_order_id ON order_status_history(order_id);

-- Add permissive policies for the status history table
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS order_status_history_insert_policy ON order_status_history;
CREATE POLICY order_status_history_insert_policy ON order_status_history
    FOR INSERT
    TO authenticated, anon
    WITH CHECK (true);

DROP POLICY IF EXISTS order_status_history_select_policy ON order_status_history;
CREATE POLICY order_status_history_select_policy ON order_status_history
    FOR SELECT
    TO authenticated, anon
    USING (true);

-- Force schema refresh
NOTIFY pgrst, 'reload schema'; 