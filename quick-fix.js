#!/usr/bin/env node

/**
 * Скрипт быстрого исправления критических проблем
 * Автоматически исправляет основные проблемы безопасности и качества кода
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}🔧 ${msg}${colors.reset}\n`)
};

class QuickFixer {
  constructor() {
    this.fixedIssues = [];
    this.errors = [];
  }

  async runAllFixes() {
    log.header('ЗАПУСК БЫСТРОГО ИСПРАВЛЕНИЯ КРИТИЧЕСКИХ ПРОБЛЕМ');
    
    await this.fixSecurityIssues();
    await this.fixCodeQuality();
    await this.fixDependencies();
    await this.optimizeBundle();
    
    this.generateReport();
  }

  async fixSecurityIssues() {
    log.header('Исправление проблем безопасности');
    
    try {
      // 1. Создание безопасного supabaseClient.js
      const newSupabaseClient = `import { createClient } from '@supabase/supabase-js';

// Безопасное получение переменных окружения
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

// Проверка наличия обязательных переменных
if (!supabaseUrl) {
  throw new Error('Missing REACT_APP_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing REACT_APP_SUPABASE_ANON_KEY environment variable');
}

// Создание клиента с безопасными настройками
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: typeof window !== 'undefined',
    flowType: 'pkce'
  },
  global: {
    headers: {
      'X-Client-Info': 'online-store-secure'
    }
  }
});

// Создание админ клиента (только для серверной части)
const supabaseServiceKey = process.env.REACT_APP_SUPABASE_SERVICE_ROLE_KEY;

export const supabaseAdmin = supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null;

// Вспомогательные функции
export const getAdminClient = () => {
  if (process.env.NODE_ENV !== 'production' && supabaseAdmin) {
    return supabaseAdmin;
  }
  return supabase;
};

export default supabase;
`;

      // Создание резервной копии
      if (fs.existsSync('src/supabaseClient.js')) {
        fs.copyFileSync('src/supabaseClient.js', 'src/supabaseClient.js.backup');
        log.info('Создана резервная копия supabaseClient.js');
      }

      // Запись нового файла
      fs.writeFileSync('src/supabaseClient.js', newSupabaseClient);
      log.success('Обновлен supabaseClient.js с безопасными настройками');
      this.fixedIssues.push('Убраны хардкод API ключи из supabaseClient.js');

      // 2. Создание .env.example
      const envExample = `# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_url_here
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key_here
REACT_APP_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Application Configuration
PORT=3000
API_PORT=3001

# Environment
NODE_ENV=development
`;

      fs.writeFileSync('.env.example', envExample);
      log.success('Создан .env.example файл');
      this.fixedIssues.push('Создан .env.example файл');

      // 3. Обновление .gitignore
      let gitignoreContent = '';
      if (fs.existsSync('.gitignore')) {
        gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
      }

      const envEntries = ['.env', '.env.local', '.env.production', '.env.development'];
      let gitignoreUpdated = false;

      for (const entry of envEntries) {
        if (!gitignoreContent.includes(entry)) {
          gitignoreContent += `\n${entry}`;
          gitignoreUpdated = true;
        }
      }

      if (gitignoreUpdated) {
        fs.writeFileSync('.gitignore', gitignoreContent);
        log.success('Обновлен .gitignore файл');
        this.fixedIssues.push('Добавлены .env файлы в .gitignore');
      }

    } catch (error) {
      this.errors.push(`Ошибка исправления безопасности: ${error.message}`);
      log.error(`Ошибка исправления безопасности: ${error.message}`);
    }
  }

  async fixCodeQuality() {
    log.header('Исправление качества кода');

    try {
      // 1. Удаление console.log из продакшн кода
      const srcFiles = this.getAllJSFiles('src');
      let removedConsoleCount = 0;

      for (const file of srcFiles) {
        try {
          let content = fs.readFileSync(file, 'utf8');
          const originalContent = content;
          
          // Удаляем console.log, но оставляем console.error и console.warn
          content = content.replace(/^\s*console\.log\(.*\);\s*$/gm, '');
          content = content.replace(/console\.log\([^)]*\);?/g, '');
          
          if (content !== originalContent) {
            fs.writeFileSync(file, content);
            removedConsoleCount++;
          }
        } catch (error) {
          // Игнорируем ошибки отдельных файлов
        }
      }

      if (removedConsoleCount > 0) {
        log.success(`Удалены console.log из ${removedConsoleCount} файлов`);
        this.fixedIssues.push(`Удалены console.log из ${removedConsoleCount} файлов`);
      }

      // 2. Исправление AuthContext
      await this.fixAuthContext();

      // 3. Исправление CartContext
      await this.fixCartContext();

      // 4. Запуск ESLint fix
      try {
        execSync('npm run lint:fix', { stdio: 'pipe' });
        log.success('Исправлены ESLint ошибки');
        this.fixedIssues.push('Исправлены ESLint ошибки');
      } catch (error) {
        log.warning('Не удалось автоматически исправить все ESLint ошибки');
      }

    } catch (error) {
      this.errors.push(`Ошибка исправления качества кода: ${error.message}`);
      log.error(`Ошибка исправления качества кода: ${error.message}`);
    }
  }

  async fixAuthContext() {
    try {
      const authContextPath = 'src/context/AuthContext.js';
      if (!fs.existsSync(authContextPath)) return;

      let content = fs.readFileSync(authContextPath, 'utf8');
      
      // Удаляем console.log из AuthContext
      content = content.replace(/^\s*console\.log\(.*\);\s*$/gm, '');
      content = content.replace(/console\.log\([^)]*\);?/g, '');
      
      fs.writeFileSync(authContextPath, content);
      log.success('Очищен AuthContext от console.log');
      
    } catch (error) {
      log.warning('Не удалось исправить AuthContext');
    }
  }

  async fixCartContext() {
    try {
      const cartContextPath = 'src/context/CartContext.js';
      if (!fs.existsSync(cartContextPath)) return;

      let content = fs.readFileSync(cartContextPath, 'utf8');
      
      // Удаляем комментарии о Firebase
      content = content.replace(/\/\/ Removed all Firebase-related logic.*$/gm, '');
      content = content.replace(/\/\/ Firebase-related logic.*$/gm, '');
      
      // Очищаем пустые try-catch блоки
      content = content.replace(/try\s*{\s*\/\/[^}]*}\s*catch\s*\([^)]*\)\s*{\s*console\.error[^}]*}/g, '');
      
      fs.writeFileSync(cartContextPath, content);
      log.success('Очищен CartContext от Firebase логики');
      
    } catch (error) {
      log.warning('Не удалось исправить CartContext');
    }
  }

  async fixDependencies() {
    log.header('Проверка зависимостей');

    try {
      // Проверяем наличие react-router-dom
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      if (!packageJson.dependencies['react-router-dom']) {
        log.warning('react-router-dom не найден в зависимостях');
        log.info('Запустите: npm install react-router-dom');
      } else {
        log.success('react-router-dom установлен');
      }

      // Проверяем на уязвимости
      try {
        execSync('npm audit --audit-level=high', { stdio: 'pipe' });
        log.success('Критических уязвимостей в зависимостях не найдено');
      } catch (error) {
        log.warning('Найдены уязвимости в зависимостях. Запустите: npm audit fix');
      }

    } catch (error) {
      this.errors.push(`Ошибка проверки зависимостей: ${error.message}`);
    }
  }

  async optimizeBundle() {
    log.header('Оптимизация bundle');

    try {
      // Создание оптимизированного webpack конфига
      const webpackConfig = `const path = require('path');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = function override(config, env) {
  // Оптимизация для продакшена
  if (env === 'production') {
    // Разделение кода
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\\\/]node_modules[\\\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      },
    };

    // Анализ bundle (только в development)
    if (process.env.ANALYZE_BUNDLE) {
      config.plugins.push(new BundleAnalyzerPlugin());
    }
  }

  return config;
};
`;

      // Создаем config-overrides.js только если используется CRACO
      if (fs.existsSync('craco.config.js')) {
        log.info('Используется CRACO, webpack конфиг не создан');
      } else {
        fs.writeFileSync('config-overrides.js', webpackConfig);
        log.success('Создан оптимизированный webpack конфиг');
        this.fixedIssues.push('Создан оптимизированный webpack конфиг');
      }

    } catch (error) {
      this.errors.push(`Ошибка оптимизации bundle: ${error.message}`);
    }
  }

  getAllJSFiles(dir) {
    const files = [];
    
    function traverse(currentDir) {
      try {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
          const fullPath = path.join(currentDir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            traverse(fullPath);
          } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx'))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Игнорируем ошибки доступа к директориям
      }
    }
    
    traverse(dir);
    return files;
  }

  generateReport() {
    log.header('ОТЧЕТ О БЫСТРОМ ИСПРАВЛЕНИИ');
    
    console.log(`${colors.green}✅ Исправлено проблем: ${this.fixedIssues.length}${colors.reset}`);
    console.log(`${colors.red}❌ Ошибок при исправлении: ${this.errors.length}${colors.reset}`);
    
    if (this.fixedIssues.length > 0) {
      console.log('\n📋 ИСПРАВЛЕННЫЕ ПРОБЛЕМЫ:');
      this.fixedIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n🚨 ОШИБКИ ПРИ ИСПРАВЛЕНИИ:');
      this.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    console.log('\n🔧 СЛЕДУЮЩИЕ ШАГИ:');
    console.log('1. Создайте .env файл с вашими Supabase ключами');
    console.log('2. Запустите: npm install (если нужно)');
    console.log('3. Запустите: npm run build');
    console.log('4. Запустите: node health-check.js для повторной проверки');
    
    console.log('\n📝 СОЗДАЙТЕ .env ФАЙЛ:');
    console.log('REACT_APP_SUPABASE_URL=https://your-project.supabase.co');
    console.log('REACT_APP_SUPABASE_ANON_KEY=your-anon-key');
    console.log('REACT_APP_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key');
  }
}

// Запуск исправлений
async function main() {
  const fixer = new QuickFixer();
  await fixer.runAllFixes();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = QuickFixer;
