const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function checkPendingProducts() {
  try {
    console.log('=== Checking Products Pending Moderation ===');
    
    const { data, error } = await supabase
      .from('products')
      .select('id, name, external_id, moderation_status, is_active, created_at, vendor')
      .eq('moderation_status', 'pending_approval')
      .order('created_at', { ascending: false })
      .limit(20);
    
    if (error) {
      console.error('Error fetching pending products:', error);
      return;
    }
    
    console.log(`Found ${data.length} products pending moderation:`);
    if (data.length > 0) {
      data.forEach((p, index) => {
        console.log(`${index + 1}. ${p.name}`);
        console.log(`   ID: ${p.id}, External ID: ${p.external_id}`);
        console.log(`   Vendor: ${p.vendor}, Active: ${p.is_active}`);
        console.log(`   Created: ${new Date(p.created_at).toLocaleString()}`);
        console.log('');
      });
    } else {
      console.log('No products are currently pending moderation.');
      
      // Check if there are any products with other statuses
      const { data: allProducts, error: allError } = await supabase
        .from('products')
        .select('moderation_status')
        .limit(10);
      
      if (!allError && allProducts.length > 0) {
        const statusCounts = {};
        allProducts.forEach(p => {
          statusCounts[p.moderation_status] = (statusCounts[p.moderation_status] || 0) + 1;
        });
        console.log('Product status breakdown (sample):', statusCounts);
      }
    }
    
  } catch (err) {
    console.error('Error:', err);
  }
}

checkPendingProducts();
