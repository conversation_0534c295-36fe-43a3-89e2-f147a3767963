#!/bin/bash

# Скрипт для тестирования автоматической системы обновления фидов

echo "🧪 Тестирование системы автоматического обновления фидов..."
echo ""

PROJECT_DIR="/Users/<USER>/e-com_new/online-store"
cd "$PROJECT_DIR"

echo "1️⃣ Проверка готовности проекта..."

# Проверяем наличие необходимых файлов
files=(
    ".github/workflows/feed-processing.yml"
    "supabase/functions/process-feed/index.ts"
    "scripts/migrations/setup-automatic-feed-processing.sql"
    "scripts/process-feeds.js"
    "src/utils/feedUtils.js"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file - НЕ НАЙДЕН!"
    fi
done

echo ""
echo "2️⃣ Проверка переменных окружения..."

if [ -f ".env.local" ]; then
    echo "  ✅ .env.local найден"
    
    # Проверяем наличие необходимых переменных
    if grep -q "REACT_APP_SUPABASE_URL" .env.local; then
        echo "  ✅ REACT_APP_SUPABASE_URL"
    else
        echo "  ❌ REACT_APP_SUPABASE_URL - НЕ НАЙДЕН!"
    fi
    
    if grep -q "REACT_APP_SUPABASE_ANON_KEY" .env.local; then
        echo "  ✅ REACT_APP_SUPABASE_ANON_KEY"
    else
        echo "  ❌ REACT_APP_SUPABASE_ANON_KEY - НЕ НАЙДЕН!"
    fi
    
    if grep -q "SUPABASE_SERVICE_ROLE_KEY" .env.local; then
        echo "  ✅ SUPABASE_SERVICE_ROLE_KEY"
    else
        echo "  ❌ SUPABASE_SERVICE_ROLE_KEY - НЕ НАЙДЕН!"
    fi
else
    echo "  ⚠️  .env.local не найден"
fi

echo ""
echo "3️⃣ Тестирование обработки фидов вручную..."

if command -v node &> /dev/null; then
    echo "  ✅ Node.js установлен"
    
    # Проверяем, есть ли активные фиды
    echo "  🔍 Проверка активных фидов..."
    
    if [ -f "scripts/process-feeds.js" ]; then
        echo "  🚀 Запускаем тестовую обработку фидов..."
        node scripts/process-feeds.js --dry-run
        
        if [ $? -eq 0 ]; then
            echo "  ✅ Тестовая обработка прошла успешно"
        else
            echo "  ❌ Ошибка в тестовой обработке"
        fi
    else
        echo "  ❌ scripts/process-feeds.js не найден"
    fi
else
    echo "  ❌ Node.js не установлен"
fi

echo ""
echo "4️⃣ Инструкции по настройке продакшена..."
echo ""
echo "📋 GitHub Actions:"
echo "   1. Перейти в Settings → Secrets and variables → Actions"
echo "   2. Добавить переменные:"
echo "      - REACT_APP_SUPABASE_URL"
echo "      - REACT_APP_SUPABASE_ANON_KEY"
echo "      - SUPABASE_SERVICE_ROLE_KEY"
echo "   3. Workflow будет запускаться автоматически каждые 6 часов"
echo ""
echo "📋 Supabase Edge Functions (альтернатива):"
echo "   1. npm install -g supabase"
echo "   2. supabase login"
echo "   3. supabase functions deploy process-feed"
echo "   4. Выполнить SQL миграцию: scripts/migrations/setup-automatic-feed-processing.sql"
echo ""
echo "📊 Мониторинг:"
echo "   - GitHub Actions: Repository → Actions → Automatic Feed Processing"
echo "   - Админ панель: /admin/feed-management"
echo "   - Логи: feed-processing.log (для локального запуска)"
echo ""
echo "🎯 Частота обновления: Каждые 6 часов (00:00, 06:00, 12:00, 18:00 UTC)"
echo ""
echo "✅ Система готова к работе в продакшене!"

# Создаем файл с инструкциями
cat > "PRODUCTION_SETUP_CHECKLIST.md" << 'EOF'
# ✅ Чек-лист настройки автоматического обновления фидов в продакшене

## 🚀 GitHub Actions (Рекомендуемый способ)

### 1. Настройка секретов:
- [ ] `Settings` → `Secrets and variables` → `Actions`
- [ ] Добавить `REACT_APP_SUPABASE_URL`
- [ ] Добавить `REACT_APP_SUPABASE_ANON_KEY`  
- [ ] Добавить `SUPABASE_SERVICE_ROLE_KEY`

### 2. Активация:
- [ ] Сделать коммит с файлом `.github/workflows/feed-processing.yml`
- [ ] Проверить в `Actions` что workflow появился
- [ ] Запустить тестовый запуск: `Actions` → `Automatic Feed Processing` → `Run workflow`

### 3. Мониторинг:
- [ ] Проверить логи в `Actions`
- [ ] Проверить обновление фидов в админ панели
- [ ] Настроить уведомления о сбоях (по желанию)

## 🔄 Альтернативный способ: Supabase Edge Functions

### 1. Установка Supabase CLI:
- [ ] `npm install -g supabase`
- [ ] `supabase login`

### 2. Деплой функции:
- [ ] `supabase functions deploy process-feed`
- [ ] Настроить переменные окружения в Supabase Dashboard

### 3. Настройка pg_cron:
- [ ] Выполнить SQL из `scripts/migrations/setup-automatic-feed-processing.sql`
- [ ] Проверить что cron задание создано: `SELECT * FROM cron.job;`

## 📊 Проверка работы

### Через SQL:
```sql
-- Проверить последние обновления фидов
SELECT name, last_fetched, 
       EXTRACT(EPOCH FROM (NOW() - last_fetched))/3600 as hours_ago
FROM feeds WHERE is_active = true;

-- Проверить задачи обработки
SELECT f.name, fj.status, fj.created_at, fj.items_created
FROM feed_jobs fj
JOIN feeds f ON fj.feed_id = f.id
ORDER BY fj.created_at DESC LIMIT 10;
```

### Через админ панель:
- [ ] Открыть `/admin/feed-management`
- [ ] Проверить колонку "Last Processed"
- [ ] Проверить колонку "Last Job"

## ⚙️ Настройка частоты (по желанию)

### GitHub Actions:
Изменить cron в `.github/workflows/feed-processing.yml`:
- `'0 */6 * * *'` - каждые 6 часов
- `'0 */4 * * *'` - каждые 4 часа  
- `'0 */12 * * *'` - каждые 12 часов

### pg_cron:
```sql
-- Удалить старое задание
SELECT cron.unschedule('process-feeds-every-6-hours');

-- Создать новое
SELECT cron.schedule('process-feeds-every-4-hours', '0 */4 * * *', 'SELECT process_all_active_feeds();');
```

## 🆘 Устранение проблем

### Если фиды не обновляются:
1. Проверить логи в GitHub Actions
2. Проверить секреты в GitHub
3. Проверить активность фидов в базе данных
4. Проверить доступность URL фидов

### Если есть ошибки 403:
1. Проверить SUPABASE_SERVICE_ROLE_KEY
2. Проверить RLS политики в Supabase
3. Убедиться что admin client работает корректно

✅ **Готово!** Фиды будут обновляться автоматически каждые 6 часов.
EOF

echo ""
echo "📝 Создан файл PRODUCTION_SETUP_CHECKLIST.md с подробными инструкциями"
echo ""
