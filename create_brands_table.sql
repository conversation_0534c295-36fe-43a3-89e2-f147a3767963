-- Enable UUID extension if not enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create brands table if it doesn't exist
CREATE TABLE IF NOT EXISTS brands (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  logo TEXT,
  description TEXT,
  website_url TEXT,
  display_order INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_brands_name ON brands(name);
CREATE INDEX IF NOT EXISTS idx_brands_featured ON brands(is_featured);

-- Insert demo data if table is empty
INSERT INTO brands (name, logo, description, website_url, is_featured)
SELECT 
  name, logo, 'Brand description', '#', is_featured
FROM (
  SELECT 
    unnest(ARRAY['Samsung', 'Apple', 'L<PERSON>', '<PERSON>', 'Sony']) AS name,
    unnest(ARRAY[
      'https://via.placeholder.com/200x100?text=Samsung',
      'https://via.placeholder.com/200x100?text=Apple',
      'https://via.placeholder.com/200x100?text=LG',
      'https://via.placeholder.com/200x100?text=Philips',
      'https://via.placeholder.com/200x100?text=Sony'
    ]) AS logo,
    unnest(ARRAY[true, true, true, true, true]) AS is_featured
) t
WHERE NOT EXISTS (SELECT 1 FROM brands LIMIT 1);
