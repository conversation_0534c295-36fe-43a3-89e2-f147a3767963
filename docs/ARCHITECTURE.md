# Архитектура проекта «Online Store"

## 1. Общий обзор

```plaintext
+---------------------+      +---------------------------+
|                     |      |                           |
|  Клиент (браузер)   +----->+  React-frontend (SPA/SSR) |
|                     |      |  - CRA (React)            |
+---------------------+      |  - Tailwind CSS           |
                             |  - Redux Toolkit          |
                             |  - React Router (SSR/CSR) |
                             +------------+--------------+
                                          |
                                          | HTTP / SPA Hydration
                                          v
                             +------------+--------------+
                             |  Express SSR-сервер       |
                             |  (server.js + ReactDOM)   |
                             +------------+--------------+
                                          |
                                          | REST / RPC / СQL скрипты
                                          v
+---------------------+      +------------+--------------+      +-------------+
|                     |      |                           |      |             |
|  Supabase (BaaS)    +----->+  Backend Scripts & API    +----->+  PostgreSQL |
|  - Auth, DB, Storage|      |  - src/scripts/database/  |      |  (Supabase) |
|                     |      |  - registration-api       |      |             |
+---------------------+      +---------------------------+      +-------------+
``` 

## 2. Модульная структура фронтенда (`src/`)

- **pages/** — страницы приложения (Home, Product, Cart, Admin и т.д.)
- **components/** — переиспользуемые UI-элементы и контролы
- **hooks/** — кастомные React-хуки (useAuth, useNotifications)
- **store/** — Redux Toolkit store, слайсы (authSlice, cartSlice и т.д.)
- **config/** — конфигурации (Supabase, роутинг, env)
- **scripts/** — утилиты и генераторы на стороне клиента (generate-sitemap)
- **utils/** — вспомогательные функции (feedUtils, imageHelpers)
- **context/** — React Context (AuthContext)

## 3. Серверная часть

- **server.js** — Express-сервер для SSR (раздача статики, хидратация)
- **router-ssr.js** — кастомная серверная маршрутизация
- **src/config/env.js** — валидация переменных окружения через envalid

## 4. Скрипты администрирования и миграции

- **src/scripts/database/** — миграции, создание таблиц, демо-данные
- **scripts/** — ad-hoc и вспомогательные скрипты (fix-format, check-pending-products)

## 5. CI/CD и тестирование

- **.github/workflows/ci.yml** — GitHub Actions workflow: lint, тесты, сборка
- **Jest + RTL** — unit-тесты (`src/components/**/*.test.js`)
- **Husky + lint-staged** — pre-commit хуки для ESLint и Prettier

---

Документ служит основой для понимания архитектуры. Пожалуйста, проверьте и дайте знать, если нужно поменять или добавить детали. 