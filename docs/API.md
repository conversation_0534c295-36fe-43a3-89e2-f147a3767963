# API Documentation

В этом документе описаны REST-эндпойнты сервера и примеры запросов/ответов для клиента и админ-панели.

## 1. Аутентификация

### POST /api/register
Регистрация пользователя.
- **URL:** `/api/register`
- **Метод:** POST
- **Тело запроса (JSON):**
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "<PERSON><PERSON><PERSON>"
  }
  ```
- **Успешный ответ (201 Created):**
  ```json
  {
    "id": "user-id",
    "email": "<EMAIL>",
    "created_at": "2025-07-03T...Z"
  }
  ```

### POST /api/login
Авторизация пользователя и получение токена.
- **URL:** `/api/login`
- **Метод:** POST
- **Тело запроса (JSON):**
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Успешный ответ (200 OK):**
  ```json
  {
    "accessToken": "jwt-token",
    "user": { "id": "user-id", "email": "<EMAIL>" }
  }
  ```

## 2. Товары

### GET /api/products
Получить список продуктов.
- **URL:** `/api/products`
- **Метод:** GET
- **Query Params:** `page`, `limit`, `categoryId`, `search`
- **Успешный ответ (200 OK):**
  ```json
  {
    "items": [ /* массив продуктов */ ],
    "total": 123,
    "page": 1,
    "limit": 20
  }
  ```

### GET /api/products/:id
Получить детали продукта.
- **URL:** `/api/products/{id}`
- **Метод:** GET
- **Успешный ответ (200 OK):**
  ```json
  {
    "id": "prod-12",
    "title": "Название товара",
    "price": 100.0,
    "description": "Описание",
    "images": [ "url1", "url2" ]
  }
  ```

## 3. Категории

### GET /api/categories
Получить список категорий.
- **URL:** `/api/categories`
- **Метод:** GET
- **Успешный ответ (200 OK):**
  ```json
  [ { "id": "cat-1", "name": "Категория 1" }, ... ]
  ```

## 4. Заказы

### POST /api/orders
Создать заказ.
- **URL:** `/api/orders`
- **Метод:** POST
- **Тело запроса (JSON):**
  ```json
  {
    "userId": "user-id",
    "items": [ { "productId": "prod-1", "quantity": 2 } ],
    "address": "Адрес доставки"
  }
  ```
- **Успешный ответ (201 Created):**
  ```json
  { "orderId": "order-123", "status": "pending" }
  ```

## 5. Админ-панель

### GET /api/admin/users
Получить список пользователей (только для админа).

### PUT /api/admin/products/:id
Обновить информацию о продукте.

*…дополнить по мере появления эндпойнтов…* 