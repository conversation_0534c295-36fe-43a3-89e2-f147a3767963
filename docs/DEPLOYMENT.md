# Руководство по деплою

## Предварительные требования

- Node.js >= 18 и npm
- Docker и Docker Compose (для контейнеризации)
- До<PERSON><PERSON>у<PERSON> к Kubernetes-кластеру (kubectl)
- CLI облачного провайдера (AWS CLI, gcloud, Azure CLI и т.п.)

## 1. Сборка приложения

1. Установить зависимости:
   ```bash
   npm install
   ```
2. Собрать фронтенд:
   ```bash
   npm run build
   ```

## 2. Docker

### Dockerfile

```dockerfile
# 1. Сборка фронтенда
FROM node:18 AS build
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci
COPY . .
RUN npm run build

# 2. Сервер для SSR
FROM node:18-alpine
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci --production
COPY --from=build /app/build ./build
COPY server.js ./server.js
COPY src/config/env.js ./src/config/env.js
ENV NODE_ENV=production
EXPOSE 3000
CMD ["node", "server.js"]
```

### docker-compose.yml

```yaml
version: '3.8'
services:
  online-store:
    build: .
    ports:
      - '3000:3000'
    environment:
      - PORT=3000
      - REACT_APP_SUPABASE_URL
      - REACT_APP_SUPABASE_ANON_KEY
      - REACT_APP_SUPABASE_SERVICE_ROLE_KEY
      - REACT_APP_API_PORT
```

## 3. Kubernetes

### deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: online-store
spec:
  replicas: 2
  selector:
    matchLabels:
      app: online-store
  template:
    metadata:
      labels:
        app: online-store
    spec:
      containers:
        - name: online-store
          image: your-registry/online-store:latest
          ports:
            - containerPort: 3000
          env:
            - name: PORT
              value: "3000"
            - name: REACT_APP_SUPABASE_URL
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: url
            # ... другие переменные окружения через Secret или ConfigMap
```

### service.yaml

```yaml
apiVersion: v1
kind: Service
metadata:
  name: online-store
spec:
  type: ClusterIP
  selector:
    app: online-store
  ports:
    - port: 80
      targetPort: 3000
```

## 4. Облачный деплой

- Vercel/Netlify: настройте **Build Command**: `npm run build`, **Publish Directory**: `build/`
- AWS/GCP/Azure:
  1. `docker build -t your-registry/online-store:latest .`
  2. `docker push your-registry/online-store:latest`
  3. `kubectl apply -f kubernetes/` (или ваш манифест)

---

Этот документ можно расширить под конкретную инфраструктуру и CI/CD-пайплайны. 