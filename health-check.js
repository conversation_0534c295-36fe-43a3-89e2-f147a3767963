#!/usr/bin/env node

/**
 * Комплексная проверка здоровья проекта Online Store
 * Проверяет все критические компоненты системы
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Цвета для консоли
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`\n${colors.bold}${colors.blue}🔍 ${msg}${colors.reset}\n`)
};

class HealthChecker {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      issues: []
    };
    
    // Инициализация Supabase клиента
    this.supabase = createClient(
      process.env.REACT_APP_SUPABASE_URL || 'https://dmdijuuwnbwngerkbfak.supabase.co',
      process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU'
    );
  }

  async runAllChecks() {
    log.header('ЗАПУСК КОМПЛЕКСНОЙ ПРОВЕРКИ СИСТЕМЫ');
    
    await this.checkEnvironment();
    await this.checkDatabase();
    await this.checkAuthentication();
    await this.checkProducts();
    await this.checkOrders();
    await this.checkCodeQuality();
    await this.checkSecurity();
    await this.checkPerformance();
    
    this.generateReport();
  }

  async checkEnvironment() {
    log.header('Проверка окружения');
    
    // Проверка package.json
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      log.success('package.json найден и валиден');
      
      // Проверка критических зависимостей
      const criticalDeps = ['react', '@supabase/supabase-js', 'react-router-dom'];
      for (const dep of criticalDeps) {
        if (packageJson.dependencies[dep]) {
          log.success(`Зависимость ${dep} установлена`);
        } else {
          this.addIssue('critical', `Отсутствует критическая зависимость: ${dep}`);
        }
      }
      
    } catch (error) {
      this.addIssue('critical', 'Не удается прочитать package.json');
    }

    // Проверка переменных окружения
    const envVars = ['REACT_APP_SUPABASE_URL', 'REACT_APP_SUPABASE_ANON_KEY'];
    for (const envVar of envVars) {
      if (process.env[envVar]) {
        log.success(`Переменная окружения ${envVar} установлена`);
      } else {
        this.addIssue('warning', `Переменная окружения ${envVar} не установлена`);
      }
    }

    // Проверка структуры проекта
    const requiredDirs = ['src', 'public', 'src/components', 'src/pages', 'src/context'];
    for (const dir of requiredDirs) {
      if (fs.existsSync(dir)) {
        log.success(`Директория ${dir} существует`);
      } else {
        this.addIssue('critical', `Отсутствует директория: ${dir}`);
      }
    }
  }

  async checkDatabase() {
    log.header('Проверка базы данных');
    
    try {
      // Проверка подключения
      const { data, error } = await this.supabase.from('products').select('count', { count: 'exact', head: true });
      
      if (error) {
        this.addIssue('critical', `Ошибка подключения к БД: ${error.message}`);
        return;
      }
      
      log.success('Подключение к Supabase установлено');
      
      // Проверка основных таблиц
      const tables = ['products', 'categories', 'orders', 'order_items', 'profiles'];
      for (const table of tables) {
        try {
          const { error: tableError } = await this.supabase.from(table).select('*').limit(1);
          if (tableError) {
            this.addIssue('critical', `Таблица ${table} недоступна: ${tableError.message}`);
          } else {
            log.success(`Таблица ${table} доступна`);
          }
        } catch (err) {
          this.addIssue('critical', `Ошибка проверки таблицы ${table}: ${err.message}`);
        }
      }
      
    } catch (error) {
      this.addIssue('critical', `Критическая ошибка БД: ${error.message}`);
    }
  }

  async checkAuthentication() {
    log.header('Проверка системы аутентификации');
    
    try {
      // Проверка сессии
      const { data: session, error } = await this.supabase.auth.getSession();
      
      if (error) {
        this.addIssue('warning', `Ошибка получения сессии: ${error.message}`);
      } else {
        log.success('Система аутентификации работает');
      }
      
      // Проверка RLS политик
      const { data: policies, error: policiesError } = await this.supabase.rpc('exec_sql', {
        query: "SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public'"
      });
      
      if (!policiesError && policies) {
        log.success(`Найдено ${policies.length} RLS политик`);
      } else {
        this.addIssue('warning', 'Не удается проверить RLS политики');
      }
      
    } catch (error) {
      this.addIssue('warning', `Ошибка проверки аутентификации: ${error.message}`);
    }
  }

  async checkProducts() {
    log.header('Проверка системы товаров');
    
    try {
      // Проверка товаров
      const { data: products, error } = await this.supabase
        .from('products')
        .select('*')
        .limit(10);
      
      if (error) {
        this.addIssue('critical', `Ошибка загрузки товаров: ${error.message}`);
        return;
      }
      
      if (products && products.length > 0) {
        log.success(`Найдено ${products.length} товаров`);
        
        // Проверка структуры товаров
        const requiredFields = ['id', 'name', 'price'];
        const firstProduct = products[0];
        
        for (const field of requiredFields) {
          if (firstProduct[field] !== undefined) {
            log.success(`Поле ${field} присутствует в товарах`);
          } else {
            this.addIssue('warning', `Отсутствует поле ${field} в товарах`);
          }
        }
      } else {
        this.addIssue('warning', 'В базе данных нет товаров');
      }
      
    } catch (error) {
      this.addIssue('critical', `Критическая ошибка системы товаров: ${error.message}`);
    }
  }

  async checkOrders() {
    log.header('Проверка системы заказов');
    
    try {
      // Проверка таблицы заказов
      const { data: orders, error } = await this.supabase
        .from('orders')
        .select('*')
        .limit(5);
      
      if (error) {
        this.addIssue('warning', `Ошибка загрузки заказов: ${error.message}`);
        return;
      }
      
      log.success('Система заказов доступна');
      
      if (orders && orders.length > 0) {
        log.success(`Найдено ${orders.length} заказов`);
      } else {
        log.info('Заказов пока нет (это нормально для нового проекта)');
      }
      
    } catch (error) {
      this.addIssue('warning', `Ошибка проверки заказов: ${error.message}`);
    }
  }

  checkCodeQuality() {
    log.header('Проверка качества кода');
    
    // Проверка на console.log в продакшн коде
    const srcFiles = this.getAllJSFiles('src');
    let consoleLogCount = 0;
    
    for (const file of srcFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const matches = content.match(/console\.log/g);
        if (matches) {
          consoleLogCount += matches.length;
        }
      } catch (error) {
        // Игнорируем ошибки чтения файлов
      }
    }
    
    if (consoleLogCount > 0) {
      this.addIssue('warning', `Найдено ${consoleLogCount} console.log в коде`);
    } else {
      log.success('Console.log не найдены в коде');
    }
    
    // Проверка на TODO/FIXME
    let todoCount = 0;
    for (const file of srcFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const matches = content.match(/TODO|FIXME|Temporarily/gi);
        if (matches) {
          todoCount += matches.length;
        }
      } catch (error) {
        // Игнорируем ошибки чтения файлов
      }
    }
    
    if (todoCount > 0) {
      this.addIssue('warning', `Найдено ${todoCount} TODO/FIXME комментариев`);
    } else {
      log.success('TODO/FIXME комментарии не найдены');
    }
  }

  checkSecurity() {
    log.header('Проверка безопасности');
    
    // Проверка на хардкод ключей
    try {
      const supabaseClientContent = fs.readFileSync('src/supabaseClient.js', 'utf8');
      
      if (supabaseClientContent.includes('eyJ') || supabaseClientContent.includes('https://')) {
        this.addIssue('critical', 'КРИТИЧЕСКАЯ УЯЗВИМОСТЬ: API ключи захардкожены в коде!');
      } else {
        log.success('API ключи не найдены в коде');
      }
      
    } catch (error) {
      this.addIssue('warning', 'Не удается проверить supabaseClient.js');
    }
    
    // Проверка .env файла
    if (fs.existsSync('.env')) {
      log.success('.env файл существует');
    } else {
      this.addIssue('warning', '.env файл не найден');
    }
    
    // Проверка .gitignore
    if (fs.existsSync('.gitignore')) {
      const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
      if (gitignoreContent.includes('.env')) {
        log.success('.env файл добавлен в .gitignore');
      } else {
        this.addIssue('critical', '.env файл НЕ добавлен в .gitignore!');
      }
    } else {
      this.addIssue('warning', '.gitignore файл не найден');
    }
  }

  checkPerformance() {
    log.header('Проверка производительности');
    
    // Проверка размера bundle
    if (fs.existsSync('build/static/js')) {
      const jsFiles = fs.readdirSync('build/static/js');
      const mainJsFile = jsFiles.find(file => file.startsWith('main.') && file.endsWith('.js'));
      
      if (mainJsFile) {
        const stats = fs.statSync(`build/static/js/${mainJsFile}`);
        const sizeKB = Math.round(stats.size / 1024);
        
        if (sizeKB > 500) {
          this.addIssue('warning', `Большой размер bundle: ${sizeKB}KB`);
        } else {
          log.success(`Размер bundle: ${sizeKB}KB (приемлемо)`);
        }
      }
    } else {
      log.info('Build не найден, запустите npm run build для проверки размера bundle');
    }
    
    // Проверка на неиспользуемые зависимости
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const depCount = Object.keys(packageJson.dependencies || {}).length;
      const devDepCount = Object.keys(packageJson.devDependencies || {}).length;
      
      log.info(`Зависимостей: ${depCount}, dev-зависимостей: ${devDepCount}`);
      
      if (depCount > 50) {
        this.addIssue('warning', `Много зависимостей (${depCount}), возможно есть неиспользуемые`);
      }
      
    } catch (error) {
      // Игнорируем ошибки
    }
  }

  getAllJSFiles(dir) {
    const files = [];
    
    function traverse(currentDir) {
      try {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
          const fullPath = path.join(currentDir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            traverse(fullPath);
          } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx'))) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Игнорируем ошибки доступа к директориям
      }
    }
    
    traverse(dir);
    return files;
  }

  addIssue(severity, message) {
    this.results.issues.push({ severity, message });
    
    if (severity === 'critical') {
      this.results.failed++;
      log.error(message);
    } else if (severity === 'warning') {
      this.results.warnings++;
      log.warning(message);
    }
  }

  generateReport() {
    log.header('ИТОГОВЫЙ ОТЧЕТ');
    
    console.log(`${colors.green}✅ Успешных проверок: ${this.results.passed}${colors.reset}`);
    console.log(`${colors.red}❌ Критических ошибок: ${this.results.failed}${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Предупреждений: ${this.results.warnings}${colors.reset}`);
    
    if (this.results.issues.length > 0) {
      console.log('\n📋 СПИСОК ПРОБЛЕМ:');
      
      const criticalIssues = this.results.issues.filter(i => i.severity === 'critical');
      const warningIssues = this.results.issues.filter(i => i.severity === 'warning');
      
      if (criticalIssues.length > 0) {
        console.log('\n🚨 КРИТИЧЕСКИЕ ПРОБЛЕМЫ:');
        criticalIssues.forEach((issue, index) => {
          console.log(`${index + 1}. ${issue.message}`);
        });
      }
      
      if (warningIssues.length > 0) {
        console.log('\n⚠️  ПРЕДУПРЕЖДЕНИЯ:');
        warningIssues.forEach((issue, index) => {
          console.log(`${index + 1}. ${issue.message}`);
        });
      }
    }
    
    // Общая оценка
    const totalIssues = this.results.failed + this.results.warnings;
    let healthScore = 'ОТЛИЧНОЕ';
    
    if (this.results.failed > 0) {
      healthScore = 'КРИТИЧЕСКОЕ';
    } else if (this.results.warnings > 5) {
      healthScore = 'ТРЕБУЕТ ВНИМАНИЯ';
    } else if (this.results.warnings > 0) {
      healthScore = 'ХОРОШЕЕ';
    }
    
    console.log(`\n🏥 СОСТОЯНИЕ ПРОЕКТА: ${healthScore}`);
    
    if (this.results.failed > 0) {
      console.log('\n🔧 РЕКОМЕНДАЦИИ:');
      console.log('1. Исправьте критические ошибки перед развертыванием');
      console.log('2. Проверьте подключение к базе данных');
      console.log('3. Убедитесь, что все переменные окружения настроены');
    }
  }
}

// Запуск проверки
async function main() {
  const checker = new HealthChecker();
  await checker.runAllChecks();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = HealthChecker;
