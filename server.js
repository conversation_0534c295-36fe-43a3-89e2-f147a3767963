const express = require('express');
const fs = require('fs');
const path = require('path');
const React = require('react');
const ReactDOMServer = require('react-dom/server');
const { St<PERSON>Router } = require('react-router-dom/server');
const { HelmetProvider } = require('react-helmet-async');
const env = require('./src/config/env');

// Определяем порт
const PORT = env.PORT;
const app = express();

// Статические файлы из build
app.use(express.static(path.resolve(__dirname, 'build')));

// Обрабатываем все GET запросы
app.get('/*', (req, res) => {
  // Импортируем компонент App (будет заменен в runtime)
  const AppSSR = () => React.createElement('div', null, 'Server Rendering...');
  
  // Для полноценного SSR потребуется настройка Babel
  // Сейчас мы используем гидратацию - сервер отдает статический HTML, а клиент "оживляет" его
  
  // Получаем содержимое HTML файла
  const indexFile = path.resolve('./build/index.html');
  fs.readFile(indexFile, 'utf8', (err, data) => {
    if (err) {
      console.error('Ошибка чтения файла index.html:', err);
      return res.status(500).send('Ошибка сервера при рендеринге');
    }
    
    // Базовые метатеги для SEO
    let seoTags = '';
    
    // Добавляем динамические метатеги на основе URL
    if (req.url.includes('/product/')) {
      seoTags = `
        <meta name="description" content="Подробная информация о товаре в нашем интернет-магазине" />
        <meta name="keywords" content="товар, интернет-магазин, купить" />
        <meta property="og:type" content="product" />
        <meta property="og:title" content="Товар - Интернет магазин" />
        <meta property="og:description" content="Подробная информация о товаре в нашем интернет-магазине" />
      `;
    } else if (req.url.includes('/category/')) {
      seoTags = `
        <meta name="description" content="Каталог товаров в категории - широкий выбор!" />
        <meta name="keywords" content="категория, каталог, товары, купить" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Категория товаров - Интернет магазин" />
        <meta property="og:description" content="Каталог товаров в категории - широкий выбор!" />
      `;
    } else if (req.url === '/') {
      seoTags = `
        <meta name="description" content="Интернет-магазин - широкий выбор товаров с доставкой" />
        <meta name="keywords" content="интернет-магазин, товары, доставка, купить" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Главная - Интернет магазин" />
        <meta property="og:description" content="Интернет-магазин - широкий выбор товаров с доставкой" />
      `;
    }
    
    // Добавляем базовые метатеги для всех страниц
    seoTags += `
      <meta name="robots" content="index, follow" />
      <meta name="author" content="Интернет-магазин" />
      <link rel="canonical" href="https://yourdomain.com${req.url}" />
    `;
    
    // Вставляем метатеги SEO в HTML
    const html = data.replace('</head>', `${seoTags}</head>`);
    
    return res.send(html);
  });
});

// Создаем обработчик для всех неизвестных маршрутов - для клиентской маршрутизации
app.get('*', (req, res) => {
  res.sendFile(path.resolve(__dirname, 'build', 'index.html'));
});

// Запускаем сервер
app.listen(PORT, () => {
  console.log(`Сервер с SSR запущен на порту ${PORT}`);
  console.log(`Откройте http://localhost:${PORT} в браузере`);
}); 