# 📧 НАСТРОЙКА EMAIL-УВЕДОМЛЕНИЙ

## Проблема
Email-уведомления не отправляются, потому что не настроен `RESEND_API_KEY` в Supabase Edge Functions.

## ✅ Что у нас уже есть:
- ✅ Supabase Edge Function `send-email` создана
- ✅ EmailService настроен в frontend 
- ✅ Заказы создаются успешно
- ✅ Код для отправки email написан

## 🔧 Что нужно сделать:

### Шаг 1: Получить Resend API ключ

1. **Зарегистрируйтесь на [resend.com](https://resend.com)**
2. **Создайте API ключ:**
   - Войдите в панель Resend
   - Перейдите в "API Keys"
   - Нажмите "Create API Key"
   - Скопируйте ключ (он будет показан только один раз!)

### Шаг 2: Добавить переменные окружения в Supabase

1. **Откройте Supabase Dashboard:**
   - Перейдите на [supabase.com](https://supabase.com)
   - Войдите в свой проект `dmdijuuwnbwngerkbfak`

2. **Настройте переменные окружения:**
   - Перейдите в `Settings` → `Edge Functions`
   - Нажмите `Add new secret`
   - Добавьте переменные:

```
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxx
FROM_EMAIL=<EMAIL>
```

*Если у вас нет домена, используйте:*
```
FROM_EMAIL=<EMAIL>
```

### Шаг 3: Повторно развернуть Edge Function

```bash
cd /Users/<USER>/e-com_new/online-store
npx supabase functions deploy send-email
```

### Шаг 4: Протестировать

После настройки протестируйте:

1. **Создайте тестовый заказ** в веб-интерфейсе
2. **Или используйте админ панель** → Email System Diagnostic

## 🧪 Быстрый тест

Запустите тест email-системы:

```bash
node test-email-system.js
```

Если все настроено правильно, вы увидите:
```
✅ send-email function responded successfully
```

## 🚨 Альтернативное решение (если нет доступа к Resend)

Если не можете настроить Resend, добавим мок-сервис:

```bash
# Создать временный мок email-сервис
echo "Создание мок-сервиса..."
```

## 📞 Результат

После настройки:
- ✅ Email подтверждения заказов будут отправляться автоматически
- ✅ Уведомления об изменении статуса будут работать
- ✅ Админ панель сможет отправлять тестовые письма

---

**🎯 Следующий шаг: Получите Resend API ключ и добавьте его в Supabase!**
