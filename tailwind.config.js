/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './public/index.html',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        heading: ['Poppins', 'sans-serif'],
      },
      colors: {
        background: '#F5F5F5',          // светлый кремовый фон всей страницы
        cardBackground: '#EDE4D8',      // более насыщенный кремовый для карточек товаров
        sectionBackground: '#EDE4D8',   // фон секций (баннеры, блоки)
        
        primary: '#D4B27D',             // золотисто-коричневый для кнопок (CTA)
        'primary-dark': '#C5A36E',      // темнее для hover состояний
        primaryText: '#FFFFFF',         // белый текст на кнопках
        
        heading: '#000000',             // черный для заголовков
        body: '#333333',                // темно-серый для основного текста
        muted: '#666666',               // светло-серый для вторичного текста
        
        star: '#D4B27D',                // золотисто-коричневый для звезд рейтинга
        
        footerBg: '#F5F5F5',            // светлый кремовый фон футера
        footerText: '#333333',          // темно-серый текст футера
        
        border: '#CCCCCC',              // цвет рамок и разделителей
        shadow: 'rgba(0, 0, 0, 0.05)',  // лёгкие тени
      },
      fontSize: {
        'h1': '3rem',         // Заголовки первого уровня
        'h2': '2.25rem',      // Заголовки второго уровня
        'h3': '1.875rem',     // Заголовки третьего уровня
        'base': '1rem',       // Основной текст
        'lg': '1.125rem',     // Крупный текст
        'sm': '0.875rem',     // Мелкий текст
      },
      aspectRatio: {
        banner: '21/9',
        product: '3/4'
      },
      height: {
        banner: '500px',
        'category-card': '250px',
        '128': '32rem',  // For medium screens (2x original)
        '160': '40rem',  // For large screens (2x original)
      },
      boxShadow: {
        'custom': '0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      },
      keyframes: {
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' }
        }
      },
      animation: {
        slideUp: 'slideUp 0.3s ease-out forwards',
      }
    }
  },
  plugins: [
    // Safely require plugins
    ...(() => {
      const plugins = [];
      try {
        plugins.push(require('@tailwindcss/aspect-ratio'));
        plugins.push(require('@tailwindcss/typography'));
      } catch (e) {
        console.warn('Some Tailwind plugins are missing. Run npm install to fix this.');
      }
      return plugins;
    })()
  ]
};
