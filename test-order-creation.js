#!/usr/bin/env node

// Quick test of order creation functionality
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testOrderCreation() {
    console.log('🧪 Testing order creation functionality...\n');
    
    try {
        // Test 1: Database connection
        console.log('1️⃣  Testing database connection...');
        const { data: connectionTest, error: connectionError } = await supabase
            .from('orders')
            .select('count')
            .limit(1);
            
        if (connectionError) {
            console.log('   ❌ Connection failed:', connectionError.message);
            return;
        } else {
            console.log('   ✅ Database connection successful');
        }
        
        // Test 2: Check table structure
        console.log('\n2️⃣  Checking table structure...');
        const { data: tableData, error: tableError } = await supabase
            .from('orders')
            .select('*')
            .limit(1);
            
        if (tableError) {
            console.log('   ❌ Table access failed:', tableError.message);
            
            // Try to create the table if it doesn't exist
            if (tableError.code === '42P01') {
                console.log('   🔨 Table does not exist, attempting to create...');
                
                // Create orders table
                const createTableQuery = `
                    CREATE TABLE IF NOT EXISTS public.orders (
                        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                        customer_name TEXT NOT NULL,
                        customer_email TEXT,
                        customer_phone TEXT NOT NULL,
                        shipping_address JSONB,
                        total_amount DECIMAL(10, 2) NOT NULL,
                        status TEXT DEFAULT 'pending',
                        payment_method TEXT,
                        payment_status TEXT DEFAULT 'pending',
                        notes TEXT,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
                    );
                    
                    -- Enable RLS but make it permissive for now
                    ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
                    
                    -- Create permissive policy for all operations
                    DROP POLICY IF EXISTS "Allow all operations" ON public.orders;
                    CREATE POLICY "Allow all operations" ON public.orders FOR ALL USING (true) WITH CHECK (true);
                    
                    -- Create order_items table
                    CREATE TABLE IF NOT EXISTS public.order_items (
                        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                        order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
                        product_id UUID,
                        product_name TEXT NOT NULL,
                        product_price DECIMAL(10, 2) NOT NULL,
                        quantity INTEGER NOT NULL DEFAULT 1,
                        subtotal DECIMAL(10, 2) NOT NULL,
                        product_image TEXT,
                        product_category TEXT,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
                    );
                    
                    -- Enable RLS for order_items
                    ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
                    DROP POLICY IF EXISTS "Allow all operations" ON public.order_items;
                    CREATE POLICY "Allow all operations" ON public.order_items FOR ALL USING (true) WITH CHECK (true);
                `;
                
                const { error: createError } = await supabase.rpc('exec', { 
                    sql: createTableQuery 
                });
                
                if (createError) {
                    console.log('   ⚠️  Could not create table automatically:', createError.message);
                    console.log('   📝 Please run the SQL script manually in Supabase dashboard');
                } else {
                    console.log('   ✅ Table created successfully');
                }
            }
        } else {
            console.log('   ✅ Table structure accessible');
        }
        
        // Test 3: Try to insert a test order
        console.log('\n3️⃣  Testing order insertion...');
        
        const testOrder = {
            customer_name: 'Test Customer',
            customer_phone: '+1234567890',
            customer_email: '<EMAIL>',
            total_amount: 99.99,
            status: 'pending',
            shipping_address: {
                street: '123 Test St',
                city: 'Test City',
                state: 'TC',
                zip: '12345'
            }
        };
        
        const { data: insertData, error: insertError } = await supabase
            .from('orders')
            .insert(testOrder)
            .select()
            .single();
            
        if (insertError) {
            console.log('   ❌ Order insertion failed:', insertError.message);
            console.log('   Code:', insertError.code);
            console.log('   Details:', insertError.details);
            
            if (insertError.code === '42501') {
                console.log('   🔑 This is likely a permissions/RLS issue');
                console.log('   💡 Suggestion: Check RLS policies in Supabase dashboard');
            }
        } else {
            console.log('   ✅ Order created successfully!');
            console.log('   📋 Order ID:', insertData.id);
            console.log('   👤 Customer:', insertData.customer_name);
            console.log('   💰 Amount:', `$${insertData.total_amount}`);
            
            // Clean up test record
            const { error: deleteError } = await supabase
                .from('orders')
                .delete()
                .eq('id', insertData.id);
                
            if (!deleteError) {
                console.log('   🧹 Test record cleaned up');
            }
        }
        
        console.log('\n🎉 Order creation test completed!');
        
    } catch (error) {
        console.error('❌ Unexpected error during testing:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Run the test
testOrderCreation().then(() => {
    process.exit(0);
}).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
