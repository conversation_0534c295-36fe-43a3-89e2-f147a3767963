# РЕШЕНИЕ ПРОБЛЕМЫ СОЗДАНИЯ ЗАКАЗОВ 

## Проблема
Ошибка: `schema "net" does not exist` при создании заказов

## Причина
Проблема вызвана триггерами электронной почты, которые используют `net.http_post()` - функция PostgreSQL extension `net`, которая не установлена в Supabase.

## Решение

### СПОСОБ 1: Через SQL Editor в Supabase Dashboard
1. Откройте Supabase Dashboard
2. Перейдите в SQL Editor
3. Выполните следующий SQL код:

```sql
-- Удаление проблематичных триггеров
DROP TRIGGER IF EXISTS trigger_send_order_confirmation_email ON orders;
DROP TRIGGER IF EXISTS trigger_send_status_update_email ON orders;

-- Удаление функций триггеров
DROP FUNCTION IF EXISTS send_order_confirmation_email();
DROP FUNCTION IF EXISTS send_status_update_email();

-- Обновление схемы
NOTIFY pgrst, 'reload schema';
```

### СПОСОБ 2: Через компонент DatabaseHealthCheck
1. Запустите приложение React
2. Перейдите в админ-панель
3. Откройте "Диагностика базы данных"
4. Нажмите кнопку "🛠️ Исправить заказы"

### Проверка исправления
После выполнения исправления протестируйте создание заказа:

```javascript
// Тестовый код для проверки
const testOrder = {
  customer_name: 'Test Customer',
  customer_email: '<EMAIL>',
  customer_phone: '+**********',
  shipping_address: { city: 'Test City' },
  total_amount: 100.00,
  status: 'pending',
  payment_method: 'cash_on_delivery'
};

// Должно работать без ошибок
const { data, error } = await supabase
  .from('orders')
  .insert([testOrder])
  .select()
  .single();
```

## Важно
После исправления электронные уведомления будут отправляться через фронтенд (EmailService), а не через триггеры базы данных. Это более надежный подход.

## Статус
- ✅ Проблема идентифицирована
- ✅ Решение подготовлено  
- ⏳ Требуется применение через SQL Editor
- ⏳ Требуется тестирование
