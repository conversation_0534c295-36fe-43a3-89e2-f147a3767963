const { createClient } = require('@supabase/supabase-js');

// Use actual credentials from supabaseClient.js
const supabaseUrl = 'https://dmdijuuwnbwngerkbfak.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._WInPtUcmHOnYRqlcHkc_6SpnhJQ6QZ8-kNQoSoDvaU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseHealth() {
  console.log('🔍 Running comprehensive database health check...\n');
  
  const results = {
    connection: false,
    tables: {},
    functions: {},
    relationships: {},
    foreignKeys: {},
    summary: { total: 0, passed: 0, failed: 0, warnings: 0 }
  };

  // 1. Test database connection
  try {
    console.log('1️⃣ Testing database connection...');
    const { error } = await supabase.from('profiles').select('count', { count: 'exact', head: true });
    
    if (error && error.code !== 'PGRST116') {
      throw error;
    }
    
    results.connection = true;
    console.log('✅ Database connection: PASSED\n');
  } catch (error) {
    console.log('❌ Database connection: FAILED -', error.message, '\n');
  }

  // 2. Test required tables
  console.log('2️⃣ Testing required tables...');
  const requiredTables = ['profiles', 'products', 'categories', 'orders', 'order_items', 'brands', 'banners', 'email_logs'];
  
  for (const table of requiredTables) {
    try {
      const { error } = await supabase.from(table).select('*', { count: 'exact', head: true });
      
      if (error) {
        results.tables[table] = { status: 'failed', error: error.message };
        console.log(`❌ Table ${table}: FAILED - ${error.message}`);
      } else {
        results.tables[table] = { status: 'passed' };
        console.log(`✅ Table ${table}: PASSED`);
      }
    } catch (error) {
      results.tables[table] = { status: 'failed', error: error.message };
      console.log(`❌ Table ${table}: FAILED - ${error.message}`);
    }
  }
  console.log('');

  // 3. Test exec_sql function
  console.log('3️⃣ Testing exec_sql function...');
  try {
    const { error } = await supabase.rpc('exec_sql', { query_text: 'SELECT 1' });
    
    if (error) {
      if (error.code === 'PGRST203') {
        results.functions.exec_sql = { status: 'passed', note: 'Multiple signatures available (normal)' };
        console.log('✅ exec_sql function: PASSED (multiple signatures detected - normal after fix)');
      } else {
        results.functions.exec_sql = { status: 'warning', error: error.message };
        console.log(`⚠️ exec_sql function: WARNING - ${error.message}`);
      }
    } else {
      results.functions.exec_sql = { status: 'passed' };
      console.log('✅ exec_sql function: PASSED');
    }
  } catch (error) {
    results.functions.exec_sql = { status: 'failed', error: error.message };
    console.log(`❌ exec_sql function: FAILED - ${error.message}`);
  }
  console.log('');

  // 4. Test get_foreign_keys helper function
  console.log('4️⃣ Testing get_foreign_keys helper function...');
  try {
    const { data, error } = await supabase.rpc('get_foreign_keys', { table_name_param: 'orders' });
    
    if (error) {
      results.functions.get_foreign_keys = { status: 'failed', error: error.message };
      console.log(`❌ get_foreign_keys function: FAILED - ${error.message}`);
    } else {
      results.functions.get_foreign_keys = { status: 'passed', data };
      console.log('✅ get_foreign_keys function: PASSED');
      
      if (data && data.length > 0) {
        console.log('📋 Foreign keys found for orders table:');
        data.forEach(fk => {
          console.log(`   - ${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
        });
      } else {
        console.log('⚠️ No foreign keys found for orders table');
      }
    }
  } catch (error) {
    results.functions.get_foreign_keys = { status: 'failed', error: error.message };
    console.log(`❌ get_foreign_keys function: FAILED - ${error.message}`);
  }
  console.log('');

  // 5. Test relationships
  console.log('5️⃣ Testing table relationships...');
  
  // Test products -> categories
  try {
    const { error } = await supabase
      .from('products')
      .select('id, category_id, categories(name)')
      .limit(1);
    
    if (error) {
      results.relationships.products_categories = { status: 'failed', error: error.message };
      console.log(`❌ products->categories relationship: FAILED - ${error.message}`);
    } else {
      results.relationships.products_categories = { status: 'passed' };
      console.log('✅ products->categories relationship: PASSED');
    }
  } catch (error) {
    results.relationships.products_categories = { status: 'failed', error: error.message };
    console.log(`❌ products->categories relationship: FAILED - ${error.message}`);
  }
  
  // Test orders -> profiles
  try {
    const { error } = await supabase
      .from('orders')
      .select('id, user_id, profiles(email)')
      .limit(1);
    
    if (error) {
      results.relationships.orders_profiles = { status: 'warning', error: error.message };
      console.log(`⚠️ orders->profiles relationship: WARNING - ${error.message}`);
    } else {
      results.relationships.orders_profiles = { status: 'passed' };
      console.log('✅ orders->profiles relationship: PASSED');
    }
  } catch (error) {
    results.relationships.orders_profiles = { status: 'failed', error: error.message };
    console.log(`❌ orders->profiles relationship: FAILED - ${error.message}`);
  }
  console.log('');

  // 6. Calculate summary
  const allChecks = [
    results.connection ? { status: 'passed' } : { status: 'failed' },
    ...Object.values(results.tables),
    ...Object.values(results.functions),
    ...Object.values(results.relationships)
  ];

  results.summary.total = allChecks.length;
  results.summary.passed = allChecks.filter(c => c.status === 'passed').length;
  results.summary.failed = allChecks.filter(c => c.status === 'failed').length;
  results.summary.warnings = allChecks.filter(c => c.status === 'warning').length;

  // Final summary
  console.log('📊 FINAL SUMMARY:');
  console.log('='.repeat(50));
  console.log(`Total checks: ${results.summary.total}`);
  console.log(`✅ Passed: ${results.summary.passed}`);
  console.log(`❌ Failed: ${results.summary.failed}`);
  console.log(`⚠️ Warnings: ${results.summary.warnings}`);
  console.log('='.repeat(50));

  if (results.summary.failed === 0) {
    console.log('🎉 All critical tests passed! Database is healthy.');
  } else if (results.summary.failed <= 2) {
    console.log('⚠️ Minor issues detected. Review failed tests.');
  } else {
    console.log('❌ Major issues detected. Database needs attention.');
  }

  return results;
}

// Run the test
testDatabaseHealth().then(results => {
  console.log('\n✨ Health check completed!');
  process.exit(results.summary.failed > 0 ? 1 : 0);
}).catch(error => {
  console.error('💥 Health check failed:', error);
  process.exit(1);
});
