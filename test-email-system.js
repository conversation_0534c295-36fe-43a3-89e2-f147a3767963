#!/usr/bin/env node

console.log('🚀 Starting email system test...');

// Test email functionality
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

console.log('📦 Modules loaded successfully');

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

console.log('🔑 Environment check:');
console.log('   URL present:', !!supabaseUrl);
console.log('   Key present:', !!supabaseKey);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('   REACT_APP_SUPABASE_URL:', supabaseUrl ? 'SET' : 'NOT SET');
  console.log('   REACT_APP_SUPABASE_ANON_KEY:', supabaseKey ? 'SET' : 'NOT SET');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testEmailFunction() {
  console.log('🧪 Testing email functionality...');
  console.log('='.repeat(50));
  
  try {
    // Test 1: Check if Supabase functions are available
    console.log('1️⃣  Checking Supabase functions availability...');
    
    if (!supabase.functions) {
      console.log('   ❌ Supabase functions not available');
      return false;
    } else {
      console.log('   ✅ Supabase functions available');
    }

    // Test 2: Try to invoke the send-email function
    console.log('\n2️⃣  Testing send-email function...');
    
    const testOrderData = {
      id: 'test-order-123',
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      customer_phone: '+1234567890',
      total_amount: 100,
      created_at: new Date().toISOString(),
      shipping_address: {
        city: 'Test City',
        nova_poshta_office: '1'
      }
    };

    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        type: 'order_confirmation',
        orderId: testOrderData.id,
        orderData: testOrderData
      }
    });

    if (error) {
      console.log('   ❌ Error invoking send-email function:', error);
      
      // Check for common error types
      if (error.message && error.message.includes('not found')) {
        console.log('   💡 Function "send-email" may not be deployed');
      } else if (error.message && error.message.includes('unauthorized')) {
        console.log('   💡 Check authentication and function permissions');
      } else if (error.message && error.message.includes('network')) {
        console.log('   💡 Network connectivity issue');
      }
      
      return false;
    } else {
      console.log('   ✅ send-email function responded:', data);
      return true;
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

async function testEmailServiceDirectly() {
  console.log('\n3️⃣  Testing EmailService directly...');
  
  try {
    // Import EmailService (require doesn't work well with ES modules in this context)
    const emailServiceCode = `
      import { EmailService } from './src/services/emailService.js';
      
      const testOrder = {
        id: 'test-order-456',
        customer_name: 'Direct Test Customer',
        customer_email: '<EMAIL>',
        customer_phone: '+1234567890',
        total_amount: 200,
        created_at: new Date().toISOString()
      };
      
      try {
        await EmailService.sendOrderConfirmation(testOrder.id, testOrder);
        console.log('   ✅ EmailService test successful');
      } catch (error) {
        console.log('   ❌ EmailService test failed:', error.message);
      }
    `;
    
    console.log('   ⚠️  EmailService direct test requires ES module support');
    console.log('   💡 This would be tested in the browser/React environment');
    
  } catch (error) {
    console.log('   ❌ Could not test EmailService directly:', error.message);
  }
}

async function main() {
  console.log('📧 Email System Test');
  console.log('='.repeat(50));
  
  const functionWorks = await testEmailFunction();
  await testEmailServiceDirectly();
  
  console.log('='.repeat(50));
  
  if (functionWorks) {
    console.log('✅ Email system appears to be working');
    console.log('💡 Try creating a test order to verify end-to-end functionality');
  } else {
    console.log('❌ Email system has issues');
    console.log('🔧 Possible solutions:');
    console.log('   1. Deploy the send-email Edge Function to Supabase');
    console.log('   2. Check Supabase project settings and permissions');
    console.log('   3. Verify environment variables are correct');
    console.log('   4. Check Supabase function logs in dashboard');
  }
}

main().catch(console.error);
