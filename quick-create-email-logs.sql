-- Quick check and create email_logs table if missing
DO $$
BEGIN
    -- Check if table exists
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'email_logs') THEN
        -- Create the table
        CREATE TABLE email_logs (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
            recipient TEXT NOT NULL,
            email_type TEXT NOT NULL CHECK (email_type IN ('order_confirmation', 'status_update')),
            subject TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'bounced')),
            error_message TEXT,
            sent_at TIMESTAMPTZ,
            external_id TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX idx_email_logs_order_id ON email_logs(order_id);
        CREATE INDEX idx_email_logs_status ON email_logs(status);
        CREATE INDEX idx_email_logs_created_at ON email_logs(created_at);

        -- Enable RLS
        ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies
        CREATE POLICY "Service role can manage email logs" ON email_logs FOR ALL TO service_role;
        CREATE POLICY "Authenticated users can view email logs" ON email_logs FOR SELECT TO authenticated;

        RAISE NOTICE 'email_logs table created successfully';
    ELSE
        RAISE NOTICE 'email_logs table already exists';
    END IF;
END $$;
